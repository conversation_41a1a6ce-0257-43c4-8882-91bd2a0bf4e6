/**
 * SVG转PNG图标转换脚本
 * 用于将项目中的SVG图标转换为PNG格式，解决微信小程序对SVG支持有限的问题
 */

const fs = require('fs');
const path = require('path');

// 需要转换的SVG图标列表
const svgIcons = [
  // images/icons/ 目录下的SVG文件
  {
    input: 'images/icons/search.svg',
    output: 'images/icons/search.png',
    size: '24x24',
    description: '搜索图标'
  },
  {
    input: 'images/icons/cart.svg',
    output: 'images/icons/cart.png',
    size: '24x24',
    description: '购物车图标'
  },
  {
    input: 'images/icons/list.svg',
    output: 'images/icons/list.png',
    size: '24x24',
    description: '列表视图图标'
  },
  {
    input: 'images/icons/grid.svg',
    output: 'images/icons/grid.png',
    size: '24x24',
    description: '网格视图图标'
  },
  {
    input: 'images/icons/eye.svg',
    output: 'images/icons/eye.png',
    size: '24x24',
    description: '查看详情图标'
  },
  {
    input: 'images/icons/package.svg',
    output: 'images/icons/package.png',
    size: '48x48',
    description: '包裹/空状态图标'
  },
  {
    input: 'images/icons/edit.svg',
    output: 'images/icons/edit.png',
    size: '24x24',
    description: '编辑图标'
  },
  {
    input: 'images/icons/trash.svg',
    output: 'images/icons/trash.png',
    size: '24x24',
    description: '删除图标'
  },
  {
    input: 'images/icons/plus.svg',
    output: 'images/icons/plus.png',
    size: '24x24',
    description: '添加图标'
  },
  {
    input: 'images/icons/check-circle.svg',
    output: 'images/icons/check-circle.png',
    size: '24x24',
    description: '完成图标'
  },
  {
    input: 'images/icons/clock.svg',
    output: 'images/icons/clock.png',
    size: '24x24',
    description: '时间图标'
  },
  {
    input: 'images/icons/map-pin.svg',
    output: 'images/icons/map-pin.png',
    size: '24x24',
    description: '位置图标'
  },
  {
    input: 'images/icons/star.svg',
    output: 'images/icons/star.png',
    size: '24x24',
    description: '星标图标'
  },
  {
    input: 'images/icons/truck.svg',
    output: 'images/icons/truck.png',
    size: '24x24',
    description: '配送图标'
  },

  // assets/icons/ 目录下的SVG文件
  {
    input: 'assets/icons/home_new.svg',
    output: 'assets/icons/home_new.png',
    size: '81x81',
    description: '首页导航图标'
  },
  {
    input: 'assets/icons/health_new.svg',
    output: 'assets/icons/health_new.png',
    size: '81x81',
    description: '健康导航图标'
  },
  {
    input: 'assets/icons/production_new.svg',
    output: 'assets/icons/production_new.png',
    size: '81x81',
    description: '生产导航图标'
  },
  {
    input: 'assets/icons/shop_new.svg',
    output: 'assets/icons/shop_new.png',
    size: '81x81',
    description: '商城导航图标'
  },
  {
    input: 'assets/icons/profile_new.svg',
    output: 'assets/icons/profile_new.png',
    size: '81x81',
    description: '个人中心导航图标'
  },
  {
    input: 'assets/icons/home_icon.svg',
    output: 'assets/icons/home_icon.png',
    size: '48x48',
    description: '首页功能图标'
  },
  {
    input: 'assets/icons/health_icon.svg',
    output: 'assets/icons/health_icon.png',
    size: '48x48',
    description: '健康功能图标'
  },
  {
    input: 'assets/icons/production_icon.svg',
    output: 'assets/icons/production_icon.png',
    size: '48x48',
    description: '生产功能图标'
  },
  {
    input: 'assets/icons/shop_icon.svg',
    output: 'assets/icons/shop_icon.png',
    size: '48x48',
    description: '商城功能图标'
  },
  {
    input: 'assets/icons/profile_icon.svg',
    output: 'assets/icons/profile_icon.png',
    size: '48x48',
    description: '个人中心功能图标'
  }
];

// 颜色配置
const colors = {
  default: '#666666',      // 默认颜色
  primary: '#0066CC',      // 主色调
  selected: '#0066CC',     // 选中状态
  unselected: '#7A7E83'    // 未选中状态
};


// 检查SVG文件是否存在
svgIcons.forEach((icon, index) => {
  const exists = fs.existsSync(icon.input);
  const status = exists ? '✅' : '❌';
  `);
});

');
svgIcons.forEach(icon => {
});
