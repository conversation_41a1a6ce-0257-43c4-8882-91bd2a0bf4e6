#!/usr/bin/env node
/**
 * 批量修复小程序组件的属性类型检查问题
 * 自动为所有字符串类型的组件属性添加安全的类型检查
 */

const fs = require('fs');
const path = require('path');

/**
 * 递归查找目录下的所有JS文件
 * @param {string} dir 目录路径
 * @param {string[]} files 文件列表
 * @returns {string[]}
 */
function findJSFiles(dir, files = []) {
  const entries = fs.readdirSync(dir);
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !entry.startsWith('.') && entry !== 'node_modules') {
      findJSFiles(fullPath, files);
    } else if (stat.isFile() && entry.endsWith('.js') && !entry.includes('fix-component-properties')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * 检查文件是否是组件文件
 * @param {string} content 文件内容
 * @returns {boolean}
 */
function isComponentFile(content) {
  return content.includes('Component({') && content.includes('properties:');
}

/**
 * 检查属性是否需要修复
 * @param {string} propertyBlock 属性块内容
 * @returns {boolean}
 */
function needsPropertyFix(propertyBlock) {
  return propertyBlock.includes('type: String') && !propertyBlock.includes('observer:');
}

/**
 * 修复组件属性的类型检查
 * @param {string} content 文件内容
 * @returns {string} 修复后的内容
 */
function fixComponentProperties(content) {
  // 如果已经引入了工具库，跳过
  if (content.includes('component-utils')) {
    return content;
  }
  
  // 如果不是组件文件，跳过
  if (!isComponentFile(content)) {
    return content;
  }
  
  console.log('正在修复组件属性...');
  
  let fixedContent = content;
  
  // 1. 添加工具库引入
  const importLine = "const { mixinComponentUtils, createPropertyObserver } = require('../../utils/component-utils');";
  
  // 查找合适的位置插入import
  const firstRequire = fixedContent.indexOf("require('");
  if (firstRequire !== -1) {
    const lineEnd = fixedContent.indexOf('\n', firstRequire);
    fixedContent = fixedContent.slice(0, lineEnd + 1) + importLine + '\n' + fixedContent.slice(lineEnd + 1);
  } else {
    // 如果没有其他require，插入到文件开头
    const firstLine = fixedContent.indexOf('\n');
    fixedContent = fixedContent.slice(0, firstLine + 1) + importLine + '\n' + fixedContent.slice(firstLine + 1);
  }
  
  // 2. 将Component({改为配置对象
  fixedContent = fixedContent.replace(/Component\({/, 'const componentConfig = {');
  
  // 3. 修复字符串属性，添加observer
  const stringPropertyRegex = /(\w+):\s*{\s*type:\s*String,\s*value:\s*['"][^'"]*['"](\s*)(},?)/g;
  fixedContent = fixedContent.replace(stringPropertyRegex, (match, propName, spacing, closing) => {
    const value = match.match(/value:\s*['"]([^'"]*)['"]/) || ['', ''];
    const defaultValue = value[1];
    return `${propName}: {
      type: String,
      value: '${defaultValue}',
      observer: createPropertyObserver('string', '${defaultValue}')${spacing}${closing}`;
  });
  
  // 4. 在文件末尾添加组件注册
  if (fixedContent.includes('});') && !fixedContent.includes('Component(componentConfig)')) {
    const lastBrace = fixedContent.lastIndexOf('});');
    fixedContent = fixedContent.slice(0, lastBrace) + '};\n\n' +
      '// 应用组件工具混入\n' +
      'mixinComponentUtils(componentConfig);\n\n' +
      '// 注册组件\n' +
      'Component(componentConfig);';
  }
  
  return fixedContent;
}

/**
 * 主执行函数
 */
function main() {
  const componentDir = path.join(process.cwd(), 'components');
  
  if (!fs.existsSync(componentDir)) {
    console.error('未找到components目录');
    return;
  }
  
  console.log('开始扫描组件文件...');
  const jsFiles = findJSFiles(componentDir);
  console.log(`找到 ${jsFiles.length} 个JS文件`);
  
  let fixedCount = 0;
  
  for (const file of jsFiles) {
    try {
      const content = fs.readFileSync(file, 'utf8');
      const fixedContent = fixComponentProperties(content);
      
      if (fixedContent !== content) {
        // 备份原文件
        fs.writeFileSync(file + '.backup', content);
        // 写入修复后的内容
        fs.writeFileSync(file, fixedContent);
        console.log(`✅ 已修复: ${file}`);
        fixedCount++;
      }
    } catch (error) {
      console.error(`❌ 修复失败: ${file}, 错误: ${error.message}`);
    }
  }
  
  console.log(`\n修复完成！共修复了 ${fixedCount} 个组件文件`);
  
  if (fixedCount > 0) {
    console.log('\n注意事项：');
    console.log('1. 原文件已备份为 .backup 后缀');
    console.log('2. 请测试修复后的组件是否正常工作');
    console.log('3. 确认无误后可删除备份文件');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { fixComponentProperties, isComponentFile, needsPropertyFix };