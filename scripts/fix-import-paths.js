#!/usr/bin/env node
/**
 * 修复组件工具库的引入路径
 */

const fs = require('fs');
const path = require('path');

function findJSFiles(dir, files = []) {
  const entries = fs.readdirSync(dir);
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !entry.startsWith('.') && entry !== 'node_modules') {
      findJSFiles(fullPath, files);
    } else if (stat.isFile() && entry.endsWith('.js')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

function calculateRelativePath(fromFile, toFile) {
  const fromDir = path.dirname(fromFile);
  const relativePath = path.relative(fromDir, toFile);
  return relativePath.replace(/\\/g, '/');
}

function fixImportPaths() {
  const componentDir = path.join(process.cwd(), 'components');
  const utilsFile = path.join(process.cwd(), 'utils', 'component-utils.js');
  
  const jsFiles = findJSFiles(componentDir);
  let fixedCount = 0;
  
  for (const file of jsFiles) {
    try {
      let content = fs.readFileSync(file, 'utf8');
      
      if (content.includes('component-utils')) {
        const correctPath = calculateRelativePath(file, utilsFile);
        
        // 替换错误的路径
        const oldPattern = /require\(['"][^'"]*component-utils['"]\)/g;
        const newImport = `require('${correctPath}')`;
        
        const newContent = content.replace(oldPattern, newImport);
        
        if (newContent !== content) {
          fs.writeFileSync(file, newContent);
          console.log(`✅ 已修复路径: ${file} -> ${correctPath}`);
          fixedCount++;
        }
      }
    } catch (error) {
      console.error(`❌ 修复失败: ${file}, 错误: ${error.message}`);
    }
  }
  
  console.log(`\n修复完成！共修复了 ${fixedCount} 个文件的引入路径`);
}

fixImportPaths();