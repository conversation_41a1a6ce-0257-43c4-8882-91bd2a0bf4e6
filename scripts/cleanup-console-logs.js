#!/usr/bin/env node
/**
 * 清理console.log脚本
 * Console Log Cleanup Script
 * 
 * 自动清理项目中的console.log语句，保留错误处理和调试相关的输出
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 配置
const CONFIG = {
  // 需要处理的文件扩展名
  extensions: ['.js'],
  // 排除的目录
  excludeDirs: ['node_modules', '.git', 'logs', 'dist', 'build'],
  // 排除的文件
  excludeFiles: ['cleanup-console-logs.js'],
  // 保留的console语句模式（这些不会被清理）
  keepPatterns: [
    /console\.error/,
    /console\.warn/,
    /console\.trace/,
    /console\.assert/,
    // 生产环境条件判断的console
    /process\.env\.NODE_ENV.*console/,
    // 明确标记为调试的console
    /\/\*\s*DEBUG\s*\*\/.*console/,
    /\/\/\s*DEBUG.*console/
  ],
  // 需要清理的console语句模式
  cleanPatterns: [
    /console\.log\s*\([^)]*\)\s*;?/g,
    /console\.info\s*\([^)]*\)\s*;?/g,
    /console\.debug\s*\([^)]*\)\s*;?/g,
    /console\.table\s*\([^)]*\)\s*;?/g,
    /console\.count\s*\([^)]*\)\s*;?/g,
    /console\.time\s*\([^)]*\)\s*;?/g,
    /console\.timeEnd\s*\([^)]*\)\s*;?/g
  ]
};

class ConsoleLogCleaner {
  constructor() {
    this.stats = {
      filesProcessed: 0,
      filesModified: 0,
      logsRemoved: 0,
      logsKept: 0
    };
  }

  /**
   * 主执行函数
   */
  async run() {
    console.log('🧹 开始清理项目中的console.log语句...\n');
    
    try {
      await this.processDirectory('./');
      this.printSummary();
    } catch (error) {
      console.error('❌ 清理过程中发生错误:', error);
      process.exit(1);
    }
  }

  /**
   * 处理目录
   */
  async processDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        if (!CONFIG.excludeDirs.includes(item)) {
          await this.processDirectory(fullPath);
        }
      } else if (stat.isFile()) {
        if (this.shouldProcessFile(fullPath)) {
          await this.processFile(fullPath);
        }
      }
    }
  }

  /**
   * 判断是否应该处理该文件
   */
  shouldProcessFile(filePath) {
    const ext = path.extname(filePath);
    const fileName = path.basename(filePath);
    
    return CONFIG.extensions.includes(ext) && 
           !CONFIG.excludeFiles.includes(fileName);
  }

  /**
   * 处理单个文件
   */
  async processFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const modifiedContent = this.cleanConsoleStatements(content, filePath);
      
      this.stats.filesProcessed++;
      
      if (content !== modifiedContent) {
        // 创建备份
        this.createBackup(filePath, content);
        
        // 写入修改后的内容
        fs.writeFileSync(filePath, modifiedContent, 'utf8');
        this.stats.filesModified++;
        
        console.log(`✅ 已处理: ${filePath}`);
      }
    } catch (error) {
      console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    }
  }

  /**
   * 清理console语句
   */
  cleanConsoleStatements(content, filePath) {
    const lines = content.split('\n');
    const modifiedLines = [];
    let logsRemovedInFile = 0;
    let logsKeptInFile = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();
      
      // 检查是否包含console语句
      if (this.containsConsoleStatement(trimmedLine)) {
        // 检查是否应该保留
        if (this.shouldKeepConsoleStatement(line)) {
          modifiedLines.push(line);
          logsKeptInFile++;
        } else {
          // 移除console语句
          const cleanedLine = this.removeConsoleFromLine(line);
          if (cleanedLine.trim()) {
            modifiedLines.push(cleanedLine);
          }
          logsRemovedInFile++;
        }
      } else {
        modifiedLines.push(line);
      }
    }

    this.stats.logsRemoved += logsRemovedInFile;
    this.stats.logsKept += logsKeptInFile;

    if (logsRemovedInFile > 0) {
      console.log(`  📝 ${path.relative('./', filePath)}: 移除${logsRemovedInFile}个console语句，保留${logsKeptInFile}个`);
    }

    return modifiedLines.join('\n');
  }

  /**
   * 检查行是否包含console语句
   */
  containsConsoleStatement(line) {
    return /console\.\w+\s*\(/.test(line);
  }

  /**
   * 检查是否应该保留console语句
   */
  shouldKeepConsoleStatement(line) {
    return CONFIG.keepPatterns.some(pattern => pattern.test(line));
  }

  /**
   * 从行中移除console语句
   */
  removeConsoleFromLine(line) {
    let result = line;
    
    for (const pattern of CONFIG.cleanPatterns) {
      result = result.replace(pattern, '');
    }
    
    return result;
  }

  /**
   * 创建备份文件
   */
  createBackup(filePath, content) {
    const backupDir = path.join(path.dirname(filePath), '.console-cleanup-backup');
    
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }
    
    const backupPath = path.join(backupDir, path.basename(filePath) + '.backup');
    fs.writeFileSync(backupPath, content, 'utf8');
  }

  /**
   * 打印清理摘要
   */
  printSummary() {
    console.log('\n📊 清理完成统计:');
    console.log('==========================================');
    console.log(`📁 处理的文件数量: ${this.stats.filesProcessed}`);
    console.log(`✏️  修改的文件数量: ${this.stats.filesModified}`);
    console.log(`🗑️  移除的console语句: ${this.stats.logsRemoved}`);
    console.log(`💾 保留的console语句: ${this.stats.logsKept}`);
    console.log('');
    
    if (this.stats.filesModified > 0) {
      console.log('💡 提示:');
      console.log('- 备份文件已保存在各目录的 .console-cleanup-backup/ 文件夹中');
      console.log('- 请仔细检查修改后的代码，确保没有破坏重要的调试逻辑');
      console.log('- 如果需要恢复，可以从备份文件中还原');
      console.log('');
      console.log('🚀 建议接下来:');
      console.log('1. 运行测试确保代码正常工作');
      console.log('2. 检查Git差异确认修改正确');
      console.log('3. 在确认无误后删除备份文件');
    } else {
      console.log('✨ 没有发现需要清理的console.log语句');
    }
  }
}

// 检查是否需要用户确认
async function getUserConfirmation() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    rl.question('⚠️  此操作将修改代码文件，是否继续？(y/N) ', (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

// 主程序
async function main() {
  const args = process.argv.slice(2);
  const forceMode = args.includes('--force') || args.includes('-f');

  if (!forceMode) {
    const confirmed = await getUserConfirmation();
    if (!confirmed) {
      console.log('❌ 操作已取消');
      process.exit(0);
    }
  }

  const cleaner = new ConsoleLogCleaner();
  await cleaner.run();
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = ConsoleLogCleaner;