/**
 * 智慧养鹅小程序 - 核心设计系统
 * 基于微信小程序设计规范和最佳实践
 * 包含：CSS变量、基础样式、设计令牌
 */

/* ==================== CSS 变量定义 ==================== */
page {
  /* 主色系 - 基于微信小程序设计规范 */
  --primary: #0066CC;
  --primary-light: #4D94FF;
  --primary-dark: #003D7A;
  --primary-subtle: rgba(0, 102, 204, 0.1);
  
  /* 辅助色系 */
  --secondary: #0099CC;
  --accent: #FF6B35;
  --accent-light: #FF8A50;
  
  /* 语义色系 */
  --success: #52C41A;
  --success-light: #73D13D;
  --success-bg: #F6FFED;
  --warning: #FAAD14;
  --warning-light: #FFC53D;
  --warning-bg: #FFFBE6;
  --error: #FF4D4F;
  --error-light: #FF7875;
  --error-bg: #FFF2F0;
  --info: #1890FF;
  --info-light: #40A9FF;
  --info-bg: #E6F7FF;
  
  /* 中性色系 */
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8C8C8C;
  --text-quaternary: #BFBFBF;
  --text-disabled: #D9D9D9;
  --text-inverse: #FFFFFF;
  
  /* 背景色系 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #FAFAFA;
  --bg-tertiary: #F5F5F5;
  --bg-quaternary: #F0F0F0;
  --bg-disabled: #F5F5F5;
  --bg-mask: rgba(0, 0, 0, 0.45);
  
  /* 边框色系 */
  --border-light: #F0F0F0;
  --border-base: #D9D9D9;
  --border-dark: #BFBFBF;
  --border-primary: var(--primary);
  
  /* 阴影系统 */
  --shadow-sm: 0 1rpx 2rpx rgba(0, 0, 0, 0.03);
  --shadow-base: 0 1rpx 8rpx rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
  
  /* 间距系统 */
  --space-xs: 4rpx;
  --space-sm: 8rpx;
  --space-md: 12rpx;
  --space-lg: 16rpx;
  --space-xl: 24rpx;
  --space-2xl: 32rpx;
  --space-3xl: 48rpx;
  --space-4xl: 64rpx;
  
  /* 圆角系统 */
  --radius-xs: 2rpx;
  --radius-sm: 4rpx;
  --radius-md: 8rpx;
  --radius-lg: 12rpx;
  --radius-xl: 16rpx;
  --radius-2xl: 20rpx;
  --radius-full: 50%;
  
  /* 字体系统 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  
  /* 字体大小 */
  --text-xs: 20rpx;
  --text-sm: 24rpx;
  --text-base: 28rpx;
  --text-lg: 32rpx;
  --text-xl: 36rpx;
  --text-2xl: 40rpx;
  --text-3xl: 48rpx;
  --text-4xl: 56rpx;
  
  /* 字体重量 */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  
  /* 行高 */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
  
  /* 动画时长 */
  --duration-fast: 150ms;
  --duration-base: 300ms;
  --duration-slow: 500ms;
  
  /* 缓动函数 */
  --ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);
  --ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  --ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* ==================== 基础样式重置 ==================== */
* {
  box-sizing: border-box;
}

page {
  font-family: var(--font-family);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ==================== 文本样式 ==================== */
.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-4xl { font-size: var(--text-4xl); }

.font-light { font-weight: var(--font-light); }
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-quaternary { color: var(--text-quaternary); }
.text-disabled { color: var(--text-disabled); }
.text-inverse { color: var(--text-inverse); }

.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-error { color: var(--error); }
.text-info { color: var(--info); }

/* ==================== 背景色 ==================== */
.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }
.bg-quaternary { background-color: var(--bg-quaternary); }

.bg-success { background-color: var(--success-bg); }
.bg-warning { background-color: var(--warning-bg); }
.bg-error { background-color: var(--error-bg); }
.bg-info { background-color: var(--info-bg); }

/* ==================== 间距系统 ==================== */
.p-xs { padding: var(--space-xs); }
.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }
.p-xl { padding: var(--space-xl); }
.p-2xl { padding: var(--space-2xl); }
.p-3xl { padding: var(--space-3xl); }
.p-4xl { padding: var(--space-4xl); }

.m-xs { margin: var(--space-xs); }
.m-sm { margin: var(--space-sm); }
.m-md { margin: var(--space-md); }
.m-lg { margin: var(--space-lg); }
.m-xl { margin: var(--space-xl); }
.m-2xl { margin: var(--space-2xl); }
.m-3xl { margin: var(--space-3xl); }
.m-4xl { margin: var(--space-4xl); }

/* ==================== 圆角系统 ==================== */
.rounded-xs { border-radius: var(--radius-xs); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

/* ==================== 阴影系统 ==================== */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-base { box-shadow: var(--shadow-base); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* ==================== 边框系统 ==================== */
.border { border: 1rpx solid var(--border-base); }
.border-light { border: 1rpx solid var(--border-light); }
.border-dark { border: 1rpx solid var(--border-dark); }
.border-primary { border: 1rpx solid var(--border-primary); }

/* ==================== 布局工具类 ==================== */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.overflow-hidden { overflow: hidden; }
.overflow-scroll { overflow: scroll; }

.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
