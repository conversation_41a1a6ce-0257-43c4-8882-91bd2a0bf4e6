/**
 * 智慧养鹅小程序 - 布局样式系统
 * 包含：容器、页面布局、网格系统、导航等
 */

/* ==================== 容器系统 ==================== */
.container {
  width: 100%;
  max-width: 750rpx;
  margin: 0 auto;
  padding: 0 var(--space-lg);
  box-sizing: border-box;
}

.container-sm { max-width: 640rpx; }
.container-md { max-width: 768rpx; }
.container-lg { max-width: 1024rpx; }
.container-xl { max-width: 1280rpx; }

.container-fluid {
  width: 100%;
  padding: 0 var(--space-lg);
}

/* ==================== 页面布局 ==================== */
.page {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
}

.page-header {
  position: sticky;
  top: 0;
  z-index: 40;
  background-color: var(--bg-primary);
  border-bottom: 1rpx solid var(--border-light);
  padding: var(--space-lg);
}

.page-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  text-align: center;
}

.page-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  text-align: center;
  margin-top: var(--space-xs);
}

.page-content {
  flex: 1;
  padding: var(--space-lg);
}

.page-footer {
  padding: var(--space-lg);
  background-color: var(--bg-primary);
  border-top: 1rpx solid var(--border-light);
}

/* ==================== 网格系统 ==================== */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(var(--space-sm) * -1);
}

.col {
  flex: 1;
  padding: 0 var(--space-sm);
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* ==================== 导航组件 ==================== */
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-lg);
  background-color: var(--bg-primary);
  border-bottom: 1rpx solid var(--border-light);
}

.navbar-brand {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.navbar-nav {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
}

.navbar-item {
  margin-left: var(--space-lg);
}

.navbar-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: var(--text-base);
  transition: color var(--duration-base) var(--ease-out);
}

.navbar-link.active {
  color: var(--primary);
}

/* ==================== 标签页组件 ==================== */
.tabs {
  background-color: var(--bg-primary);
  border-bottom: 1rpx solid var(--border-light);
}

.tabs-nav {
  display: flex;
  align-items: center;
}

.tabs-item {
  flex: 1;
  padding: var(--space-lg);
  text-align: center;
  font-size: var(--text-base);
  color: var(--text-secondary);
  border-bottom: 2rpx solid transparent;
  transition: all var(--duration-base) var(--ease-out);
}

.tabs-item.active {
  color: var(--primary);
  border-bottom-color: var(--primary);
}

.tabs-content {
  padding: var(--space-lg);
}

/* ==================== 面包屑导航 ==================== */
.breadcrumb {
  display: flex;
  align-items: center;
  padding: var(--space-lg);
  background-color: var(--bg-tertiary);
  font-size: var(--text-sm);
}

.breadcrumb-item {
  color: var(--text-tertiary);
}

.breadcrumb-item:not(:last-child)::after {
  content: '/';
  margin: 0 var(--space-sm);
  color: var(--text-quaternary);
}

.breadcrumb-item.active {
  color: var(--text-primary);
}

/* ==================== 侧边栏 ==================== */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 280rpx;
  height: 100vh;
  background-color: var(--bg-primary);
  border-right: 1rpx solid var(--border-light);
  transform: translateX(-100%);
  transition: transform var(--duration-base) var(--ease-out);
  z-index: 50;
}

.sidebar.open {
  transform: translateX(0);
}

.sidebar-header {
  padding: var(--space-xl);
  border-bottom: 1rpx solid var(--border-light);
}

.sidebar-content {
  padding: var(--space-lg);
}

.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-mask);
  opacity: 0;
  visibility: hidden;
  transition: all var(--duration-base) var(--ease-out);
  z-index: 40;
}

.sidebar-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* ==================== 模态框 ==================== */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-mask);
  opacity: 0;
  visibility: hidden;
  transition: all var(--duration-base) var(--ease-out);
  z-index: 50;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 90%;
  max-height: 90%;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform var(--duration-base) var(--ease-out);
}

.modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  padding: var(--space-xl);
  border-bottom: 1rpx solid var(--border-light);
}

.modal-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--space-xl);
}

.modal-footer {
  padding: var(--space-xl);
  border-top: 1rpx solid var(--border-light);
  display: flex;
  justify-content: flex-end;
  gap: var(--space-lg);
}

/* ==================== 抽屉组件 ==================== */
.drawer {
  position: fixed;
  top: 0;
  right: 0;
  width: 80%;
  max-width: 400rpx;
  height: 100vh;
  background-color: var(--bg-primary);
  box-shadow: var(--shadow-xl);
  transform: translateX(100%);
  transition: transform var(--duration-base) var(--ease-out);
  z-index: 50;
}

.drawer.open {
  transform: translateX(0);
}

.drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-mask);
  opacity: 0;
  visibility: hidden;
  transition: all var(--duration-base) var(--ease-out);
  z-index: 40;
}

.drawer-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* ==================== 固定定位 ==================== */
.fixed-top {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 30;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 30;
}

.sticky-top {
  position: sticky;
  top: 0;
  z-index: 20;
}
