/**
 * 智慧养鹅小程序 - 组件样式系统
 * 包含：按钮、卡片、表单、图标等组件样式
 */

/* ==================== 按钮组件 ==================== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  line-height: var(--leading-none);
  text-align: center;
  border: 1rpx solid transparent;
  cursor: pointer;
  transition: all var(--duration-base) var(--ease-out);
  user-select: none;
  white-space: nowrap;
}

.btn-primary {
  background-color: var(--primary);
  color: var(--text-inverse);
  border-color: var(--primary);
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary);
  border-color: var(--primary);
}

.btn-success {
  background-color: var(--success);
  color: var(--text-inverse);
  border-color: var(--success);
}

.btn-warning {
  background-color: var(--warning);
  color: var(--text-inverse);
  border-color: var(--warning);
}

.btn-error {
  background-color: var(--error);
  color: var(--text-inverse);
  border-color: var(--error);
}

.btn-sm {
  padding: var(--space-sm) var(--space-lg);
  font-size: var(--text-sm);
}

.btn-lg {
  padding: var(--space-lg) var(--space-2xl);
  font-size: var(--text-lg);
}

.btn-block {
  width: 100%;
}

.btn-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* ==================== 卡片组件 ==================== */
.card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-base);
  border: 1rpx solid var(--border-light);
  overflow: hidden;
}

.card-sm {
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
}

.card-lg {
  padding: var(--space-2xl);
  border-radius: var(--radius-2xl);
}

.card-header {
  padding-bottom: var(--space-lg);
  border-bottom: 1rpx solid var(--border-light);
  margin-bottom: var(--space-lg);
}

.card-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.card-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-top: var(--space-xs);
}

.card-body {
  flex: 1;
}

.card-footer {
  padding-top: var(--space-lg);
  border-top: 1rpx solid var(--border-light);
  margin-top: var(--space-lg);
}

/* ==================== 表单组件 ==================== */
.form-group {
  margin-bottom: var(--space-xl);
}

.form-label {
  display: block;
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.form-input {
  width: 100%;
  padding: var(--space-md) var(--space-lg);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border: 1rpx solid var(--border-base);
  border-radius: var(--radius-md);
  transition: border-color var(--duration-base) var(--ease-out);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2rpx var(--primary-subtle);
}

.form-input-error {
  border-color: var(--error);
}

.form-input-error:focus {
  border-color: var(--error);
  box-shadow: 0 0 0 2rpx rgba(255, 77, 79, 0.1);
}

.form-help {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  margin-top: var(--space-xs);
}

.form-error {
  font-size: var(--text-sm);
  color: var(--error);
  margin-top: var(--space-xs);
}

/* ==================== 图标组件 ==================== */
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1em;
  height: 1em;
  font-size: inherit;
  vertical-align: middle;
}

.icon-xs { font-size: var(--text-xs); }
.icon-sm { font-size: var(--text-sm); }
.icon-base { font-size: var(--text-base); }
.icon-lg { font-size: var(--text-lg); }
.icon-xl { font-size: var(--text-xl); }
.icon-2xl { font-size: var(--text-2xl); }

/* ==================== 标签组件 ==================== */
.tag {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  line-height: var(--leading-none);
  border-radius: var(--radius-sm);
  white-space: nowrap;
}

.tag-primary {
  background-color: var(--primary-subtle);
  color: var(--primary);
}

.tag-success {
  background-color: var(--success-bg);
  color: var(--success);
}

.tag-warning {
  background-color: var(--warning-bg);
  color: var(--warning);
}

.tag-error {
  background-color: var(--error-bg);
  color: var(--error);
}

.tag-info {
  background-color: var(--info-bg);
  color: var(--info);
}

/* ==================== 加载组件 ==================== */
.loading {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
}

.loading-spinner {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid var(--border-light);
  border-top-color: var(--primary);
  border-radius: 50%;
  animation: spin var(--duration-slow) linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-left: var(--space-sm);
  font-size: var(--text-sm);
}

/* ==================== 分割线组件 ==================== */
.divider {
  height: 1rpx;
  background-color: var(--border-light);
  margin: var(--space-xl) 0;
}

.divider-vertical {
  width: 1rpx;
  height: 100%;
  background-color: var(--border-light);
  margin: 0 var(--space-lg);
}

/* ==================== 徽章组件 ==================== */
.badge {
  position: relative;
  display: inline-block;
}

.badge-dot {
  position: absolute;
  top: 0;
  right: 0;
  width: 8rpx;
  height: 8rpx;
  background-color: var(--error);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.badge-count {
  position: absolute;
  top: 0;
  right: 0;
  min-width: 16rpx;
  height: 16rpx;
  padding: 0 4rpx;
  font-size: 10rpx;
  line-height: 16rpx;
  text-align: center;
  background-color: var(--error);
  color: var(--text-inverse);
  border-radius: 8rpx;
  transform: translate(50%, -50%);
}

/* ==================== 空状态组件 ==================== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-4xl) var(--space-xl);
  text-align: center;
}

.empty-state-icon {
  font-size: var(--text-4xl);
  color: var(--text-quaternary);
  margin-bottom: var(--space-lg);
}

.empty-state-title {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  margin-bottom: var(--space-sm);
}

.empty-state-description {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  line-height: var(--leading-relaxed);
}
