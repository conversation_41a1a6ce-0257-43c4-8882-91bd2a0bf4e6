/* 智慧养鹅小程序 - 设计系统 V2.0 */
/* 基于微信小程序设计规范和最佳实践 */

/* ==================== CSS 变量定义 ==================== */
page {
  /* 主色系 */
  --primary: #0066CC;
  --primary-light: #4D94FF;
  --primary-dark: #003D7A;
  --primary-subtle: rgba(0, 102, 204, 0.1);
  
  /* 辅助色系 */
  --secondary: #0099CC;
  --accent: #FF6B35;
  --accent-light: #FF8A50;
  
  /* 语义色系 */
  --success: #52C41A;
  --success-light: #73D13D;
  --success-bg: #F6FFED;
  --warning: #FAAD14;
  --warning-light: #FFC53D;
  --warning-bg: #FFFBE6;
  --error: #FF4D4F;
  --error-light: #FF7875;
  --error-bg: #FFF2F0;
  --info: #1890FF;
  --info-light: #40A9FF;
  --info-bg: #E6F7FF;
  
  /* 中性色系 */
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8C8C8C;
  --text-quaternary: #BFBFBF;
  --text-disabled: #D9D9D9;
  --text-inverse: #FFFFFF;
  
  /* 背景色系 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #FAFAFA;
  --bg-tertiary: #F5F5F5;
  --bg-quaternary: #F0F0F0;
  --bg-disabled: #F5F5F5;
  --bg-mask: rgba(0, 0, 0, 0.45);
  
  /* 边框色系 */
  --border-light: #F0F0F0;
  --border-medium: #D9D9D9;
  --border-dark: #BFBFBF;
  
  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
  --shadow-md: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 2px 8px -2px rgba(0, 0, 0, 0.05), 0 6px 12px 0 rgba(0, 0, 0, 0.04);
  --shadow-lg: 0 4px 8px 0 rgba(0, 0, 0, 0.04), 0 6px 20px 0 rgba(0, 0, 0, 0.10);
  --shadow-xl: 0 8px 40px 0 rgba(0, 0, 0, 0.12);
  --shadow-primary: 0 4px 12px 0 rgba(0, 102, 204, 0.15);
  
  /* 间距系统 */
  --space-xs: 4rpx;
  --space-sm: 8rpx;
  --space-md: 12rpx;
  --space-lg: 16rpx;
  --space-xl: 24rpx;
  --space-2xl: 32rpx;
  --space-3xl: 48rpx;
  --space-4xl: 64rpx;
  
  /* 圆角系统 */
  --radius-xs: 2rpx;
  --radius-sm: 4rpx;
  --radius-md: 8rpx;
  --radius-lg: 12rpx;
  --radius-xl: 16rpx;
  --radius-2xl: 20rpx;
  --radius-full: 50%;
  
  /* 字体系统 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Consolas', 'Liberation Mono', 'Courier New', monospace;
  
  /* 字体大小 */
  --text-xs: 20rpx;
  --text-sm: 24rpx;
  --text-base: 28rpx;
  --text-lg: 32rpx;
  --text-xl: 36rpx;
  --text-2xl: 40rpx;
  --text-3xl: 48rpx;
  --text-4xl: 56rpx;
  
  /* 行高系统 */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
  
  /* 层级系统 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ==================== 字体系统 ==================== */
/* 基础字体样式 */
.text-xs { font-size: var(--text-xs); line-height: var(--leading-normal); font-family: var(--font-family); }
.text-sm { font-size: var(--text-sm); line-height: var(--leading-normal); font-family: var(--font-family); }
.text-base { font-size: var(--text-base); line-height: var(--leading-normal); font-family: var(--font-family); }
.text-lg { font-size: var(--text-lg); line-height: var(--leading-snug); font-family: var(--font-family); }
.text-xl { font-size: var(--text-xl); line-height: var(--leading-snug); font-family: var(--font-family); }
.text-2xl { font-size: var(--text-2xl); line-height: var(--leading-tight); font-family: var(--font-family); }
.text-3xl { font-size: var(--text-3xl); line-height: var(--leading-tight); font-family: var(--font-family); }
.text-4xl { font-size: var(--text-4xl); line-height: var(--leading-tight); font-family: var(--font-family); }

/* 标题字体系统 */
.text-h1 {
  font-size: var(--text-4xl);
  font-weight: 700;
  line-height: var(--leading-tight);
  color: var(--text-primary);
  font-family: var(--font-family);
  letter-spacing: -0.025em;
}

.text-h2 {
  font-size: var(--text-3xl);
  font-weight: 600;
  line-height: var(--leading-tight);
  color: var(--text-primary);
  font-family: var(--font-family);
  letter-spacing: -0.025em;
}

.text-h3 {
  font-size: var(--text-2xl);
  font-weight: 600;
  line-height: var(--leading-snug);
  color: var(--text-primary);
  font-family: var(--font-family);
}

.text-h4 {
  font-size: var(--text-xl);
  font-weight: 600;
  line-height: var(--leading-snug);
  color: var(--text-primary);
  font-family: var(--font-family);
}

.text-h5 {
  font-size: var(--text-lg);
  font-weight: 600;
  line-height: var(--leading-snug);
  color: var(--text-primary);
  font-family: var(--font-family);
}

.text-h6 {
  font-size: var(--text-base);
  font-weight: 600;
  line-height: var(--leading-normal);
  color: var(--text-primary);
  font-family: var(--font-family);
}

/* 正文字体系统 */
.text-body-lg {
  font-size: var(--text-lg);
  font-weight: 400;
  line-height: var(--leading-relaxed);
  color: var(--text-primary);
  font-family: var(--font-family);
}

.text-body {
  font-size: var(--text-base);
  font-weight: 400;
  line-height: var(--leading-relaxed);
  color: var(--text-primary);
  font-family: var(--font-family);
}

.text-body-sm {
  font-size: var(--text-sm);
  font-weight: 400;
  line-height: var(--leading-normal);
  color: var(--text-secondary);
  font-family: var(--font-family);
}

.text-caption {
  font-size: var(--text-xs);
  font-weight: 400;
  line-height: var(--leading-normal);
  color: var(--text-tertiary);
  font-family: var(--font-family);
}

/* 字体颜色系统 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-quaternary { color: var(--text-quaternary); }
.text-disabled { color: var(--text-disabled); }
.text-inverse { color: var(--text-inverse); }

/* 语义色彩 */
.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-error { color: var(--error); }
.text-info { color: var(--info); }
.text-brand { color: var(--primary); }

/* 字体重量系统 */
.font-thin { font-weight: 100; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

/* 行高系统 */
.leading-none { line-height: var(--leading-none); }
.leading-tight { line-height: var(--leading-tight); }
.leading-snug { line-height: var(--leading-snug); }
.leading-normal { line-height: var(--leading-normal); }
.leading-relaxed { line-height: var(--leading-relaxed); }
.leading-loose { line-height: var(--leading-loose); }

/* 字母间距 */
.tracking-tighter { letter-spacing: -0.05em; }
.tracking-tight { letter-spacing: -0.025em; }
.tracking-normal { letter-spacing: 0em; }
.tracking-wide { letter-spacing: 0.025em; }
.tracking-wider { letter-spacing: 0.05em; }
.tracking-widest { letter-spacing: 0.1em; }

/* ==================== 间距系统 ==================== */
/* Padding 系统 */
.p-0 { padding: 0; }
.p-xs { padding: var(--space-xs); }
.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }
.p-xl { padding: var(--space-xl); }
.p-2xl { padding: var(--space-2xl); }
.p-3xl { padding: var(--space-3xl); }
.p-4xl { padding: var(--space-4xl); }

/* Padding 方向性 */
.px-0 { padding-left: 0; padding-right: 0; }
.px-xs { padding-left: var(--space-xs); padding-right: var(--space-xs); }
.px-sm { padding-left: var(--space-sm); padding-right: var(--space-sm); }
.px-md { padding-left: var(--space-md); padding-right: var(--space-md); }
.px-lg { padding-left: var(--space-lg); padding-right: var(--space-lg); }
.px-xl { padding-left: var(--space-xl); padding-right: var(--space-xl); }
.px-2xl { padding-left: var(--space-2xl); padding-right: var(--space-2xl); }
.px-3xl { padding-left: var(--space-3xl); padding-right: var(--space-3xl); }

.py-0 { padding-top: 0; padding-bottom: 0; }
.py-xs { padding-top: var(--space-xs); padding-bottom: var(--space-xs); }
.py-sm { padding-top: var(--space-sm); padding-bottom: var(--space-sm); }
.py-md { padding-top: var(--space-md); padding-bottom: var(--space-md); }
.py-lg { padding-top: var(--space-lg); padding-bottom: var(--space-lg); }
.py-xl { padding-top: var(--space-xl); padding-bottom: var(--space-xl); }
.py-2xl { padding-top: var(--space-2xl); padding-bottom: var(--space-2xl); }
.py-3xl { padding-top: var(--space-3xl); padding-bottom: var(--space-3xl); }

/* Margin 系统 */
.m-0 { margin: 0; }
.m-xs { margin: var(--space-xs); }
.m-sm { margin: var(--space-sm); }
.m-md { margin: var(--space-md); }
.m-lg { margin: var(--space-lg); }
.m-xl { margin: var(--space-xl); }
.m-2xl { margin: var(--space-2xl); }
.m-3xl { margin: var(--space-3xl); }
.m-4xl { margin: var(--space-4xl); }

/* Margin 方向性 */
.mx-0 { margin-left: 0; margin-right: 0; }
.mx-xs { margin-left: var(--space-xs); margin-right: var(--space-xs); }
.mx-sm { margin-left: var(--space-sm); margin-right: var(--space-sm); }
.mx-md { margin-left: var(--space-md); margin-right: var(--space-md); }
.mx-lg { margin-left: var(--space-lg); margin-right: var(--space-lg); }
.mx-xl { margin-left: var(--space-xl); margin-right: var(--space-xl); }
.mx-auto { margin-left: auto; margin-right: auto; }

.my-0 { margin-top: 0; margin-bottom: 0; }
.my-xs { margin-top: var(--space-xs); margin-bottom: var(--space-xs); }
.my-sm { margin-top: var(--space-sm); margin-bottom: var(--space-sm); }
.my-md { margin-top: var(--space-md); margin-bottom: var(--space-md); }
.my-lg { margin-top: var(--space-lg); margin-bottom: var(--space-lg); }
.my-xl { margin-top: var(--space-xl); margin-bottom: var(--space-xl); }

/* Gap 系统 */
.gap-0 { gap: 0; }
.gap-xs { gap: var(--space-xs); }
.gap-sm { gap: var(--space-sm); }
.gap-md { gap: var(--space-md); }
.gap-lg { gap: var(--space-lg); }
.gap-xl { gap: var(--space-xl); }
.gap-2xl { gap: var(--space-2xl); }

/* ==================== 圆角系统 ==================== */
.rounded-none { border-radius: 0; }
.rounded-xs { border-radius: var(--radius-xs); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-md); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

/* 圆角方向性 */
.rounded-t-sm { border-top-left-radius: var(--radius-sm); border-top-right-radius: var(--radius-sm); }
.rounded-t-md { border-top-left-radius: var(--radius-md); border-top-right-radius: var(--radius-md); }
.rounded-t-lg { border-top-left-radius: var(--radius-lg); border-top-right-radius: var(--radius-lg); }
.rounded-b-sm { border-bottom-left-radius: var(--radius-sm); border-bottom-right-radius: var(--radius-sm); }
.rounded-b-md { border-bottom-left-radius: var(--radius-md); border-bottom-right-radius: var(--radius-md); }
.rounded-b-lg { border-bottom-left-radius: var(--radius-lg); border-bottom-right-radius: var(--radius-lg); }

/* ==================== 阴影系统 ==================== */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-md); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-primary { box-shadow: var(--shadow-primary); }

/* ==================== 边框系统 ==================== */
.border-0 { border-width: 0; }
.border { border-width: 1rpx; }
.border-2 { border-width: 2rpx; }
.border-4 { border-width: 4rpx; }

/* 边框样式 */
.border-solid { border-style: solid; }
.border-dashed { border-style: dashed; }
.border-dotted { border-style: dotted; }

/* 边框颜色 */
.border-light { border-color: var(--border-light); }
.border-medium { border-color: var(--border-medium); }
.border-dark { border-color: var(--border-dark); }
.border-primary { border-color: var(--primary); }
.border-success { border-color: var(--success); }
.border-warning { border-color: var(--warning); }
.border-error { border-color: var(--error); }

/* 边框方向性 */
.border-t { border-top-width: 1rpx; border-top-style: solid; }
.border-b { border-bottom-width: 1rpx; border-bottom-style: solid; }
.border-l { border-left-width: 1rpx; border-left-style: solid; }
.border-r { border-right-width: 1rpx; border-right-style: solid; }

/* ==================== 布局系统 ==================== */
/* 容器系统 */
.container {
  width: 100%;
  max-width: 750rpx;
  margin: 0 auto;
  padding: var(--space-lg);
  box-sizing: border-box;
}

.container-sm { max-width: 640rpx; }
.container-md { max-width: 768rpx; }
.container-lg { max-width: 1024rpx; }
.container-xl { max-width: 1280rpx; }

/* 页面容器 */
.page-container {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
}

/* 卡片系统 */
.card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
  overflow: hidden;
}

.card-sm {
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
}

.card-lg {
  padding: var(--space-2xl);
  border-radius: var(--radius-2xl);
}

.card-header {
  padding-bottom: var(--space-lg);
  border-bottom: 1rpx solid var(--border-light);
  margin-bottom: var(--space-lg);
}

.card-body {
  flex: 1;
}

.card-footer {
  padding-top: var(--space-lg);
  border-top: 1rpx solid var(--border-light);
  margin-top: var(--space-lg);
}

/* Flexbox 布局 */
.flex { display: flex; }
.inline-flex { display: inline-flex; }

/* Flex 方向 */
.flex-row { flex-direction: row; }
.flex-col { flex-direction: column; }
.flex-row-reverse { flex-direction: row-reverse; }
.flex-col-reverse { flex-direction: column-reverse; }

/* Flex 换行 */
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.flex-wrap-reverse { flex-wrap: wrap-reverse; }

/* Flex 项目 */
.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

/* Flex 增长 */
.flex-grow-0 { flex-grow: 0; }
.flex-grow { flex-grow: 1; }

/* Flex 收缩 */
.flex-shrink-0 { flex-shrink: 0; }
.flex-shrink { flex-shrink: 1; }

/* 对齐项目 */
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

/* 对齐内容 */
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

/* 自对齐 */
.self-auto { align-self: auto; }
.self-start { align-self: flex-start; }
.self-end { align-self: flex-end; }
.self-center { align-self: center; }
.self-stretch { align-self: stretch; }
.self-baseline { align-self: baseline; }

/* 定位系统 */
.static { position: static; }
.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }
.sticky { position: sticky; }

/* 层级 */
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
.z-dropdown { z-index: var(--z-dropdown); }
.z-sticky { z-index: var(--z-sticky); }
.z-fixed { z-index: var(--z-fixed); }
.z-modal { z-index: var(--z-modal); }
.z-toast { z-index: var(--z-toast); }

/* 显示 */
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.hidden { display: none; }

/* 可见性 */
.visible { visibility: visible; }
.invisible { visibility: hidden; }

/* 溢出 */
.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }

/* ==================== 按钮系统 ==================== */
/* 基础按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-lg) var(--space-2xl);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-weight: 500;
  font-family: var(--font-family);
  line-height: var(--leading-none);
  border: 1rpx solid transparent;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  user-select: none;
  white-space: nowrap;
  vertical-align: middle;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  gap: var(--space-sm);
}

/* 按钮尺寸 */
.btn-xs {
  padding: var(--space-xs) var(--space-md);
  font-size: var(--text-xs);
  border-radius: var(--radius-sm);
  min-height: 44rpx;
}

.btn-sm {
  padding: var(--space-sm) var(--space-lg);
  font-size: var(--text-sm);
  border-radius: var(--radius-md);
  min-height: 52rpx;
}

.btn-md {
  padding: var(--space-lg) var(--space-2xl);
  font-size: var(--text-base);
  border-radius: var(--radius-lg);
  min-height: 64rpx;
}

.btn-lg {
  padding: var(--space-xl) var(--space-3xl);
  font-size: var(--text-lg);
  border-radius: var(--radius-xl);
  min-height: 80rpx;
}

.btn-xl {
  padding: var(--space-2xl) var(--space-4xl);
  font-size: var(--text-xl);
  border-radius: var(--radius-xl);
  min-height: 96rpx;
}

/* 按钮变体 */
.btn-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: var(--text-inverse);
  border-color: var(--primary);
  box-shadow: var(--shadow-primary);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
  box-shadow: var(--shadow-lg);
}

.btn-primary:active {
  transform: translateY(1rpx);
  box-shadow: var(--shadow-sm);
}

.btn-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-color: var(--border-medium);
}

.btn-secondary:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--border-dark);
}

.btn-ghost {
  background-color: transparent;
  color: var(--primary);
  border-color: var(--primary);
}

.btn-ghost:hover {
  background-color: var(--primary-subtle);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary);
  border-color: var(--primary);
  border-width: 2rpx;
}

.btn-outline:hover {
  background-color: var(--primary);
  color: var(--text-inverse);
}

.btn-text {
  background-color: transparent;
  color: var(--primary);
  border-color: transparent;
  box-shadow: none;
}

.btn-text:hover {
  background-color: var(--primary-subtle);
}

/* 语义化按钮 */
.btn-success {
  background-color: var(--success);
  color: var(--text-inverse);
  border-color: var(--success);
}

.btn-success:hover {
  background-color: var(--success-light);
}

.btn-warning {
  background-color: var(--warning);
  color: var(--text-primary);
  border-color: var(--warning);
}

.btn-warning:hover {
  background-color: var(--warning-light);
}

.btn-error {
  background-color: var(--error);
  color: var(--text-inverse);
  border-color: var(--error);
}

.btn-error:hover {
  background-color: var(--error-light);
}

/* 按钮状态 */
.btn:active {
  transform: scale(0.98);
}

.btn:disabled,
.btn-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-loading {
  position: relative;
  pointer-events: none;
}

.btn-loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid transparent;
  border-top: 3rpx solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 按钮形状 */
.btn-round {
  border-radius: var(--radius-full);
}

.btn-square {
  border-radius: 0;
}

.btn-block {
  width: 100%;
  display: flex;
}

/* 按钮组 */
.btn-group {
  display: inline-flex;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.btn-group .btn {
  border-radius: 0;
  border-right-width: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: var(--radius-lg);
  border-bottom-left-radius: var(--radius-lg);
}

.btn-group .btn:last-child {
  border-top-right-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
  border-right-width: 1rpx;
}

/* 浮动按钮 */
.btn-float {
  position: fixed;
  bottom: var(--space-2xl);
  right: var(--space-2xl);
  width: 112rpx;
  height: 112rpx;
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-fixed);
}

/* 使用类选择器实现旋转效果 */
.spin-start {
  transform: translate(-50%, -50%) rotate(0deg);
}

.spin-end {
  transform: translate(-50%, -50%) rotate(360deg);
  transition: transform 1s linear;
}

/* ==================== 输入框系统 ==================== */
/* 基础输入框 */
.input {
  display: block;
  width: 100%;
  padding: var(--space-lg) var(--space-xl);
  font-size: var(--text-base);
  font-family: var(--font-family);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border: 1rpx solid var(--border-medium);
  border-radius: var(--radius-lg);
  outline: none;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
}

/* 移除伪元素选择器，使用类选择器替代 */
.input-placeholder {
  color: var(--text-tertiary);
}

.input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 6rpx var(--primary-subtle);
}

.input:disabled {
  background-color: var(--bg-disabled);
  color: var(--text-disabled);
  cursor: not-allowed;
}

/* 输入框尺寸 */
.input-sm {
  padding: var(--space-md) var(--space-lg);
  font-size: var(--text-sm);
  border-radius: var(--radius-md);
}

.input-lg {
  padding: var(--space-xl) var(--space-2xl);
  font-size: var(--text-lg);
  border-radius: var(--radius-xl);
}

/* 输入框状态 */
.input-success {
  border-color: var(--success);
}

.input-success:focus {
  border-color: var(--success);
  box-shadow: 0 0 0 6rpx var(--success-bg);
}

.input-warning {
  border-color: var(--warning);
}

.input-warning:focus {
  border-color: var(--warning);
  box-shadow: 0 0 0 6rpx var(--warning-bg);
}

.input-error {
  border-color: var(--error);
}

.input-error:focus {
  border-color: var(--error);
  box-shadow: 0 0 0 6rpx var(--error-bg);
}

/* 输入框组 */
.input-group {
  display: flex;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.input-group .input {
  border-radius: 0;
  border-right-width: 0;
}

.input-group .input:first-child {
  border-top-left-radius: var(--radius-lg);
  border-bottom-left-radius: var(--radius-lg);
}

.input-group .input:last-child {
  border-top-right-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
  border-right-width: 1rpx;
}

/* 文本域 */
.textarea {
  min-height: 120rpx;
  resize: vertical;
  line-height: var(--leading-relaxed);
}

/* ==================== 表单系统 ==================== */
.form-group {
  margin-bottom: var(--space-xl);
}

.form-label {
  display: block;
  margin-bottom: var(--space-sm);
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--text-primary);
}

.form-label-required::after {
  content: '*';
  color: var(--error);
  margin-left: var(--space-xs);
}

.form-help {
  margin-top: var(--space-sm);
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.form-error {
  margin-top: var(--space-sm);
  font-size: var(--text-xs);
  color: var(--error);
}

/* ==================== 动画系统 ==================== */
/* 基础过渡 */
.transition {
  transition: all 0.15s ease-out;
}

.transition-colors {
  transition: color 0.15s ease-out, background-color 0.15s ease-out, border-color 0.15s ease-out;
}

.transition-opacity {
  transition: opacity 0.15s ease-out;
}

.transition-transform {
  transition: transform 0.15s ease-out;
}

.transition-all {
  transition: all 0.15s ease-out;
}

/* 缓动函数 */
.ease-in { transition-timing-function: cubic-bezier(0.4, 0, 1, 1); }
.ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1); }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

/* 持续时间 */
.duration-75 { transition-duration: 75ms; }
.duration-100 { transition-duration: 100ms; }
.duration-150 { transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }

/* 交互动画 */
.hover-lift:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-lg);
}

.scale-press:active {
  transform: scale(0.98);
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.fade-out {
  animation: fadeOut 0.3s ease-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.slide-down {
  animation: slideDown 0.3s ease-out;
}

/* 加载动画 */
.spin {
  animation: spin 1s linear infinite;
}

.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.bounce {
  animation: bounce 1s infinite;
}

/* 使用类选择器实现动画效果 */
.fade-in-start {
  opacity: 0;
}

.fade-in-end {
  opacity: 1;
  transition: opacity 0.3s ease-out;
}

.fade-out-start {
  opacity: 1;
}

.fade-out-end {
  opacity: 0;
  transition: opacity 0.3s ease-out;
}

.slide-up-start {
  opacity: 0;
  transform: translateY(20rpx);
}

.slide-up-end {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.3s ease-out;
}

.slide-down-start {
  opacity: 0;
  transform: translateY(-20rpx);
}

.slide-down-end {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.3s ease-out;
}

.pulse-dim {
  opacity: 0.5;
  transition: opacity 1s cubic-bezier(0.4, 0, 0.6, 1);
}

.pulse-bright {
  opacity: 1;
  transition: opacity 1s cubic-bezier(0.4, 0, 0.6, 1);
}

.bounce-up {
  transform: translateY(-25%);
  transition: transform 0.5s cubic-bezier(0.8, 0, 1, 1);
}

.bounce-down {
  transform: none;
  transition: transform 0.5s cubic-bezier(0, 0, 0.2, 1);
}

/* ==================== 状态系统 ==================== */
/* 通用状态 */
.loading {
  opacity: 0.6;
  pointer-events: none;
  cursor: wait;
}

.disabled {
  opacity: 0.6;
  pointer-events: none;
  cursor: not-allowed;
}

.readonly {
  pointer-events: none;
  background-color: var(--bg-tertiary);
}

/* 可见性 */
.show {
  display: block !important;
}

.hide {
  display: none !important;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

/* 透明度 */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* ==================== 工具类 ==================== */
/* 清除浮动 - 移除伪元素选择器，使用类选择器替代 */
.clearfix-after {
  display: table;
  clear: both;
}

/* 截断文本 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* 选择性 */
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }
.select-auto { user-select: auto; }

/* 指针事件 */
.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

/* 光标样式 */
.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-not-allowed { cursor: not-allowed; }

/* 屏幕阅读器 */
.sr-only {
  position: absolute;
  width: 1rpx;
  height: 1rpx;
  padding: 0;
  margin: -1rpx;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

