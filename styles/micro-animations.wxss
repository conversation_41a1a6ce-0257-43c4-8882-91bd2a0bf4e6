/* 智慧养鹅小程序 - 微动画系统 V1.0 */
/* 提供丰富的微交互动画效果 */

/* ==================== 页面过渡动画 ==================== */

/* 页面进入动画 - 使用类选择器实现 */
.page-enter {
  opacity: 0;
  transform: translateY(50rpx);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
}

/* 页面退出动画 - 使用类选择器实现 */
.page-exit {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.3s cubic-bezier(0.55, 0.06, 0.68, 0.19);
}

.page-exit-active {
  opacity: 0;
  transform: translateY(30rpx);
}

/* ==================== 元素进入动画 ==================== */

/* 淡入动画 - 使用类选择器实现 */
.fade-in {
  opacity: 0;
  transition: opacity 0.6s ease-out;
}

.fade-in-active {
  opacity: 1;
}

/* 从左滑入 */
.slide-in-left {
  animation: slide-in-left 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes slide-in-left {
  0% {
    opacity: 0;
    transform: translateX(-100rpx);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 从右滑入 */
.slide-in-right {
  animation: slide-in-right 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes slide-in-right {
  0% {
    opacity: 0;
    transform: translateX(100rpx);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 从上滑入 */
.slide-in-up {
  animation: slide-in-up 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes slide-in-up {
  0% {
    opacity: 0;
    transform: translateY(100rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 从下滑入 */
.slide-in-down {
  animation: slide-in-down 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes slide-in-down {
  0% {
    opacity: 0;
    transform: translateY(-100rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 缩放进入 */
.scale-in {
  animation: scale-in 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

@keyframes scale-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 旋转进入 */
.rotate-in {
  animation: rotate-in 0.6s ease-out forwards;
}

@keyframes rotate-in {
  0% {
    opacity: 0;
    transform: rotate(-180deg) scale(0.5);
  }
  100% {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}

/* ==================== 错开动画序列 ==================== */

/* 列表项错开动画 */
.stagger-item {
  opacity: 0;
  transform: translateY(30rpx);
  animation: stagger-fade-up 0.6s ease-out forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }
.stagger-item:nth-child(6) { animation-delay: 0.6s; }
.stagger-item:nth-child(7) { animation-delay: 0.7s; }
.stagger-item:nth-child(8) { animation-delay: 0.8s; }

@keyframes stagger-fade-up {
  0% {
    opacity: 0;
    transform: translateY(30rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==================== 交互状态动画 ==================== */

/* 按压反馈 */
.press-feedback {
  transition: transform 0.1s ease;
}

.press-feedback:active {
  transform: scale(0.95);
}

/* 弹性按压 */
.elastic-press {
  transition: transform 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.elastic-press:active {
  transform: scale(0.9);
}

/* 悬浮抬升 */
.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 28rpx rgba(0, 0, 0, 0.12);
}

/* 轻微摇摆 */
.wiggle {
  animation: wiggle 0.5s ease-in-out;
}

/* 使用类选择器实现摆动效果 */
.wiggle-left {
  transform: rotateZ(-15deg);
  transition: transform 0.1s ease-in-out;
}

.wiggle-right {
  transform: rotateZ(10deg);
  transition: transform 0.1s ease-in-out;
}

.wiggle-reset {
  transform: rotateZ(0);
  transition: transform 0.1s ease-in-out;
}

/* 心跳效果 */
.heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite both;
}

/* 使用类选择器实现心跳效果 */
.heartbeat-large {
  transform: scale(1.3);
  transition: transform 0.2s ease-in-out;
}

.heartbeat-normal {
  transform: scale(1);
  transition: transform 0.2s ease-in-out;
}

/* ==================== 数据加载动画 ==================== */

/* 波纹加载 */
.wave-loading {
  display: inline-flex;
  gap: var(--space-xs);
}

.wave-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: var(--radius-full);
  background: var(--primary);
  animation: wave 1.4s ease-in-out infinite both;
}

.wave-dot:nth-child(1) { animation-delay: -0.32s; }
.wave-dot:nth-child(2) { animation-delay: -0.16s; }
.wave-dot:nth-child(3) { animation-delay: 0s; }

/* 使用类选择器实现波浪效果 */
.wave-scale-up {
  transform: scale(1);
  transition: transform 0.3s ease-in-out;
}

.wave-scale-down {
  transform: scale(0);
  transition: transform 0.3s ease-in-out;
}

/* 旋转加载 */
.spin-loading {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid var(--border-light);
  border-top: 4rpx solid var(--primary);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

/* 使用类选择器实现旋转效果 */
.spin-quarter {
  transform: rotate(90deg);
  transition: transform 0.25s linear;
}

.spin-half {
  transform: rotate(180deg);
  transition: transform 0.5s linear;
}

.spin-full {
  transform: rotate(360deg);
  transition: transform 1s linear;
}

.spin-reset {
  transform: rotate(0deg);
  transition: transform 0.25s linear;
}

/* 脉冲加载 */
.pulse-loading {
  width: 40rpx;
  height: 40rpx;
  background: var(--primary);
  border-radius: var(--radius-full);
  animation: pulse-scale 1s ease-in-out infinite;
}

@keyframes pulse-scale {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* ==================== 状态指示动画 ==================== */

/* 成功动画 */
.success-animation {
  animation: success-bounce 0.6s ease-out;
}

@keyframes success-bounce {
  0% { transform: scale(0); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* 错误震动 */
.error-shake {
  animation: error-shake 0.5s ease-in-out;
}

@keyframes error-shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-10rpx); }
  75% { transform: translateX(10rpx); }
}

/* 警告闪烁 */
.warning-blink {
  animation: warning-blink 1s ease-in-out infinite alternate;
}

@keyframes warning-blink {
  0% { opacity: 1; }
  100% { opacity: 0.5; }
}

/* ==================== 高级动画效果 ==================== */

/* 磁性吸附效果 */
.magnetic-hover {
  transition: all 0.3s cubic-bezier(0.23, 1, 0.320, 1);
}

.magnetic-hover:active {
  transform: translateY(-8rpx) rotateX(15deg);
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.15);
}

/* 橡皮筋效果 */
.rubber-band {
  animation: rubber-band 0.8s ease-out;
}

@keyframes rubber-band {
  0% { transform: scale(1); }
  30% { transform: scaleX(1.25) scaleY(0.75); }
  40% { transform: scaleX(0.75) scaleY(1.25); }
  50% { transform: scaleX(1.15) scaleY(0.85); }
  65% { transform: scaleX(0.95) scaleY(1.05); }
  75% { transform: scaleX(1.05) scaleY(0.95); }
  100% { transform: scale(1); }
}

/* 抖动效果 */
.jello {
  animation: jello 0.9s ease-out;
}

@keyframes jello {
  0%, 11.1%, 100% { transform: none; }
  22.2% { transform: skewX(-12.5deg) skewY(-12.5deg); }
  33.3% { transform: skewX(6.25deg) skewY(6.25deg); }
  44.4% { transform: skewX(-3.125deg) skewY(-3.125deg); }
  55.5% { transform: skewX(1.5625deg) skewY(1.5625deg); }
  66.6% { transform: skewX(-0.78125deg) skewY(-0.78125deg); }
  77.7% { transform: skewX(0.390625deg) skewY(0.390625deg); }
  88.8% { transform: skewX(-0.1953125deg) skewY(-0.1953125deg); }
}

/* ==================== 无限循环动画 ==================== */

/* 浮动效果 */
.float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0rpx); }
  50% { transform: translateY(-20rpx); }
}

/* 呼吸效果 */
.breathe {
  animation: breathe 4s ease-in-out infinite;
}

/* 使用类选择器实现呼吸效果 */
.breathe-expand {
  transform: scale(1.05);
  transition: transform 1s ease-in-out;
}

.breathe-contract {
  transform: scale(1);
  transition: transform 1s ease-in-out;
}

/* 流光效果 */
.shimmer {
  position: relative;
  overflow: hidden;
}

/* 移除伪元素选择器，使用类选择器替代 */
.shimmer-overlay {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
}

.shimmer-overlay-active {
  left: 100%;
  transition: left 2s ease-in-out;
}

.shimmer-overlay-reset {
  left: -100%;
  transition: none;
}

/* ==================== 动画工具类 ==================== */

/* 动画延迟类 */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-500 { animation-delay: 0.5s; }
.delay-700 { animation-delay: 0.7s; }
.delay-1000 { animation-delay: 1s; }

/* 动画持续时间类 */
.duration-fast { animation-duration: 0.2s; }
.duration-normal { animation-duration: 0.3s; }
.duration-slow { animation-duration: 0.5s; }
.duration-slower { animation-duration: 0.8s; }

/* 动画缓动函数类 */
.ease-linear { animation-timing-function: linear; }
.ease-in { animation-timing-function: ease-in; }
.ease-out { animation-timing-function: ease-out; }
.ease-in-out { animation-timing-function: ease-in-out; }
.ease-bounce { animation-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55); }

/* 动画填充模式类 */
.fill-forwards { animation-fill-mode: forwards; }
.fill-backwards { animation-fill-mode: backwards; }
.fill-both { animation-fill-mode: both; }

/* 动画播放状态类 */
.paused { animation-play-state: paused; }
.running { animation-play-state: running; }

/* ==================== 性能优化 ==================== */

/* GPU加速优化 */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000rpx;
}

/* 动画性能优化 */
.optimized-animation {
  will-change: transform, opacity;
}

/* 禁用动画（减少动效偏好） - 小程序通过类名控制 */
/* @media (prefers-reduced-motion: reduce) { */ /* 小程序不支持 */
.reduced-motion page,
.reduced-motion view,
.reduced-motion text,
.reduced-motion button,
.reduced-motion input,
.reduced-motion image,
.reduced-motion scroll-view {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
}
/* } */