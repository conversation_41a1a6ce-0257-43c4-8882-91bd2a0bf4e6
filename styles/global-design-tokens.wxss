/**
 * 智慧养鹅小程序 - 统一设计令牌系统
 * 基于微信小程序最佳实践和企业级设计规范
 * 整合global和oa双重系统，提供统一的设计语言
 * 
 * 版本: v2.0
 * 更新: 2024年 - 全面统一设计系统
 */

/* ==================== 颜色系统 ==================== */

/* 主品牌色 - 智慧养鹅蓝 */
:root {
  --primary-color: #007AFF;
  --primary-color-light: #40A9FF;
  --primary-color-dark: #0056CC;
  --primary-color-focus: rgba(0, 122, 255, 0.1);
  --primary-color-hover: rgba(0, 122, 255, 0.8);
  --primary-color-disabled: #A6C8FF;
}

/* 功能色系 */
:root {
  /* 成功色 - 养殖业绿 */
  --success-color: #00A86B;
  --success-color-light: #52C41A;
  --success-color-dark: #006644;
  --success-color-focus: rgba(0, 168, 107, 0.1);
  
  /* 警告色 - 橙色 */
  --warning-color: #FF9500;
  --warning-color-light: #FFA940;
  --warning-color-dark: #CC7700;
  --warning-color-focus: rgba(255, 149, 0, 0.1);
  
  /* 错误色 - 红色 */
  --error-color: #FF3B30;
  --error-color-light: #FF4D4F;
  --error-color-dark: #CC2E26;
  --error-color-focus: rgba(255, 59, 48, 0.1);
  
  /* 信息色 - 蓝色 */
  --info-color: #1890FF;
  --info-color-light: #40A9FF;
  --info-color-dark: #096DD9;
  --info-color-focus: rgba(24, 144, 255, 0.1);
}

/* 中性色系 */
:root {
  /* 文字颜色 */
  --text-color-primary: #262626;
  --text-color-secondary: #595959;
  --text-color-tertiary: #8C8C8C;
  --text-color-placeholder: #BFBFBF;
  --text-color-disabled: #D9D9D9;
  --text-color-inverse: #FFFFFF;
  
  /* 背景颜色 */
  --bg-color-page: #F5F5F5;
  --bg-color-container: #FFFFFF;
  --bg-color-hover: #FAFAFA;
  --bg-color-active: #F0F0F0;
  --bg-color-disabled: #F5F5F5;
  --bg-color-mask: rgba(0, 0, 0, 0.6);
  
  /* 边框颜色 */
  --border-color: #E5E5E5;
  --border-color-hover: #D9D9D9;
  --border-color-focus: var(--primary-color);
  --border-color-disabled: #F0F0F0;
}

/* 模块主题色 */
:root {
  /* OA办公模块 */
  --oa-theme-color: #007AFF;
  --oa-theme-bg: rgba(0, 122, 255, 0.05);
  
  /* 健康管理模块 */
  --health-theme-color: #00A86B;
  --health-theme-bg: rgba(0, 168, 107, 0.05);
  
  /* 生产管理模块 */
  --production-theme-color: #FF9500;
  --production-theme-bg: rgba(255, 149, 0, 0.05);
  
  /* 商城模块 */
  --shop-theme-color: #FF3B30;
  --shop-theme-bg: rgba(255, 59, 48, 0.05);
}

/* ==================== 字体系统 ==================== */

:root {
  /* 字体大小 - 基于微信小程序推荐 */
  --font-size-xs: 20rpx;    /* 10px - 辅助信息 */
  --font-size-s: 24rpx;     /* 12px - 次要信息 */
  --font-size-m: 28rpx;     /* 14px - 正文 */
  --font-size-l: 32rpx;     /* 16px - 主要文字 */
  --font-size-xl: 36rpx;    /* 18px - 小标题 */
  --font-size-xxl: 40rpx;   /* 20px - 大标题 */
  --font-size-xxxl: 48rpx;  /* 24px - 页面标题 */
  --font-size-huge: 56rpx;  /* 28px - 特大标题 */
  
  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.6;
  --line-height-loose: 1.8;
  
  /* 字重 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
}

/* ==================== 间距系统 ==================== */

/* 基础间距单位: 4rpx */
:root {
  --spacer-1: 4rpx;     /* 2px */
  --spacer-2: 8rpx;     /* 4px */
  --spacer-3: 12rpx;    /* 6px */
  --spacer-4: 16rpx;    /* 8px */
  --spacer-5: 20rpx;    /* 10px */
  --spacer-6: 24rpx;    /* 12px */
  --spacer-8: 32rpx;    /* 16px */
  --spacer-10: 40rpx;   /* 20px */
  --spacer-12: 48rpx;   /* 24px */
  --spacer-16: 64rpx;   /* 32px */
  --spacer-20: 80rpx;   /* 40px */
  --spacer-24: 96rpx;   /* 48px */
}

/* 语义化间距 */
:root {
  --page-padding: var(--spacer-8);
  --section-margin: var(--spacer-6);
  --component-margin: var(--spacer-4);
  --element-margin: var(--spacer-2);
}

/* ==================== 圆角系统 ==================== */

:root {
  --radius-xs: 4rpx;    /* 2px - 小元素 */
  --radius-s: 8rpx;     /* 4px - 按钮、标签 */
  --radius-m: 12rpx;    /* 6px - 输入框 */
  --radius-l: 16rpx;    /* 8px - 卡片 */
  --radius-xl: 24rpx;   /* 12px - 大卡片 */
  --radius-round: 50%;  /* 圆形 */
}

/* ==================== 阴影系统 ==================== */

:root {
  --shadow-xs: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  --shadow-s: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  --shadow-m: 0 4rpx 12rpx rgba(0, 0, 0, 0.12);
  --shadow-l: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
  --shadow-xl: 0 8rpx 24rpx rgba(0, 0, 0, 0.18);
}

/* ==================== 动画系统 ==================== */

:root {
  /* 过渡时间 */
  --transition-fast: 0.15s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
  
  /* 缓动函数 */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

/* ==================== Z-index层级 ==================== */

:root {
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;
}

/* ==================== 工具类 ==================== */

/* 布局工具类 */
.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

/* 间距工具类 */
.m-1 { margin: var(--spacer-1); }
.m-2 { margin: var(--spacer-2); }
.m-3 { margin: var(--spacer-3); }
.m-4 { margin: var(--spacer-4); }
.mt-1 { margin-top: var(--spacer-1); }
.mt-2 { margin-top: var(--spacer-2); }
.mt-3 { margin-top: var(--spacer-3); }
.mt-4 { margin-top: var(--spacer-4); }
.mb-1 { margin-bottom: var(--spacer-1); }
.mb-2 { margin-bottom: var(--spacer-2); }
.mb-3 { margin-bottom: var(--spacer-3); }
.mb-4 { margin-bottom: var(--spacer-4); }

.p-1 { padding: var(--spacer-1); }
.p-2 { padding: var(--spacer-2); }
.p-3 { padding: var(--spacer-3); }
.p-4 { padding: var(--spacer-4); }
.pt-1 { padding-top: var(--spacer-1); }
.pt-2 { padding-top: var(--spacer-2); }
.pt-3 { padding-top: var(--spacer-3); }
.pt-4 { padding-top: var(--spacer-4); }
.pb-1 { padding-bottom: var(--spacer-1); }
.pb-2 { padding-bottom: var(--spacer-2); }
.pb-3 { padding-bottom: var(--spacer-3); }
.pb-4 { padding-bottom: var(--spacer-4); }

/* 文字工具类 */
.text-xs { font-size: var(--font-size-xs); }
.text-s { font-size: var(--font-size-s); }
.text-m { font-size: var(--font-size-m); }
.text-l { font-size: var(--font-size-l); }
.text-xl { font-size: var(--font-size-xl); }

.text-primary { color: var(--text-color-primary); }
.text-secondary { color: var(--text-color-secondary); }
.text-tertiary { color: var(--text-color-tertiary); }

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* 背景工具类 */
.bg-white { background-color: var(--bg-color-container); }
.bg-gray { background-color: var(--bg-color-page); }

/* 圆角工具类 */
.rounded-xs { border-radius: var(--radius-xs); }
.rounded-s { border-radius: var(--radius-s); }
.rounded-m { border-radius: var(--radius-m); }
.rounded-l { border-radius: var(--radius-l); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-round); }

/* 阴影工具类 */
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-s { box-shadow: var(--shadow-s); }
.shadow-m { box-shadow: var(--shadow-m); }
.shadow-l { box-shadow: var(--shadow-l); }
.shadow-xl { box-shadow: var(--shadow-xl); }