/**
 * 智慧养鹅小程序 - 工具类样式系统
 * 包含：动画、响应式、无障碍、性能优化等工具类
 */

/* ==================== 动画工具类 ==================== */
.fade-in {
  animation: fadeIn var(--duration-base) var(--ease-out);
}

.fade-out {
  animation: fadeOut var(--duration-base) var(--ease-out);
}

.slide-in-up {
  animation: slideInUp var(--duration-base) var(--ease-out);
}

.slide-in-down {
  animation: slideInDown var(--duration-base) var(--ease-out);
}

.slide-in-left {
  animation: slideInLeft var(--duration-base) var(--ease-out);
}

.slide-in-right {
  animation: slideInRight var(--duration-base) var(--ease-out);
}

.bounce-in {
  animation: bounceIn var(--duration-slow) var(--ease-out);
}

.scale-in {
  animation: scaleIn var(--duration-base) var(--ease-out);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideInUp {
  from { transform: translateY(100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInDown {
  from { transform: translateY(-100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInLeft {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes bounceIn {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* ==================== 交互状态 ==================== */
.hover-scale {
  transition: transform var(--duration-fast) var(--ease-out);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-shadow {
  transition: box-shadow var(--duration-fast) var(--ease-out);
}

.hover-shadow:hover {
  box-shadow: var(--shadow-lg);
}

.hover-opacity {
  transition: opacity var(--duration-fast) var(--ease-out);
}

.hover-opacity:hover {
  opacity: 0.8;
}

.active-scale {
  transition: transform var(--duration-fast) var(--ease-out);
}

.active-scale:active {
  transform: scale(0.95);
}

/* ==================== 无障碍工具类 ==================== */
.sr-only {
  position: absolute;
  width: 1rpx;
  height: 1rpx;
  padding: 0;
  margin: -1rpx;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-visible {
  outline: 2rpx solid var(--primary);
  outline-offset: 2rpx;
}

.high-contrast {
  filter: contrast(150%);
}

.reduce-motion {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
}

/* ==================== 响应式工具类 ==================== */
.show-mobile {
  display: block;
}

.hide-mobile {
  display: none;
}

@media (min-width: 768rpx) {
  .show-mobile {
    display: none;
  }
  
  .hide-mobile {
    display: block;
  }
  
  .show-tablet {
    display: block;
  }
  
  .hide-tablet {
    display: none;
  }
}

@media (min-width: 1024rpx) {
  .show-desktop {
    display: block;
  }
  
  .hide-desktop {
    display: none;
  }
}

/* ==================== 性能优化工具类 ==================== */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

.contain-layout {
  contain: layout;
}

.contain-style {
  contain: style;
}

.contain-paint {
  contain: paint;
}

.contain-strict {
  contain: strict;
}

.lazy-load {
  opacity: 0;
  transition: opacity var(--duration-base) var(--ease-out);
}

.lazy-load.loaded {
  opacity: 1;
}

/* ==================== 图片工具类 ==================== */
.img-responsive {
  max-width: 100%;
  height: auto;
}

.img-circle {
  border-radius: 50%;
}

.img-rounded {
  border-radius: var(--radius-lg);
}

.img-cover {
  object-fit: cover;
}

.img-contain {
  object-fit: contain;
}

.img-placeholder {
  background-color: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-quaternary);
  font-size: var(--text-sm);
}

/* ==================== 文本工具类 ==================== */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-uppercase {
  text-transform: uppercase;
}

.text-lowercase {
  text-transform: lowercase;
}

.text-capitalize {
  text-transform: capitalize;
}

.text-nowrap {
  white-space: nowrap;
}

.text-break {
  word-break: break-all;
}

/* ==================== 状态工具类 ==================== */
.loading-state {
  pointer-events: none;
  opacity: 0.6;
}

.disabled-state {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}

.error-state {
  border-color: var(--error) !important;
  color: var(--error) !important;
}

.success-state {
  border-color: var(--success) !important;
  color: var(--success) !important;
}

.warning-state {
  border-color: var(--warning) !important;
  color: var(--warning) !important;
}

/* ==================== 调试工具类 ==================== */
.debug-border {
  border: 1rpx solid red !important;
}

.debug-bg {
  background-color: rgba(255, 0, 0, 0.1) !important;
}

.debug-outline {
  outline: 2rpx dashed blue !important;
}

/* ==================== 打印工具类 ==================== */
@media print {
  .print-hidden {
    display: none !important;
  }
  
  .print-visible {
    display: block !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
  
  .print-no-break {
    page-break-inside: avoid;
  }
}

/* ==================== 滚动工具类 ==================== */
.scroll-smooth {
  scroll-behavior: smooth;
}

.scroll-auto {
  scroll-behavior: auto;
}

.scrollbar-hidden {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

/* ==================== 选择工具类 ==================== */
.select-none {
  user-select: none;
}

.select-text {
  user-select: text;
}

.select-all {
  user-select: all;
}

.select-auto {
  user-select: auto;
}
