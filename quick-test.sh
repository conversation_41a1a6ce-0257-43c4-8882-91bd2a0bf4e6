#!/bin/bash
# 快速测试增强版系统脚本

echo "🧪 开始测试增强版系统..."
echo "================================"

# 检查关键文件
echo "📁 检查文件状态:"
echo "✅ 首页: $(wc -l < pages/home/<USER>"
echo "✅ 增强request: $(wc -l < utils/request-enhanced.js) 行"
echo "✅ 原始备份: $(wc -l < pages/home/<USER>"

# 语法检查
echo -e "\n🔍 语法检查:"
if node -c pages/home/<USER>
    echo "✅ 首页语法正确"
else
    echo "❌ 首页语法错误"
    exit 1
fi

if node -c utils/request-enhanced.js; then
    echo "✅ 增强request语法正确"
else
    echo "❌ 增强request语法错误"
    exit 1
fi

# 检查集成状态
echo -e "\n🔧 检查集成状态:"
if grep -q "request-enhanced" pages/home/<USER>
    echo "✅ 增强版request已引入"
else
    echo "❌ 增强版request未引入"
fi

logger_count=$(grep -c "Logger\." pages/home/<USER>
echo "✅ 检测到 $logger_count 个日志调用点"

# 数据库连接测试
echo -e "\n🗄️ 测试数据库连接:"
node -e "
const { testConnection } = require('./backend/config/database');
testConnection().then(success => {
  if (success) {
    console.log('✅ 数据库连接正常');
  } else {
    console.log('⚠️ 数据库连接需要检查');
  }
}).catch(() => {
  console.log('⚠️ 数据库配置需要检查');
});
" 2>/dev/null || echo "⚠️ 数据库测试跳过（正常）"

echo -e "\n🎯 测试就绪！可以启动服务器:"
echo "================================"
echo "# 启动开发服务器"
echo "npm run dev"
echo ""
echo "# 在另一个终端观察日志"
echo "tail -f backend/logs/app.log"
echo ""
echo "# 如果需要回滚"
echo "cp pages/home/<USER>/home/<USER>"
echo ""
echo "🚀 开始测试！"