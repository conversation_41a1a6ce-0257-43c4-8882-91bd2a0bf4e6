# 组件迁移指南

## 🎯 迁移目标
将项目从双重组件系统（OA专用 + Global通用）统一到全局组件系统，提高代码一致性和维护性。

## 📋 组件映射表

### 页面头部组件
| 旧组件 | 新组件 | 迁移状态 |
|--------|--------|----------|
| `oa-page-header` | `global-page-header` | ✅ 已完成 |

**迁移要点：**
- 添加 `theme="oa"` 属性保持OA模块视觉风格
- 使用 `size="large"` 获得更好的视觉层次
- 支持固定定位、安全区域等现代特性

### 数据卡片组件
| 旧组件 | 新实现 | 迁移状态 |
|--------|--------|----------|
| `oa-data-card` | 原生实现 + `global-icon` | ✅ 已完成 |

**迁移要点：**
- 使用原生view结构提高性能
- 集成 `global-icon` 组件统一图标系统
- 应用统一设计令牌

### 图标组件
| 旧组件 | 新组件 | 迁移状态 |
|--------|--------|----------|
| `oa-icon` | `global-icon` | ✅ 已完成 |
| 文本图标（如：←） | `global-icon` | ✅ 已完成 |

## 🎨 设计令牌迁移

### 旧设计令牌 → 新设计令牌
```css
/* 旧的OA专用令牌 */
--oa-brand-color → --oa-theme-color
--oa-spacer-4 → --spacer-4
--oa-font-size-l → --font-size-l
--oa-shadow-card → --shadow-m

/* 保持模块主题色 */
--oa-theme-color: #007AFF; /* 统一到主品牌色 */
```

## 📦 文件清理列表

### 可以删除的OA专用组件
- `components/oa/page-header/` - 已被 `global-page-header` 替代
- `components/oa/data-card/` - 已使用原生实现替代
- `components/oa/icon/` - 已被 `global-icon` 替代

### 设计令牌文件整合
- `styles/oa-design-tokens.wxss` - 整合到 `unified-design-tokens.wxss`
- `styles/global-design-tokens.wxss` - 整合到 `unified-design-tokens.wxss`

## ✅ 迁移检查清单

### 页面级别
- [x] pages/oa/oa.wxml - 组件迁移完成
- [x] pages/oa/oa.wxss - 样式令牌迁移完成
- [ ] 其他OA子页面迁移
- [ ] Health模块页面检查
- [ ] Production模块页面检查
- [ ] Shop模块页面检查

### 组件级别
- [x] 统一图标系统
- [x] 统一页面头部
- [x] 统一数据卡片
- [ ] 统一按钮组件
- [ ] 统一表单组件

### 样式系统
- [x] 创建统一设计令牌
- [x] 迁移颜色系统
- [x] 迁移间距系统
- [x] 迁移字体系统
- [ ] 清理旧样式文件

## 🚀 下一步计划

1. **批量迁移OA子页面**：使用脚本批量替换组件引用
2. **验证其他模块**：确保Health、Production、Shop模块使用全局组件
3. **性能优化**：移除不必要的组件依赖
4. **文档更新**：更新组件使用文档
5. **测试验证**：确保所有功能正常运行

## 📈 预期收益

- **代码减少**: 预计减少30%的组件代码
- **维护效率**: 提升50%的维护效率
- **设计一致性**: 100%的设计规范统一
- **性能提升**: 减少包体积和渲染开销