{"name": "smart-goose-saas-platform", "version": "1.0.0", "description": "智慧养鹅SAAS平台 - 多租户养鹅管理系统", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "NODE_ENV=development node backend/server.js", "start:admin": "node backend/saas-admin/app.js", "dev:admin": "NODE_ENV=development node backend/saas-admin/app.js", "start:all": "bash scripts/start-dev.sh", "test": "mocha tests/**/*.test.js --timeout 10000", "test:integration": "mocha tests/integration/*.test.js --timeout 15000", "db:init": "mysql -u saas_admin -p smart_goose_saas < database/saas-platform-init.sql", "db:seed": "mysql -u saas_admin -p smart_goose_saas < scripts/create-test-data.sql", "setup": "npm install && npm run db:init && npm run db:seed", "prestart": "echo '请确保MySQL服务已启动，并且已经初始化数据库'", "postinstall": "echo '依赖安装完成，请运行 npm run setup 来初始化数据库'"}, "keywords": ["saas", "multi-tenant", "poultry", "farm-management", "wechat-miniprogram", "nodejs", "mysql"], "author": "Smart Goose Team", "license": "MIT", "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "ejs": "^3.1.9", "exceljs": "^4.4.0", "express": "^4.18.2", "express-ejs-layouts": "^2.5.1", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "playwright": "^1.54.1", "sequelize": "^6.35.2", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@playwright/test": "^1.54.1", "chai": "^4.3.10", "mocha": "^10.2.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}