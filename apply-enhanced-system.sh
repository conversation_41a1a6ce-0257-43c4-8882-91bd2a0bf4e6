#!/bin/bash
# 快速应用增强版系统脚本

echo "🚀 开始应用增强版网络请求系统..."

# 备份原始文件
if [ ! -f "pages/home/<USER>" ]; then
    cp pages/home/<USER>/home/<USER>
    echo "✅ 原始首页已备份"
fi

# 询问是否应用
read -p "是否将增强版系统应用到首页? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    cp pages/home/<USER>/home/<USER>
    echo "✅ 增强版系统已应用到首页"
    echo "📝 原始文件备份为: pages/home/<USER>"
    echo "🧪 请测试功能是否正常"
else
    echo "⏸️ 未应用更改，可稍后手动应用"
fi

echo "🎯 下一步: npm run dev 启动测试"
