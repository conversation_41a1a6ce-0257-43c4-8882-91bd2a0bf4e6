# 🔄 网络请求迁移示例指南

## 概述
本文档提供详细的代码迁移示例，帮助将现有的 `wx.request` 调用替换为新的统一网络请求封装。

## 🎯 迁移目标

### 之前的问题
- 每个页面都有重复的网络请求代码
- 错误处理不一致
- Loading状态管理混乱
- 没有统一的重试机制

### 迁移后的优势
- 统一的请求接口
- 自动错误处理和用户提示
- 自动Loading管理
- 内置重试机制
- Token自动管理

## 📝 基础迁移示例

### 示例1: 简单GET请求

#### 迁移前
```javascript
// pages/home/<USER>
Page({
  data: {
    userInfo: {},
    loading: false
  },

  onLoad() {
    this.loadUserInfo();
  },

  loadUserInfo() {
    const app = getApp();
    this.setData({ loading: true });
    
    wx.request({
      url: `${app.globalData.baseUrl}/api/user/info`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      success: (res) => {
        this.setData({ loading: false });
        if (res.statusCode === 200 && res.data.success) {
          this.setData({
            userInfo: res.data.data
          });
        } else {
          wx.showToast({
            title: res.data.message || '获取用户信息失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        this.setData({ loading: false });
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
        console.error('获取用户信息失败:', error);
      }
    });
  }
});
```

#### 迁移后
```javascript
// pages/home/<USER>
const { createPage } = require('../../utils/page-mixin');
const { get } = require('../../utils/request-unified');

createPage({
  async initPage() {
    await this.loadUserInfo();
  },

  async loadUserInfo() {
    try {
      const response = await get('/api/user/info');
      this.setData({
        userInfo: response.data
      });
    } catch (error) {
      // 错误已由统一系统处理，这里可以添加页面特定逻辑
      this.setData({
        userInfo: {} // 设置默认值
      });
    }
  }
});
```

### 示例2: POST请求带数据

#### 迁移前
```javascript
// pages/login/login.js
Page({
  data: {
    submitting: false
  },

  async login(userData) {
    if (this.data.submitting) return;
    
    this.setData({ submitting: true });
    wx.showLoading({ title: '登录中...' });

    wx.request({
      url: `${getApp().globalData.baseUrl}/api/auth/login`,
      method: 'POST',
      data: userData,
      header: {
        'Content-Type': 'application/json'
      },
      success: (res) => {
        wx.hideLoading();
        this.setData({ submitting: false });
        
        if (res.statusCode === 200 && res.data.success) {
          wx.setStorageSync('token', res.data.token);
          wx.setStorageSync('userInfo', res.data.user);
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          });
          setTimeout(() => {
            wx.reLaunch({ url: '/pages/home/<USER>' });
          }, 1500);
        } else {
          wx.showToast({
            title: res.data.message || '登录失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        this.setData({ submitting: false });
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
        console.error('登录失败:', error);
      }
    });
  }
});
```

#### 迁移后
```javascript
// pages/login/login.js
const { createPage } = require('../../utils/page-mixin');
const { post } = require('../../utils/request-unified');

createPage({
  data: {
    submitting: false
  },

  async login(userData) {
    if (this.data.submitting) return;
    
    this.setData({ submitting: true });
    
    try {
      const response = await post('/api/auth/login', userData);
      
      // 存储认证信息
      wx.setStorageSync('token', response.data.token);
      wx.setStorageSync('userInfo', response.data.user);
      
      this.showSuccess('登录成功');
      
      setTimeout(() => {
        wx.reLaunch({ url: '/pages/home/<USER>' });
      }, 1500);
      
    } catch (error) {
      // 错误已统一处理，这里处理页面特定逻辑
    } finally {
      this.setData({ submitting: false });
    }
  }
});
```

### 示例3: 并发请求

#### 迁移前
```javascript
// pages/production/production.js
Page({
  data: {
    productionData: {},
    environmentData: {},
    materialsData: {},
    loading: true
  },

  onLoad() {
    this.loadAllData();
  },

  loadAllData() {
    this.setData({ loading: true });
    
    // 需要手动管理多个请求的状态
    let completedRequests = 0;
    const totalRequests = 3;
    const errors = [];

    const checkComplete = () => {
      completedRequests++;
      if (completedRequests >= totalRequests) {
        this.setData({ loading: false });
        if (errors.length > 0) {
          wx.showToast({
            title: '部分数据加载失败',
            icon: 'none'
          });
        }
      }
    };

    // 生产记录
    wx.request({
      url: `${getApp().globalData.baseUrl}/api/production/records`,
      success: (res) => {
        if (res.statusCode === 200) {
          this.setData({ productionData: res.data.data });
        } else {
          errors.push('生产记录');
        }
        checkComplete();
      },
      fail: () => {
        errors.push('生产记录');
        checkComplete();
      }
    });

    // 环境数据
    wx.request({
      url: `${getApp().globalData.baseUrl}/api/production/environment`,
      success: (res) => {
        if (res.statusCode === 200) {
          this.setData({ environmentData: res.data.data });
        } else {
          errors.push('环境数据');
        }
        checkComplete();
      },
      fail: () => {
        errors.push('环境数据');
        checkComplete();
      }
    });

    // 物料数据
    wx.request({
      url: `${getApp().globalData.baseUrl}/api/production/materials`,
      success: (res) => {
        if (res.statusCode === 200) {
          this.setData({ materialsData: res.data.data });
        } else {
          errors.push('物料数据');
        }
        checkComplete();
      },
      fail: () => {
        errors.push('物料数据');
        checkComplete();
      }
    });
  }
});
```

#### 迁移后
```javascript
// pages/production/production.js
const { createPage } = require('../../utils/page-mixin');
const { get } = require('../../utils/request-unified');

createPage({
  async initPage() {
    await this.loadAllData();
  },

  async loadAllData() {
    try {
      // 并发请求，自动处理loading和错误
      const [production, environment, materials] = await Promise.allSettled([
        get('/api/production/records'),
        get('/api/production/environment'),
        get('/api/production/materials')
      ]);

      // 处理成功的请求
      if (production.status === 'fulfilled') {
        this.setData({ productionData: production.value.data });
      }
      
      if (environment.status === 'fulfilled') {
        this.setData({ environmentData: environment.value.data });
      }
      
      if (materials.status === 'fulfilled') {
        this.setData({ materialsData: materials.value.data });
      }

      // 检查失败的请求
      const failedRequests = [production, environment, materials]
        .filter(result => result.status === 'rejected');
      
      if (failedRequests.length > 0) {
        this.showError(`${failedRequests.length}个数据源加载失败`);
      }

    } catch (error) {
      // 统一错误处理已生效
      this.setData({
        productionData: {},
        environmentData: {},
        materialsData: {}
      });
    }
  }
});
```

## 🔧 高级迁移示例

### 示例4: 带文件上传的请求

#### 迁移前
```javascript
Page({
  uploadFile(filePath) {
    wx.showLoading({ title: '上传中...' });
    
    wx.uploadFile({
      url: `${getApp().globalData.baseUrl}/api/upload`,
      filePath: filePath,
      name: 'file',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      success: (res) => {
        wx.hideLoading();
        const data = JSON.parse(res.data);
        if (data.success) {
          wx.showToast({
            title: '上传成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: data.message || '上传失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        });
      }
    });
  }
});
```

#### 迁移后
```javascript
const { createPage } = require('../../utils/page-mixin');

createPage({
  async uploadFile(filePath) {
    try {
      // 可以扩展request-unified.js支持文件上传
      // 或在这里使用优化的wx.uploadFile
      const result = await this.uploadFileWithProgress(filePath);
      this.showSuccess('上传成功');
      return result;
    } catch (error) {
      this.showError('上传失败');
    }
  },

  uploadFileWithProgress(filePath) {
    return new Promise((resolve, reject) => {
      const uploadTask = wx.uploadFile({
        url: this.getApiUrl('/api/upload'),
        filePath: filePath,
        name: 'file',
        header: this.getAuthHeaders(),
        success: (res) => {
          const data = JSON.parse(res.data);
          if (data.success) {
            resolve(data);
          } else {
            reject(new Error(data.message || '上传失败'));
          }
        },
        fail: reject
      });

      // 可以监听上传进度
      uploadTask.onProgressUpdate((res) => {
        this.setData({
          uploadProgress: res.progress
        });
      });
    });
  },

  getApiUrl(path) {
    return getApp().globalData.baseUrl + path;
  },

  getAuthHeaders() {
    const token = wx.getStorageSync('token');
    return token ? { 'Authorization': `Bearer ${token}` } : {};
  }
});
```

## 📋 迁移检查清单

### 页面迁移前检查
- [ ] 确认页面使用了 `wx.request`
- [ ] 识别所有网络请求位置
- [ ] 记录当前的错误处理逻辑
- [ ] 备份原始文件

### 迁移过程检查
- [ ] 引入新的工具模块
- [ ] 使用 `createPage` 替换 `Page`
- [ ] 将 `onLoad` 逻辑移到 `initPage`
- [ ] 替换所有 `wx.request` 调用
- [ ] 移除手动的loading管理代码
- [ ] 简化错误处理逻辑

### 迁移后验证
- [ ] 功能正常工作
- [ ] 错误处理正确
- [ ] Loading效果正常
- [ ] 没有重复的错误提示
- [ ] 代码更简洁易读

## 🚨 注意事项

### 1. 错误处理策略
```javascript
// 新系统会自动显示错误toast
// 如果不需要自动提示，可以这样：
try {
  const response = await get('/api/data', {}, {
    enableErrorToast: false  // 禁用自动错误提示
  });
} catch (error) {
  // 自定义错误处理
  this.showError('自定义错误消息');
}
```

### 2. Loading管理
```javascript
// 新系统自动管理loading
// 如果需要自定义loading文本：
const response = await get('/api/data', {}, {
  loadingText: '正在加载数据...'
});

// 如果不需要loading：
const response = await get('/api/data', {}, {
  enableLoading: false
});
```

### 3. 保持向后兼容
```javascript
// 如果需要逐步迁移，可以这样：
if (this.useNewRequest) {
  // 使用新的请求方式
  const response = await get('/api/data');
} else {
  // 保持旧的方式
  wx.request({ ... });
}
```

## 📊 迁移进度跟踪

### 高优先级页面 (建议先迁移)
- [ ] pages/home/<USER>
- [ ] pages/login/login.js - 登录页
- [ ] pages/production/production.js - 生产管理
- [ ] pages/health/health.js - 健康管理
- [ ] pages/shop/shop.js - 商城首页

### 中优先级页面
- [ ] pages/oa/oa.js - OA系统首页
- [ ] pages/profile/profile.js - 个人中心
- [ ] pages/shop/cart.js - 购物车
- [ ] pages/shop/checkout.js - 结算页面

### 低优先级页面
- [ ] 其他详情页面
- [ ] 设置相关页面
- [ ] 静态展示页面

---

**建议**: 从高优先级页面开始迁移，每迁移一个页面都要进行充分测试，确保功能正常后再继续下一个。