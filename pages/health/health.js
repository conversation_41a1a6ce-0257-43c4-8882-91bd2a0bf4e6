// pages/health/health.js
const { IMAGES, UI, BUSINESS, API } = require('../../constants/index.js');
const request = require('../../utils/request.js');

Page({
  data: {
    activeTab: 0,
    tabs: [
      { id: 0, name: '健康记录' },
      { id: 1, name: 'AI诊断' },
      { id: 2, name: '知识库' },
      { id: 3, name: '健康报告' }
    ],

    // 健康记录数据
    healthRecords: [],
    loading: true,

    // AI诊断数据
    symptoms: '',
    uploadedImages: [],
    diagnosisResult: null,
    isDiagnosing: false,
    diagnosisMode: 'input', // input, analyzing, result

    // 知识库数据
    categories: [],
    articles: [],
    activeCategory: 'all',
    searchKeyword: '',

    // 健康报告数据
    reportTypes: [
      { id: 'week', name: '周报' },
      { id: 'month', name: '月报' },
      { id: 'quarter', name: '季报' },
      { id: 'year', name: '年报' }
    ],
    activeReportType: 'week',
    reportData: {
      overview: {
        totalGeese: 0,
        healthyCount: 0,
        sickCount: 0,
        deathCount: 0,
        healthyRate: '0%'
      },
      diseaseStats: [],
      treatmentStats: [],
      trendData: [],
      updateTime: ''
    },


  },

  onLoad: function (options) {
    // 页面加载时初始化所有数据
    this.loadHealthRecords();
    this.loadCategories();
    this.loadReportData();
  },

  onShow: function () {
    // 页面显示时刷新当前Tab的数据
    switch (this.data.activeTab) {
      case 0:
        this.loadHealthRecords();
        break;
      case 1:
        // AI诊断页面不需要额外加载
        break;
      case 2:
        this.loadArticles();
        break;
      case 3:
        this.loadReportData();
        break;
    }
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.loadHealthRecords(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 切换Tab
  onTabChange: function (e) {
    const index = e.detail.index;
    // 确保index是有效的数字
    if (typeof index === 'number' && index >= 0) {
      this.setData({
        activeTab: index
      });
    } else {
      console.warn('[Health] Invalid tab index:', index);
      return;
    }

    // 根据选中的tab加载对应数据
    switch (index) {
      case 0:
        this.loadHealthRecords();
        break;
      case 1:
        // AI诊断页面不需要额外加载
        break;
      case 2:
        this.loadArticles();
        break;
      case 3:
        this.loadReportData();
        break;
    }
  },

  // 加载健康记录
  loadHealthRecords: function (callback) {
    // 模拟加载数据
    setTimeout(() => {
      const records = [
        {
          id: 1,
          title: '鹅群死亡记录',
          date: '2023-06-15',
          status: '死亡',
          description: '发现1只成年鹅死亡，疑似疾病导致'
        },
        {
          id: 2,
          title: '疫苗接种',
          date: '2023-06-10',
          status: '已完成',
          description: '完成春季疫苗接种工作'
        },
        {
          id: 3,
          title: '疾病治疗',
          date: '2023-06-05',
          status: '已治愈',
          description: '治疗鹅群呼吸道感染'
        }
      ];

      this.setData({
        healthRecords: records,
        loading: false
      });

      callback && callback();
    }, 500);
  },

  // 添加健康记录
  onAddRecord: function () {
    wx.navigateTo({
      url: '/pages/health/record-detail/record-detail'
    });
  },

  // 快速AI诊断
  onQuickDiagnosis: function () {
    this.setData({
      activeTab: 1
    });
    wx.vibrateShort();
  },

  // 查看记录详情
  onViewRecord: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/health/record-detail/record-detail?id=${id}`
    });
  },

  // ==================== AI诊断相关方法 ====================

  // 输入症状描述
  onSymptomInput: function (e) {
    this.setData({
      symptoms: e.detail.value
    });
  },

  // 选择图片
  onChooseImage: function () {
    const that = this;
    wx.chooseImage({
      count: 3,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: function (res) {
        const tempFiles = res.tempFiles;
        const uploadedImages = that.data.uploadedImages;

        tempFiles.forEach(file => {
          if (uploadedImages.length < 3) {
            uploadedImages.push({
              url: file.path,
              size: file.size
            });
          }
        });

        that.setData({
          uploadedImages: uploadedImages
        });
      }
    });
  },

  // 删除图片
  onDeleteImage: function (e) {
    const index = e.currentTarget.dataset.index;
    const uploadedImages = this.data.uploadedImages;
    uploadedImages.splice(index, 1);
    this.setData({
      uploadedImages: uploadedImages
    });
  },

  // 开始诊断
  onStartDiagnosis: function () {
    const { symptoms, uploadedImages } = this.data;

    if (!symptoms && uploadedImages.length === 0) {
      wx.showToast({
        title: '请填写症状或上传图片',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isDiagnosing: true,
      diagnosisResult: null
    });

    // 调用AI诊断服务
    this.callAIDiagnosis();
  },

  // 调用AI诊断服务
  async callAIDiagnosis() {
    try {
      // 引入AI服务
      const { callAIService } = require('../../utils/ai-service.js');

      // 构建诊断提示
      const userMessage = `
症状描述：${this.data.symptoms || '无明显症状描述'}

${this.data.uploadedImages.length > 0 ? '已上传图片：' + this.data.uploadedImages.length + '张' : ''}

请根据以上症状描述，对鹅的健康状况进行专业诊断分析。
      `.trim();

      // 调用AI诊断
      const result = await callAIService('HEALTH_DIAGNOSIS', userMessage);

      if (result.success) {
        // 解析AI返回的诊断结果
        const diagnosisContent = result.data.content;

        // 尝试解析结构化的诊断结果
        const parsedResult = this.parseAIDiagnosisResult(diagnosisContent);

        this.setData({
          diagnosisResult: parsedResult,
          isDiagnosing: false
        });

        wx.showToast({
          title: 'AI诊断完成',
          icon: 'success'
        });

      } else {
        // AI诊断失败，使用备用诊断
        this.fallbackDiagnosis();
      }

    } catch (error) {
      console.error('AI诊断错误:', error);
      // 网络错误，使用备用诊断
      this.fallbackDiagnosis();
    }
  },

  // 解析AI诊断结果
  parseAIDiagnosisResult(content) {
    try {
      // 尝试从AI返回的文本中提取结构化信息
      const lines = content.split('\n').filter(line => line.trim());

      let disease = '未知疾病';
      let confidence = '待评估';
      let description = '';
      let suggestions = [];
      let medications = [];

      // 简单的文本解析逻辑
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();

        if (line.includes('可能疾病') || line.includes('诊断结果')) {
          const match = line.match(/[:：](.+)/);
          if (match) disease = match[1].trim();
        }

        if (line.includes('概率') || line.includes('置信度')) {
          const match = line.match(/(\d+%)/);
          if (match) confidence = match[1];
        }

        if (line.includes('症状分析') || line.includes('病情描述')) {
          const match = line.match(/[:：](.+)/);
          if (match) description = match[1].trim();
        }

        if (line.includes('治疗建议') || line.includes('处理建议')) {
          // 收集后续的建议行
          for (let j = i + 1; j < lines.length && j < i + 5; j++) {
            const suggestionLine = lines[j].trim();
            if (suggestionLine && !suggestionLine.includes(':') && !suggestionLine.includes('：')) {
              suggestions.push(suggestionLine.replace(/^[-•\d\.]\s*/, ''));
            }
          }
        }
      }

      // 如果解析失败，使用原始内容作为描述
      if (!description && content.length > 0) {
        description = content.substring(0, 200) + (content.length > 200 ? '...' : '');
      }

      return {
        disease: disease,
        confidence: confidence,
        description: description,
        suggestions: suggestions.length > 0 ? suggestions : [
          '建议咨询专业兽医',
          '密切观察鹅群状况',
          '加强饲养管理'
        ],
        medications: medications.length > 0 ? medications : [
          { name: '请咨询兽医', dosage: '根据专业建议使用' }
        ],
        aiGenerated: true,
        rawContent: content
      };

    } catch (error) {
      console.error('解析AI诊断结果失败:', error);
      return this.getDefaultDiagnosisResult();
    }
  },

  // 备用诊断（当AI服务不可用时）
  fallbackDiagnosis() {
    wx.showModal({
      title: 'AI诊断服务暂时不可用',
      content: '将为您提供基础诊断建议，建议咨询专业兽医获取准确诊断',
      showCancel: false,
      success: () => {
        const result = this.getDefaultDiagnosisResult();
        this.setData({
          diagnosisResult: result,
          isDiagnosing: false
        });
      }
    });
  },

  // 获取默认诊断结果
  getDefaultDiagnosisResult() {
    return {
      disease: '需要进一步检查',
      confidence: '建议专业诊断',
      description: '根据您提供的症状描述，建议联系专业兽医进行详细检查以获得准确诊断。',
      suggestions: [
        '立即隔离疑似病鹅',
        '联系专业兽医进行检查',
        '保持鹅舍清洁卫生',
        '密切观察鹅群健康状况'
      ],
      medications: [
        { name: '请咨询兽医', dosage: '根据专业建议使用药物' }
      ],
      aiGenerated: false
    };
  },

  // 清空诊断结果
  onClearResult: function () {
    this.setData({
      diagnosisResult: null,
      symptoms: '',
      uploadedImages: []
    });
  },

  // ==================== 知识库相关方法 ====================

  // 加载分类
  loadCategories: function () {
    const categories = [
      { id: 'all', name: '全部' },
      { id: 'disease', name: '疾病防治' },
      { id: 'feed', name: '饲料营养' },
      { id: 'breed', name: '品种繁育' },
      { id: 'manage', name: '饲养管理' }
    ];

    this.setData({
      categories: categories
    });
  },

  // 加载文章列表
  loadArticles: function (callback) {
    this.setData({
      loading: true
    });

    setTimeout(() => {
      // 完整的文章数据
      const allArticles = [
        {
          id: 1,
          title: '小鹅瘟的预防与治疗',
          summary: '小鹅瘟是由小鹅瘟病毒引起的雏鹅急性败血性传染病...',
          category: 'disease',
          categoryName: '疾病防治',
          publishDate: '2023-06-15',
          readCount: 1256
        },
        {
          id: 2,
          title: '鹅副粘病毒病的诊断与防治',
          summary: '鹅副粘病毒病是一种急性、高度接触性传染病...',
          category: 'disease',
          categoryName: '疾病防治',
          publishDate: '2023-06-12',
          readCount: 856
        },
        {
          id: 3,
          title: '优质鹅饲料的配制技术',
          summary: '合理的饲料配制是提高鹅群生产性能的关键...',
          category: 'feed',
          categoryName: '饲料营养',
          publishDate: '2023-06-10',
          readCount: 742
        },
        {
          id: 4,
          title: '鹅舍建设与环境控制',
          summary: '良好的鹅舍环境是保证鹅群健康生长的基础...',
          category: 'breed',
          categoryName: '品种繁育',
          publishDate: '2023-06-08',
          readCount: 623
        },
        {
          id: 5,
          title: '鹅蛋孵化技术要点',
          summary: '掌握正确的孵化技术是提高孵化率的关键...',
          category: 'breed',
          categoryName: '品种繁育',
          publishDate: '2023-06-05',
          readCount: 892
        },
        {
          id: 6,
          title: '鹅群营养需求分析',
          summary: '不同生长阶段的鹅群对营养的需求各不相同...',
          category: 'feed',
          categoryName: '饲料营养',
          publishDate: '2023-06-03',
          readCount: 567
        },
        {
          id: 7,
          title: '鹅病综合防控策略',
          summary: '建立完善的疾病防控体系，减少疾病发生...',
          category: 'disease',
          categoryName: '疾病防治',
          publishDate: '2023-06-01',
          readCount: 734
        }
      ];

      const { activeCategory, searchKeyword } = this.data;
      let filteredArticles = allArticles;

      // 根据分类过滤
      if (activeCategory !== 'all') {
        filteredArticles = filteredArticles.filter(article => article.category === activeCategory);
      }

      // 根据搜索关键词过滤
      if (searchKeyword && searchKeyword.trim()) {
        const keyword = searchKeyword.trim().toLowerCase();
        filteredArticles = filteredArticles.filter(article =>
          article.title.toLowerCase().includes(keyword) ||
          article.summary.toLowerCase().includes(keyword)
        );
      }

      this.setData({
        articles: filteredArticles,
        loading: false
      });

      callback && callback();
    }, 500);
  },

  // 搜索输入
  onSearchInput: function (e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 执行搜索
  onSearch: function () {
    const searchKeyword = this.data.searchKeyword.trim();

    if (searchKeyword) {
      // 如果有搜索关键词，先进行AI智能搜索
      this.performAISearch(searchKeyword);
    } else {
      // 没有关键词，正常加载文章
      this.loadArticles();
    }
  },

  // AI智能搜索
  async performAISearch(keyword) {
    try {
      this.setData({ loading: true });

      // 引入AI服务
      const { callAIService } = require('../../utils/ai-service.js');

      // 构建搜索提示
      const userMessage = `
用户搜索关键词：${keyword}

请根据这个关键词，推荐相关的鹅类养殖知识内容，包括：
1. 相关的疾病防治知识
2. 饲料营养相关内容
3. 品种繁育相关信息
4. 饲养管理技巧

请提供3-5个具体的知识点标题和简要说明。
      `.trim();

      // 调用AI知识问答服务
      const result = await callAIService('KNOWLEDGE_QA', userMessage);

      if (result.success) {
        // 解析AI推荐的内容
        const recommendations = this.parseAIRecommendations(result.data.content);

        // 合并AI推荐和现有文章
        const filteredArticles = this.filterArticlesByKeyword(keyword);
        const enhancedArticles = [...recommendations, ...filteredArticles];

        this.setData({
          articles: enhancedArticles,
          loading: false,
          aiSearchUsed: true
        });

        if (recommendations.length > 0) {
          wx.showToast({
            title: `AI为您推荐了${recommendations.length}个相关内容`,
            icon: 'none',
            duration: 2000
          });
        }

      } else {
        // AI搜索失败，使用普通搜索
        this.loadArticles();
      }

    } catch (error) {
      console.error('AI搜索失败:', error);
      // 出错时使用普通搜索
      this.loadArticles();
    }
  },

  // 解析AI推荐内容
  parseAIRecommendations(content) {
    try {
      const lines = content.split('\n').filter(line => line.trim());
      const recommendations = [];

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();

        // 查找标题行（通常包含数字序号或特殊标记）
        if (line.match(/^\d+[\.、]/) || line.includes('：') || line.includes(':')) {
          let title = line.replace(/^\d+[\.、]\s*/, '').split(/[：:]/)[0].trim();
          let summary = '';

          // 查找描述内容
          if (line.includes('：') || line.includes(':')) {
            summary = line.split(/[：:]/)[1]?.trim() || '';
          }

          // 如果当前行没有描述，查看下一行
          if (!summary && i + 1 < lines.length) {
            const nextLine = lines[i + 1].trim();
            if (nextLine && !nextLine.match(/^\d+[\.、]/)) {
              summary = nextLine;
            }
          }

          if (title && title.length > 3) {
            recommendations.push({
              id: `ai_rec_${recommendations.length + 1}`,
              title: title,
              summary: summary || '详细内容请咨询专业兽医',
              category: this.getCategoryByTitle(title),
              isAIRecommendation: true,
              readCount: Math.floor(Math.random() * 1000) + 100,
              publishTime: '刚刚'
            });
          }
        }
      }

      return recommendations.slice(0, 5); // 最多返回5个推荐

    } catch (error) {
      console.error('解析AI推荐失败:', error);
      return [];
    }
  },

  // 根据标题判断分类
  getCategoryByTitle(title) {
    if (title.includes('病') || title.includes('疾') || title.includes('治') || title.includes('防')) {
      return 'disease';
    } else if (title.includes('饲料') || title.includes('营养') || title.includes('喂')) {
      return 'nutrition';
    } else if (title.includes('繁殖') || title.includes('育种') || title.includes('品种')) {
      return 'breeding';
    } else {
      return 'management';
    }
  },

  // 按关键词筛选现有文章
  filterArticlesByKeyword(keyword) {
    const allArticles = this.getAllArticles();
    return allArticles.filter(article =>
      article.title.includes(keyword) ||
      article.summary.includes(keyword) ||
      article.content?.includes(keyword)
    );
  },

  // 获取所有文章（用于搜索）
  getAllArticles() {
    return [
      {
        id: 1,
        title: '小鹅瘟的预防与治疗',
        summary: '小鹅瘟是危害雏鹅的主要传染病，了解预防措施和治疗方法对养鹅成功至关重要。',
        category: 'disease',
        readCount: 1234,
        publishTime: '2024-01-15'
      },
      {
        id: 2,
        title: '鹅的营养需求与饲料配制',
        summary: '合理的营养搭配是鹅健康成长的基础，本文详细介绍各生长阶段的营养需求。',
        category: 'nutrition',
        readCount: 987,
        publishTime: '2024-01-10'
      },
      {
        id: 3,
        title: '优质鹅种的选择标准',
        summary: '选择优质的种鹅是提高养殖效益的关键，本文介绍选种的基本标准和方法。',
        category: 'breeding',
        readCount: 756,
        publishTime: '2024-01-08'
      },
      {
        id: 4,
        title: '鹅舍建设与环境管理',
        summary: '良好的饲养环境是鹅健康成长的保障，合理的鹅舍设计能有效提高养殖效率。',
        category: 'management',
        readCount: 654,
        publishTime: '2024-01-05'
      },
      {
        id: 5,
        title: '鹅病毒性肝炎防控要点',
        summary: '鹅病毒性肝炎是影响鹅群健康的重要疾病，及早预防是关键。',
        category: 'disease',
        readCount: 543,
        publishTime: '2024-01-03'
      }
    ];
  },

  // 分类变化
  onCategoryChange: function (e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      activeCategory: category,
      aiSearchUsed: false // 重置AI搜索标记
    });
    this.loadArticles();
  },

  // 查看文章
  onViewArticle: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/health/knowledge-detail/knowledge-detail?id=${id}`
    });
  },

  // ==================== 健康报告相关方法 ====================

  // 切换报告类型
  onReportTypeChange: function (e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      activeReportType: type
    });
    this.loadReportData();
  },

  // 加载报告数据
  loadReportData: function (callback) {
    this.setData({
      loading: true
    });

    // 尝试从API获取真实数据
    const { activeReportType } = this.data;
    const api = require('../../utils/api.js');

    api.health.getReport({ type: activeReportType }).then(res => {
      if (res && res.code === 0 && res.data) {
        // 使用API返回的真实数据
        this.setData({
          reportData: res.data,
          loading: false
        });
        callback && callback();
      } else {
        // API失败时使用模拟数据
        this.loadMockReportData(callback);
      }
    }).catch(err => {
      // API失败时使用模拟数据
      this.loadMockReportData(callback);
    });
  },

  // 加载模拟报告数据
  loadMockReportData: function (callback) {
    setTimeout(() => {
      const { activeReportType } = this.data;

      // 根据报告类型生成不同的数据
      let overview, diseaseStats, treatmentStats, trendData;

      switch (activeReportType) {
        case 'week':
          overview = {
            totalGeese: 1200,
            healthyCount: 1120,
            sickCount: 65,
            deathCount: 15,
            healthyRate: '93.3%'
          };
          diseaseStats = [
            { name: '小鹅瘟', value: 25, rate: '38.5%' },
            { name: '禽流感', value: 18, rate: '27.7%' },
            { name: '大肠杆菌病', value: 12, rate: '18.5%' },
            { name: '寄生虫病', value: 7, rate: '10.8%' }
          ];
          trendData = [
            { date: '06-09', healthy: 1150, sick: 35, death: 15 },
            { date: '06-11', healthy: 1140, sick: 45, death: 15 },
            { date: '06-13', healthy: 1130, sick: 55, death: 15 },
            { date: '06-15', healthy: 1120, sick: 65, death: 15 }
          ];
          break;

        case 'month':
          overview = {
            totalGeese: 1200,
            healthyCount: 1050,
            sickCount: 120,
            deathCount: 30,
            healthyRate: '87.5%'
          };
          diseaseStats = [
            { name: '小鹅瘟', value: 45, rate: '37.5%' },
            { name: '禽流感', value: 32, rate: '26.7%' },
            { name: '大肠杆菌病', value: 25, rate: '20.8%' },
            { name: '寄生虫病', value: 18, rate: '15.0%' }
          ];
          trendData = [
            { date: '05-15', healthy: 1180, sick: 15, death: 5 },
            { date: '05-22', healthy: 1160, sick: 25, death: 15 },
            { date: '05-29', healthy: 1140, sick: 40, death: 20 },
            { date: '06-05', healthy: 1120, sick: 60, death: 20 },
            { date: '06-12', healthy: 1080, sick: 90, death: 30 },
            { date: '06-15', healthy: 1050, sick: 120, death: 30 }
          ];
          break;

        case 'quarter':
          overview = {
            totalGeese: 1200,
            healthyCount: 980,
            sickCount: 150,
            deathCount: 70,
            healthyRate: '81.7%'
          };
          diseaseStats = [
            { name: '小鹅瘟', value: 58, rate: '38.7%' },
            { name: '禽流感', value: 42, rate: '28.0%' },
            { name: '大肠杆菌病', value: 30, rate: '20.0%' },
            { name: '寄生虫病', value: 20, rate: '13.3%' }
          ];
          trendData = [
            { date: '04月', healthy: 1150, sick: 30, death: 20 },
            { date: '05月', healthy: 1100, sick: 70, death: 30 },
            { date: '06月', healthy: 980, sick: 150, death: 70 }
          ];
          break;

        case 'year':
          overview = {
            totalGeese: 1200,
            healthyCount: 900,
            sickCount: 200,
            deathCount: 100,
            healthyRate: '75.0%'
          };
          diseaseStats = [
            { name: '小鹅瘟', value: 75, rate: '37.5%' },
            { name: '禽流感', value: 55, rate: '27.5%' },
            { name: '大肠杆菌病', value: 40, rate: '20.0%' },
            { name: '寄生虫病', value: 30, rate: '15.0%' }
          ];
          trendData = [
            { date: '1季度', healthy: 1100, sick: 50, death: 50 },
            { date: '2季度', healthy: 1000, sick: 100, death: 100 },
            { date: '3季度', healthy: 950, sick: 150, death: 100 },
            { date: '4季度', healthy: 900, sick: 200, death: 100 }
          ];
          break;
      }

      // 治疗效果数据（相对稳定）
      treatmentStats = [
        { name: '治愈', value: Math.floor(overview.sickCount * 0.8), rate: '80.0%' },
        { name: '好转', value: Math.floor(overview.sickCount * 0.15), rate: '15.0%' },
        { name: '死亡', value: Math.floor(overview.sickCount * 0.05), rate: '5.0%' }
      ];

      // 生成趋势数据
      trendData = this.generateTrendData(activeReportType, overview);

      this.setData({
        'reportData.overview': overview,
        'reportData.diseaseStats': diseaseStats,
        'reportData.treatmentStats': treatmentStats,
        'reportData.trendData': trendData,
        'reportData.updateTime': new Date().toLocaleString(),
        loading: false
      });

      // 保存健康数据到全局状态，供其他模块使用
      this.saveHealthReportDataForSharing(overview, trendData);

      callback && callback();
    }, 500);
  },

  // 生成趋势数据
  generateTrendData: function(reportType, overview) {
    const now = new Date();
    let trendData = [];

    switch (reportType) {
      case 'week':
        // 生成7天的数据
        for (let i = 6; i >= 0; i--) {
          const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
          const dateStr = `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

          // 模拟数据变化趋势
          const progress = (6 - i) / 6;
          const healthyVariation = Math.floor(Math.random() * 40 - 20);
          const sickVariation = Math.floor(Math.random() * 20 - 10);
          const deathVariation = Math.floor(Math.random() * 10 - 5);

          trendData.push({
            date: dateStr,
            healthy: Math.max(0, overview.healthyCount + healthyVariation),
            sick: Math.max(0, overview.sickCount + sickVariation),
            death: Math.max(0, overview.deathCount + deathVariation)
          });
        }
        break;

      case 'month':
        // 生成30天的数据，每5天一个点
        for (let i = 25; i >= 0; i -= 5) {
          const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
          const dateStr = `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

          const progress = (25 - i) / 25;
          const healthyBase = 1200 - Math.floor(progress * 150);
          const sickBase = Math.floor(progress * 120);
          const deathBase = Math.floor(progress * 30);

          trendData.push({
            date: dateStr,
            healthy: healthyBase + Math.floor(Math.random() * 40 - 20),
            sick: sickBase + Math.floor(Math.random() * 20 - 10),
            death: deathBase + Math.floor(Math.random() * 10 - 5)
          });
        }
        break;

      case 'quarter':
        // 生成3个月的数据
        const months = ['04月', '05月', '06月'];
        months.forEach((month, index) => {
          const progress = index / 2;
          const healthyBase = 1200 - Math.floor(progress * 220);
          const sickBase = Math.floor(progress * 150);
          const deathBase = Math.floor(progress * 70);

          trendData.push({
            date: month,
            healthy: healthyBase + Math.floor(Math.random() * 50 - 25),
            sick: sickBase + Math.floor(Math.random() * 30 - 15),
            death: deathBase + Math.floor(Math.random() * 15 - 7)
          });
        });
        break;

      case 'year':
        // 生成4个季度的数据
        const quarters = ['1季度', '2季度', '3季度', '4季度'];
        quarters.forEach((quarter, index) => {
          const progress = index / 3;
          const healthyBase = 1200 - Math.floor(progress * 300);
          const sickBase = Math.floor(progress * 200);
          const deathBase = Math.floor(progress * 100);

          trendData.push({
            date: quarter,
            healthy: healthyBase + Math.floor(Math.random() * 60 - 30),
            sick: sickBase + Math.floor(Math.random() * 40 - 20),
            death: deathBase + Math.floor(Math.random() * 20 - 10)
          });
        });
        break;
    }

    return trendData;
  },



  // 导出报告
  onExportReport: function () {
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    });
  },

  // === Health专用组件事件处理 ===

  // 健康记录卡片事件
  onRecordTap: function(e) {
    const { record } = e.detail;
    this.onViewRecord({ currentTarget: { dataset: { id: record.id } } });
  },

  onRecordDetail: function(e) {
    const { record } = e.detail;
    wx.navigateTo({
      url: `/pages/health/record-detail/record-detail?id=${record.id}`
    });
  },

  onRecordEdit: function(e) {
    const { record } = e.detail;
    wx.navigateTo({
      url: `/pages/health/record-edit/record-edit?id=${record.id}`
    });
  },

  onRecordDelete: function(e) {
    const { record } = e.detail;
    const index = this.data.healthRecords.findIndex(r => r.id === record.id);
    if (index > -1) {
      const newRecords = [...this.data.healthRecords];
      newRecords.splice(index, 1);
      this.setData({
        healthRecords: newRecords
      });
      
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
    }
  },

  onRecordShare: function(e) {
    const { record } = e.detail;
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  // AI诊断面板事件
  onStartAIDiagnosis: function(e) {
    const { symptoms, selectedSymptoms } = e.detail;
    
    this.setData({
      diagnosisMode: 'analyzing'
    });

    // 模拟AI诊断过程
    setTimeout(() => {
      this.setData({
        diagnosisMode: 'result'
      });
    }, 3000);
  },

  onDiagnosisComplete: function(e) {
    const { result } = e.detail;
    
    wx.showToast({
      title: '诊断完成',
      icon: 'success'
    });
  },

  onSaveDiagnosisResult: function(e) {
    const { result } = e.detail;
    
    // 保存诊断结果到健康记录
    const newRecord = {
      id: Date.now(),
      title: `AI诊断 - ${result.diagnosis}`,
      type: 'AI诊断',
      recordType: 'diagnosis',
      date: new Date().toISOString().split('T')[0],
      description: result.description,
      status: result.urgency === 'immediate' ? '需关注' : '正常',
      metrics: [
        { key: 'confidence', label: '可信度', value: result.confidence, unit: '%', status: 'normal' }
      ]
    };

    const newRecords = [newRecord, ...this.data.healthRecords];
    this.setData({
      healthRecords: newRecords
    });

    wx.showToast({
      title: '保存成功',
      icon: 'success'
    });
  },

  onShareDiagnosisResult: function(e) {
    const { result } = e.detail;
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  onViewDiagnosisHistory: function() {
    wx.navigateTo({
      url: '/pages/health/diagnosis-history/diagnosis-history'
    });
  },

  onDiagnosisHistoryTap: function(e) {
    const { record } = e.detail;
  },

  /**
   * 保存健康报告数据供其他模块使用
   * @param {Object} overview 健康概览数据
   * @param {Object} trendData 趋势数据
   */
  saveHealthReportDataForSharing: function(overview, trendData) {
    try {
      // 保存到全局应用数据
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.healthReport = {
          overview: overview,
          trendData: trendData,
          updateTime: new Date().toISOString(),
          source: 'health-management'
        };
      }

      // 保存到本地存储，供其他页面使用
      wx.setStorageSync('health_report_cache', {
        overview: overview,
        trendData: trendData,
        updateTime: Date.now(),
        expireTime: Date.now() + 24 * 60 * 60 * 1000 // 24小时过期
      });

    } catch (error) {
      console.error('保存健康报告数据失败:', error);
    }
  }
});