// pages/health/record-detail/record-detail.js
const api = require('../../../utils/api.js');

Page({
  data: {
    id: null,
    record: {},
    isEditing: false,
    loading: true,

    // 死亡原因选项
    deathCauseOptions: [
      { label: '疾病死亡', value: 'disease' },
      { label: '意外死亡', value: 'accident' },
      { label: '老龄死亡', value: 'aging' },
      { label: '营养不良', value: 'malnutrition' },
      { label: '环境应激', value: 'stress' },
      { label: '其他原因', value: 'other' }
    ],
    // 疾病类型选项
    diseaseTypeOptions: [
      { label: '病毒感染', value: 'virus' },
      { label: '细菌感染', value: 'bacteria' },
      { label: '寄生虫病', value: 'parasite' },
      { label: '营养缺乏', value: 'nutrition' },
      { label: '消化系统疾病', value: 'digestive' },
      { label: '呼吸系统疾病', value: 'respiratory' },
      { label: '其他疾病', value: 'other' }
    ],
    // 严重程度选项
    severityOptions: [
      { label: '轻度', value: 'mild' },
      { label: '中度', value: 'moderate' },
      { label: '重度', value: 'severe' },
      { label: '危重', value: 'critical' }
    ],
    // 治疗方案选项
    treatmentOptions: [
      { label: '药物治疗', value: 'medication' },
      { label: '手术治疗', value: 'surgery' },
      { label: '隔离治疗', value: 'isolation' },
      { label: '营养调理', value: 'nutrition' },
      { label: '环境改善', value: 'environment' },
      { label: '疫苗接种', value: 'vaccination' },
      { label: '综合治疗', value: 'comprehensive' }
    ],
    // 治疗效果选项
    treatmentEffectOptions: [
      { label: '完全治愈', value: 'cured' },
      { label: '明显好转', value: 'improved' },
      { label: '略有好转', value: 'slightly_improved' },
      { label: '无变化', value: 'no_change' },
      { label: '病情加重', value: 'worsened' }
    ]
  },

  onLoad: function (options) {
    // 获取记录ID
    if (options.id) {
      this.setData({
        id: options.id
      });
      this.loadRecordDetail(options.id);
    } else {
      // 新建记录
      this.setData({
        isEditing: true,
        loading: false,
        record: {
          date: this.getCurrentDate(),
          status: 'death',
          description: '',
          images: [],
          // 死亡相关字段
          deathCauseIndex: undefined,
          deathSymptoms: '',
          deathTime: '',
          affectedCount: '',
          // 疾病相关字段
          diseaseTypeIndex: undefined,
          diseaseSymptoms: '',
          severityIndex: undefined,
          onsetDate: '',
          diagnosisDate: '',
          // 治疗相关字段
          treatmentIndex: undefined,
          medicationName: '',
          dosage: '',
          treatmentDuration: '',
          treatmentEffectIndex: undefined,
          treatmentCost: '',
          followUpDate: ''
        }
      });
    }
  },

  onShow: function () {
    // 页面显示
  },

  // 获取当前日期
  getCurrentDate: function () {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 加载记录详情
  loadRecordDetail: function (id) {
    this.setData({
      loading: true
    });

    api.health.getRecordDetail(id).then(res => {
      if (res.code === 0) {
        this.setData({
          record: res.data,
          loading: false
        });
      } else {
        this.setData({
          loading: false
        });
        wx.showToast({
          title: res.message || '获取详情失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      this.setData({
        loading: false
      });
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
    });
  },

  // 编辑记录
  onEdit: function () {
    this.setData({
      isEditing: true
    });
  },

  // 取消编辑
  onCancelEdit: function () {
    if (!this.data.id) {
      // 新建记录时取消编辑，返回上一页
      wx.navigateBack();
      return;
    }

    this.setData({
      isEditing: false
    });

    // 重新加载数据以恢复原始状态
    this.loadRecordDetail(this.data.id);
  },

  // 保存记录
  onSave: function () {
    const { id, record } = this.data;

    // 简单验证
    if (!record.date) {
      wx.showToast({
        title: '请选择日期',
        icon: 'none'
      });
      return;
    }

    if (!record.description) {
      wx.showToast({
        title: '请输入描述',
        icon: 'none'
      });
      return;
    }

    // 保存数据
    if (id) {
      // 更新记录
      api.health.updateRecord(id, record).then(res => {
        if (res.code === 0) {
          wx.showToast({
            title: '保存成功',
            icon: 'success'
          });

          // 更新页面数据
          this.setData({
            record: res.data,
            isEditing: false
          });

          // 保存数据到全局用于关联
          this.saveHealthRecordToGlobal(res.data);

          // 通知健康管理页面刷新数据
          const pages = getCurrentPages();
          const healthPage = pages.find(page => page.route === 'pages/health/health');
          if (healthPage) {
            healthPage.loadHealthRecords();
            healthPage.loadReportData();
          }
        } else {
          wx.showToast({
            title: res.message || '保存失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
      });
    } else {
      // 创建记录
      api.health.createRecord(record).then(res => {
        if (res.code === 0) {
          wx.showToast({
            title: '创建成功',
            icon: 'success'
          });

          // 设置ID并退出编辑模式
          this.setData({
            id: res.data.id,
            record: res.data,
            isEditing: false
          });

          // 保存数据到全局用于关联
          this.saveHealthRecordToGlobal(res.data);

          // 通知健康管理页面刷新数据
          const pages = getCurrentPages();
          const healthPage = pages.find(page => page.route === 'pages/health/health');
          if (healthPage) {
            healthPage.loadHealthRecords();
            healthPage.loadReportData();
          }
        } else {
          wx.showToast({
            title: res.message || '创建失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        wx.showToast({
          title: '创建失败',
          icon: 'none'
        });
      });
    }
  },

  // 删除记录
  onDelete: function () {
    const { id } = this.data;

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条健康记录吗？',
      success: (res) => {
        if (res.confirm) {
          api.health.deleteRecord(id).then(res => {
            if (res.code === 0) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });

              // 返回上一页
              setTimeout(() => {
                wx.navigateBack();
              }, 1000);
            } else {
              wx.showToast({
                title: res.message || '删除失败',
                icon: 'none'
              });
            }
          }).catch(err => {
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },



  // 选择日期
  onDateChange: function (e) {
    this.setData({
      'record.date': e.detail.value
    });
  },

  // 选择状态
  onStatusChange: function (e) {
    const status = e.currentTarget.dataset.status;
    
    // 切换状态时，清空状态特定字段
    const updatedRecord = {
      ...this.data.record,
      status: status,
      // 清空状态特定字段
      deathCauseIndex: undefined,
      deathSymptoms: '',
      deathTime: '',
      affectedCount: '',
      diseaseTypeIndex: undefined,
      diseaseSymptoms: '',
      severityIndex: undefined,
      onsetDate: '',
      diagnosisDate: '',
      treatmentIndex: undefined,
      medicationName: '',
      dosage: '',
      treatmentDuration: '',
      treatmentEffectIndex: undefined,
      treatmentCost: '',
      followUpDate: ''
    };
    
    this.setData({
      record: updatedRecord
    });
  },

  // 输入描述
  onDescriptionInput: function (e) {
    this.setData({
      'record.description': e.detail.value
    });
  },

  // 通用输入处理
  onInputChange: function (e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`record.${field}`]: value
    });
  },

  // 选择器变化处理
  onPickerChange: function (e) {
    const field = e.currentTarget.dataset.field;
    const value = parseInt(e.detail.value);
    this.setData({
      [`record.${field}`]: value
    });
  },

  // 选择图片
  onChooseImage: function () {
    const that = this;
    wx.chooseImage({
      count: 9,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: function (res) {
        const tempFiles = res.tempFiles;
        const images = that.data.record.images || [];

        // 添加新选择的图片
        tempFiles.forEach(file => {
          images.push({
            url: file.path,
            size: file.size
          });
        });

        that.setData({
          'record.images': images
        });
      }
    });
  },

  // 删除图片
  onDeleteImage: function (e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.record.images || [];
    images.splice(index, 1);

    this.setData({
      'record.images': images
    });
  },

  // 选择时间
  onTimeChange: function (e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`record.${field}`]: value
    });
  },

  // 选择日期字段
  onDateFieldChange: function (e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`record.${field}`]: value
    });
  },

  // 保存健康记录数据到全局用于关联
  saveHealthRecordToGlobal: function (record) {
    try {
      // 获取当前全局健康数据
      const app = getApp();
      let globalHealthData = app.globalData.healthRecords || [];
      
      // 将记录转换为健康报告格式的数据
      const healthData = this.convertRecordToHealthData(record);
      
      // 更新或添加记录
      const existingIndex = globalHealthData.findIndex(item => item.id === record.id);
      if (existingIndex >= 0) {
        globalHealthData[existingIndex] = healthData;
      } else {
        globalHealthData.push(healthData);
      }
      
      // 保存到全局数据
      app.globalData.healthRecords = globalHealthData;
      
      // 同时保存到本地存储
      wx.setStorageSync('healthRecords', globalHealthData);
      
    } catch (error) {
      console.error('保存健康记录数据失败:', error);
    }
  },

  // 将健康记录转换为健康报告格式
  convertRecordToHealthData: function (record) {
    const now = new Date();
    const timestamp = now.getTime();
    
    // 根据状态生成默认标题
    let title = '';
    switch (record.status) {
      case 'death':
        title = '死亡记录';
        break;
      case 'sick':
        title = '疾病记录';
        break;
      case 'treated':
        title = '治疗记录';
        break;
      default:
        title = '健康记录';
    }
    
    return {
      id: record.id || `health_${timestamp}`,
      date: record.date,
      timestamp: timestamp,
      status: record.status,
      title: title,
      
      // 状态特定数据
      deathCause: record.deathCauseIndex !== undefined ? this.data.deathCauseOptions[record.deathCauseIndex]?.value : null,
      deathSymptoms: record.deathSymptoms || '',
      deathTime: record.deathTime || '',
      affectedCount: parseInt(record.affectedCount) || 0,
      
      diseaseType: record.diseaseTypeIndex !== undefined ? this.data.diseaseTypeOptions[record.diseaseTypeIndex]?.value : null,
      diseaseSymptoms: record.diseaseSymptoms || '',
      severity: record.severityIndex !== undefined ? this.data.severityOptions[record.severityIndex]?.value : null,
      onsetDate: record.onsetDate || '',
      diagnosisDate: record.diagnosisDate || '',
      
      treatment: record.treatmentIndex !== undefined ? this.data.treatmentOptions[record.treatmentIndex]?.value : null,
      medicationName: record.medicationName || '',
      dosage: record.dosage || '',
      treatmentDuration: record.treatmentDuration || '',
      treatmentEffect: record.treatmentEffectIndex !== undefined ? this.data.treatmentEffectOptions[record.treatmentEffectIndex]?.value : null,
      treatmentCost: parseFloat(record.treatmentCost) || 0,
      followUpDate: record.followUpDate || '',
      
      description: record.description || '',
      images: record.images || []
    };
  }
});