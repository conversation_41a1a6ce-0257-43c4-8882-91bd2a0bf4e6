// pages/health/knowledge/detail/detail.js
Page({
  data: {
    articleId: null,
    articleData: {},
    relatedArticles: [],
    comments: [],
    commentText: '',
    loading: true
  },

  onLoad: function (options) {
    const articleId = options.id;
    if (articleId) {
      this.setData({ articleId });
      this.loadArticleDetail(articleId);
      this.loadRelatedArticles(articleId);
      this.loadComments(articleId);
    }
  },

  // 加载文章详情
  loadArticleDetail: function(articleId) {
    // 模拟数据
    const mockArticles = {
      1: {
        id: 1,
        title: '鹅的常见疾病及防治方法',
        categoryName: '疾病防治',
        author: '兽医专家',
        publishDate: '2023-06-15',
        readCount: 128,
        likeCount: 25,
        isLiked: false,
        isCollected: false,
        tags: ['疾病防治', '鹅病', '预防', '治疗'],
        content: `
          <h3>一、常见疾病类型</h3>
          <p>鹅的常见疾病主要包括传染性疾病、寄生虫病、营养代谢病和中毒性疾病等。了解这些疾病的症状和防治方法对于养鹅成功至关重要。</p>
          
          <h4>1. 鹅瘟</h4>
          <p><strong>症状：</strong>急性死亡、精神沉郁、食欲废绝、腹泻等。</p>
          <p><strong>防治：</strong>接种鹅瘟疫苗，加强饲养管理，发病时隔离治疗。</p>
          
          <h4>2. 小鹅瘟</h4>
          <p><strong>症状：</strong>主要危害20日龄以内的雏鹅，表现为精神萎靡、食欲不振、白色稀便等。</p>
          <p><strong>防治：</strong>种鹅免疫，雏鹅注射抗血清，加强环境卫生。</p>
          
          <h4>3. 禽霍乱</h4>
          <p><strong>症状：</strong>急性型突然死亡，慢性型关节肿胀、跛行。</p>
          <p><strong>防治：</strong>疫苗免疫，抗生素治疗，改善饲养环境。</p>
          
          <h3>二、预防措施</h3>
          <p>1. <strong>免疫接种：</strong>按照免疫程序及时接种疫苗</p>
          <p>2. <strong>环境卫生：</strong>保持鹅舍清洁干燥，定期消毒</p>
          <p>3. <strong>饲养管理：</strong>合理密度，营养均衡，减少应激</p>
          <p>4. <strong>隔离观察：</strong>新引进鹅群要隔离观察</p>
          
          <h3>三、治疗原则</h3>
          <p>发现病鹅要及时隔离，根据症状确诊后对症治疗。同时要加强健康鹅群的预防措施，防止疫病扩散。</p>
        `
      },
      2: {
        id: 2,
        title: '鹅的营养需求与饲料配制',
        categoryName: '饲料营养',
        author: '营养专家',
        publishDate: '2023-06-10',
        readCount: 96,
        likeCount: 18,
        isLiked: false,
        isCollected: false,
        tags: ['营养', '饲料', '配制', '生长'],
        content: `
          <h3>一、鹅的营养需求特点</h3>
          <p>鹅是草食性水禽，具有消化粗纤维能力强、生长速度快、饲料转化率高等特点。不同生长阶段的营养需求有所差异。</p>
          
          <h4>1. 雏鹅期（0-4周）</h4>
          <p>粗蛋白：20-22%，代谢能：2800-3000kcal/kg</p>
          
          <h4>2. 中鹅期（5-10周）</h4>
          <p>粗蛋白：16-18%，代谢能：2700-2900kcal/kg</p>
          
          <h4>3. 育肥期（11周以后）</h4>
          <p>粗蛋白：14-16%，代谢能：2900-3100kcal/kg</p>
          
          <h3>二、饲料配制原则</h3>
          <p>1. 营养全面均衡</p>
          <p>2. 原料新鲜优质</p>
          <p>3. 适口性好</p>
          <p>4. 成本合理</p>
          
          <h3>三、常用饲料原料</h3>
          <p><strong>能量饲料：</strong>玉米、小麦、大麦等</p>
          <p><strong>蛋白饲料：</strong>豆粕、鱼粉、菜籽粕等</p>
          <p><strong>矿物质饲料：</strong>石粉、磷酸氢钙、食盐等</p>
          <p><strong>维生素添加剂：</strong>复合维生素、维生素预混料等</p>
        `
      }
    };

    const articleData = mockArticles[articleId] || mockArticles[1];
    
    this.setData({
      articleData: articleData,
      loading: false
    });

    // 增加浏览量
    this.increaseViewCount(articleId);
  },

  // 加载相关文章
  loadRelatedArticles: function(articleId) {
    const mockRelated = [
      {
        id: 3,
        title: '朗德鹅品种特性及养殖要点',
        categoryName: '品种繁育',
        publishDate: '2023-06-05'
      },
      {
        id: 4,
        title: '夏季鹅的饲养管理要点',
        categoryName: '饲养管理',
        publishDate: '2023-06-01'
      }
    ];

    this.setData({ relatedArticles: mockRelated });
  },

  // 加载评论
  loadComments: function(articleId) {
    const mockComments = [
      {
        id: 1,
        author: '养鹅新手',
        avatar: '/images/default_avatar.png',
        content: '这篇文章写得很详细，对我这个新手来说很有帮助！',
        time: '2023-06-16 10:30'
      },
      {
        id: 2,
        author: '老养殖户',
        avatar: '/images/default_avatar.png',
        content: '预防确实比治疗重要，我现在都是按照文章说的做免疫。',
        time: '2023-06-16 14:20'
      }
    ];

    this.setData({ comments: mockComments });
  },

  // 增加浏览量
  increaseViewCount: function(articleId) {
    // 这里可以调用API增加浏览量
  },

  // 点赞
  onLikeTap: function() {
    const isLiked = this.data.articleData.isLiked;
    const likeCount = this.data.articleData.likeCount || 0;
    
    this.setData({
      'articleData.isLiked': !isLiked,
      'articleData.likeCount': isLiked ? likeCount - 1 : likeCount + 1
    });

    wx.showToast({
      title: isLiked ? '取消点赞' : '点赞成功',
      icon: 'success',
      duration: 1000
    });
  },

  // 收藏
  onCollectTap: function() {
    const isCollected = this.data.articleData.isCollected;
    
    this.setData({
      'articleData.isCollected': !isCollected
    });

    wx.showToast({
      title: isCollected ? '取消收藏' : '收藏成功',
      icon: 'success',
      duration: 1000
    });
  },

  // 分享
  onShareTap: function() {
    wx.showShareMenu({
      withShareTicket: true
    });
  },

  // 评论输入
  onCommentInput: function(e) {
    this.setData({ commentText: e.detail.value });
  },

  // 提交评论
  onSubmitComment: function() {
    const commentText = this.data.commentText.trim();
    if (!commentText) return;

    const newComment = {
      id: Date.now(),
      author: '我',
      avatar: '/images/default_avatar.png',
      content: commentText,
      time: new Date().toLocaleString()
    };

    this.setData({
      comments: [newComment, ...this.data.comments],
      commentText: ''
    });

    wx.showToast({
      title: '评论成功',
      icon: 'success',
      duration: 1000
    });
  },

  // 点击相关文章
  onRelatedItemTap: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.redirectTo({
      url: `/pages/health/knowledge/detail/detail?id=${id}`
    });
  },

  // 分享配置
  onShareAppMessage: function () {
    return {
      title: this.data.articleData.title,
      path: `/pages/health/knowledge/detail/detail?id=${this.data.articleId}`,
      imageUrl: '/images/share_default.png'
    };
  }
});
