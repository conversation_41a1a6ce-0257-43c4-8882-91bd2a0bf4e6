// pages/management/dashboard/dashboard.js
const { IMAGES, UI, BUSINESS, API } = require('../../../constants/index.js');
const request = require('../../../utils/request.js');
const app = getApp();

/**
 * 管理仪表板页面 - 租户管理中心
 * 功能：数据概览、快捷管理、员工监控、业务统计
 */

Page({
  data: {
    // 用户权限
    userRole: 'manager',
    hasManagementAccess: false,
    
    // 仪表板数据
    overview: {
      totalStaff: 0,
      activeStaff: 0,
      totalFlocks: 0,
      totalGeese: 0,
      todayProduction: 0,
      monthlyRevenue: 0
    },
    
    // 待处理事项
    pendingTasks: {
      approvals: 0,
      alerts: 0,
      reports: 0,
      maintenance: 0
    },
    
    // 快捷功能菜单
    quickActions: [
      {
        id: 'staff',
        title: '员工管理',
        icon: '/assets/icons/users.png',
        url: '/pages/management/staff/staff',
        description: '查看和管理员工信息'
      },
      {
        id: 'approval',
        title: '审批中心',
        icon: '/assets/icons/approval.png',
        url: '/pages/management/approval/approval',
        description: '处理待审批事项'
      },
      {
        id: 'monitor',
        title: '实时监控',
        icon: '/assets/icons/monitor.png',
        url: '/pages/management/monitor/monitor',
        description: '监控生产和设备状态'
      },
      {
        id: 'reports',
        title: '数据报表',
        icon: '/assets/icons/chart.png',
        url: '/pages/management/reports/reports',
        description: '查看经营数据分析'
      },
      {
        id: 'settings',
        title: '系统设置',
        icon: '/assets/icons/settings.png',
        url: '/pages/management/settings/settings',
        description: '管理系统参数和配置'
      },
      {
        id: 'finance',
        title: '财务管理',
        icon: '/assets/icons/finance.png',
        url: '/pages/management/finance/finance',
        description: '财务数据和成本分析'
      }
    ],
    
    // 页面状态
    loading: true,
    refreshing: false
  },

  onLoad: function(options) {
    this.checkUserPermission();
    this.loadDashboardData();
  },

  onShow: function() {
    // 页面显示时刷新数据
    if (!this.data.loading) {
      this.refreshData();
    }
  },

  // 检查用户权限
  checkUserPermission: function() {
    const userInfo = app.globalData.userInfo || {};
    const userRole = userInfo.role || 'user';
    
    // 检查是否有管理权限
    const hasManagementAccess = ['admin', 'manager'].includes(userRole);
    
    if (!hasManagementAccess) {
      wx.showModal({
        title: '权限不足',
        content: '您没有访问管理功能的权限',
        showCancel: false,
        complete: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    this.setData({
      userRole,
      hasManagementAccess
    });
  },

  // 加载仪表板数据
  loadDashboardData: function() {
    this.setData({ loading: true });
    
    Promise.all([
      this.loadOverviewData(),
      this.loadPendingTasks(),
      this.loadRecentActivities()
    ]).then(() => {
      this.setData({ loading: false });
    }).catch(error => {
      console.error('加载仪表板数据失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    });
  },

  // 加载概览数据
  loadOverviewData: function() {
    return request.get(API.ENDPOINTS.MANAGEMENT.OVERVIEW)
      .then(response => {
        if (response.success) {
          this.setData({
            overview: response.data
          });
        }
      })
      .catch(error => {
        console.error('加载概览数据失败:', error);
        // 使用模拟数据
        this.setData({
          overview: {
            totalStaff: 12,
            activeStaff: 10,
            totalFlocks: 5,
            totalGeese: 2500,
            todayProduction: 1200,
            monthlyRevenue: 85000
          }
        });
      });
  },

  // 加载待处理事项
  loadPendingTasks: function() {
    return request.get(API.ENDPOINTS.MANAGEMENT.PENDING_TASKS)
      .then(response => {
        if (response.success) {
          this.setData({
            pendingTasks: response.data
          });
        }
      })
      .catch(error => {
        console.error('加载待处理事项失败:', error);
        // 使用模拟数据
        this.setData({
          pendingTasks: {
            approvals: 3,
            alerts: 2,
            reports: 1,
            maintenance: 0
          }
        });
      });
  },

  // 加载最近活动
  loadRecentActivities: function() {
    return request.get(API.ENDPOINTS.MANAGEMENT.RECENT_ACTIVITIES)
      .then(response => {
        if (response.success) {
          this.setData({
            recentActivities: response.data
          });
        }
      })
      .catch(error => {
        console.error('加载最近活动失败:', error);
      });
  },

  // 刷新数据
  refreshData: function() {
    this.setData({ refreshing: true });
    this.loadDashboardData().finally(() => {
      this.setData({ refreshing: false });
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.refreshData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 点击快捷功能
  onQuickActionTap: function(e) {
    const { url, id } = e.currentTarget.dataset;
    
    if (url) {
      wx.navigateTo({
        url: url,
        fail: () => {
          wx.showToast({
            title: '页面暂未开放',
            icon: 'none'
          });
        }
      });
    }
  },

  // 查看待处理详情
  onPendingTaskTap: function(e) {
    const { type } = e.currentTarget.dataset;
    
    switch (type) {
      case 'approvals':
        wx.navigateTo({
          url: '/pages/management/approval/approval'
        });
        break;
      case 'alerts':
        wx.navigateTo({
          url: '/pages/management/monitor/monitor?tab=alerts'
        });
        break;
      case 'reports':
        wx.navigateTo({
          url: '/pages/management/reports/reports'
        });
        break;
      case 'maintenance':
        wx.navigateTo({
          url: '/pages/management/monitor/monitor?tab=maintenance'
        });
        break;
    }
  },

  // 查看更多概览数据
  onViewMoreOverview: function() {
    wx.navigateTo({
      url: '/pages/management/reports/reports?type=overview'
    });
  }
});