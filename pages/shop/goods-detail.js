// pages/shop/goods-detail.js - 商品详情页 (企业级重构版)

/**
 * 商品详情页控制器
 * 基于全局组件重构，提供完整的商品详情和购买体验
 */

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 商品基本信息
    goods: null,
    
    // 商品图片列表
    goodsImages: [],
    
    // 推荐商品
    recommendGoods: [
      {
        id: 101,
        name: '智能喂水器',
        price: 189.99,
        originalPrice: 229.99,
        image: '/images/icons/goods7.png',
        rating: 4.7,
        reviewCount: 56,
        isNew: false,
        isHot: true,
        isRecommended: true
      },
      {
        id: 102,
        name: '鹅舍保温灯',
        price: 129.99,
        originalPrice: null,
        image: '/images/icons/goods8.png',
        rating: 4.5,
        reviewCount: 34,
        isNew: true,
        isHot: false,
        isRecommended: false
      },
      {
        id: 103,
        name: '养殖围栏',
        price: 259.99,
        originalPrice: 319.99,
        image: '/images/icons/goods9.png',
        rating: 4.8,
        reviewCount: 78,
        isNew: false,
        isHot: false,
        isRecommended: true
      }
    ],
    
    // 购买数量
    quantity: 1,
    
    // 购物车数量
    cartCount: 0,
    
    // 页面状态
    loading: false,
    
    // 商品模拟数据
    goodsData: [
      {
        id: 1,
        name: '优质鹅饲料',
        price: 99.99,
        originalPrice: 129.99,
        image: '/images/icons/goods1.png',
        images: [
          '/images/icons/goods1.png',
          '/images/icons/goods1-detail1.png',
          '/images/icons/goods1-detail2.png'
        ],
        category: 1,
        description: '专为鹅类设计的高营养饲料，促进健康成长，富含蛋白质和维生素，适合各年龄段的鹅类食用。采用天然原料，无添加剂，安全可靠。经过科学配比，营养均衡，能有效提高鹅的产蛋率和肉质品质。',
        stock: 150,
        sales: 1200,
        reviewCount: 286,
        rating: 4.8,
        isNew: true,
        isHot: false,
        isRecommended: true,
        isFavorited: false
      },
      {
        id: 2,
        name: '疫苗套装',
        price: 199.99,
        originalPrice: 249.99,
        image: '/images/icons/goods2.png',
        images: [
          '/images/icons/goods2.png',
          '/images/icons/goods2-detail1.png'
        ],
        category: 2,
        description: '预防常见鹅类疾病的疫苗组合套装，包含最常见的病毒性疾病预防疫苗，使用方便，效果显著。由权威机构认证，安全性高，免疫效果持久。适用于各种养殖规模，是养鹅场必备的防疫用品。',
        stock: 88,
        sales: 350,
        reviewCount: 128,
        rating: 4.9,
        isNew: false,
        isHot: true,
        isRecommended: true,
        isFavorited: true
      },
      {
        id: 3,
        name: '养殖设备',
        price: 299.99,
        originalPrice: 399.99,
        image: '/images/icons/goods3.png',
        images: [
          '/images/icons/goods3.png'
        ],
        category: 3,
        description: '现代化鹅类养殖设备，提高养殖效率，自动喂食和清洁系统，节省大量人工成本。采用优质材料制造，耐用性强，操作简单。适合中大型养殖场使用，能显著提高养殖效率和经济效益。',
        stock: 45,
        sales: 89,
        reviewCount: 73,
        rating: 4.6,
        isNew: false,
        isHot: false,
        isRecommended: false,
        isFavorited: false
      }
    ]
  },

  /**
   * 计算属性 - 折扣
   */
  get discount() {
    const { goods } = this.data;
    if (goods && goods.originalPrice && goods.price) {
      return ((goods.originalPrice - goods.price) / goods.originalPrice * 100).toFixed(0);
    }
    return 0;
  },

  /**
   * 计算属性 - 库存状态
   */
  get stockStatus() {
    const { goods } = this.data;
    if (!goods) {
      return { text: '加载中...', color: 'var(--global-text-color-tertiary)' };
    }
    
    const stock = goods.stock || 0;
    if (stock <= 0) {
      return { text: '缺货', color: 'var(--global-error-color)' };
    } else if (stock <= 10) {
      return { text: `库存紧张 (${stock}件)`, color: 'var(--global-warning-color)' };
    } else {
      return { text: `库存充足 (${stock}件)`, color: 'var(--global-success-color)' };
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const goodsId = parseInt(options.id) || 1;
    
    this.loadGoodsDetail(goodsId);
    this.loadCartCount();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadCartCount();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshGoodsDetail();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { goods } = this.data;
    return {
      title: goods ? `${goods.name} - 智慧养鹅商城` : '智慧养鹅商城',
      path: `/pages/shop/goods-detail?id=${goods?.id || 1}`,
      imageUrl: goods?.image || '/images/share/goods-share.png'
    };
  },

  /**
   * 分享按钮点击
   */
  onShareTap() {
    wx.vibrateShort({ type: 'light' });
    
    wx.showShareMenu({
      withShareTicket: true,
      showShareItems: ['shareAppMessage', 'shareTimeline']
    });
  },

  /**
   * 加载商品详情
   */
  async loadGoodsDetail(goodsId) {
    this.setData({ loading: true });
    
    try {
      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const goods = this.data.goodsData.find(item => item.id === goodsId);
      
      if (goods) {
        this.setData({
          goods: goods,
          goodsImages: goods.images || [goods.image],
          discount: this.discount,
          stockStatus: this.stockStatus
        });
        
        // 设置页面标题
        wx.setNavigationBarTitle({
          title: goods.name
        });
      } else {
        wx.showToast({
          title: '商品不存在',
          icon: 'error'
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('加载商品详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 刷新商品详情
   */
  async refreshGoodsDetail() {
    const { goods } = this.data;
    if (!goods) return;
    
    try {
      await this.loadGoodsDetail(goods.id);
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    } catch (error) {
      wx.showToast({
        title: '刷新失败',
        icon: 'error'
      });
    } finally {
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 图片预览
   */
  onImagePreview(e) {
    const url = e.currentTarget.dataset.url;
    
    wx.previewImage({
      current: url,
      urls: this.data.goodsImages
    });
  },

  /**
   * 收藏点击
   */
  onFavoriteTap() {
    const { goods } = this.data;
    if (!goods) return;
    
    wx.vibrateShort({ type: 'light' });
    
    const isFavorited = !goods.isFavorited;
    
    this.setData({
      'goods.isFavorited': isFavorited
    });
    
    wx.showToast({
      title: isFavorited ? '已收藏' : '取消收藏',
      icon: 'success'
    });
    
    // 这里可以调用API保存收藏状态
  },

  /**
   * 数量减少
   */
  onQuantityMinus() {
    const { quantity } = this.data;
    
    if (quantity <= 1) {
      wx.showToast({
        title: '最少购买1件',
        icon: 'none'
      });
      return;
    }
    
    wx.vibrateShort({ type: 'light' });
    
    this.setData({
      quantity: quantity - 1
    });
  },

  /**
   * 数量增加
   */
  onQuantityPlus() {
    const { quantity, goods } = this.data;
    
    if (quantity >= goods.stock) {
      wx.showToast({
        title: '库存不足',
        icon: 'none'
      });
      return;
    }
    
    wx.vibrateShort({ type: 'light' });
    
    this.setData({
      quantity: quantity + 1
    });
  },

  /**
   * 数量输入
   */
  onQuantityInput(e) {
    let value = parseInt(e.detail.value) || 1;
    const { goods } = this.data;
    
    // 数量边界检查
    if (value < 1) {
      value = 1;
    } else if (value > goods.stock) {
      value = goods.stock;
      wx.showToast({
        title: '库存不足',
        icon: 'none'
      });
    }
    
    this.setData({ quantity: value });
  },

  /**
   * 客服点击
   */
  onServiceTap() {
    wx.vibrateShort({ type: 'light' });
    
    wx.showModal({
      title: '联系客服',
      content: '客服电话：400-123-4567\n工作时间：9:00-18:00',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 购物车点击
   */
  onCartTap() {
    wx.vibrateShort({ type: 'light' });
    
    wx.navigateTo({
      url: '/pages/shop/cart',
      fail: () => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 加入购物车
   */
  onAddToCart() {
    const { goods, quantity } = this.data;
    
    if (!goods || goods.stock <= 0) {
      wx.showToast({
        title: '商品缺货',
        icon: 'error'
      });
      return;
    }
    
    wx.vibrateShort({ type: 'medium' });
    
    try {
      // 获取现有购物车
      const cartData = wx.getStorageSync('shop_cart') || { items: [], count: 0 };
      let cartItems = cartData.items || [];
      
      // 查找是否已存在
      const existingItem = cartItems.find(item => item.product.id === goods.id);
      
      if (existingItem) {
        // 增加数量
        existingItem.quantity += quantity;
        if (existingItem.quantity > goods.stock) {
          existingItem.quantity = goods.stock;
          wx.showToast({
            title: '已达最大库存',
            icon: 'none'
          });
        }
      } else {
        // 新增商品
        cartItems.push({
          id: Date.now(),
          product: goods,
          quantity: quantity,
          selected: true,
          addTime: Date.now()
        });
      }
      
      // 保存购物车
      const totalCount = cartItems.reduce((sum, item) => sum + item.quantity, 0);
      wx.setStorageSync('shop_cart', {
        items: cartItems,
        count: totalCount,
        updateTime: Date.now()
      });
      
      this.setData({ cartCount: totalCount });
      
      wx.showToast({
        title: '已加入购物车',
        icon: 'success'
      });
      
    } catch (error) {
      console.error('加入购物车失败:', error);
      wx.showToast({
        title: '加入失败',
        icon: 'error'
      });
    }
  },

  /**
   * 立即购买
   */
  onBuyNow() {
    const { goods, quantity } = this.data;
    
    if (!goods || goods.stock <= 0) {
      wx.showToast({
        title: '商品缺货',
        icon: 'error'
      });
      return;
    }
    
    wx.vibrateShort({ type: 'heavy' });
    
    try {
      // 创建立即购买的商品项
      const buyItem = {
        id: Date.now(),
        product: goods,
        quantity: quantity,
        selected: true,
        addTime: Date.now()
      };
      
      // 保存到结算数据
      wx.setStorageSync('checkout_items', {
        items: [buyItem],
        totalPrice: (goods.price * quantity).toFixed(2),
        originalPrice: ((goods.originalPrice || goods.price) * quantity).toFixed(2),
        createTime: Date.now()
      });
      
      wx.navigateTo({
        url: '/pages/shop/checkout',
        fail: () => {
          wx.showToast({
            title: '页面跳转失败',
            icon: 'error'
          });
        }
      });
      
    } catch (error) {
      console.error('立即购买失败:', error);
      wx.showToast({
        title: '购买失败',
        icon: 'error'
      });
    }
  },

  /**
   * 推荐商品点击
   */
  onRecommendTap(e) {
    const goodsId = e.currentTarget.dataset.id;
    
    wx.vibrateShort({ type: 'light' });
    
    wx.redirectTo({
      url: `/pages/shop/goods-detail?id=${goodsId}`,
      fail: () => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 推荐商品加购物车
   */
  onRecommendAddCart(e) {
    const { product } = e.detail;
    
    wx.vibrateShort({ type: 'medium' });
    
    try {
      // 获取现有购物车
      const cartData = wx.getStorageSync('shop_cart') || { items: [], count: 0 };
      let cartItems = cartData.items || [];
      
      // 查找是否已存在
      const existingItem = cartItems.find(item => item.product.id === product.id);
      
      if (existingItem) {
        existingItem.quantity += 1;
      } else {
        cartItems.push({
          id: Date.now(),
          product: product,
          quantity: 1,
          selected: true,
          addTime: Date.now()
        });
      }
      
      // 保存购物车
      const totalCount = cartItems.reduce((sum, item) => sum + item.quantity, 0);
      wx.setStorageSync('shop_cart', {
        items: cartItems,
        count: totalCount,
        updateTime: Date.now()
      });
      
      this.setData({ cartCount: totalCount });
      
      wx.showToast({
        title: '已加入购物车',
        icon: 'success'
      });
      
    } catch (error) {
      console.error('加入购物车失败:', error);
      wx.showToast({
        title: '加入失败',
        icon: 'error'
      });
    }
  },

  /**
   * 加载购物车数量
   */
  loadCartCount() {
    try {
      const cartData = wx.getStorageSync('shop_cart');
      if (cartData && cartData.count) {
        this.setData({ cartCount: cartData.count });
      }
    } catch (error) {
      console.error('加载购物车数量失败:', error);
    }
  }
});