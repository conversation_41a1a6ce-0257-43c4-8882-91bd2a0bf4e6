// pages/shop/order-success.js - 订单成功页面 (企业级重构版)

/**
 * 订单成功页面控制器
 * 提供完整的订单成功确认和后续操作
 */

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 订单信息
    orderInfo: {
      orderId: '',
      orderTime: '',
      amount: '0.00',
      itemCount: 0,
      items: []
    },
    
    // 配送信息
    deliveryInfo: {
      address: '',
      method: '',
      estimatedTime: ''
    },
    
    // 推荐商品
    recommendGoods: [
      {
        id: 201,
        name: '智能温控器',
        price: 299.99,
        image: '/images/icons/goods10.png',
        description: '智能控温，节能环保'
      },
      {
        id: 202,
        name: '自动喂料机',
        price: 599.99,
        image: '/images/icons/goods11.png',
        description: '定时定量，科学喂养'
      },
      {
        id: 203,
        name: '环境监测仪',
        price: 199.99,
        image: '/images/icons/goods12.png',
        description: '实时监测，健康养殖'
      }
    ],
    
    // 页面状态
    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { orderId } = options;
    
    this.loadOrderData(orderId);
    this.loadRecommendGoods();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 播放成功音效
    this.playSuccessSound();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    const { orderInfo } = this.data;
    if (orderInfo.orderId) {
      this.loadOrderData(orderInfo.orderId);
    }
    wx.stopPullDownRefresh();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { orderInfo } = this.data;
    return {
      title: `我在智慧养鹅商城下单成功！订单金额￥${orderInfo.amount}`,
      path: `/pages/shop/shop`,
      imageUrl: '/images/share/order-success-share.png'
    };
  },

  /**
   * 分享按钮点击
   */
  onShareTap() {
    wx.vibrateShort({ type: 'light' });
    
    wx.showShareMenu({
      withShareTicket: true,
      showShareItems: ['shareAppMessage', 'shareTimeline']
    });
  },

  /**
   * 加载订单数据
   */
  loadOrderData(orderId) {
    this.setData({ loading: true });
    
    try {
      // 尝试从存储中获取订单数据
      const submittedOrder = wx.getStorageSync('submitted_order');
      
      if (submittedOrder && submittedOrder.orderId === orderId) {
        this.processOrderData(submittedOrder);
      } else {
        // 如果存储中没有数据，从服务器获取
        this.fetchOrderFromServer(orderId);
      }
    } catch (error) {
      console.error('加载订单数据失败:', error);
      this.showOrderNotFound();
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 处理订单数据
   */
  processOrderData(orderData) {
    // 格式化订单信息
    const orderInfo = {
      orderId: orderData.orderId,
      orderTime: this.formatDateTime(orderData.createTime),
      amount: orderData.amount.finalTotal,
      itemCount: orderData.items.reduce((sum, item) => sum + item.quantity, 0),
      items: orderData.items
    };
    
    // 格式化配送信息
    const deliveryInfo = {
      address: this.formatAddress(orderData.address),
      method: this.formatDeliveryMethod(orderData.delivery),
      estimatedTime: this.calculateEstimatedTime(orderData.delivery)
    };
    
    this.setData({
      orderInfo: orderInfo,
      deliveryInfo: deliveryInfo
    });
    
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: `订单${orderInfo.orderId.slice(-6)}`
    });
  },

  /**
   * 从服务器获取订单数据（模拟）
   */
  async fetchOrderFromServer(orderId) {
    try {
      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟订单数据
      const mockOrderData = {
        orderId: orderId,
        createTime: Date.now() - 300000, // 5分钟前
        amount: { finalTotal: '199.99' },
        items: [
          {
            product: { name: '优质鹅饲料', price: 99.99 },
            quantity: 2
          }
        ],
        address: {
          name: '张三',
          phone: '13800138000',
          fullAddress: '广东省深圳市南山区科技园中区科苑路15号'
        },
        delivery: {
          type: 'standard',
          fee: 0
        }
      };
      
      this.processOrderData(mockOrderData);
    } catch (error) {
      console.error('获取订单数据失败:', error);
      this.showOrderNotFound();
    }
  },

  /**
   * 显示订单未找到
   */
  showOrderNotFound() {
    wx.showModal({
      title: '订单不存在',
      content: '未找到相关订单信息，请检查订单号或稍后重试',
      showCancel: false,
      success: () => {
        wx.navigateBack({
          fail: () => {
            wx.redirectTo({
              url: '/pages/shop/shop'
            });
          }
        });
      }
    });
  },

  /**
   * 播放成功音效
   */
  playSuccessSound() {
    try {
      wx.createInnerAudioContext && wx.createInnerAudioContext().play();
    } catch (error) {
    }
  },

  /**
   * 复制订单号
   */
  onCopyOrderId() {
    const { orderInfo } = this.data;
    
    wx.vibrateShort({ type: 'medium' });
    
    wx.setClipboardData({
      data: orderInfo.orderId,
      success: () => {
        wx.showToast({
          title: '订单号已复制',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showModal({
          title: '复制失败',
          content: `订单号：${orderInfo.orderId}`,
          showCancel: false
        });
      }
    });
  },

  /**
   * 联系客服
   */
  onContactService() {
    wx.vibrateShort({ type: 'light' });
    
    const { orderInfo } = this.data;
    
    wx.showModal({
      title: '联系客服',
      content: `客服电话：************\n工作时间：9:00-18:00\n\n订单号：${orderInfo.orderId}`,
      confirmText: '拨打电话',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '4001234567',
            fail: () => {
              wx.showToast({
                title: '拨打失败',
                icon: 'error'
              });
            }
          });
        }
      }
    });
  },

  /**
   * 查看订单
   */
  onViewOrder() {
    const { orderInfo } = this.data;
    
    wx.vibrateShort({ type: 'light' });
    
    wx.navigateTo({
      url: `/pages/shop/order-detail?id=${orderInfo.orderId}`,
      fail: () => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 物流跟踪
   */
  onTrackDelivery() {
    const { orderInfo } = this.data;
    
    wx.vibrateShort({ type: 'light' });
    
    wx.showModal({
      title: '物流跟踪',
      content: '订单正在配货中，预计今晚发货。我们将通过短信及时通知您物流进度。',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 继续购物
   */
  onContinueShopping() {
    wx.vibrateShort({ type: 'light' });
    
    wx.switchTab({
      url: '/pages/shop/shop',
      fail: () => {
        wx.redirectTo({
          url: '/pages/shop/shop'
        });
      }
    });
  },

  /**
   * 返回首页
   */
  onGoHome() {
    wx.vibrateShort({ type: 'light' });
    
    wx.switchTab({
      url: '/pages/home/<USER>',
      fail: () => {
        wx.redirectTo({
          url: '/pages/home/<USER>'
        });
      }
    });
  },

  /**
   * 推荐商品点击
   */
  onRecommendTap(e) {
    const goodsId = e.currentTarget.dataset.id;
    
    wx.vibrateShort({ type: 'light' });
    
    wx.navigateTo({
      url: `/pages/shop/goods-detail?id=${goodsId}`,
      fail: () => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 加载推荐商品
   */
  loadRecommendGoods() {
    // 这里可以根据订单商品推荐相关商品
    // 目前使用静态数据
  },

  /**
   * 格式化日期时间
   */
  formatDateTime(timestamp) {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  /**
   * 格式化地址
   */
  formatAddress(address) {
    if (!address) return '地址信息缺失';
    
    return `${address.name} ${address.phone}\n${address.fullAddress}`;
  },

  /**
   * 格式化配送方式
   */
  formatDeliveryMethod(delivery) {
    if (!delivery) return '标准配送';
    
    const methods = {
      'standard': '标准配送',
      'express': '加急配送'
    };
    
    const methodName = methods[delivery.type] || '标准配送';
    const fee = delivery.fee > 0 ? ` (+¥${delivery.fee.toFixed(2)})` : ' (免运费)';
    
    return methodName + fee;
  },

  /**
   * 计算预计送达时间
   */
  calculateEstimatedTime(delivery) {
    const now = new Date();
    let days = 3; // 默认3天
    
    if (delivery && delivery.type === 'express') {
      days = 1; // 加急配送1天
    }
    
    const estimatedDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);
    const month = String(estimatedDate.getMonth() + 1).padStart(2, '0');
    const day = String(estimatedDate.getDate()).padStart(2, '0');
    
    return `${month}月${day}日`;
  },

  /**
   * 页面卸载
   */
  onUnload() {
    // 清理订单数据（可选）
    try {
      wx.removeStorageSync('submitted_order');
    } catch (error) {
      console.error('清理数据失败:', error);
    }
  }
});