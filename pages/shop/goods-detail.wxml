<!-- pages/shop/goods-detail.wxml -->
<view class="goods-detail-container">
  <!-- 页面头部 -->
  <global-page-header 
    title="商品详情" 
    show-back="{{true}}"
    theme="shop"
    custom-class="detail-header">
    <view slot="extra" class="header-actions">
      <!-- 分享按钮 -->
      <view class="share-button" bind:tap="onShareTap">
        <global-icon name="分享" size="18" color="var(--global-text-color-inverse)"></global-icon>
      </view>
      <!-- 收藏按钮 -->
      <view class="favorite-button {{goods.isFavorited ? 'favorited' : ''}}" bind:tap="onFavoriteTap">
        <global-icon name="收藏" size="18" color="{{(goods.isFavorited ? 'var(--shop-accent-color)' : 'var(--global-text-color-inverse)') || 'var(--global-text-color-inverse)'}}"></global-icon>
      </view>
    </view>
  </global-page-header>

  <!-- 商品详情内容 -->
  <scroll-view class="detail-content" scroll-y="true" enhanced="true">
    <!-- 商品图片轮播 -->
    <swiper 
      class="goods-swiper" 
      indicator-dots="{{goodsImages.length > 1}}"
      indicator-color="rgba(255, 255, 255, 0.3)"
      indicator-active-color="var(--shop-primary-color)"
      autoplay="{{goodsImages.length > 1}}"
      interval="5000"
      circular="{{goodsImages.length > 1}}">
      <swiper-item wx:for="{{goodsImages}}" wx:key="index">
        <image 
          class="goods-image" 
          src="{{item}}" 
          mode="aspectFit"
          bind:tap="onImagePreview"
          data-url="{{item}}"
          lazy-load="{{true}}">
        </image>
      </swiper-item>
    </swiper>

    <!-- 商品基本信息 -->
    <view class="goods-info-section">
      <!-- 价格区域 -->
      <view class="price-section">
        <view class="current-price">
          <text class="price-symbol">¥</text>
          <text class="price-value">{{goods.price}}</text>
        </view>
        <view wx:if="{{goods.originalPrice && goods.originalPrice > goods.price}}" class="original-price">
          <text class="original-text">原价</text>
          <text class="original-value">¥{{goods.originalPrice}}</text>
        </view>
        <view wx:if="{{discount > 0}}" class="discount-info">
          <text class="discount-text">立省¥{{goods.discountAmount}}</text>
        </view>
      </view>

      <!-- 商品标题和标签 -->
      <view class="title-section">
        <text class="goods-title">{{goods.name}}</text>
        <view class="goods-tags">
          <text wx:if="{{goods.isNew}}" class="tag tag-new">新品</text>
          <text wx:if="{{goods.isHot}}" class="tag tag-hot">热销</text>
          <text wx:if="{{goods.isRecommended}}" class="tag tag-recommend">推荐</text>
          <text wx:if="{{discount > 0}}" class="tag tag-discount">特价</text>
        </view>
      </view>

      <!-- 销量和评价 -->
      <view class="stats-section">
        <view class="stat-item">
          <global-icon name="总数" size="14" color="var(--global-text-color-tertiary)"></global-icon>
          <text class="stat-text">销量 {{goods.sales || 0}}</text>
        </view>
        <view class="stat-item">
          <global-icon name="评分" size="14" color="var(--global-text-color-tertiary)"></global-icon>
          <text class="stat-text">评价 {{goods.reviewCount || 0}}</text>
        </view>
        <view class="stat-item">
                        <global-icon name="库存" size="14" color="{{stockStatus.color || 'var(--global-text-color-tertiary)'}}"></global-icon>
          <text class="stat-text">{{stockStatus.text}}</text>
        </view>
      </view>
    </view>

    <!-- 商品规格选择 -->
    <view class="specs-section">
      <view class="section-title">
        <global-icon name="设置" size="16" color="var(--shop-primary-color)"></global-icon>
        <text class="title-text">商品规格</text>
      </view>
      
      <!-- 数量选择 -->
      <view class="quantity-selector">
        <text class="quantity-label">购买数量</text>
        <view class="quantity-controls">
          <view 
            class="quantity-btn {{quantity <= 1 ? 'disabled' : ''}}"
            bind:tap="onQuantityMinus">
            <global-icon name="减少" size="16" color="var(--global-text-color-secondary)"></global-icon>
          </view>
          <input 
            class="quantity-input"
            type="number"
            value="{{quantity}}"
            bind:input="onQuantityInput"
            maxlength="3"
          />
          <view 
            class="quantity-btn {{quantity >= goods.stock ? 'disabled' : ''}}"
            bind:tap="onQuantityPlus">
            <global-icon name="添加" size="16" color="var(--global-text-color-secondary)"></global-icon>
          </view>
        </view>
        <text class="quantity-hint">库存{{goods.stock}}件</text>
      </view>
    </view>

    <!-- 商品详情描述 -->
    <view class="description-section">
      <view class="section-title">
        <global-icon name="文档" size="16" color="var(--shop-primary-color)"></global-icon>
        <text class="title-text">商品详情</text>
      </view>
      <view class="description-content">
        <text class="description-text">{{goods.description}}</text>
      </view>
    </view>

    <!-- 推荐商品 -->
    <view wx:if="{{recommendGoods.length > 0}}" class="recommend-section">
      <view class="section-title">
        <global-icon name="推荐" size="16" color="var(--shop-primary-color)"></global-icon>
        <text class="title-text">相关推荐</text>
      </view>
      <scroll-view class="recommend-list" scroll-x="true" show-scrollbar="{{false}}">
        <view class="recommend-item"
              wx:for="{{recommendGoods}}" 
              wx:key="id"
              bind:tap="onRecommendTap"
              data-id="{{item.id}}">
          <shop-product-card 
            product="{{item}}"
            mode="grid"
            size="small"
            bind:productTap="onRecommendTap"
            bind:cartTap="onRecommendAddCart">
          </shop-product-card>
        </view>
      </scroll-view>
    </view>

    <!-- 底部安全区域 -->
    <view class="bottom-safe-area"></view>
  </scroll-view>

  <!-- 底部操作栏 -->
  <view class="footer-actions">
    <view class="action-left">
      <!-- 客服按钮 -->
      <view class="service-button" bind:tap="onServiceTap">
        <global-icon name="消息" size="20" color="var(--global-text-color-secondary)"></global-icon>
        <text class="service-text">客服</text>
      </view>
      <!-- 购物车按钮 -->
      <view class="cart-button" bind:tap="onCartTap">
        <global-icon name="购物车" size="20" color="var(--global-text-color-secondary)"></global-icon>
        <text class="cart-text">购物车</text>
        <view wx:if="{{cartCount > 0}}" class="cart-badge">{{cartCount}}</view>
      </view>
    </view>

    <view class="action-right">
      <!-- 加入购物车 -->
      <view 
        class="action-btn cart-btn {{goods.stock <= 0 ? 'disabled' : ''}}"
        bind:tap="onAddToCart">
        <text class="btn-text">加入购物车</text>
      </view>
      <!-- 立即购买 -->
      <view 
        class="action-btn buy-btn {{goods.stock <= 0 ? 'disabled' : ''}}"
        bind:tap="onBuyNow">
        <text class="btn-text">{{goods.stock <= 0 ? '缺货' : '立即购买'}}</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <loading text="加载中..."></loading>
  </view>
</view>