// pages/shop/checkout.js - 结算页面 (企业级重构版)

/**
 * 结算页面控制器
 * 提供完整的订单确认和提交流程
 */

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 结算商品列表
    checkoutItems: [],
    
    // 收货地址相关
    selectedAddress: null,
    availableAddresses: [
      {
        id: 1,
        name: '张三',
        phone: '13800138000',
        province: '广东省',
        city: '深圳市',
        district: '南山区',
        detail: '科技园中区科苑路15号',
        fullAddress: '广东省深圳市南山区科技园中区科苑路15号',
        isDefault: true
      },
      {
        id: 2,
        name: '李四',
        phone: '13900139000',
        province: '北京市',
        city: '北京市',
        district: '朝阳区',
        detail: '建国门外大街1号',
        fullAddress: '北京市北京市朝阳区建国门外大街1号',
        isDefault: false
      }
    ],
    
    // 配送方式
    selectedDelivery: 'standard',
    deliveryOptions: [
      {
        type: 'standard',
        name: '标准配送',
        desc: '预计3-5天到达',
        fee: 0
      },
      {
        type: 'express',
        name: '加急配送',
        desc: '预计1-2天到达',
        fee: 15.00
      }
    ],
    
    // 优惠券相关
    selectedCoupon: null,
    availableCoupons: [
      {
        id: 1,
        name: '满100减20',
        amount: 20,
        minAmount: 100,
        validUntil: '2024-12-31',
        type: 'discount'
      },
      {
        id: 2,
        name: '新用户专享',
        amount: 50,
        minAmount: 200,
        validUntil: '2024-12-31',
        type: 'newuser'
      }
    ],
    
    // 订单备注
    orderNote: '',
    
    // 费用计算
    goodsTotal: 0,      // 商品总额
    deliveryFee: 0,     // 运费
    couponDiscount: 0,  // 优惠券折扣
    finalTotal: 0,      // 最终金额
    savings: 0,         // 节省金额
    
    // 页面状态
    loading: false,
    submitting: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadCheckoutData();
    this.initDefaultAddress();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 可能从地址选择页面返回，重新加载地址信息
    this.updateSelectedAddress();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '智慧养鹅商城 - 确认订单',
      path: '/pages/shop/shop',
      imageUrl: '/images/share/checkout-share.png'
    };
  },

  /**
   * 加载结算数据
   */
  loadCheckoutData() {
    this.setData({ loading: true });
    
    try {
      // 获取从购物车或商品详情页传递的数据
      const checkoutData = wx.getStorageSync('checkout_items');
      
      if (checkoutData && checkoutData.items) {
        this.setData({
          checkoutItems: checkoutData.items
        });
        
        this.calculateTotalAmount();
      } else {
        wx.showToast({
          title: '订单数据异常',
          icon: 'error'
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('加载结算数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 初始化默认地址
   */
  initDefaultAddress() {
    const defaultAddress = this.data.availableAddresses.find(addr => addr.isDefault);
    if (defaultAddress) {
      this.setData({ selectedAddress: defaultAddress });
    }
  },

  /**
   * 更新选中地址（从地址选择页面返回时调用）
   */
  updateSelectedAddress() {
    try {
      const selectedAddressData = wx.getStorageSync('selected_address');
      if (selectedAddressData) {
        this.setData({ selectedAddress: selectedAddressData });
        wx.removeStorageSync('selected_address');
      }
    } catch (error) {
      console.error('更新地址失败:', error);
    }
  },

  /**
   * 地址选择点击
   */
  onAddressSelect() {
    wx.vibrateShort({ type: 'light' });
    
    // 这里应该跳转到地址选择页面
    wx.showModal({
      title: '地址选择',
      content: '跳转到地址选择页面（演示模式）',
      showCancel: false,
      success: () => {
        // 模拟选择新地址
        if (!this.data.selectedAddress) {
          this.setData({ 
            selectedAddress: this.data.availableAddresses[0] 
          });
        }
      }
    });
  },

  /**
   * 配送方式选择
   */
  onDeliverySelect(e) {
    const deliveryType = e.currentTarget.dataset.type;
    
    wx.vibrateShort({ type: 'light' });
    
    this.setData({ selectedDelivery: deliveryType });
    this.calculateTotalAmount();
  },

  /**
   * 优惠券选择点击
   */
  onCouponSelect() {
    wx.vibrateShort({ type: 'light' });
    
    const { availableCoupons, goodsTotal } = this.data;
    
    // 筛选可用优惠券
    const usableCoupons = availableCoupons.filter(coupon => {
      return goodsTotal >= coupon.minAmount;
    });
    
    if (usableCoupons.length === 0) {
      wx.showToast({
        title: '暂无可用优惠券',
        icon: 'none'
      });
      return;
    }
    
    // 模拟优惠券选择
    const couponNames = usableCoupons.map(coupon => coupon.name);
    
    wx.showActionSheet({
      itemList: ['不使用优惠券', ...couponNames],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 不使用优惠券
          this.setData({ selectedCoupon: null });
        } else {
          // 选择优惠券
          const selectedCoupon = usableCoupons[res.tapIndex - 1];
          this.setData({ selectedCoupon: selectedCoupon });
        }
        this.calculateTotalAmount();
      }
    });
  },

  /**
   * 订单备注输入
   */
  onNoteInput(e) {
    this.setData({
      orderNote: e.detail.value
    });
  },

  /**
   * 计算总金额
   */
  calculateTotalAmount() {
    const { checkoutItems, selectedDelivery, selectedCoupon, deliveryOptions } = this.data;
    
    // 计算商品总额
    let goodsTotal = 0;
    let originalTotal = 0;
    
    checkoutItems.forEach(item => {
      const itemTotal = item.product.price * item.quantity;
      goodsTotal += itemTotal;
      
      // 计算原价总额（用于显示节省金额）
      const originalPrice = item.product.originalPrice || item.product.price;
      originalTotal += originalPrice * item.quantity;
    });
    
    // 计算运费
    const deliveryOption = deliveryOptions.find(option => option.type === selectedDelivery);
    const deliveryFee = deliveryOption ? deliveryOption.fee : 0;
    
    // 计算优惠券折扣
    let couponDiscount = 0;
    if (selectedCoupon && goodsTotal >= selectedCoupon.minAmount) {
      couponDiscount = selectedCoupon.amount;
    }
    
    // 计算最终金额
    const finalTotal = Math.max(0, goodsTotal + deliveryFee - couponDiscount);
    
    // 计算节省金额
    const savings = originalTotal - finalTotal;
    
    this.setData({
      goodsTotal: goodsTotal.toFixed(2),
      deliveryFee: deliveryFee,
      couponDiscount: couponDiscount,
      finalTotal: finalTotal.toFixed(2),
      savings: Math.max(0, savings)
    });
  },

  /**
   * 获取规格文本（用于WXML中的辅助函数）
   */
  getSpecsText(specs) {
    if (!specs || specs.length === 0) return '';
    return specs.map(spec => `${spec.name}:${spec.value}`).join(' ');
  },

  /**
   * 提交订单
   */
  async onSubmitOrder() {
    const { selectedAddress, checkoutItems, submitting } = this.data;
    
    // 防止重复提交
    if (submitting) return;
    
    // 验证必填信息
    if (!selectedAddress) {
      wx.showToast({
        title: '请选择收货地址',
        icon: 'none'
      });
      return;
    }
    
    if (!checkoutItems || checkoutItems.length === 0) {
      wx.showToast({
        title: '订单商品为空',
        icon: 'error'
      });
      return;
    }
    
    wx.vibrateShort({ type: 'heavy' });
    
    this.setData({ submitting: true });
    
    try {
      // 构建订单数据
      const orderData = {
        orderId: this.generateOrderId(),
        items: checkoutItems,
        address: selectedAddress,
        delivery: {
          type: this.data.selectedDelivery,
          fee: this.data.deliveryFee
        },
        coupon: this.data.selectedCoupon,
        note: this.data.orderNote,
        amount: {
          goodsTotal: this.data.goodsTotal,
          deliveryFee: this.data.deliveryFee,
          couponDiscount: this.data.couponDiscount,
          finalTotal: this.data.finalTotal
        },
        createTime: Date.now()
      };
      
      // 模拟API提交
      await this.submitOrderToServer(orderData);
      
      // 保存订单信息（用于订单成功页面显示）
      wx.setStorageSync('submitted_order', orderData);
      
      // 清除结算数据
      wx.removeStorageSync('checkout_items');
      
      // 更新购物车（移除已购买的商品）
      this.updateCartAfterOrder(checkoutItems);
      
      wx.showToast({
        title: '订单提交成功',
        icon: 'success'
      });
      
      // 跳转到订单成功页面
      setTimeout(() => {
        wx.redirectTo({
          url: `/pages/shop/order-success?orderId=${orderData.orderId}`,
          fail: () => {
            wx.showToast({
              title: '页面跳转失败',
              icon: 'error'
            });
          }
        });
      }, 1000);
      
    } catch (error) {
      console.error('订单提交失败:', error);
      wx.showModal({
        title: '提交失败',
        content: error.message || '网络异常，请重试',
        showCancel: false
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  /**
   * 生成订单ID
   */
  generateOrderId() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `SH${timestamp}${random}`;
  },

  /**
   * 提交订单到服务器（模拟）
   */
  async submitOrderToServer(orderData) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟提交成功/失败
        const success = Math.random() > 0.1; // 90%成功率
        
        if (success) {
          resolve({
            success: true,
            orderId: orderData.orderId,
            message: '订单提交成功'
          });
        } else {
          reject(new Error('服务器异常，请稍后重试'));
        }
      }, 1500);
    });
  },

  /**
   * 订单提交后更新购物车
   */
  updateCartAfterOrder(checkoutItems) {
    try {
      const cartData = wx.getStorageSync('shop_cart') || { items: [], count: 0 };
      let cartItems = cartData.items || [];
      
      // 移除已购买的商品
      checkoutItems.forEach(checkoutItem => {
        const index = cartItems.findIndex(cartItem => 
          cartItem.product.id === checkoutItem.product.id
        );
        
        if (index > -1) {
          if (cartItems[index].quantity > checkoutItem.quantity) {
            // 部分购买，减少数量
            cartItems[index].quantity -= checkoutItem.quantity;
          } else {
            // 全部购买，移除商品
            cartItems.splice(index, 1);
          }
        }
      });
      
      // 重新计算数量
      const totalCount = cartItems.reduce((sum, item) => sum + item.quantity, 0);
      
      // 保存更新后的购物车
      wx.setStorageSync('shop_cart', {
        items: cartItems,
        count: totalCount,
        updateTime: Date.now()
      });
      
    } catch (error) {
      console.error('更新购物车失败:', error);
    }
  },

  /**
   * 页面卸载
   */
  onUnload() {
    // 如果订单未提交成功，保留结算数据
    if (!this.data.submitting) {
    }
  }
});