// pages/shop/shop.js - 商城主页 (企业级重构版)

/**
 * 商城主页控制器
 * 基于全局设计系统重构，提供现代化的购物体验
 */

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 商品列表数据
    goods: [
      {
        id: 1,
        name: '优质鹅饲料',
        price: 99.99,
        originalPrice: 129.99,
        image: '/images/icons/goods1.png',
        category: 1,
        description: '专为鹅类设计的高营养饲料，促进健康成长，提高产蛋率',
        stock: 150,
        rating: 4.8,
        reviewCount: 128,
        isNew: true,
        isHot: false,
        isRecommended: true,
        isFavorited: false
      },
      {
        id: 2,
        name: '疫苗套装',
        price: 199.99,
        originalPrice: null,
        image: '/images/icons/goods2.png',
        category: 2,
        description: '预防常见鹅类疾病的疫苗组合套装，全面保护鹅群健康',
        stock: 88,
        rating: 4.9,
        reviewCount: 95,
        isNew: false,
        isHot: true,
        isRecommended: true,
        isFavorited: true
      },
      {
        id: 3,
        name: '养殖设备',
        price: 299.99,
        originalPrice: 399.99,
        image: '/images/icons/goods3.png',
        category: 3,
        description: '现代化鹅类养殖设备，提高养殖效率，降低人工成本',
        stock: 45,
        rating: 4.6,
        reviewCount: 73,
        isNew: false,
        isHot: false,
        isRecommended: false,
        isFavorited: false
      },
      {
        id: 4,
        name: '营养补充剂',
        price: 159.99,
        originalPrice: 189.99,
        image: '/images/icons/goods4.png',
        category: 1,
        description: '维生素和矿物质补充剂，增强鹅的免疫力，促进生长发育',
        stock: 0,
        rating: 4.7,
        reviewCount: 156,
        isNew: false,
        isHot: false,
        isRecommended: true,
        isFavorited: false
      },
      {
        id: 5,
        name: '清洁消毒剂',
        price: 79.99,
        originalPrice: null,
        image: '/images/icons/goods5.png',
        category: 2,
        description: '高效消毒剂，保持鹅舍环境清洁卫生，预防疾病传播',
        stock: 200,
        rating: 4.5,
        reviewCount: 89,
        isNew: true,
        isHot: false,
        isRecommended: false,
        isFavorited: false
      },
      {
        id: 6,
        name: '智能喂食器',
        price: 499.99,
        originalPrice: 699.99,
        image: '/images/icons/goods6.png',
        category: 3,
        description: '定时定量自动喂食器，智能控制，节省人工，提高效率',
        stock: 25,
        rating: 4.9,
        reviewCount: 42,
        isNew: true,
        isHot: true,
        isRecommended: true,
        isFavorited: true
      }
    ],
    
    // 分类数据
    categories: [
      { id: 1, name: '饲料营养', count: 12 },
      { id: 2, name: '医疗防疫', count: 8 },
      { id: 3, name: '设备工具', count: 15 },
      { id: 4, name: '环境控制', count: 6 }
    ],
    
    // 筛选和视图状态
    selectedCategory: 'all',
    selectedPriceRange: 'all',
    selectedSort: 'default',
    viewMode: 'grid',
    
    // 搜索状态
    showSearch: false,
    searchKeyword: '',
    searchFocus: false,
    
    // 购物车状态
    cartCount: 0,
    cartItems: [],
    
    // 页面状态
    loading: false,
    refreshing: false,
    
    // 分页数据
    currentPage: 1,
    pageSize: 10,
    hasMore: true
  },

  /**
   * 计算属性 - 筛选后的商品列表
   */
  getFilteredGoods() {
    try {
      // 确保goods是数组
      const goods = Array.isArray(this.data.goods) ? this.data.goods : [];
      let result = [...goods];
      
      // 分类筛选
      if (this.data.selectedCategory !== 'all') {
        result = result.filter(item => item.category == this.data.selectedCategory);
      }
      
      // 价格筛选
      if (this.data.selectedPriceRange !== 'all') {
        const priceRange = this.getPriceRangeById(this.data.selectedPriceRange);
        if (priceRange) {
          result = result.filter(item => 
            item.price >= priceRange.min && item.price <= priceRange.max
          );
        }
      }
      
      // 搜索筛选
      if (this.data.searchKeyword) {
        const keyword = this.data.searchKeyword.toLowerCase();
        result = result.filter(item =>
          item.name.toLowerCase().includes(keyword) ||
          item.description.toLowerCase().includes(keyword)
        );
      }
      
      // 排序
      result = this.sortGoods(result, this.data.selectedSort);
      
      return result;
    } catch (error) {
      console.error('筛选商品时出错:', error);
      return [];
    }
  },

  /**
   * 计算属性 - 筛选后商品数量
   */
  getFilteredGoodsCount() {
    try {
      const filteredGoods = this.getFilteredGoods();
      return Array.isArray(filteredGoods) ? filteredGoods.length : 0;
    } catch (error) {
      console.error('获取筛选商品数量时出错:', error);
      return 0;
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initPageData();
    this.loadCartData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.refreshCartCount();
    this.updateFilteredData();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.loadMoreData();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '智慧养鹅商城 - 优质养殖用品',
      path: '/pages/shop/shop',
      imageUrl: '/images/share/shop-share.png'
    };
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    this.setData({
      filteredGoods: this.getFilteredGoods(),
      filteredGoodsCount: this.getFilteredGoodsCount()
    });
  },

  /**
   * 更新筛选数据
   */
  updateFilteredData() {
    this.setData({
      filteredGoods: this.getFilteredGoods(),
      filteredGoodsCount: this.getFilteredGoodsCount()
    });
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({ refreshing: true });
    
    try {
      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 重新加载数据
      this.updateFilteredData();
      
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('刷新失败:', error);
      wx.showToast({
        title: '刷新失败',
        icon: 'error'
      });
    } finally {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 加载更多数据
   */
  async loadMoreData() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }
    
    this.setData({ loading: true });
    
    try {
      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // 这里可以加载更多商品数据
      
    } catch (error) {
      console.error('加载更多失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 搜索按钮点击
   */
  onSearchTap() {
    wx.vibrateShort({ type: 'light' });
    
    this.setData({
      showSearch: !this.data.showSearch,
      searchFocus: !this.data.showSearch
    });
  },

  /**
   * 购物车按钮点击
   */
  onCartTap() {
    wx.vibrateShort({ type: 'light' });
    
    wx.navigateTo({
      url: '/pages/shop/cart',
      fail: () => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({ searchKeyword: keyword });
    
    // 防抖搜索
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
    
    this.searchTimer = setTimeout(() => {
      this.updateFilteredData();
    }, 300);
  },

  /**
   * 搜索确认
   */
  onSearchConfirm(e) {
    const keyword = e.detail.value;
    this.setData({ searchKeyword: keyword });
    this.updateFilteredData();
    
    // 记录搜索历史
    this.saveSearchHistory(keyword);
  },

  /**
   * 清除搜索
   */
  onClearSearch() {
    wx.vibrateShort({ type: 'light' });
    
    this.setData({
      searchKeyword: '',
      searchFocus: false
    });
    this.updateFilteredData();
  },

  /**
   * 分类变更
   */
  onCategoryChange(e) {
    const { categoryId } = e.detail;
    
    wx.vibrateShort({ type: 'light' });
    
    this.setData({ selectedCategory: categoryId });
    this.updateFilteredData();
  },

  /**
   * 筛选应用
   */
  onFilterApply(e) {
    const { filters } = e.detail;
    
    wx.vibrateShort({ type: 'medium' });
    
    this.setData({
      selectedCategory: filters.category,
      selectedPriceRange: filters.priceRange,
      selectedSort: filters.sort
    });
    this.updateFilteredData();
  },

  /**
   * 清除筛选
   */
  onFilterClear() {
    wx.vibrateShort({ type: 'light' });
    
    this.setData({
      selectedCategory: 'all',
      selectedPriceRange: 'all',
      selectedSort: 'default',
      searchKeyword: ''
    });
    this.updateFilteredData();
  },

  /**
   * 视图模式切换
   */
  onViewModeChange(e) {
    const mode = e.currentTarget.dataset.mode;
    
    wx.vibrateShort({ type: 'light' });
    
    this.setData({ viewMode: mode });
  },

  /**
   * 商品点击
   */
  onProductTap(e) {
    const { product } = e.detail;
    
    wx.vibrateShort({ type: 'light' });
    
    wx.navigateTo({
      url: `/pages/shop/goods-detail?id=${product.id}`,
      fail: () => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 收藏点击
   */
  onFavoriteTap(e) {
    const { product, isFavorited } = e.detail;
    
    wx.vibrateShort({ type: 'light' });
    
    // 更新商品收藏状态
    const goods = this.data.goods.map(item => {
      if (item.id === product.id) {
        return { ...item, isFavorited: isFavorited };
      }
      return item;
    });
    
    this.setData({ goods });
    this.updateFilteredData();
    
    wx.showToast({
      title: isFavorited ? '已收藏' : '取消收藏',
      icon: 'success'
    });
  },

  /**
   * 加入购物车
   */
  onAddToCart(e) {
    const { product } = e.detail;
    
    wx.vibrateShort({ type: 'medium' });
    
    // 检查库存
    if (product.stock <= 0) {
      wx.showToast({
        title: '商品缺货',
        icon: 'error'
      });
      return;
    }
    
    // 添加到购物车
    this.addToCart(product);
    
    wx.showToast({
      title: '已加入购物车',
      icon: 'success'
    });
  },

  /**
   * 添加到购物车逻辑
   */
  addToCart(product) {
    let cartItems = [...this.data.cartItems];
    const existingItem = cartItems.find(item => item.productId === product.id);
    
    if (existingItem) {
      // 增加数量
      existingItem.quantity += 1;
    } else {
      // 新增商品
      cartItems.push({
        id: Date.now(),
        productId: product.id,
        product: product,
        quantity: 1,
        selected: true,
        specs: []
      });
    }
    
    this.setData({ 
      cartItems,
      cartCount: cartItems.reduce((sum, item) => sum + item.quantity, 0)
    });
    
    // 保存到本地存储
    this.saveCartData();
  },

  /**
   * 加载购物车数据
   */
  loadCartData() {
    try {
      const cartData = wx.getStorageSync('shop_cart');
      if (cartData) {
        this.setData({
          cartItems: cartData.items || [],
          cartCount: cartData.count || 0
        });
      }
    } catch (error) {
      console.error('加载购物车数据失败:', error);
    }
  },

  /**
   * 保存购物车数据
   */
  saveCartData() {
    try {
      wx.setStorageSync('shop_cart', {
        items: this.data.cartItems,
        count: this.data.cartCount,
        updateTime: Date.now()
      });
    } catch (error) {
      console.error('保存购物车数据失败:', error);
    }
  },

  /**
   * 刷新购物车数量
   */
  refreshCartCount() {
    const count = this.data.cartItems.reduce((sum, item) => sum + item.quantity, 0);
    this.setData({ cartCount: count });
  },

  /**
   * 保存搜索历史
   */
  saveSearchHistory(keyword) {
    if (!keyword.trim()) return;
    
    try {
      let history = wx.getStorageSync('shop_search_history') || [];
      
      // 去重并添加到开头
      history = history.filter(item => item !== keyword);
      history.unshift(keyword);
      
      // 限制历史记录数量
      if (history.length > 10) {
        history = history.slice(0, 10);
      }
      
      wx.setStorageSync('shop_search_history', history);
    } catch (error) {
      console.error('保存搜索历史失败:', error);
    }
  },

  /**
   * 获取价格区间配置
   */
  getPriceRangeById(rangeId) {
    const ranges = [
      { id: 'all', label: '全部价格', min: 0, max: 999999 },
      { id: 'low', label: '0-100元', min: 0, max: 100 },
      { id: 'medium', label: '100-500元', min: 100, max: 500 },
      { id: 'high', label: '500元以上', min: 500, max: 999999 }
    ];
    
    return ranges.find(range => range.id === rangeId);
  },

  /**
   * 商品排序
   */
  sortGoods(goods, sortType) {
    const sortedGoods = [...goods];
    
    switch (sortType) {
      case 'price_asc':
        return sortedGoods.sort((a, b) => a.price - b.price);
      case 'price_desc':
        return sortedGoods.sort((a, b) => b.price - a.price);
      case 'sales':
        return sortedGoods.sort((a, b) => (b.reviewCount || 0) - (a.reviewCount || 0));
      case 'rating':
        return sortedGoods.sort((a, b) => (b.rating || 0) - (a.rating || 0));
      case 'newest':
        return sortedGoods.sort((a, b) => {
          if (a.isNew && !b.isNew) return -1;
          if (!a.isNew && b.isNew) return 1;
          return b.id - a.id;
        });
      default:
        return sortedGoods;
    }
  },

  /**
   * 页面卸载
   */
  onUnload() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  }
});