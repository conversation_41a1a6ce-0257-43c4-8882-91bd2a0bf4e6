// pages/shop/cart.js - 购物车页面 (企业级重构版)

/**
 * 购物车页面控制器
 * 基于shop-cart-item组件重构，提供完整的购物车管理体验
 */

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 购物车商品列表
    cartItems: [],
    
    // 推荐商品列表
    recommendGoods: [
      {
        id: 101,
        name: '智能喂水器',
        price: 189.99,
        image: '/images/icons/goods7.png',
        description: '自动补水，智能控制'
      },
      {
        id: 102,
        name: '鹅舍保温灯',
        price: 129.99,
        image: '/images/icons/goods8.png',
        description: '节能环保，温度可调'
      },
      {
        id: 103,
        name: '养殖围栏',
        price: 259.99,
        image: '/images/icons/goods9.png',
        description: '结实耐用，安装简便'
      }
    ],
    
    // 选择状态
    isAllSelected: false,
    selectedCount: 0,
    
    // 价格计算
    totalPrice: '0.00',
    originalPrice: 0,
    
    // 编辑模式
    editMode: false,
    
    // 页面状态
    loading: false,
    refreshing: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadCartData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadCartData();
    this.calculateTotals();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshCartData();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '我的购物车 - 智慧养鹅商城',
      path: '/pages/shop/cart',
      imageUrl: '/images/share/cart-share.png'
    };
  },

  /**
   * 加载购物车数据
   */
  loadCartData() {
    try {
      const cartData = wx.getStorageSync('shop_cart');
      if (cartData && cartData.items) {
        // 确保每个商品都有选中状态
        const cartItems = cartData.items.map(item => ({
          ...item,
          selected: item.selected !== false // 默认选中
        }));
        
        this.setData({ cartItems });
        this.calculateTotals();
      } else {
        this.setData({ cartItems: [] });
      }
    } catch (error) {
      console.error('加载购物车数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  },

  /**
   * 刷新购物车数据
   */
  async refreshCartData() {
    this.setData({ refreshing: true });
    
    try {
      // 模拟网络请求
      await new Promise(resolve => setTimeout(resolve, 800));
      
      this.loadCartData();
      
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('刷新失败:', error);
      wx.showToast({
        title: '刷新失败',
        icon: 'error'
      });
    } finally {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 编辑模式切换
   */
  onEditModeTap() {
    wx.vibrateShort({ type: 'light' });
    
    this.setData({
      editMode: !this.data.editMode
    });
  },

  /**
   * 全选/取消全选
   */
  onSelectAll() {
    wx.vibrateShort({ type: 'light' });
    
    const newSelectState = !this.data.isAllSelected;
    const cartItems = this.data.cartItems.map(item => ({
      ...item,
      selected: newSelectState
    }));
    
    this.setData({ cartItems });
    this.calculateTotals();
    this.saveCartData();
  },

  /**
   * 商品项选择
   */
  onItemSelect(e) {
    const { cartItem, selected } = e.detail;
    
    wx.vibrateShort({ type: 'light' });
    
    const cartItems = this.data.cartItems.map(item => {
      if (item.id === cartItem.id) {
        return { ...item, selected: selected };
      }
      return item;
    });
    
    this.setData({ cartItems });
    this.calculateTotals();
    this.saveCartData();
  },

  /**
   * 商品数量变更
   */
  onQuantityChange(e) {
    const { cartItem, newQuantity } = e.detail;
    
    wx.vibrateShort({ type: 'light' });
    
    const cartItems = this.data.cartItems.map(item => {
      if (item.id === cartItem.id) {
        return { ...item, quantity: newQuantity };
      }
      return item;
    });
    
    this.setData({ cartItems });
    this.calculateTotals();
    this.saveCartData();
  },

  /**
   * 删除商品项
   */
  onItemDelete(e) {
    const { cartItem } = e.detail;
    
    wx.vibrateShort({ type: 'medium' });
    
    const cartItems = this.data.cartItems.filter(item => item.id !== cartItem.id);
    
    this.setData({ cartItems });
    this.calculateTotals();
    this.saveCartData();
    
    wx.showToast({
      title: '已删除',
      icon: 'success'
    });
  },

  /**
   * 商品项点击
   */
  onItemTap(e) {
    const { cartItem } = e.detail;
    
    wx.vibrateShort({ type: 'light' });
    
    wx.navigateTo({
      url: `/pages/shop/goods-detail?id=${cartItem.product.id}`,
      fail: () => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 批量删除
   */
  onBatchDelete() {
    const selectedItems = this.data.cartItems.filter(item => item.selected);
    
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要删除的商品',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的${selectedItems.length}件商品吗？`,
      confirmText: '删除',
      confirmColor: '#FF3B30',
      success: (res) => {
        if (res.confirm) {
          wx.vibrateShort({ type: 'heavy' });
          
          const cartItems = this.data.cartItems.filter(item => !item.selected);
          
          this.setData({ 
            cartItems,
            editMode: false 
          });
          this.calculateTotals();
          this.saveCartData();
          
          wx.showToast({
            title: `已删除${selectedItems.length}件商品`,
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 推荐商品点击
   */
  onRecommendTap(e) {
    const goodsId = e.currentTarget.dataset.id;
    
    wx.vibrateShort({ type: 'light' });
    
    wx.navigateTo({
      url: `/pages/shop/goods-detail?id=${goodsId}`,
      fail: () => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 去购物
   */
  onGoShopping() {
    wx.vibrateShort({ type: 'light' });
    
    wx.switchTab({
      url: '/pages/shop/shop',
      fail: () => {
        wx.navigateTo({
          url: '/pages/shop/shop',
          fail: () => {
            wx.showToast({
              title: '页面跳转失败',
              icon: 'error'
            });
          }
        });
      }
    });
  },

  /**
   * 结算
   */
  onCheckout() {
    if (this.data.editMode) {
      // 编辑模式下执行删除
      this.onBatchDelete();
      return;
    }
    
    const selectedItems = this.data.cartItems.filter(item => item.selected);
    
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要结算的商品',
        icon: 'none'
      });
      return;
    }
    
    // 检查库存
    const outOfStockItems = selectedItems.filter(item => {
      return !item.product.stock || item.product.stock < item.quantity;
    });
    
    if (outOfStockItems.length > 0) {
      wx.showModal({
        title: '库存不足',
        content: `${outOfStockItems[0].product.name}等商品库存不足，请调整数量后再结算`,
        showCancel: false
      });
      return;
    }
    
    wx.vibrateShort({ type: 'medium' });
    
    // 保存结算商品到全局数据
    try {
      wx.setStorageSync('checkout_items', {
        items: selectedItems,
        totalPrice: this.data.totalPrice,
        originalPrice: this.data.originalPrice,
        createTime: Date.now()
      });
      
      wx.navigateTo({
        url: '/pages/shop/checkout',
        fail: () => {
          wx.showToast({
            title: '页面跳转失败',
            icon: 'error'
          });
        }
      });
    } catch (error) {
      console.error('保存结算数据失败:', error);
      wx.showToast({
        title: '结算失败',
        icon: 'error'
      });
    }
  },

  /**
   * 计算总价和统计
   */
  calculateTotals() {
    let totalPrice = 0;
    let originalPrice = 0;
    let selectedCount = 0;
    
    this.data.cartItems.forEach(item => {
      if (item.selected) {
        const itemPrice = item.product.price * item.quantity;
        const itemOriginalPrice = (item.product.originalPrice || item.product.price) * item.quantity;
        
        totalPrice += itemPrice;
        originalPrice += itemOriginalPrice;
        selectedCount++;
      }
    });
    
    const isAllSelected = selectedCount > 0 && selectedCount === this.data.cartItems.length;
    
    this.setData({
      totalPrice: totalPrice.toFixed(2),
      originalPrice: originalPrice,
      selectedCount: selectedCount,
      isAllSelected: isAllSelected
    });
  },

  /**
   * 保存购物车数据
   */
  saveCartData() {
    try {
      wx.setStorageSync('shop_cart', {
        items: this.data.cartItems,
        count: this.data.cartItems.reduce((sum, item) => sum + item.quantity, 0),
        updateTime: Date.now()
      });
    } catch (error) {
      console.error('保存购物车数据失败:', error);
    }
  },

  /**
   * 检查商品是否在购物车中
   */
  isProductInCart(productId) {
    return this.data.cartItems.some(item => item.product.id === productId);
  },

  /**
   * 获取购物车商品数量
   */
  getCartItemCount() {
    return this.data.cartItems.reduce((sum, item) => sum + item.quantity, 0);
  },

  /**
   * 清空购物车
   */
  clearCart() {
    wx.showModal({
      title: '清空购物车',
      content: '确定要清空购物车吗？此操作不可撤销',
      confirmText: '清空',
      confirmColor: '#FF3B30',
      success: (res) => {
        if (res.confirm) {
          wx.vibrateShort({ type: 'heavy' });
          
          this.setData({ 
            cartItems: [],
            editMode: false 
          });
          this.calculateTotals();
          this.saveCartData();
          
          wx.showToast({
            title: '购物车已清空',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 页面卸载
   */
  onUnload() {
    // 保存最新的购物车状态
    this.saveCartData();
  }
});