// pages/home/<USER>
const { IMAGES, UI, BUSINESS, API } = require('../../constants/index.js');
const request = require('../../utils/request.js');
const { home, auth } = require('../../utils/api.js');
const { performanceMixin, dataCacheManager, debounce } = require('../../utils/performance.js');

/**
 * 主页页面 - 智慧养鹅小程序核心页面
 * 功能：展示用户信息、健康数据、任务管理、公告和知识库
 * 遵循微信小程序最佳实践和性能优化规范
 * 性能优化：数据缓存、防抖机制、批量更新、内存管理
 */

Page(Object.assign({}, performanceMixin, {
  data: {
    userInfo: {
      inventoryCount: 0,
      healthRate: '0%',
      environmentStatus: '优',
      avatar: IMAGES.DEFAULTS.AVATAR,
      name: '养殖户',
      farmName: '未设置养殖场'
    },
    healthData: {
      healthyCount: 1120,
      sickCount: 65,
      deathCount: 15,
      statusText: '整体健康',
      statusDesc: '鹅群状态良好，无异常'
    },
    tasks: [],
    announcements: [],
    knowledgeList: [],
    loading: true,
    unreadCount: 0, // 未读通知数量
    // 性能优化相关
    refreshing: false,
    lastRefreshTime: 0
  },

  /**
   * 页面加载时执行
   * 初始化页面数据和配置
   */
  onLoad: function () {
    this.initPage();
  },

  /**
   * 页面显示时执行
   * 刷新页面数据，支持防抖优化
   */
  onShow: function () {
    this.refreshPageData();
  },

  /**
   * 初始化页面
   */
  initPage: function() {
    this.setData({
      loading: true
    });
    // 加载通知数量
    this.loadNotificationCount();
  },

  /**
   * 刷新页面数据 - 支持防抖
   * 优化：防抖机制、批量更新、错误处理
   */
  refreshPageData: debounce(function() {
    const now = Date.now();
    const { refreshing } = this.data;
    
    if (refreshing) {
      return;
    }

    this.safeSetData({
      refreshing: true,
      lastRefreshTime: now
    });

    // 并行获取数据，提高性能
    Promise.all([
      this.getUserInfo(),
      this.getAnnouncements(),
      this.getTasks(),
      this.getKnowledgeList()
    ])
    .then(() => {
    })
    .catch(error => {
      console.error('[Home] 数据刷新失败:', error);
      wx.showToast({
        title: '刷新失败',
        icon: 'error'
      });
    })
    .finally(() => {
      this.safeSetData({
        refreshing: false,
        loading: false
      });
    });
  }, 1000),

  /**
   * 获取用户信息 - 返回Promise支持并行执行
   * 优化：数据缓存、安全setData
   */
  getUserInfo: function() {
    return new Promise((resolve) => {
      try {
        // 检查缓存
        const cachedUserInfo = dataCacheManager.get('userInfo');
        if (cachedUserInfo) {
          this.safeSetData({ userInfo: cachedUserInfo });
          resolve();
          return;
        }

        // 设置默认数据
        const defaultUserInfo = {
          inventoryCount: 1250,
          healthRate: '95%',
          environmentStatus: '优',
          avatar: IMAGES.DEFAULTS.AVATAR,
          name: '张养殖户',
          farmName: '绿野生态养殖场'
        };

        // 先设置默认数据，避免页面空白
        this.safeSetData({
          userInfo: defaultUserInfo
        });

        // 尝试获取真实数据
        auth.getUserInfo()
          .then(res => {
            if (res && res.success && res.data) {
              const userInfo = {
                inventoryCount: res.data.inventoryCount || 1250,
                healthRate: (res.data.healthRate || 95) + '%',
                environmentStatus: res.data.environmentStatus || '优',
                avatar: res.data.avatar || IMAGES.DEFAULTS.AVATAR,
                name: res.data.name || '张养殖户',
                farmName: res.data.farmName || '绿野生态养殖场'
              };
              
              // 缓存用户信息
              dataCacheManager.set('userInfo', userInfo, 10 * 60 * 1000); // 10分钟缓存
              
              this.safeSetData({ userInfo });
            }
            resolve();
          })
          .catch(err => {
            console.warn('[Home] 获取用户信息失败，使用默认数据:', err);
            resolve(); // 即使失败也resolve，避免阻塞其他操作
          });
      } catch (error) {
        console.error('[Home] getUserInfo方法执行失败:', error);
        resolve();
      }
    });
  },

  /**
   * 获取公告信息 - 返回Promise支持并行执行
   */
  getAnnouncements: function() {
    return new Promise((resolve) => {
      try {
        // 设置默认公告数据
        const defaultAnnouncements = [
          {
            id: 1,
            title: '冬季养鹅注意事项及防疫措施',
            time: '12-15',
            isImportant: true
          },
          {
            id: 2,
            title: '新品种鹅苗到货通知',
            time: '12-14',
            isImportant: false
          },
          {
            id: 3,
            title: '养殖技术培训通知',
            time: '12-13',
            isImportant: false
          },
          {
            id: 4,
            title: '年终总结会议安排',
            time: '12-12',
            isImportant: true
          },
          {
            id: 5,
            title: '设备维护保养提醒',
            time: '12-11',
            isImportant: false
          }
        ];

        this.setData({
          announcements: defaultAnnouncements
        });

        // 尝试获取真实数据
        home.getAnnouncements({ page: 1, limit: 5 })
          .then(res => {
            if (res && res.success && res.data && Array.isArray(res.data)) {
              this.setData({
                announcements: res.data.map(item => ({
                  id: item.id,
                  title: item.title || '未知公告',
                  time: this.formatDate(item.createdAt) || '未知时间',
                  isImportant: Boolean(item.isImportant)
                }))
              });
            }
            resolve();
          })
          .catch(err => {
            console.warn('[Home] 获取公告失败，使用默认数据:', err);
            resolve();
          });
      } catch (error) {
        console.error('[Home] getAnnouncements方法执行失败:', error);
        resolve();
      }
    });
  },

  /**
   * 格式化日期 - 增强容错性
   */
  formatDate: function(dateString) {
    try {
      if (!dateString) return '未知时间';
      
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        console.warn('[Home] 无效的日期格式:', dateString);
        return '未知时间';
      }
      
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
    } catch (error) {
      console.error('[Home] 日期格式化失败:', error);
      return '未知时间';
    }
  },

  /**
   * 获取待办任务 - 返回Promise支持并行执行
   */
  getTasks: function() {
    return new Promise((resolve) => {
      try {
        // 设置默认待办任务数据
        const defaultTasks = [
          {
            id: 1,
            title: '健康检查提醒',
            description: '今日需要对A区鹅群进行健康检查',
            time: '今天',
            type: 'health',
            completed: false
          },
          {
            id: 2,
            title: '疫苗接种',
            description: 'B区鹅群疫苗接种到期提醒',
            time: '明天',
            type: 'health',
            completed: false
          },
          {
            id: 3,
            title: '饲料补充',
            description: 'C区饲料库存不足，需要及时补充',
            time: '今天',
            type: 'production',
            completed: false
          }
        ];

        this.setData({
          tasks: defaultTasks
        });

        resolve();
      } catch (error) {
        console.error('[Home] getTasks方法执行失败:', error);
        resolve();
      }
    });
  },

  /**
   * 获取知识库数据 - 返回Promise支持并行执行
   */
  getKnowledgeList: function() {
    return new Promise((resolve) => {
      try {
        const defaultKnowledge = [
          {
            id: 1,
            title: '鹅苗孵化技术要点',
            summary: '详细介绍鹅苗孵化过程中的温度控制、湿度管理和通风要求，确保孵化成功率...',
            publishTime: '2024-01-15'
          },
          {
            id: 2,
            title: '春季鹅群疾病预防指南',
            summary: '春季是鹅群疾病高发期，本文介绍常见疾病的预防措施和早期识别方法...',
            publishTime: '2024-01-12'
          }
        ];

        this.setData({
          knowledgeList: defaultKnowledge
        });

        resolve();
      } catch (error) {
        console.error('[Home] getKnowledgeList方法执行失败:', error);
        resolve();
      }
    });
  },



  /**
   * 任务完成处理 - 优化用户体验
   */
  onTaskComplete: function(e) {
    try {
      const taskId = parseInt(e.currentTarget.dataset.id);

      // 数据验证
      if (!taskId || isNaN(taskId)) {
        console.error('[Home] 无效的任务ID:', taskId);
        wx.showToast({
          title: '任务ID无效',
          icon: 'error'
        });
        return;
      }

      const tasks = this.data.tasks;
      if (!Array.isArray(tasks) || tasks.length === 0) {
        console.warn('[Home] 任务列表为空');
        return;
      }

      // 找到对应的任务并标记为完成
      const updatedTasks = tasks.map(task => {
        if (task.id === taskId) {
          return { ...task, completed: true };
        }
        return task;
      });

      // 更新数据
      this.setData({
        tasks: updatedTasks
      });

      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });

      // 显示提示
      wx.showToast({
        title: '任务已完成',
        icon: 'success',
        duration: 1000
      });

      // 延迟1.5秒后移除已完成的任务
      setTimeout(() => {
        const filteredTasks = updatedTasks.filter(task => !task.completed);
        this.setData({
          tasks: filteredTasks
        });
      }, 1500);

    } catch (error) {
      console.error('[Home] 任务完成处理失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      });
    }
  },

  /**
   * 跳转到任务详情 - 增强错误处理
   */
  onTaskTap: function(e) {
    try {
      const taskId = e.currentTarget.dataset.id;
      
      if (!taskId) {
        console.warn('[Home] 任务ID为空');
        return;
      }
      
      wx.navigateTo({
        url: `/pages/task/detail/detail?id=${taskId}`,
        fail: (err) => {
          console.error('[Home] 跳转任务详情失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'error'
          });
        }
      });
    } catch (error) {
      console.error('[Home] onTaskTap执行失败:', error);
    }
  },

  /**
   * 跳转到公告详情 - 增强错误处理
   */
  onAnnouncementTap: function(e) {
    try {
      const announcementId = e.currentTarget.dataset.id;
      
      if (!announcementId) {
        console.warn('[Home] 公告ID为空');
        return;
      }
      
      wx.navigateTo({
        url: `/pages/announcement/announcement-detail/announcement-detail?id=${announcementId}`,
        fail: (err) => {
          console.error('[Home] 跳转公告详情失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'error'
          });
        }
      });
    } catch (error) {
      console.error('[Home] onAnnouncementTap执行失败:', error);
    }
  },

  // 点击存栏总数跳转到库存详情
  onInventoryTap: function() {
    wx.navigateTo({
      url: '/pages/inventory/inventory-detail/inventory-detail'
    });
  },

  // 点击健康率跳转到健康报告
  onHealthRateTap: function() {
    wx.navigateTo({
      url: '/pages/health/report/report'
    });
  },

  // 点击环境状态跳转到环境监控
  onEnvironmentTap: function() {
    wx.navigateTo({
      url: '/pages/production/environment/environment'
    });
  },

  // 健康检查
  onHealthCheckTap: function() {
    wx.navigateTo({
      url: '/pages/health/health?tab=0'
    });
  },

  // 疫苗接种
  onVaccineTap: function() {
    wx.navigateTo({
      url: '/pages/health/health?tab=1'
    });
  },

  // 疾病治疗
  onTreatmentTap: function() {
    wx.navigateTo({
      url: '/pages/health/health?tab=2'
    });
  },


  // 查看全部任务
  onViewAllTasks: function() {
    wx.navigateTo({
      url: '/pages/task/task'
    });
  },

  // 查看全部公告
  onViewAllAnnouncements: function() {
    wx.navigateTo({
      url: '/pages/announcement/announcement'
    });
  },





  // 查看更多公告
  onMoreAnnouncementsTap: function() {
    wx.navigateTo({
      url: '/pages/announcement/announcement-list/announcement-list'
    });
  },

  /**
   * 下拉刷新处理
   */
  onPullDownRefresh: function() {
    this.refreshPageData();
    
    // 刷新完成后停止下拉刷新动画
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面隐藏时清除定时器和重置状态
   */
  onHide: function() {
    if (this.data.announcementTimer) {
      clearInterval(this.data.announcementTimer);
      this.setData({
        announcementTimer: null
      });
    }
  },

  /**
   * 页面卸载时清理资源
   */
  onUnload: function() {
    if (this.data.announcementTimer) {
      clearInterval(this.data.announcementTimer);
    }
  },

  // 知识库相关方法
  // "查看全部"跳转到健康模块知识库
  onKnowledgeMoreTap: function() {
    wx.navigateTo({
      url: '/pages/health/knowledge/knowledge'
    });
  },

  // 点击文章跳转到独立的详情页面
  onKnowledgeItemTap: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/health/knowledge/detail/detail?id=${id}`
    });
  },

  // 通知按钮事件
  onNotificationTap: function() {
    wx.navigateTo({
      url: '/pages/announcement/announcement-list/announcement-list'
    });
    wx.vibrateShort();
  },

  // 设置按钮事件
  onSettingsTap: function() {
    wx.navigateTo({
      url: '/pages/profile/settings/settings'
    });
    wx.vibrateShort();
  },

  // 加载通知数量
  loadNotificationCount: function() {
    // 模拟加载未读通知数量
    setTimeout(() => {
      this.setData({
        unreadCount: Math.floor(Math.random() * 5) // 随机0-4个未读通知
      });
    }, 300);
  }
}));