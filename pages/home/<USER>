/* pages/home/<USER>/

/* 首页容器 */
.container {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  padding: var(--space-lg);
  padding-bottom: calc(var(--space-lg) + env(safe-area-inset-bottom));
}

/* 页面头部样式 */
.home-header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
}

.notification-button, .settings-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: var(--global-border-radius-full);
  position: relative;
  transition: var(--global-transition-all);
}

.notification-button:active, .settings-button:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.3);
}

.notification-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  min-width: 18px;
  height: 18px;
  background: var(--global-error-color);
  color: var(--global-text-color-inverse);
  font-size: var(--global-font-size-xs);
  font-weight: var(--global-font-weight-bold);
  border-radius: var(--global-border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  border: 2px solid var(--global-bg-color-primary);
}

/* 用户信息横幅 */
.user-info-banner {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: var(--radius-2xl);
  padding: var(--space-xl);
  color: var(--text-inverse);
  margin-bottom: var(--space-xl);
  box-shadow: var(--shadow-primary);
  position: relative;
  overflow: hidden;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-info-banner::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: var(--radius-full);
  transform: rotate(45deg);
}

.user-info-banner:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-lg);
}

.user-info-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

/* 用户区域布局 */
.user-section {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  flex-shrink: 0;
  border-radius: var(--radius-full);
  border: 4rpx solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  background: transparent;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
}

.avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  flex: 1;
  margin-left: var(--space-xl);
  min-width: 0;
}

.user-name-line {
  margin-bottom: var(--space-sm);
}

.username {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-inverse);
  line-height: var(--leading-tight);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: 0.5rpx;
}

.farm-name-line {
  margin-top: var(--space-xs);
}

.farm-name {
  font-size: var(--text-base);
  color: rgba(255, 255, 255, 0.85);
  line-height: var(--leading-normal);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

/* 天气信息区域 */
.weather-info {
  flex-shrink: 0;
  margin-left: var(--space-lg);
}



/* 数据概览 */
.overview {
  display: flex;
  background-color: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  margin-bottom: var(--space-xl);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
  gap: var(--space-lg);
}

.overview-item {
  flex: 1;
  text-align: center;
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
}

.overview-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--primary-subtle);
  border-radius: var(--radius-lg);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.overview-item:active::after {
  opacity: 1;
}

.overview-item:active {
  transform: scale(0.98);
}

.overview-item .value {
  position: relative;
  z-index: 1;
  display: block;
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--primary);
  margin-bottom: var(--space-sm);
  line-height: var(--leading-none);
}

.overview-item .label {
  position: relative;
  z-index: 1;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: 500;
}



/* 区块系统 */
.section {
  background-color: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  margin-bottom: var(--space-xl);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
  transition: var(--transition-all);
}

.section:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-lg);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2xl);
  padding-bottom: var(--space-lg);
  border-bottom: 2rpx solid var(--bg-secondary);
}

.section-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.section-title::before {
  content: '';
  width: 4rpx;
  height: 24rpx;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: var(--radius-full);
}

.more {
  font-size: var(--text-sm);
  color: var(--primary);
  font-weight: var(--font-medium);
  padding: var(--space-sm) var(--space-md);
  background-color: var(--primary-subtle);
  border-radius: var(--radius-full);
  transition: var(--transition-all);
}

.more:active {
  background-color: var(--primary-light);
  color: var(--text-inverse);
  transform: scale(0.95);
}



/* 知识库模块 */
.knowledge-list {
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.knowledge-item {
  padding: var(--space-xl);
  border-radius: var(--radius-lg);
  background-color: var(--bg-secondary);
  border: 1rpx solid var(--border-light);
  transition: var(--transition-all);
  position: relative;
  overflow: hidden;
}

.knowledge-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: linear-gradient(180deg, var(--primary) 0%, var(--secondary) 100%);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.knowledge-item:active {
  transform: translateX(8rpx);
  background-color: var(--bg-primary);
  box-shadow: var(--shadow-md);
}

.knowledge-item:active::before {
  transform: scaleY(1);
}

.knowledge-content {
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

.knowledge-title {
  font-size: var(--text-lg);
  color: var(--text-primary);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-sm);
  line-height: var(--leading-snug);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.knowledge-summary {
  font-size: var(--text-base);
  color: var(--text-secondary);
  margin-bottom: var(--space-sm);
  line-height: var(--leading-relaxed);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.knowledge-time {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  font-weight: var(--font-medium);
  background-color: var(--bg-primary);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  align-self: flex-start;
}

/* 公告组件 */
.announcement-section {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  border-radius: var(--radius-2xl);
  margin-bottom: var(--space-xl);
  padding: var(--space-2xl);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
  position: relative;
  overflow: hidden;
}

.announcement-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, var(--accent) 0%, var(--accent-light) 100%);
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.announcement-header .section-title {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.title-icon {
  width: 28rpx;
  height: 28rpx;
  padding: var(--space-xs);
  background-color: var(--accent-subtle);
  border-radius: var(--radius-md);
}

.more-btn {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-sm) var(--space-md);
  background-color: var(--primary-subtle);
  border-radius: var(--radius-full);
  border: 1rpx solid var(--border-light);
  transition: var(--transition-all);
}

.more-btn:active {
  background-color: var(--primary);
  transform: scale(0.95);
}

.more-btn text {
  font-size: var(--text-xs);
  color: var(--primary);
  font-weight: var(--font-medium);
}

.more-btn:active text {
  color: var(--text-inverse);
}

.arrow-icon {
  width: 20rpx;
  height: 20rpx;
  transition: transform 0.2s ease;
}

.more-btn:active .arrow-icon {
  transform: translateX(2rpx);
}

.announcement-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.announcement-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-lg) var(--space-xl);
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1rpx solid var(--border-light);
  transition: var(--transition-all);
  position: relative;
  overflow: hidden;
}

.announcement-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: linear-gradient(180deg, var(--accent) 0%, var(--accent-light) 100%);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.announcement-item:active {
  background-color: var(--bg-secondary);
  transform: translateX(8rpx) scale(0.98);
  box-shadow: var(--shadow-md);
}

.announcement-item:active::before {
  transform: scaleY(1);
}

.announcement-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.announcement-title {
  font-size: var(--text-sm);
  color: var(--text-primary);
  font-weight: var(--font-medium);
  line-height: var(--leading-snug);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.announcement-time {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  font-weight: var(--font-medium);
}

.announcement-badge {
  padding: var(--space-xs) var(--space-sm);
  background: linear-gradient(135deg, var(--accent) 0%, var(--accent-light) 100%);
  color: var(--text-inverse);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  box-shadow: var(--shadow-sm);
  flex-shrink: 0;
}




/* 操作按钮样式 */
.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-lg);
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1rpx solid var(--border-light);
  transition: var(--transition-all);
  box-shadow: var(--shadow-sm);
}

.action-btn:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, var(--primary-subtle) 0%, var(--secondary-subtle) 100%);
  box-shadow: var(--shadow-md);
}

.action-btn image {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: var(--space-sm);
}

.action-btn text {
  font-size: var(--text-sm);
  color: var(--text-primary);
  font-weight: var(--font-medium);
  line-height: var(--leading-tight);
}

/* 任务列表样式 */
.task-list {
  margin-bottom: var(--space-xl);
}

.task-item {
  display: flex;
  align-items: flex-start;
  padding: var(--space-lg) 0;
  border-bottom: 1rpx solid var(--border-light);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.task-item:last-child {
  border-bottom: none;
}

.task-item:active {
  background-color: var(--bg-secondary);
  margin: 0 calc(-1 * var(--space-lg));
  padding-left: var(--space-lg);
  padding-right: var(--space-lg);
  border-radius: var(--radius-lg);
}

/* 任务复选框 */
.task-checkbox {
  margin-right: var(--space-lg);
  padding: var(--space-sm);
  flex-shrink: 0;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid var(--border-medium);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: var(--bg-primary);
  cursor: pointer;
}

.checkbox.checked {
  background-color: var(--primary);
  border-color: var(--primary);
  transform: scale(1.1);
}

.checkmark {
  color: var(--text-inverse);
  font-size: var(--text-sm);
  font-weight: 600;
  line-height: 1;
}

/* 任务内容 */
.task-content {
  flex: 1;
  min-width: 0;
  margin-right: var(--space-lg);
}

.task-title {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
  line-height: var(--leading-snug);
  transition: all 0.2s ease;
}

.task-title.completed {
  color: var(--text-tertiary);
  text-decoration: line-through;
}

.task-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-normal);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: all 0.2s ease;
}

.task-desc.completed {
  color: var(--text-tertiary);
  text-decoration: line-through;
}

/* 任务时间 */
.task-time {
  flex-shrink: 0;
  align-self: flex-start;
}

.task-time text {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  background-color: var(--bg-secondary);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-weight: 500;
  line-height: 1;
}

/* 空状态 */
.task-list .empty,
.empty {
  text-align: center;
  padding: var(--space-3xl) var(--space-xl);
  color: var(--text-tertiary);
  font-size: var(--text-lg);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 2rpx dashed var(--border-medium);
  margin: var(--space-xl) 0;
}

.empty::before {
  content: '📋';
  display: block;
  font-size: 60rpx;
  margin-bottom: var(--space-md);
  opacity: 0.5;
}

/* 功能区域网格 */
.function-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.function-item {
  background-color: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl) var(--space-lg);
  text-align: center;
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-light);
  transition: var(--transition-all);
  position: relative;
  overflow: hidden;
}

.function-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.function-item:active {
  transform: translateY(-4rpx) scale(0.98);
  box-shadow: var(--shadow-lg);
}

.function-item:active::before {
  transform: scaleX(1);
}

.function-icon {
  width: 68rpx;
  height: 68rpx;
  margin: 0 auto var(--space-lg);
  padding: var(--space-sm);
  background: linear-gradient(135deg, var(--primary-subtle) 0%, var(--secondary-subtle) 100%);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
}

.function-icon image {
  width: 40rpx;
  height: 40rpx;
}

.function-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
  line-height: var(--leading-tight);
}

.function-desc {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  line-height: var(--leading-relaxed);
}

/* 工具类样式 */
.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-multiline-truncate {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* iconfont 图标基础样式 */
@import "/styles/iconfont.wxss";

.iconfont {
  font-family: "iconfont" !important;
  font-size: var(--text-base);
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}