// pages/home/<USER>
const { IMAGES, UI, BUSINESS, API } = require('../../constants/index.js');

// 使用增强版请求系统
const request = require('../../utils/request-enhanced.js');
const { home, auth } = require('../../utils/api.js');
const { performanceMixin, dataCacheManager, debounce } = require('../../utils/performance.js');

/**
 * 主页页面 - 智慧养鹅小程序核心页面
 * ✨ 新特性：集成增强版网络请求系统
 * - 详细的请求日志记录
 * - 增强的错误处理
 * - 更好的用户体验
 */

Page(Object.assign({}, performanceMixin, {
  data: {
    userInfo: {
      inventoryCount: 0,
      healthRate: '0%',
      environmentStatus: '优',
      avatar: IMAGES.DEFAULTS.AVATAR,
      name: '养殖户',
      farmName: '未设置养殖场'
    },
    healthData: {
      healthyCount: 1120,
      sickCount: 65,
      deathCount: 15,
      statusText: '整体健康',
      statusDesc: '鹅群状态良好，无异常'
    },
    tasks: [],
    announcements: [],
    knowledgeList: [],
    loading: true,
    unreadCount: 0,
    refreshing: false,
    lastRefreshTime: 0
  },

  /**
   * 页面加载时执行
   */
  onLoad: function () {
    // 记录页面加载开始
    request.Logger.info('首页开始加载', { 
      timestamp: new Date().toISOString() 
    });
    
    this.initPage();
  },

  /**
   * 页面显示时执行
   */
  onShow: function () {
    this.refreshPageData();
  },

  /**
   * 初始化页面
   */
  initPage: function() {
    this.setData({
      loading: true
    });
    
    // 记录初始化开始
    request.Logger.info('首页初始化开始');
    
    // 加载通知数量
    this.loadNotificationCount();
  },

  /**
   * 刷新页面数据 - 使用增强版错误处理
   */
  refreshPageData: debounce(function() {
    const now = Date.now();
    const { refreshing } = this.data;
    
    if (refreshing) {
      return;
    }

    this.safeSetData({
      refreshing: true,
      lastRefreshTime: now
    });

    // 记录刷新开始
    request.Logger.info('首页数据刷新开始', { timestamp: now });

    // 并行获取数据，使用增强版错误处理
    Promise.all([
      this.getUserInfoEnhanced(),
      this.getAnnouncementsEnhanced(),
      this.getTasksEnhanced(),
      this.getKnowledgeListEnhanced()
    ])
    .then(() => {
      request.Logger.info('首页数据刷新成功');
    })
    .catch(error => {
      // 使用增强版错误处理
      request.enhancedErrorHandler(error, 'refreshPageData', {
        url: '首页数据刷新',
        method: 'BATCH'
      });
    })
    .finally(() => {
      this.safeSetData({
        refreshing: false,
        loading: false
      });
    });
  }, 1000),

  /**
   * 获取用户信息 - 增强版
   */
  getUserInfoEnhanced: function() {
    return new Promise((resolve) => {
      try {
        // 检查缓存
        const cachedUserInfo = dataCacheManager.get('userInfo');
        if (cachedUserInfo) {
          request.Logger.info('使用缓存的用户信息');
          this.safeSetData({ userInfo: cachedUserInfo });
          resolve();
          return;
        }

        // 设置默认数据
        const defaultUserInfo = {
          inventoryCount: 1250,
          healthRate: '95%',
          environmentStatus: '优',
          avatar: IMAGES.DEFAULTS.AVATAR,
          name: '张养殖户',
          farmName: '绿野生态养殖场'
        };

        // 先设置默认数据
        this.safeSetData({
          userInfo: defaultUserInfo
        });

        // 获取真实数据，使用增强版请求
        auth.getUserInfo()
          .then(res => {
            if (res && res.success && res.data) {
              const userInfo = {
                inventoryCount: res.data.inventoryCount || 1250,
                healthRate: (res.data.healthRate || 95) + '%',
                environmentStatus: res.data.environmentStatus || '优',
                avatar: res.data.avatar || IMAGES.DEFAULTS.AVATAR,
                name: res.data.name || '张养殖户',
                farmName: res.data.farmName || '绿野生态养殖场'
              };
              
              // 缓存用户信息
              dataCacheManager.set('userInfo', userInfo, 10 * 60 * 1000);
              
              this.safeSetData({ userInfo });
              
              // 记录成功
              request.Logger.info('用户信息获取成功', { userInfo });
            }
            resolve();
          })
          .catch(err => {
            // 使用增强版错误处理
            request.enhancedErrorHandler(err, 'getUserInfo', {
              url: '/auth/userinfo',
              method: 'GET',
              showUserError: false // 不显示用户错误，因为有默认数据
            });
            resolve();
          });
      } catch (error) {
        request.enhancedErrorHandler(error, 'getUserInfoEnhanced');
        resolve();
      }
    });
  },

  /**
   * 获取公告信息 - 增强版
   */
  getAnnouncementsEnhanced: function() {
    return new Promise((resolve) => {
      try {
        // 设置默认公告数据
        const defaultAnnouncements = [
          {
            id: 1,
            title: '冬季养鹅注意事项及防疫措施',
            time: '12-15',
            isImportant: true
          },
          {
            id: 2,
            title: '新品种鹅苗到货通知',
            time: '12-14',
            isImportant: false
          },
          {
            id: 3,
            title: '养殖技术培训通知',
            time: '12-13',
            isImportant: false
          }
        ];

        this.setData({
          announcements: defaultAnnouncements
        });

        // 获取真实数据
        home.getAnnouncements({ page: 1, limit: 5 })
          .then(res => {
            if (res && res.success && res.data && Array.isArray(res.data)) {
              const announcements = res.data.map(item => ({
                id: item.id,
                title: item.title || '未知公告',
                time: this.formatDate(item.createdAt) || '未知时间',
                isImportant: Boolean(item.isImportant)
              }));
              
              this.setData({ announcements });
              
              // 记录成功
              request.Logger.info('公告信息获取成功', { 
                count: announcements.length 
              });
            }
            resolve();
          })
          .catch(err => {
            // 使用增强版错误处理
            request.enhancedErrorHandler(err, 'getAnnouncements', {
              url: '/home/<USER>',
              method: 'GET',
              showUserError: false // 使用默认数据，不显示错误
            });
            resolve();
          });
      } catch (error) {
        request.enhancedErrorHandler(error, 'getAnnouncementsEnhanced');
        resolve();
      }
    });
  },

  /**
   * 获取任务信息 - 增强版
   */
  getTasksEnhanced: function() {
    return new Promise((resolve) => {
      try {
        // 设置默认任务数据
        const defaultTasks = [
          {
            id: 1,
            title: '日常巡检',
            description: '检查鹅群健康状况',
            priority: 'high',
            deadline: '今天 18:00'
          },
          {
            id: 2,
            title: '饲料补充',
            description: 'A区饲料即将用完',
            priority: 'medium',
            deadline: '明天 09:00'
          }
        ];

        this.setData({
          tasks: defaultTasks
        });

        // 这里可以添加真实的任务API调用
        // 目前使用默认数据
        request.Logger.info('任务信息设置为默认数据', { 
          count: defaultTasks.length 
        });
        
        resolve();
      } catch (error) {
        request.enhancedErrorHandler(error, 'getTasksEnhanced');
        resolve();
      }
    });
  },

  /**
   * 获取知识库信息 - 增强版
   */
  getKnowledgeListEnhanced: function() {
    return new Promise((resolve) => {
      try {
        // 设置默认知识库数据
        const defaultKnowledge = [
          {
            id: 1,
            title: '冬季养鹅管理要点',
            category: '养殖技术',
            readCount: 1256
          },
          {
            id: 2,
            title: '鹅病防治指南',
            category: '疾病防控',
            readCount: 987
          }
        ];

        this.setData({
          knowledgeList: defaultKnowledge
        });

        request.Logger.info('知识库信息设置为默认数据', { 
          count: defaultKnowledge.length 
        });
        
        resolve();
      } catch (error) {
        request.enhancedErrorHandler(error, 'getKnowledgeListEnhanced');
        resolve();
      }
    });
  },

  /**
   * 加载通知数量
   */
  loadNotificationCount: function() {
    // 设置默认通知数量
    this.setData({
      unreadCount: 3
    });
    
    request.Logger.info('通知数量设置为默认值', { count: 3 });
  },

  /**
   * 格式化日期
   */
  formatDate: function(dateString) {
    try {
      if (!dateString) return '未知时间';
      
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        request.Logger.warn('无效的日期格式', { dateString });
        return '未知时间';
      }
      
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      
      const diffTime = targetDate.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays === 0) {
        return '今天';
      } else if (diffDays === -1) {
        return '昨天';
      } else if (diffDays === 1) {
        return '明天';
      } else {
        return `${date.getMonth() + 1}-${date.getDate()}`;
      }
    } catch (error) {
      request.enhancedErrorHandler(error, 'formatDate', {
        dateString,
        showUserError: false
      });
      return '未知时间';
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    request.Logger.info('用户触发下拉刷新');
    this.refreshPageData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 点击任务
   */
  onTaskTap: function(e) {
    const { task } = e.currentTarget.dataset;
    request.Logger.info('用户点击任务', { taskId: task.id, title: task.title });
    
    wx.showToast({
      title: `查看任务: ${task.title}`,
      icon: 'none'
    });
  },

  /**
   * 点击公告
   */
  onAnnouncementTap: function(e) {
    const { announcement } = e.currentTarget.dataset;
    request.Logger.info('用户点击公告', { 
      announcementId: announcement.id, 
      title: announcement.title 
    });
    
    wx.showToast({
      title: `查看公告: ${announcement.title}`,
      icon: 'none'
    });
  },

  /**
   * 页面卸载
   */
  onUnload: function() {
    request.Logger.info('首页卸载');
  }
}));