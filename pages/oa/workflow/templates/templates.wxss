/* pages/oa/workflow/templates/templates.wxss */

@import '/styles/oa-design-tokens.wxss';

.container {
  min-height: 100vh;
  background-color: var(--oa-bg-color-page);
  padding-bottom: var(--oa-spacer-10);
}

/* 模板页面头部自定义样式 */
.templates-header {
  background: linear-gradient(135deg, var(--oa-brand-color) 0%, var(--oa-brand-color-dark) 100%);
  margin: var(--oa-spacer-4);
  border-radius: var(--oa-radius-xl);
}

/* 统计卡片区域 */
.stats-section {
  display: flex;
  padding: 24rpx 32rpx;
  gap: 16rpx;
}

/* 搜索和筛选区域 */
.search-section {
  background-color: #FFFFFF;
  margin: 0 32rpx 24rpx;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.search-bar {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.search-input {
  flex: 1;
  height: 72rpx;
  padding: 0 20rpx;
  border: 1rpx solid #E5E5E7;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #F8F8F8;
}

.filter-btn {
  padding: 0 24rpx;
  height: 72rpx;
  line-height: 72rpx;
  font-size: 26rpx;
  color: #007AFF;
  background-color: transparent;
  border: 1rpx solid #007AFF;
  border-radius: 8rpx;
}

/* 移除伪元素选择器，使用类选择器替代 */
.filter-btn-no-border {
  border: none;
}

/* 筛选面板 */
.filter-panel {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #E5E5E7;
}

.filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  width: 120rpx;
  font-size: 28rpx;
  color: #1D1D1F;
}

.filter-picker {
  flex: 1;
  height: 64rpx;
  padding: 0 20rpx;
  border: 1rpx solid #E5E5E7;
  border-radius: 8rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  color: #1D1D1F;
}

.arrow {
  color: #8E8E93;
  font-size: 24rpx;
}

.filter-actions {
  margin-top: 24rpx;
  text-align: right;
}

.clear-btn {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  color: #8E8E93;
  background-color: transparent;
  border: 1rpx solid #E5E5E7;
  border-radius: 6rpx;
}

.clear-btn::after {
  border: none;
}

/* 列表区域 */
.list-section {
  margin: 0 32rpx;
}

/* 加载和空状态 */
.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #8E8E93;
  margin-bottom: 32rpx;
}

.create-btn {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  color: #FFFFFF;
  background-color: #007AFF;
  border-radius: 8rpx;
}

.create-btn::after {
  border: none;
}

/* 模板列表 */
.template-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.template-item {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.item-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.title-area {
  flex: 1;
  margin-right: 16rpx;
}

.item-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.tags {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.time-ago {
  font-size: 24rpx;
  color: #8E8E93;
  white-space: nowrap;
}

.item-description {
  margin-bottom: 20rpx;
  padding: 16rpx;
  background-color: #F8F8F8;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #1D1D1F;
  line-height: 1.5;
}

.item-info {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-label {
  font-size: 24rpx;
  color: #8E8E93;
  margin-right: 8rpx;
}

.info-value {
  font-size: 24rpx;
  color: #1D1D1F;
  font-weight: 500;
}

/* 操作按钮 */
.item-actions {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.action-btn {
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  border-radius: 6rpx;
  line-height: 1;
  color: #FFFFFF;
}

.action-btn::after {
  border: none;
}

.action-btn.design {
  background-color: #007AFF;
}

.action-btn.edit {
  background-color: #00A86B;
}

.action-btn.copy {
  background-color: #FF9500;
}

.action-btn.toggle {
  background-color: #8E8E93;
}

.action-btn.delete {
  background-color: #FF3B30;
}

/* 加载更多和没有更多 */
.loading-more,
.no-more {
  text-align: center;
  padding: 40rpx;
  font-size: 26rpx;
  color: #8E8E93;
}

/* 浮动创建按钮 */
.fab {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 112rpx;
  height: 112rpx;
  background-color: #007AFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);
  z-index: 100;
  transition: transform 0.2s ease;
}

.fab:active {
  transform: scale(0.9);
}

.fab-icon {
  font-size: 48rpx;
  color: #FFFFFF;
  font-weight: 300;
  line-height: 1;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.template-modal {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5E7;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1D1D1F;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #F2F2F7;
  color: #8E8E93;
  font-size: 32rpx;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.modal-close::after {
  border: none;
}

.modal-content {
  padding: 32rpx;
  max-height: 500rpx;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #1D1D1F;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 1rpx solid #E5E5E7;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #1D1D1F;
  background-color: #FFFFFF;
}

.form-input.error {
  border-color: #FF3B30;
}

.form-input:focus {
  border-color: #007AFF;
}

.form-picker {
  height: 88rpx;
  padding: 0 24rpx;
  border: 1rpx solid #E5E5E7;
  border-radius: 8rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  color: #1D1D1F;
}

.form-picker.error {
  border-color: #FF3B30;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx 24rpx;
  border: 1rpx solid #E5E5E7;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #1D1D1F;
  background-color: #FFFFFF;
  line-height: 1.5;
}

.form-textarea:focus {
  border-color: #007AFF;
}

.switch-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.switch-label {
  font-size: 28rpx;
  color: #1D1D1F;
}

.error-text {
  display: block;
  font-size: 24rpx;
  color: #FF3B30;
  margin-top: 8rpx;
}

.modal-actions {
  display: flex;
  padding: 24rpx 32rpx 32rpx;
  gap: 16rpx;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.modal-btn::after {
  border: none;
}

.modal-btn.cancel {
  background-color: #F2F2F7;
  color: #8E8E93;
}

.modal-btn.confirm {
  background-color: #007AFF;
  color: #FFFFFF;
}

.modal-btn[disabled] {
  background-color: #C7C7CC;
  color: #FFFFFF;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .stats-section {
    flex-wrap: wrap;
  }

  .filter-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-label {
    width: auto;
    margin-bottom: 12rpx;
  }

  .filter-picker {
    width: 100%;
  }

  .item-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .time-ago {
    margin-top: 8rpx;
  }

  .item-info {
    flex-direction: column;
    gap: 12rpx;
  }

  .item-actions {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
  }
}