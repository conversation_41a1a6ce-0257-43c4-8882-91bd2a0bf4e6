// pages/oa/workflow/templates/templates.js

const { getCurrentUserInfo, getUserPermissions } = require('../../../../utils/user-info');
const { formatDate } = require('../../../../utils/format');
const authHelper = require('../../../../utils/auth-helper.js');
const { request } = require('../../../../utils/request.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: null,
    permissions: null,

    // 模板列表
    templates: [
      {
        id: 1,
        name: '报销申请流程',
        category: 'reimbursement',
        description: '员工报销申请审批流程',
        is_active: true,
        statusLabel: '启用',
        statusColor: 'success',
        createdTimeAgo: '2024-01-01',
        usageCount: 156
      },
      {
        id: 2,
        name: '请假申请流程',
        category: 'leave',
        description: '员工请假申请审批流程',
        is_active: true,
        statusLabel: '启用',
        statusColor: 'success',
        createdTimeAgo: '2024-01-02',
        usageCount: 89
      },
      {
        id: 3,
        name: '采购申请流程',
        category: 'purchase',
        description: '部门采购申请审批流程',
        is_active: true,
        statusLabel: '启用',
        statusColor: 'success',
        createdTimeAgo: '2024-01-03',
        usageCount: 45
      }
    ],
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      hasMore: true
    },

    // 筛选和搜索
    filters: {
      category: 'all',
      status: 'all',
      keyword: ''
    },

    // 业务类别选项
    categoryOptions: [
      { value: 'all', label: '全部类别' },
      { value: 'reimbursement', label: '报销申请' },
      { value: 'leave', label: '请假申请' },
      { value: 'purchase', label: '采购申请' },
      { value: 'contract', label: '合同审批' },
      { value: 'other', label: '其他' }
    ],

    statusOptions: [
      { value: 'all', label: '全部状态' },
      { value: 'active', label: '启用' },
      { value: 'inactive', label: '停用' }
    ],

    // 选择器索引
    categoryIndex: 0,
    statusIndex: 0,

    // 显示标签
    selectedCategoryLabel: '全部类别',
    selectedStatusLabel: '全部状态',

    // 页面状态
    loading: false,
    refreshing: false,
    showFilters: false,

    // 模板操作
    showTemplateModal: false,
    currentTemplate: null,
    templateForm: {
      name: '',
      category: 'reimbursement',
      description: '',
      isActive: true
    },

    // 表单验证
    errors: {},
    submitting: false,

    // 统计数据
    statistics: {
      total: 8,
      active: 7,
      inactive: 1,
      recentUsed: 5
    },

    // 计算属性 - 用于WXML中的复杂表达式
    categoryOptionsForForm: [], // 去掉"全部类别"选项的类别列表
    selectedCategoryIndex: 0 // 当前选中的类别索引
  },

  onLoad(options) {

    // 检查页面权限
    const hasPermission = authHelper.checkPagePermission(options, 'workflow:manage', () => {
      // 权限检查通过，初始化页面数据
      this.initPage();
    });

    if (!hasPermission) {
      return; // 权限检查失败，不继续执行
    }
  },

  onShow() {
    this.loadTemplates(true);
  },

  onPullDownRefresh() {
    this.loadTemplates(true).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    if (this.data.pagination.hasMore && !this.data.loading) {
      this.loadTemplates(false);
    }
  },

  /**
   * 初始化页面
   */
  initPage() {
    const userInfo = getCurrentUserInfo();
    const permissions = getUserPermissions();

    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 检查管理权限
    if (!permissions.canManageWorkflow) {
      wx.showModal({
        title: '没有权限',
        content: '您没有流程模板管理权限',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    this.setData({
      userInfo,
      permissions
    });

    // 初始化选择器索引
    this.updateFilterIndexes();

    // 初始化计算属性
    this.updateComputedProperties();
  },

  /**
   * 更新筛选器索引
   */
  updateFilterIndexes() {
    const { filters, categoryOptions, statusOptions } = this.data;

    // 更新类别索引
    const categoryIndex = categoryOptions.findIndex(item => item.value === filters.category);
    const selectedCategory = categoryOptions[categoryIndex >= 0 ? categoryIndex : 0];

    // 更新状态索引
    const statusIndex = statusOptions.findIndex(item => item.value === filters.status);
    const selectedStatus = statusOptions[statusIndex >= 0 ? statusIndex : 0];

    this.setData({
      categoryIndex: categoryIndex >= 0 ? categoryIndex : 0,
      statusIndex: statusIndex >= 0 ? statusIndex : 0,
      selectedCategoryLabel: selectedCategory.label,
      selectedStatusLabel: selectedStatus.label
    });
  },

  /**
   * 更新计算属性
   */
  updateComputedProperties() {
    const { categoryOptions, templateForm } = this.data;

    // 创建去掉"全部类别"选项的类别列表
    const categoryOptionsForForm = categoryOptions.slice(1);

    // 计算当前选中的类别索引
    const selectedCategoryIndex = categoryOptionsForForm.findIndex(cat => cat.value === templateForm.category);

    this.setData({
      categoryOptionsForForm,
      selectedCategoryIndex: selectedCategoryIndex >= 0 ? selectedCategoryIndex : 0
    });
  },

  /**
   * 加载模板列表
   */
  async loadTemplates(refresh = false) {
    if (this.data.loading) return;

    if (refresh) {
      this.setData({
        refreshing: true,
        'pagination.page': 1,
        'pagination.hasMore': true
      });
    } else {
      this.setData({ loading: true });
    }

    try {
      const { pagination, filters } = this.data;
      const token = wx.getStorageSync('token');

      const params = {
        page: refresh ? 1 : pagination.page,
        limit: pagination.limit,
        ...filters
      };

      const queryString = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&');

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + `/api/v1/oa/workflows/templates?${queryString}`,
          method: 'GET',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.success) {
        const { list, pagination: newPagination } = res.data.data;

        let templates = [];
        if (refresh) {
          templates = list;
        } else {
          templates = [...this.data.templates, ...list];
        }

        // 格式化数据
        const formattedTemplates = templates.map(template => ({
          ...template,
          categoryLabel: this.getCategoryLabel(template.category),
          statusLabel: template.is_active ? '启用' : '停用',
          statusColor: template.is_active ? '#00A86B' : '#8E8E93',
          createdTimeAgo: this.formatTimeAgo(template.created_at),
          stepsCount: template.steps_config ? JSON.parse(template.steps_config).length : 0
        }));

        this.setData({
          templates: formattedTemplates,
          'pagination.page': newPagination.page,
          'pagination.total': newPagination.total,
          'pagination.hasMore': newPagination.page < newPagination.pages
        });

        // 加载统计数据
        if (refresh) {
          this.loadStatistics();
        }
      } else {
        throw new Error(res.data.message || '加载失败');
      }
    } catch (error) {
      console.error('加载模板列表失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        loading: false,
        refreshing: false
      });
    }
  },

  /**
   * 加载统计数据
   */
  async loadStatistics() {
    try {
      const token = wx.getStorageSync('token');

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + '/api/v1/oa/workflows/templates/statistics',
          method: 'GET',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.success) {
        this.setData({
          statistics: res.data.data
        });
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  },

  /**
   * 获取类别标签
   */
  getCategoryLabel(category) {
    const categoryMap = {
      reimbursement: '报销申请',
      leave: '请假申请',
      purchase: '采购申请',
      contract: '合同审批',
      other: '其他'
    };
    return categoryMap[category] || category;
  },

  /**
   * 获取当前表单类别的标签
   */
  getCurrentCategoryLabel() {
    return this.getCategoryLabel(this.data.templateForm.category);
  },

  /**
   * 格式化时间差
   */
  formatTimeAgo(timeStr) {
    const time = new Date(timeStr);
    const now = new Date();
    const diff = now - time;

    // 小于1小时显示分钟
    if (diff < 3600000) {
      const minutes = Math.floor(diff / 60000);
      return `${minutes}分钟前`;
    }

    // 小于24小时显示小时
    if (diff < 86400000) {
      const hours = Math.floor(diff / 3600000);
      return `${hours}小时前`;
    }

    // 超过24小时显示天数
    const days = Math.floor(diff / 86400000);
    return `${days}天前`;
  },

  /**
   * 显示/隐藏筛选器
   */
  toggleFilters() {
    this.setData({
      showFilters: !this.data.showFilters
    });
  },

  /**
   * 类别筛选
   */
  onCategoryFilter(e) {
    const categoryIndex = e.detail.value;
    const selectedCategory = this.data.categoryOptions[categoryIndex];

    this.setData({
      'filters.category': selectedCategory.value,
      categoryIndex: categoryIndex,
      selectedCategoryLabel: selectedCategory.label
    });
    this.loadTemplates(true);
  },

  /**
   * 状态筛选
   */
  onStatusFilter(e) {
    const statusIndex = e.detail.value;
    const selectedStatus = this.data.statusOptions[statusIndex];

    this.setData({
      'filters.status': selectedStatus.value,
      statusIndex: statusIndex,
      selectedStatusLabel: selectedStatus.label
    });
    this.loadTemplates(true);
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({
      'filters.keyword': keyword
    });

    // 延迟搜索，避免频繁请求
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      this.loadTemplates(true);
    }, 500);
  },

  /**
   * 清除筛选
   */
  clearFilters() {
    this.setData({
      filters: {
        category: 'all',
        status: 'all',
        keyword: ''
      },
      showFilters: false,
      categoryIndex: 0,
      statusIndex: 0,
      selectedCategoryLabel: '全部类别',
      selectedStatusLabel: '全部状态'
    });
    this.loadTemplates(true);
  },

  /**
   * 创建新模板
   */
  onCreateTemplate() {
    this.setData({
      showTemplateModal: true,
      currentTemplate: null,
      templateForm: {
        name: '',
        category: 'reimbursement',
        description: '',
        isActive: true
      },
      errors: {}
    });

    // 更新计算属性
    this.updateComputedProperties();
  },

  /**
   * 编辑模板
   */
  onEditTemplate(e) {
    const { template } = e.currentTarget.dataset;

    this.setData({
      showTemplateModal: true,
      currentTemplate: template,
      templateForm: {
        name: template.name,
        category: template.category,
        description: template.description || '',
        isActive: template.is_active
      },
      errors: {}
    });

    // 更新计算属性
    this.updateComputedProperties();
  },

  /**
   * 设计流程
   */
  onDesignWorkflow(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/oa/workflow/designer/designer?templateId=${id}`
    });
  },

  /**
   * 复制模板
   */
  onCopyTemplate(e) {
    const { template } = e.currentTarget.dataset;

    wx.showModal({
      title: '复制模板',
      content: `确定要复制模板"${template.name}"吗？`,
      success: (res) => {
        if (res.confirm) {
          this.copyTemplate(template);
        }
      }
    });
  },

  /**
   * 执行复制操作
   */
  async copyTemplate(template) {
    try {
      const token = wx.getStorageSync('token');

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + `/api/v1/oa/workflows/templates/${template.id}/copy`,
          method: 'POST',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.success) {
        wx.showToast({
          title: '复制成功',
          icon: 'success'
        });
        this.loadTemplates(true);
      } else {
        throw new Error(res.data.message || '复制失败');
      }
    } catch (error) {
      console.error('复制模板失败:', error);
      wx.showToast({
        title: error.message || '复制失败',
        icon: 'none'
      });
    }
  },

  /**
   * 删除模板
   */
  onDeleteTemplate(e) {
    const { template } = e.currentTarget.dataset;

    wx.showModal({
      title: '确认删除',
      content: `确定要删除模板"${template.name}"吗？删除后无法恢复。`,
      success: (res) => {
        if (res.confirm) {
          this.deleteTemplate(template.id);
        }
      }
    });
  },

  /**
   * 执行删除操作
   */
  async deleteTemplate(id) {
    try {
      const token = wx.getStorageSync('token');

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + `/api/v1/oa/workflows/templates/${id}`,
          method: 'DELETE',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        this.loadTemplates(true);
      } else {
        throw new Error(res.data.message || '删除失败');
      }
    } catch (error) {
      console.error('删除模板失败:', error);
      wx.showToast({
        title: error.message || '删除失败',
        icon: 'none'
      });
    }
  },

  /**
   * 切换模板状态
   */
  onToggleStatus(e) {
    const { template } = e.currentTarget.dataset;
    const newStatus = !template.is_active;

    wx.showModal({
      title: '确认操作',
      content: `确定要${newStatus ? '启用' : '停用'}模板"${template.name}"吗？`,
      success: (res) => {
        if (res.confirm) {
          this.toggleTemplateStatus(template.id, newStatus);
        }
      }
    });
  },

  /**
   * 执行状态切换
   */
  async toggleTemplateStatus(id, isActive) {
    try {
      const token = wx.getStorageSync('token');

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + `/api/v1/oa/workflows/templates/${id}/status`,
          method: 'PUT',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          data: { isActive },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.success) {
        wx.showToast({
          title: isActive ? '启用成功' : '停用成功',
          icon: 'success'
        });
        this.loadTemplates(true);
      } else {
        throw new Error(res.data.message || '操作失败');
      }
    } catch (error) {
      console.error('切换状态失败:', error);
      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 隐藏模板模态框
   */
  hideTemplateModal() {
    this.setData({
      showTemplateModal: false,
      currentTemplate: null,
      templateForm: {
        name: '',
        category: 'reimbursement',
        description: '',
        isActive: true
      },
      errors: {}
    });
  },

  /**
   * 表单输入处理
   */
  onFormInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`templateForm.${field}`]: value
    });

    // 清除对应字段的错误信息
    if (this.data.errors[field]) {
      this.setData({
        [`errors.${field}`]: ''
      });
    }

    // 如果是类别字段变更，更新计算属性
    if (field === 'category') {
      this.updateComputedProperties();
    }
  },

  /**
   * 类别选择处理
   */
  onCategorySelect(e) {
    const categoryIndex = e.detail.value;
    const { categoryOptionsForForm } = this.data;
    const selectedCategory = categoryOptionsForForm[categoryIndex];

    if (selectedCategory) {
      this.setData({
        'templateForm.category': selectedCategory.value,
        selectedCategoryIndex: categoryIndex
      });

      // 清除类别字段的错误信息
      if (this.data.errors.category) {
        this.setData({
          'errors.category': ''
        });
      }
    }
  },

  /**
   * 状态开关切换
   */
  onStatusSwitch(e) {
    this.setData({
      'templateForm.isActive': e.detail.value
    });
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { templateForm } = this.data;
    const errors = {};

    if (!templateForm.name.trim()) {
      errors.name = '请输入模板名称';
    }

    if (!templateForm.category) {
      errors.category = '请选择业务类别';
    }

    this.setData({ errors });
    return Object.keys(errors).length === 0;
  },

  /**
   * 保存模板
   */
  async saveTemplate() {
    if (this.data.submitting) return;

    // 表单验证
    if (!this.validateForm()) {
      return;
    }

    this.setData({ submitting: true });

    try {
      const { templateForm, currentTemplate } = this.data;
      const token = wx.getStorageSync('token');

      const isEdit = !!currentTemplate;
      const url = isEdit
        ? `/api/v1/oa/workflows/templates/${currentTemplate.id}`
        : '/api/v1/oa/workflows/templates';

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + url,
          method: isEdit ? 'PUT' : 'POST',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          data: {
            name: templateForm.name,
            category: templateForm.category,
            description: templateForm.description,
            isActive: templateForm.isActive
          },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.success) {
        wx.showToast({
          title: isEdit ? '更新成功' : '创建成功',
          icon: 'success'
        });

        this.hideTemplateModal();
        this.loadTemplates(true);
      } else {
        throw new Error(res.data.message || '保存失败');
      }
    } catch (error) {
      console.error('保存模板失败:', error);
      wx.showToast({
        title: error.message || '保存失败',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  }
});