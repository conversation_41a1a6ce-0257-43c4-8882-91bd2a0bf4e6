// pages/oa/reimbursement/detail/detail.js

const { getCurrentUserInfo, getUserPermissions } = require('../../../../utils/user-info');
const { formatDate } = require('../../../../utils/format');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: null,
    permissions: null,

    // 报销申请详情
    reimbursement: null,

    // 页面状态
    loading: true,
    submitting: false,

    // 审批操作
    showApprovalModal: false,
    approvalAction: '', // approve/reject
    approvalRemarks: '',

    // 状态配置
    statusConfig: {
      draft: { label: '草稿', color: '#8E8E93', bgColor: '#F2F2F7' },
      submitted: { label: '待审批', color: '#FF9500', bgColor: '#FFF4E6' },
      approved: { label: '已批准', color: '#00A86B', bgColor: '#E8F5E8' },
      rejected: { label: '已拒绝', color: '#FF3B30', bgColor: '#FFE8E8' },
      paid: { label: '已付款', color: '#007AFF', bgColor: '#E3F2FD' },
      cancelled: { label: '已取消', color: '#8E8E93', bgColor: '#F2F2F7' }
    },

    // 类别配置
    categoryConfig: {
      travel: '差旅费',
      office: '办公用品',
      meal: '餐费',
      transport: '交通费',
      communication: '通讯费',
      training: '培训费',
      entertainment: '招待费',
      other: '其他'
    }
  },

  onLoad(options) {
    if (!options.id) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.initPage(options.id);
  },

  onShow() {
    // 每次显示时刷新数据
    if (this.data.reimbursement) {
      this.loadReimbursementDetail(this.data.reimbursement.id);
    }
  },

  onPullDownRefresh() {
    if (this.data.reimbursement) {
      this.loadReimbursementDetail(this.data.reimbursement.id).finally(() => {
        wx.stopPullDownRefresh();
      });
    }
  },

  /**
   * 初始化页面
   */
  initPage(id) {
    const userInfo = getCurrentUserInfo();
    const permissions = getUserPermissions();

    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({
      userInfo,
      permissions
    });

    this.loadReimbursementDetail(id);
  },

  /**
   * 加载报销申请详情
   */
  async loadReimbursementDetail(id) {
    this.setData({ loading: true });

    try {
      const token = wx.getStorageSync('token');

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + `/api/v1/oa/reimbursements/${id}`,
          method: 'GET',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.success) {
        const reimbursement = res.data.data;

        this.setData({
          reimbursement
        });

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: reimbursement.title || '报销详情'
        });
      } else {
        throw new Error(res.data.message || '加载失败');
      }
    } catch (error) {
      console.error('加载报销申请详情失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 获取状态配置
   */
  getStatusConfig(status) {
    return this.data.statusConfig[status] || this.data.statusConfig.draft;
  },

  /**
   * 获取类别标签
   */
  getCategoryLabel(category) {
    return this.data.categoryConfig[category] || category;
  },

  /**
   * 检查是否可以编辑
   */
  canEdit() {
    const { reimbursement, userInfo } = this.data;
    if (!reimbursement || !userInfo) return false;

    return reimbursement.user_id === userInfo.id &&
           (reimbursement.status === 'draft' || reimbursement.status === 'rejected');
  },

  /**
   * 检查是否可以删除
   */
  canDelete() {
    const { reimbursement, userInfo } = this.data;
    if (!reimbursement || !userInfo) return false;

    return reimbursement.user_id === userInfo.id &&
           (reimbursement.status === 'draft' || reimbursement.status === 'rejected');
  },

  /**
   * 检查是否可以审批
   */
  canApprove() {
    const { reimbursement, userInfo, permissions } = this.data;
    if (!reimbursement || !userInfo || !permissions.canApprove) return false;

    return reimbursement.status === 'submitted' &&
           reimbursement.user_id !== userInfo.id;
  },

  /**
   * 编辑报销申请
   */
  onEdit() {
    if (!this.canEdit()) return;

    wx.navigateTo({
      url: `/pages/oa/reimbursement/apply/apply?id=${this.data.reimbursement.id}`
    });
  },

  /**
   * 删除报销申请
   */
  onDelete() {
    if (!this.canDelete()) return;

    const { reimbursement } = this.data;

    wx.showModal({
      title: '确认删除',
      content: `确定要删除报销申请"${reimbursement.title}"吗？`,
      success: (res) => {
        if (res.confirm) {
          this.deleteReimbursement();
        }
      }
    });
  },

  /**
   * 执行删除操作
   */
  async deleteReimbursement() {
    try {
      const token = wx.getStorageSync('token');

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + `/api/v1/oa/reimbursements/${this.data.reimbursement.id}`,
          method: 'DELETE',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(res.data.message || '删除失败');
      }
    } catch (error) {
      console.error('删除报销申请失败:', error);
      wx.showToast({
        title: error.message || '删除失败',
        icon: 'none'
      });
    }
  },

  /**
   * 显示审批模态框
   */
  showApprovalModal(action) {
    if (!this.canApprove()) return;

    this.setData({
      showApprovalModal: true,
      approvalAction: action,
      approvalRemarks: ''
    });
  },

  /**
   * 隐藏审批模态框
   */
  hideApprovalModal() {
    this.setData({
      showApprovalModal: false,
      approvalAction: '',
      approvalRemarks: ''
    });
  },

  /**
   * 审批意见输入
   */
  onRemarksInput(e) {
    this.setData({
      approvalRemarks: e.detail.value
    });
  },

  /**
   * 确认审批
   */
  async confirmApproval() {
    const { approvalAction, approvalRemarks, reimbursement } = this.data;

    if (!approvalRemarks.trim()) {
      wx.showToast({
        title: '请输入审批意见',
        icon: 'none'
      });
      return;
    }

    this.setData({ submitting: true });

    try {
      const token = wx.getStorageSync('token');

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + `/api/v1/oa/reimbursements/${reimbursement.id}/approve`,
          method: 'POST',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          data: {
            action: approvalAction,
            remarks: approvalRemarks
          },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.success) {
        wx.showToast({
          title: approvalAction === 'approve' ? '审批通过' : '审批拒绝',
          icon: 'success'
        });

        this.hideApprovalModal();

        // 重新加载数据
        setTimeout(() => {
          this.loadReimbursementDetail(reimbursement.id);
        }, 1000);
      } else {
        throw new Error(res.data.message || '审批失败');
      }
    } catch (error) {
      console.error('审批失败:', error);
      wx.showToast({
        title: error.message || '审批失败',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  /**
   * 审批流程操作
   */
  onApprovalAction(e) {
    const { action } = e.detail;
    this.showApprovalModal(action);
  },

  /**
   * 查看费用明细
   */
  onViewExpenseItem(e) {
    const { index } = e.currentTarget.dataset;
    const item = this.data.reimbursement.items[index];

    // 可以在这里实现费用明细的详细查看功能
  }
});