/* pages/oa/oa.wxss */

@import '/styles/unified-design-tokens.wxss';

.page-container {
  padding: var(--page-padding);
  background-color: var(--bg-color-page);
  min-height: 100vh;
}

/* 页面头部自定义样式 */
.page-main-header {
  background: linear-gradient(135deg, var(--oa-theme-color) 0%, var(--primary-color-light) 100%);
  border-radius: var(--radius-xl);
  padding: var(--spacer-8) var(--spacer-6);
  margin-bottom: var(--section-margin);
  box-shadow: var(--shadow-m);
}

/* 统计概览 */
.stats-section {
  margin-bottom: var(--section-margin);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-4);
  gap: var(--spacer-2);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacer-4);
}

.stat-card {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6) var(--spacer-5);
  display: flex;
  align-items: center;
  position: relative;
  box-shadow: var(--shadow-s);
  transition: all var(--transition-normal) var(--ease-out);
  border: 1rpx solid var(--border-color);
}

.stat-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-m);
  background: var(--bg-color-hover);
}

.stat-icon {
  width: 64rpx;
  height: 64rpx;
  margin-right: var(--spacer-4);
  border-radius: var(--radius-m);
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-icon.approval {
  background: var(--primary-color-focus);
}

.stat-icon.task {
  background: var(--success-color-focus);
}

.stat-icon.expense {
  background: var(--warning-color-focus);
}

.stat-icon.notice {
  background: var(--info-color-focus);
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-number {
  display: block;
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-1);
  line-height: var(--line-height-tight);
}

.stat-label {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  line-height: var(--line-height-normal);
}

.stat-badge {
  position: absolute;
  top: var(--spacer-3);
  right: var(--spacer-3);
  background: var(--error-color);
  color: var(--text-color-inverse);
  font-size: var(--font-size-xs);
  padding: var(--spacer-1) var(--spacer-2);
  border-radius: var(--radius-round);
  min-width: 32rpx;
  text-align: center;
  font-weight: var(--font-weight-medium);
}

/* 功能模块 */
.modules-section, .quick-actions-section, .activities-section {
  margin-bottom: var(--section-margin);
}

.modules-section .section-title,
.quick-actions-section .section-title,
.activities-section .section-title {
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-4);
  padding-left: var(--spacer-3);
  border-left: 6rpx solid var(--oa-theme-color);
  display: flex;
  align-items: center;
}

.modules-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-3);
}

.module-item {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  display: flex;
  align-items: center;
  box-shadow: var(--shadow-s);
  transition: all var(--transition-normal) var(--ease-out);
  position: relative;
  border: 1rpx solid var(--border-color);
}

.module-item:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-m);
  background: var(--bg-color-hover);
}

.module-icon {
  width: 64rpx;
  height: 64rpx;
  margin-right: 24rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
}

.module-icon image {
  width: 40rpx;
  height: 40rpx;
}

.module-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #FF4757;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 24rpx;
  text-align: center;
}

.module-content {
  flex: 1;
}

.module-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.module-description {
  font-size: 24rpx;
  color: #666;
}

.module-arrow {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

.module-arrow image {
  width: 100%;
  height: 100%;
}

/* 快捷操作 */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.quick-action-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.quick-action-item:active {
  transform: scale(0.98);
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  margin: 0 auto 16rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon image {
  width: 32rpx;
  height: 32rpx;
}

.action-title {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 最近动态 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.view-all-btn {
  font-size: 26rpx;
  color: #0066CC;
}

.activities-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.activity-item {
  padding: 24rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item:active {
  background-color: #f8f9fa;
}

.activity-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-icon.approval {
  background: rgba(155, 89, 182, 0.1);
}

.activity-icon.reimbursement {
  background: rgba(255, 107, 53, 0.1);
}

.activity-icon.purchase {
  background: rgba(46, 204, 113, 0.1);
}

.activity-icon image {
  width: 28rpx;
  height: 28rpx;
}

.activity-content {
  flex: 1;
}

.activity-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.activity-description {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.activity-time {
  font-size: 22rpx;
  color: #999;
}

.activity-status {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  white-space: nowrap;
}

.activity-status.approved {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.activity-status.pending {
  background: rgba(255, 193, 7, 0.1);
  color: #f39c12;
}

.activity-status.submitted {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  text-align: center;
  min-width: 200rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #0066CC;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}