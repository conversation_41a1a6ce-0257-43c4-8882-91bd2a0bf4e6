// pages/oa/permission/users/users.js

const { getCurrentUserInfo, getUserPermissions } = require('../../../../utils/user-info');
const { formatDate } = require('../../../../utils/format');
const authHelper = require('../../../../utils/auth-helper.js');
const { request } = require('../../../../utils/request.js');
const { PERMISSIONS, PermissionMixin } = require('../../../../utils/oa-permissions');

Page({
  // 混入权限检查功能
  ...PermissionMixin,
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: null,
    permissions: null,
    
    // 用户列表
    users: [
      {
        id: 1,
        username: '张三',
        email: 'zhang<PERSON>@company.com',
        real_name: '张三',
        avatar: '',
        is_active: true,
        department_id: 1,
        department_name: '技术部',
        statusLabel: '启用',
        statusColor: 'success',
        createdTimeAgo: '2024-01-01',
        roles: [
          { id: 1, name: '系统管理员', code: 'admin' }
        ],
        rolesText: '系统管理员'
      },
      {
        id: 2,
        username: '李四',
        email: '<EMAIL>',
        real_name: '李四',
        avatar: '',
        is_active: true,
        department_id: 2,
        department_name: '人事部',
        statusLabel: '启用',
        statusColor: 'success',
        createdTimeAgo: '2024-01-02',
        roles: [
          { id: 2, name: '部门经理', code: 'manager' }
        ],
        rolesText: '部门经理'
      },
      {
        id: 3,
        username: '王五',
        email: '<EMAIL>',
        real_name: '王五',
        avatar: '',
        is_active: true,
        department_id: 1,
        department_name: '技术部',
        statusLabel: '启用',
        statusColor: 'success',
        createdTimeAgo: '2024-01-03',
        roles: [
          { id: 3, name: '普通员工', code: 'employee' }
        ],
        rolesText: '普通员工'
      }
    ],
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      hasMore: true
    },
    
    // 筛选和搜索
    filters: {
      department: 'all',
      role: 'all',
      status: 'all',
      keyword: ''
    },
    
    // 部门选项
    departmentOptions: [
      { value: 'all', label: '全部部门' }
    ],
    
    // 角色选项
    roleOptions: [
      { value: 'all', label: '全部角色' }
    ],
    
    statusOptions: [
      { value: 'all', label: '全部状态' },
      { value: 'active', label: '启用' },
      { value: 'inactive', label: '停用' }
    ],
    
    // 选择器索引
    departmentIndex: 0,
    roleIndex: 0,
    statusIndex: 0,
    
    // 显示标签
    selectedDepartmentLabel: '全部部门',
    selectedRoleLabel: '全部角色',
    selectedStatusLabel: '全部状态',
    
    // 页面状态
    loading: false,
    refreshing: false,
    showFilters: false,
    
    // 用户角色分配
    showRoleModal: false,
    currentUser: null,
    allRoles: [],
    userRoles: [],
    
    // 表单验证
    submitting: false,
    
    // 统计数据
    statistics: {
      total: 15,
      active: 14,
      inactive: 1,
      hasRoles: 12
    }
  },

  onLoad(options) {

    // 检查页面权限
    const hasPermission = authHelper.checkPagePermission(options, 'system:user:manage', () => {
      // 权限检查通过，初始化页面数据
      this.initPage();
    });

    if (!hasPermission) {
      return; // 权限检查失败，不继续执行
    }
  },

  onShow() {
    this.loadUsers(true);
  },

  onPullDownRefresh() {
    this.loadUsers(true).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    if (this.data.pagination.hasMore && !this.data.loading) {
      this.loadUsers(false);
    }
  },

  /**
   * 初始化页面
   */
  initPage() {
    const userInfo = getCurrentUserInfo();
    const permissions = getUserPermissions();
    
    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    
    this.setData({
      userInfo,
      permissions
    });
    
    // 初始化选择器索引
    this.updateFilterIndexes();
    
    // 加载基础数据
    this.loadDepartments();
    this.loadRoles();
  },

  /**
   * 权限就绪后的回调
   */
  onPermissionReady() {
    // 检查用户管理权限
    if (!this.hasPermission(PERMISSIONS.MANAGE_USERS)) {
      wx.showModal({
        title: '没有权限',
        content: '您没有用户管理权限',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    // 权限验证通过，加载数据
    this.loadUsers(true);
  },

  /**
   * 更新筛选器索引
   */
  updateFilterIndexes() {
    const { filters, departmentOptions, roleOptions, statusOptions } = this.data;
    
    // 更新部门索引
    const departmentIndex = departmentOptions.findIndex(item => item.value === filters.department);
    const selectedDepartment = departmentOptions[departmentIndex >= 0 ? departmentIndex : 0];
    
    // 更新角色索引
    const roleIndex = roleOptions.findIndex(item => item.value === filters.role);
    const selectedRole = roleOptions[roleIndex >= 0 ? roleIndex : 0];
    
    // 更新状态索引
    const statusIndex = statusOptions.findIndex(item => item.value === filters.status);
    const selectedStatus = statusOptions[statusIndex >= 0 ? statusIndex : 0];
    
    this.setData({
      departmentIndex: departmentIndex >= 0 ? departmentIndex : 0,
      roleIndex: roleIndex >= 0 ? roleIndex : 0,
      statusIndex: statusIndex >= 0 ? statusIndex : 0,
      selectedDepartmentLabel: selectedDepartment.label,
      selectedRoleLabel: selectedRole.label,
      selectedStatusLabel: selectedStatus.label
    });
  },

  /**
   * 加载部门列表
   */
  async loadDepartments() {
    try {
      const token = wx.getStorageSync('token');
      
      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + '/api/v1/oa/departments',
          method: 'GET',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });
      
      if (res.data.success) {
        const departments = res.data.data;
        const departmentOptions = [
          { value: 'all', label: '全部部门' },
          ...departments.map(dept => ({
            value: dept.id.toString(),
            label: dept.name
          }))
        ];
        
        this.setData({ departmentOptions });
        this.updateFilterIndexes();
      }
    } catch (error) {
      console.error('加载部门列表失败:', error);
    }
  },

  /**
   * 加载角色列表
   */
  async loadRoles() {
    try {
      const token = wx.getStorageSync('token');
      
      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + '/api/v1/oa/permissions/roles?status=active&limit=100',
          method: 'GET',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });
      
      if (res.data.success) {
        const roles = res.data.data.list;
        const roleOptions = [
          { value: 'all', label: '全部角色' },
          ...roles.map(role => ({
            value: role.id.toString(),
            label: role.name
          }))
        ];
        
        this.setData({ 
          roleOptions,
          allRoles: roles
        });
        this.updateFilterIndexes();
      }
    } catch (error) {
      console.error('加载角色列表失败:', error);
    }
  },

  /**
   * 加载用户列表
   */
  async loadUsers(refresh = false) {
    if (this.data.loading) return;

    if (refresh) {
      this.setData({
        refreshing: true,
        'pagination.page': 1,
        'pagination.hasMore': true
      });
    } else {
      this.setData({ loading: true });
    }

    try {
      const { pagination, filters } = this.data;
      const token = wx.getStorageSync('token');

      const params = {
        page: refresh ? 1 : pagination.page,
        limit: pagination.limit,
        ...filters
      };

      const queryString = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&');

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + `/api/v1/oa/permissions/users?${queryString}`,
          method: 'GET',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.success) {
        const { list, pagination: newPagination } = res.data.data;

        let users = [];
        if (refresh) {
          users = list;
        } else {
          users = [...this.data.users, ...list];
        }

        // 格式化数据
        const formattedUsers = users.map(user => ({
          ...user,
          statusLabel: user.is_active ? '启用' : '停用',
          statusColor: user.is_active ? '#00A86B' : '#8E8E93',
          rolesText: user.roles ? user.roles.map(r => r.name).join('、') : '无角色',
          createdTimeAgo: this.formatTimeAgo(user.created_at)
        }));

        this.setData({
          users: formattedUsers,
          'pagination.page': newPagination.page,
          'pagination.total': newPagination.total,
          'pagination.hasMore': newPagination.page < newPagination.pages
        });

        // 加载统计数据
        if (refresh) {
          this.loadStatistics();
        }
      } else {
        throw new Error(res.data.message || '加载失败');
      }
    } catch (error) {
      console.error('加载用户列表失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        loading: false,
        refreshing: false
      });
    }
  },

  /**
   * 加载统计数据
   */
  async loadStatistics() {
    try {
      const token = wx.getStorageSync('token');

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + '/api/v1/oa/permissions/users/statistics',
          method: 'GET',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.success) {
        this.setData({
          statistics: res.data.data
        });
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  },

  /**
   * 格式化时间差
   */
  formatTimeAgo(timeStr) {
    const time = new Date(timeStr);
    const now = new Date();
    const diff = now - time;

    // 小于1小时显示分钟
    if (diff < 3600000) {
      const minutes = Math.floor(diff / 60000);
      return `${minutes}分钟前`;
    }

    // 小于24小时显示小时
    if (diff < 86400000) {
      const hours = Math.floor(diff / 3600000);
      return `${hours}小时前`;
    }

    // 超过24小时显示天数
    const days = Math.floor(diff / 86400000);
    return `${days}天前`;
  },

  /**
   * 显示/隐藏筛选器
   */
  toggleFilters() {
    this.setData({
      showFilters: !this.data.showFilters
    });
  },

  /**
   * 部门筛选
   */
  onDepartmentFilter(e) {
    const departmentIndex = e.detail.value;
    const selectedDepartment = this.data.departmentOptions[departmentIndex];

    this.setData({
      'filters.department': selectedDepartment.value,
      departmentIndex: departmentIndex,
      selectedDepartmentLabel: selectedDepartment.label
    });
    this.loadUsers(true);
  },

  /**
   * 角色筛选
   */
  onRoleFilter(e) {
    const roleIndex = e.detail.value;
    const selectedRole = this.data.roleOptions[roleIndex];

    this.setData({
      'filters.role': selectedRole.value,
      roleIndex: roleIndex,
      selectedRoleLabel: selectedRole.label
    });
    this.loadUsers(true);
  },

  /**
   * 状态筛选
   */
  onStatusFilter(e) {
    const statusIndex = e.detail.value;
    const selectedStatus = this.data.statusOptions[statusIndex];

    this.setData({
      'filters.status': selectedStatus.value,
      statusIndex: statusIndex,
      selectedStatusLabel: selectedStatus.label
    });
    this.loadUsers(true);
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({
      'filters.keyword': keyword
    });

    // 延迟搜索，避免频繁请求
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      this.loadUsers(true);
    }, 500);
  },

  /**
   * 清除筛选
   */
  clearFilters() {
    this.setData({
      filters: {
        department: 'all',
        role: 'all',
        status: 'all',
        keyword: ''
      },
      showFilters: false,
      departmentIndex: 0,
      roleIndex: 0,
      statusIndex: 0,
      selectedDepartmentLabel: '全部部门',
      selectedRoleLabel: '全部角色',
      selectedStatusLabel: '全部状态'
    });
    this.loadUsers(true);
  },

  /**
   * 分配角色
   */
  async onAssignRoles(e) {
    const { user } = e.currentTarget.dataset;

    // 加载用户角色
    await this.loadUserRoles(user.id);

    this.setData({
      showRoleModal: true,
      currentUser: user
    });
  },

  /**
   * 加载用户角色
   */
  async loadUserRoles(userId) {
    try {
      const token = wx.getStorageSync('token');

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + `/api/v1/oa/permissions/users/${userId}/roles`,
          method: 'GET',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.success) {
        this.setData({
          userRoles: res.data.data.map(r => r.id)
        });
      }
    } catch (error) {
      console.error('加载用户角色失败:', error);
    }
  },

  /**
   * 隐藏角色分配模态框
   */
  hideRoleModal() {
    this.setData({
      showRoleModal: false,
      currentUser: null,
      userRoles: []
    });
  },

  /**
   * 角色选择切换
   */
  onRoleToggle(e) {
    const { roleId } = e.currentTarget.dataset;
    const userRoles = [...this.data.userRoles];

    const index = userRoles.indexOf(roleId);
    if (index > -1) {
      userRoles.splice(index, 1);
    } else {
      userRoles.push(roleId);
    }

    this.setData({ userRoles });
  },

  /**
   * 保存用户角色
   */
  async saveUserRoles() {
    if (this.data.submitting) return;

    this.setData({ submitting: true });

    try {
      const { currentUser, userRoles } = this.data;
      const token = wx.getStorageSync('token');

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + `/api/v1/oa/permissions/users/${currentUser.id}/roles`,
          method: 'PUT',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          data: {
            roleIds: userRoles
          },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.success) {
        wx.showToast({
          title: '角色分配成功',
          icon: 'success'
        });

        this.hideRoleModal();
        this.loadUsers(true);
      } else {
        throw new Error(res.data.message || '分配失败');
      }
    } catch (error) {
      console.error('保存用户角色失败:', error);
      wx.showToast({
        title: error.message || '分配失败',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  }
});
