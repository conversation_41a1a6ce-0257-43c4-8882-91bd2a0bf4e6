// pages/oa/permission/roles/roles.js

const { getCurrentUserInfo, getUserPermissions } = require('../../../../utils/user-info');
const { formatDate } = require('../../../../utils/format');
const authHelper = require('../../../../utils/auth-helper.js');
const { request } = require('../../../../utils/request.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: null,
    permissions: null,
    
    // 角色列表
    roles: [
      {
        id: 1,
        name: '系统管理员',
        code: 'admin',
        description: '拥有系统所有权限',
        level: 1,
        data_scope: 'all',
        is_active: true,
        statusLabel: '启用',
        statusColor: 'success',
        createdTimeAgo: '2024-01-01',
        permissionCount: 25
      },
      {
        id: 2,
        name: '部门经理',
        code: 'manager',
        description: '管理部门内的业务流程',
        level: 2,
        data_scope: 'department',
        is_active: true,
        statusLabel: '启用',
        statusColor: 'success',
        createdTimeAgo: '2024-01-02',
        permissionCount: 15
      },
      {
        id: 3,
        name: '普通员工',
        code: 'employee',
        description: '基础业务操作权限',
        level: 3,
        data_scope: 'self',
        is_active: true,
        statusLabel: '启用',
        statusColor: 'success',
        createdTimeAgo: '2024-01-03',
        permissionCount: 8
      }
    ],
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      hasMore: true
    },
    
    // 筛选和搜索
    filters: {
      level: 'all',
      status: 'all',
      keyword: ''
    },
    
    // 角色级别选项
    levelOptions: [
      { value: 'all', label: '全部级别' },
      { value: '1', label: '一级（管理员）' },
      { value: '2', label: '二级（经理）' },
      { value: '3', label: '三级（专员）' },
      { value: '4', label: '四级（员工）' }
    ],
    
    statusOptions: [
      { value: 'all', label: '全部状态' },
      { value: 'active', label: '启用' },
      { value: 'inactive', label: '停用' }
    ],
    
    // 选择器索引
    levelIndex: 0,
    statusIndex: 0,
    
    // 显示标签
    selectedLevelLabel: '全部级别',
    selectedStatusLabel: '全部状态',
    
    // 页面状态
    loading: false,
    refreshing: false,
    showFilters: false,
    
    // 角色操作
    showRoleModal: false,
    currentRole: null,
    roleForm: {
      name: '',
      code: '',
      description: '',
      level: 3,
      dataScope: 'self',
      isActive: true
    },
    
    // 数据权限选项
    dataScopeOptions: [
      { value: 'all', label: '全部数据' },
      { value: 'department', label: '部门数据' },
      { value: 'self', label: '个人数据' }
    ],
    
    // 权限配置
    showPermissionModal: false,
    allPermissions: [],
    rolePermissions: [],
    permissionTree: [],
    
    // 表单验证
    errors: {},
    submitting: false,

    // 统计数据
    statistics: {
      total: 5,
      active: 4,
      inactive: 1,
      systemRoles: 2
    },

    // 计算属性 - 用于WXML中的复杂表达式
    selectedDataScopeIndex: 0, // 当前选中的数据权限索引
    permissionTreeWithCounts: [] // 带有权限计数的权限树
  },

  onLoad(options) {

    // 检查页面权限
    const hasPermission = authHelper.checkPagePermission(options, 'system:role:manage', () => {
      // 权限检查通过，初始化页面数据
      this.initPage();
    });

    if (!hasPermission) {
      return; // 权限检查失败，不继续执行
    }
  },

  onShow() {
    this.loadRoles(true);
  },

  onPullDownRefresh() {
    this.loadRoles(true).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    if (this.data.pagination.hasMore && !this.data.loading) {
      this.loadRoles(false);
    }
  },

  /**
   * 初始化页面
   */
  initPage() {
    const userInfo = getCurrentUserInfo();
    const permissions = getUserPermissions();
    
    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    
    // 检查角色管理权限
    if (!permissions.canManageRole) {
      wx.showModal({
        title: '没有权限',
        content: '您没有角色管理权限',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    this.setData({
      userInfo,
      permissions
    });
    
    // 初始化选择器索引
    this.updateFilterIndexes();

    // 加载权限列表
    this.loadPermissions();

    // 初始化计算属性
    this.updateComputedProperties();
  },

  /**
   * 更新筛选器索引
   */
  updateFilterIndexes() {
    const { filters, levelOptions, statusOptions } = this.data;

    // 更新级别索引
    const levelIndex = levelOptions.findIndex(item => item.value === filters.level);
    const selectedLevel = levelOptions[levelIndex >= 0 ? levelIndex : 0];

    // 更新状态索引
    const statusIndex = statusOptions.findIndex(item => item.value === filters.status);
    const selectedStatus = statusOptions[statusIndex >= 0 ? statusIndex : 0];

    this.setData({
      levelIndex: levelIndex >= 0 ? levelIndex : 0,
      statusIndex: statusIndex >= 0 ? statusIndex : 0,
      selectedLevelLabel: selectedLevel.label,
      selectedStatusLabel: selectedStatus.label
    });
  },

  /**
   * 更新计算属性
   */
  updateComputedProperties() {
    const { roleForm, dataScopeOptions, permissionTree, rolePermissions } = this.data;

    // 计算数据权限选择器索引
    const selectedDataScopeIndex = dataScopeOptions.findIndex(item => item.value === roleForm.dataScope);

    // 计算带有权限计数的权限树
    const permissionTreeWithCounts = permissionTree.map(module => {
      const selectedCount = module.permissions.filter(p => rolePermissions.includes(p.id)).length;
      return {
        ...module,
        selectedCount,
        totalCount: module.permissions.length
      };
    });

    this.setData({
      selectedDataScopeIndex: selectedDataScopeIndex >= 0 ? selectedDataScopeIndex : 0,
      permissionTreeWithCounts
    });
  },

  /**
   * 加载角色列表
   */
  async loadRoles(refresh = false) {
    if (this.data.loading) return;
    
    if (refresh) {
      this.setData({ 
        refreshing: true,
        'pagination.page': 1,
        'pagination.hasMore': true
      });
    } else {
      this.setData({ loading: true });
    }
    
    try {
      const { pagination, filters } = this.data;
      const token = wx.getStorageSync('access_token'); // 使用统一的token键名
      
      const params = {
        page: refresh ? 1 : pagination.page,
        limit: pagination.limit,
        ...filters
      };
      
      const queryString = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&');
      
      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + `/api/v1/oa/permissions/roles?${queryString}`,
          method: 'GET',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });
      
      if (res.data.success) {
        const { list, pagination: newPagination } = res.data.data;
        
        let roles = [];
        if (refresh) {
          roles = list;
        } else {
          roles = [...this.data.roles, ...list];
        }
        
        // 格式化数据
        const formattedRoles = roles.map(role => ({
          ...role,
          levelLabel: this.getLevelLabel(role.level),
          statusLabel: role.is_active ? '启用' : '停用',
          statusColor: role.is_active ? '#00A86B' : '#8E8E93',
          dataScopeLabel: this.getDataScopeLabel(role.data_scope),
          createdTimeAgo: this.formatTimeAgo(role.created_at)
        }));
        
        this.setData({
          roles: formattedRoles,
          'pagination.page': newPagination.page,
          'pagination.total': newPagination.total,
          'pagination.hasMore': newPagination.page < newPagination.pages
        });
        
        // 加载统计数据
        if (refresh) {
          this.loadStatistics();
        }
      } else {
        throw new Error(res.data.message || '加载失败');
      }
    } catch (error) {
      console.error('加载角色列表失败:', error);

      // 处理认证错误
      authHelper.handleAuthError(error);

      // 只有非认证错误才显示toast
      if (error.statusCode !== 401) {
        wx.showToast({
          title: error.message || '加载失败',
          icon: 'none'
        });
      }
    } finally {
      this.setData({ 
        loading: false, 
        refreshing: false 
      });
    }
  },

  /**
   * 加载统计数据
   */
  async loadStatistics() {
    try {
      const token = wx.getStorageSync('token');
      
      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + '/api/v1/oa/permissions/roles/statistics',
          method: 'GET',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });
      
      if (res.data.success) {
        this.setData({
          statistics: res.data.data
        });
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  },

  /**
   * 加载权限列表
   */
  async loadPermissions() {
    try {
      const token = wx.getStorageSync('token');
      
      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + '/api/v1/oa/permissions',
          method: 'GET',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });
      
      if (res.data.success) {
        const permissions = res.data.data;
        const permissionTree = this.buildPermissionTree(permissions);

        this.setData({
          allPermissions: permissions,
          permissionTree
        });

        // 更新计算属性
        this.updateComputedProperties();
      }
    } catch (error) {
      console.error('加载权限列表失败:', error);
    }
  },

  /**
   * 构建权限树
   */
  buildPermissionTree(permissions) {
    const tree = {};
    
    permissions.forEach(permission => {
      if (!tree[permission.module]) {
        tree[permission.module] = {
          name: this.getModuleLabel(permission.module),
          permissions: []
        };
      }
      tree[permission.module].permissions.push(permission);
    });
    
    return Object.keys(tree).map(key => ({
      module: key,
      ...tree[key]
    }));
  },

  /**
   * 获取模块标签
   */
  getModuleLabel(module) {
    const moduleMap = {
      system: '系统管理',
      reimbursement: '报销管理',
      approval: '审批管理',
      workflow: '流程管理',
      report: '报表管理'
    };
    return moduleMap[module] || module;
  },

  /**
   * 获取级别标签
   */
  getLevelLabel(level) {
    const levelMap = {
      1: '一级（管理员）',
      2: '二级（经理）',
      3: '三级（专员）',
      4: '四级（员工）'
    };
    return levelMap[level] || `${level}级`;
  },

  /**
   * 获取数据权限标签
   */
  getDataScopeLabel(dataScope) {
    const scopeMap = {
      all: '全部数据',
      department: '部门数据',
      self: '个人数据'
    };
    return scopeMap[dataScope] || dataScope;
  },

  /**
   * 格式化时间差
   */
  formatTimeAgo(timeStr) {
    const time = new Date(timeStr);
    const now = new Date();
    const diff = now - time;
    
    // 小于1小时显示分钟
    if (diff < 3600000) {
      const minutes = Math.floor(diff / 60000);
      return `${minutes}分钟前`;
    }
    
    // 小于24小时显示小时
    if (diff < 86400000) {
      const hours = Math.floor(diff / 3600000);
      return `${hours}小时前`;
    }
    
    // 超过24小时显示天数
    const days = Math.floor(diff / 86400000);
    return `${days}天前`;
  },

  /**
   * 显示/隐藏筛选器
   */
  toggleFilters() {
    this.setData({
      showFilters: !this.data.showFilters
    });
  },

  /**
   * 级别筛选
   */
  onLevelFilter(e) {
    const levelIndex = e.detail.value;
    const selectedLevel = this.data.levelOptions[levelIndex];

    this.setData({
      'filters.level': selectedLevel.value,
      levelIndex: levelIndex,
      selectedLevelLabel: selectedLevel.label
    });
    this.loadRoles(true);
  },

  /**
   * 状态筛选
   */
  onStatusFilter(e) {
    const statusIndex = e.detail.value;
    const selectedStatus = this.data.statusOptions[statusIndex];

    this.setData({
      'filters.status': selectedStatus.value,
      statusIndex: statusIndex,
      selectedStatusLabel: selectedStatus.label
    });
    this.loadRoles(true);
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({
      'filters.keyword': keyword
    });

    // 延迟搜索，避免频繁请求
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      this.loadRoles(true);
    }, 500);
  },

  /**
   * 清除筛选
   */
  clearFilters() {
    this.setData({
      filters: {
        level: 'all',
        status: 'all',
        keyword: ''
      },
      showFilters: false,
      levelIndex: 0,
      statusIndex: 0,
      selectedLevelLabel: '全部级别',
      selectedStatusLabel: '全部状态'
    });
    this.loadRoles(true);
  },

  /**
   * 创建新角色
   */
  onCreateRole() {
    this.setData({
      showRoleModal: true,
      currentRole: null,
      roleForm: {
        name: '',
        code: '',
        description: '',
        level: 3,
        dataScope: 'self',
        isActive: true
      },
      errors: {}
    });

    // 更新计算属性
    this.updateComputedProperties();
  },

  /**
   * 编辑角色
   */
  onEditRole(e) {
    const { role } = e.currentTarget.dataset;

    this.setData({
      showRoleModal: true,
      currentRole: role,
      roleForm: {
        name: role.name,
        code: role.code,
        description: role.description || '',
        level: role.level,
        dataScope: role.data_scope,
        isActive: role.is_active
      },
      errors: {}
    });

    // 更新计算属性
    this.updateComputedProperties();
  },

  /**
   * 配置权限
   */
  async onConfigPermissions(e) {
    const { role } = e.currentTarget.dataset;

    // 加载角色权限
    await this.loadRolePermissions(role.id);

    this.setData({
      showPermissionModal: true,
      currentRole: role
    });

    // 更新计算属性
    this.updateComputedProperties();
  },

  /**
   * 加载角色权限
   */
  async loadRolePermissions(roleId) {
    try {
      const token = wx.getStorageSync('token');

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + `/api/v1/oa/permissions/roles/${roleId}/permissions`,
          method: 'GET',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.success) {
        this.setData({
          rolePermissions: res.data.data.map(p => p.id)
        });

        // 更新计算属性
        this.updateComputedProperties();
      }
    } catch (error) {
      console.error('加载角色权限失败:', error);
    }
  },

  /**
   * 删除角色
   */
  onDeleteRole(e) {
    const { role } = e.currentTarget.dataset;

    if (role.is_system) {
      wx.showToast({
        title: '系统角色不能删除',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: `确定要删除角色"${role.name}"吗？删除后无法恢复。`,
      success: (res) => {
        if (res.confirm) {
          this.deleteRole(role.id);
        }
      }
    });
  },

  /**
   * 执行删除操作
   */
  async deleteRole(id) {
    try {
      const token = wx.getStorageSync('token');

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + `/api/v1/oa/permissions/roles/${id}`,
          method: 'DELETE',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        this.loadRoles(true);
      } else {
        throw new Error(res.data.message || '删除失败');
      }
    } catch (error) {
      console.error('删除角色失败:', error);
      wx.showToast({
        title: error.message || '删除失败',
        icon: 'none'
      });
    }
  },

  /**
   * 切换角色状态
   */
  onToggleStatus(e) {
    const { role } = e.currentTarget.dataset;
    const newStatus = !role.is_active;

    wx.showModal({
      title: '确认操作',
      content: `确定要${newStatus ? '启用' : '停用'}角色"${role.name}"吗？`,
      success: (res) => {
        if (res.confirm) {
          this.toggleRoleStatus(role.id, newStatus);
        }
      }
    });
  },

  /**
   * 执行状态切换
   */
  async toggleRoleStatus(id, isActive) {
    try {
      const token = wx.getStorageSync('token');

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + `/api/v1/oa/permissions/roles/${id}/status`,
          method: 'PUT',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          data: { isActive },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.success) {
        wx.showToast({
          title: isActive ? '启用成功' : '停用成功',
          icon: 'success'
        });
        this.loadRoles(true);
      } else {
        throw new Error(res.data.message || '操作失败');
      }
    } catch (error) {
      console.error('切换状态失败:', error);
      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 隐藏角色模态框
   */
  hideRoleModal() {
    this.setData({
      showRoleModal: false,
      currentRole: null,
      roleForm: {
        name: '',
        code: '',
        description: '',
        level: 3,
        dataScope: 'self',
        isActive: true
      },
      errors: {}
    });
  },

  /**
   * 隐藏权限配置模态框
   */
  hidePermissionModal() {
    this.setData({
      showPermissionModal: false,
      currentRole: null,
      rolePermissions: []
    });
  },

  /**
   * 表单输入处理
   */
  onFormInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`roleForm.${field}`]: value
    });

    // 清除对应字段的错误信息
    if (this.data.errors[field]) {
      this.setData({
        [`errors.${field}`]: ''
      });
    }
  },

  /**
   * 级别选择
   */
  onLevelSelect(e) {
    const level = parseInt(e.detail.value) + 1;
    this.setData({
      'roleForm.level': level
    });
  },

  /**
   * 数据权限选择
   */
  onDataScopeSelect(e) {
    const dataScopeIndex = e.detail.value;
    const dataScope = this.data.dataScopeOptions[dataScopeIndex].value;

    this.setData({
      'roleForm.dataScope': dataScope,
      selectedDataScopeIndex: dataScopeIndex
    });
  },

  /**
   * 状态开关切换
   */
  onStatusSwitch(e) {
    this.setData({
      'roleForm.isActive': e.detail.value
    });
  },

  /**
   * 权限选择切换
   */
  onPermissionToggle(e) {
    const { permissionId } = e.currentTarget.dataset;
    const rolePermissions = [...this.data.rolePermissions];

    const index = rolePermissions.indexOf(permissionId);
    if (index > -1) {
      rolePermissions.splice(index, 1);
    } else {
      rolePermissions.push(permissionId);
    }

    this.setData({ rolePermissions });

    // 更新计算属性
    this.updateComputedProperties();
  },

  /**
   * 模块权限全选/取消全选
   */
  onModuleToggle(e) {
    const { module } = e.currentTarget.dataset;
    const modulePermissions = this.data.permissionTree.find(m => m.module === module);

    if (!modulePermissions) return;

    const modulePermissionIds = modulePermissions.permissions.map(p => p.id);
    const rolePermissions = [...this.data.rolePermissions];

    // 检查是否全部选中
    const allSelected = modulePermissionIds.every(id => rolePermissions.includes(id));

    if (allSelected) {
      // 取消全选
      modulePermissionIds.forEach(id => {
        const index = rolePermissions.indexOf(id);
        if (index > -1) {
          rolePermissions.splice(index, 1);
        }
      });
    } else {
      // 全选
      modulePermissionIds.forEach(id => {
        if (!rolePermissions.includes(id)) {
          rolePermissions.push(id);
        }
      });
    }

    this.setData({ rolePermissions });

    // 更新计算属性
    this.updateComputedProperties();
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { roleForm } = this.data;
    const errors = {};

    if (!roleForm.name.trim()) {
      errors.name = '请输入角色名称';
    }

    if (!roleForm.code.trim()) {
      errors.code = '请输入角色代码';
    } else if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(roleForm.code)) {
      errors.code = '角色代码只能包含字母、数字和下划线，且以字母或下划线开头';
    }

    this.setData({ errors });
    return Object.keys(errors).length === 0;
  },

  /**
   * 保存角色
   */
  async saveRole() {
    if (this.data.submitting) return;

    // 表单验证
    if (!this.validateForm()) {
      return;
    }

    this.setData({ submitting: true });

    try {
      const { roleForm, currentRole } = this.data;
      const token = wx.getStorageSync('token');

      const isEdit = !!currentRole;
      const url = isEdit
        ? `/api/v1/oa/permissions/roles/${currentRole.id}`
        : '/api/v1/oa/permissions/roles';

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + url,
          method: isEdit ? 'PUT' : 'POST',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          data: {
            name: roleForm.name,
            code: roleForm.code,
            description: roleForm.description,
            level: roleForm.level,
            dataScope: roleForm.dataScope,
            isActive: roleForm.isActive
          },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.success) {
        wx.showToast({
          title: isEdit ? '更新成功' : '创建成功',
          icon: 'success'
        });

        this.hideRoleModal();
        this.loadRoles(true);
      } else {
        throw new Error(res.data.message || '保存失败');
      }
    } catch (error) {
      console.error('保存角色失败:', error);
      wx.showToast({
        title: error.message || '保存失败',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  /**
   * 保存权限配置
   */
  async savePermissions() {
    if (this.data.submitting) return;

    this.setData({ submitting: true });

    try {
      const { currentRole, rolePermissions } = this.data;
      const token = wx.getStorageSync('token');

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + `/api/v1/oa/permissions/roles/${currentRole.id}/permissions`,
          method: 'PUT',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          data: {
            permissionIds: rolePermissions
          },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.success) {
        wx.showToast({
          title: '权限配置成功',
          icon: 'success'
        });

        this.hidePermissionModal();
      } else {
        throw new Error(res.data.message || '配置失败');
      }
    } catch (error) {
      console.error('保存权限配置失败:', error);
      wx.showToast({
        title: error.message || '配置失败',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  }
});
