// pages/production/materials/detail/detail.js
Page({
  data: {
    materialId: '',
    materialInfo: {},
    stockInfo: {},
    records: [],
    timeRange: '7d',
    alertSettings: {
      lowStock: true,
      expiry: true,
      purchase: false
    }
  },

  onLoad: function (options) {
    const { id, name } = options;
    this.setData({
      materialId: id,
      materialName: decodeURIComponent(name || '')
    });
    
    this.loadMaterialDetail();
  },

  onPullDownRefresh: function () {
    this.loadMaterialDetail(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载物料详情
  loadMaterialDetail(callback) {
    wx.showLoading({
      title: '加载中...'
    });

    // 模拟API调用
    setTimeout(() => {
      const mockData = this.getMockMaterialDetail();
      this.setData({
        ...mockData
      });
      
      this.drawStockChart();
      wx.hideLoading();
      callback && callback();
    }, 1000);
  },

  // 获取模拟物料详情数据
  getMockMaterialDetail() {
    const materialInfo = {
      id: this.data.materialId,
      name: this.data.materialName || '优质鹅饲料',
      code: 'MT001',
      categoryName: '饲料',
      specification: '25kg/袋',
      unit: '袋',
      supplier: '绿野饲料有限公司',
      shelfLife: '6个月',
      status: 'normal',
      statusText: '正常',
      icon: '/assets/icons/feed.png'
    };

    const stockInfo = {
      currentStock: 156,
      safetyStock: 50,
      maxStock: 300,
      updateTime: this.formatTime(new Date())
    };

    const records = [
      {
        id: 1,
        type: 'in',
        title: '采购入库',
        description: '供应商：绿野饲料有限公司',
        amount: 50,
        time: '2小时前'
      },
      {
        id: 2,
        type: 'out',
        title: '生产领用',
        description: '用途：A区饲养',
        amount: 20,
        time: '5小时前'
      },
      {
        id: 3,
        type: 'out',
        title: '生产领用',
        description: '用途：B区饲养',
        amount: 15,
        time: '1天前'
      },
      {
        id: 4,
        type: 'in',
        title: '采购入库',
        description: '供应商：绿野饲料有限公司',
        amount: 100,
        time: '3天前'
      }
    ];

    return {
      materialInfo,
      stockInfo,
      records
    };
  },

  // 绘制库存趋势图
  async drawStockChart() {
    // 获取Canvas 2D上下文
    const query = wx.createSelectorQuery().in(this);
    const res = await new Promise((resolve) => {
      query.select('#stockChart')
        .fields({ node: true, size: true })
        .exec(resolve);
    });
    
    const canvas = res[0].node;
    const ctx = canvas.getContext('2d');
    
    // 生成模拟数据
    const data = this.generateStockData();
    
    if (!data || data.length === 0) return;

    // 设置画布尺寸
    const dpr = wx.getSystemInfoSync().pixelRatio;
    const canvasWidth = 300;
    const canvasHeight = 150;
    canvas.width = canvasWidth * dpr;
    canvas.height = canvasHeight * dpr;
    ctx.scale(dpr, dpr);
    
    const padding = 30;
    
    // 数据范围
    const stocks = data.map(item => item.stock);
    const minStock = Math.min(...stocks);
    const maxStock = Math.max(...stocks);
    const stockRange = maxStock - minStock || 1;
    
    // 清空画布
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);
    
    // 绘制网格线
    ctx.strokeStyle = '#f0f0f0';
    ctx.lineWidth = 1;
    for (let i = 0; i <= 3; i++) {
      const y = padding + (canvasHeight - 2 * padding) * i / 3;
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(canvasWidth - padding, y);
      ctx.stroke();
    }
    
    // 绘制安全库存线
    const safetyY = canvasHeight - padding - (this.data.stockInfo.safetyStock - minStock) / stockRange * (canvasHeight - 2 * padding);
    ctx.strokeStyle = '#faad14';
    ctx.lineWidth = 2;
    ctx.setLineDash([5, 5]);
    ctx.beginPath();
    ctx.moveTo(padding, safetyY);
    ctx.lineTo(canvasWidth - padding, safetyY);
    ctx.stroke();
    ctx.setLineDash([]);
    
    // 绘制库存线
    ctx.strokeStyle = '#0066cc';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    data.forEach((item, index) => {
      const x = padding + (canvasWidth - 2 * padding) * index / (data.length - 1);
      const y = canvasHeight - padding - (item.stock - minStock) / stockRange * (canvasHeight - 2 * padding);
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    
    ctx.stroke();
  },

  // 生成库存数据
  generateStockData() {
    const data = [];
    const days = this.data.timeRange === '7d' ? 7 : 30;
    let currentStock = this.data.stockInfo.currentStock || 100;
    
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() - (days - 1 - i));
      
      // 模拟库存变化
      if (Math.random() > 0.7) {
        currentStock += Math.random() > 0.5 ? 
          Math.floor(Math.random() * 50) : // 入库
          -Math.floor(Math.random() * 30); // 出库
      }
      
      currentStock = Math.max(currentStock, 0);
      
      data.push({
        date: this.formatDate(date),
        stock: currentStock
      });
    }
    
    return data;
  },

  // 切换时间范围
  onTimeRangeChange(e) {
    const range = e.currentTarget.dataset.range;
    this.setData({
      timeRange: range
    });
    this.drawStockChart();
  },

  // 查看全部记录
  onViewAllRecords() {
    wx.navigateTo({
      url: `/pages/production/materials/records/records?id=${this.data.materialId}`
    });
  },

  // 点击记录项
  onRecordTap(e) {
    const recordId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/production/materials/record-detail/record-detail?id=${recordId}`
    });
  },

  // 预警设置变更
  onAlertChange(e) {
    const type = e.currentTarget.dataset.type;
    const value = e.detail.value;
    
    this.setData({
      [`alertSettings.${type}`]: value
    });
    
    // 保存设置
    wx.setStorageSync(`alert_${this.data.materialId}_${type}`, value);
    
    wx.showToast({
      title: value ? '已开启预警' : '已关闭预警',
      icon: 'success'
    });
  },

  // 入库操作
  onInStockTap() {
    wx.navigateTo({
      url: `/pages/production/materials/in-stock/in-stock?id=${this.data.materialId}`
    });
  },

  // 出库操作
  onOutStockTap() {
    wx.navigateTo({
      url: `/pages/production/materials/out-stock/out-stock?id=${this.data.materialId}`
    });
  },

  // 采购申请
  onPurchaseTap() {
    wx.navigateTo({
      url: `/pages/production/materials/purchase/purchase?id=${this.data.materialId}`
    });
  },

  // 格式化时间
  formatTime(date) {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${month}月${day}日 ${hours}:${minutes}`;
  },

  // 格式化日期
  formatDate(date) {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}-${day}`;
  }
});
