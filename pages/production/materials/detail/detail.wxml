<!-- pages/production/materials/detail/detail.wxml -->
<view class="material-detail-container">
  <!-- 物料基本信息 -->
  <view class="info-section">
    <view class="material-header">
      <view class="material-icon">
        <image src="{{materialInfo.icon}}" mode="aspectFit"></image>
      </view>
      <view class="material-basic">
        <text class="material-name">{{materialInfo.name}}</text>
        <text class="material-category">{{materialInfo.categoryName}}</text>
        <text class="material-code">编号: {{materialInfo.code}}</text>
      </view>
      <view class="material-status">
        <text class="status-text {{materialInfo.status}}">{{materialInfo.statusText}}</text>
      </view>
    </view>
    
    <view class="material-specs">
      <view class="spec-item">
        <text class="spec-label">规格</text>
        <text class="spec-value">{{materialInfo.specification}}</text>
      </view>
      <view class="spec-item">
        <text class="spec-label">单位</text>
        <text class="spec-value">{{materialInfo.unit}}</text>
      </view>
      <view class="spec-item">
        <text class="spec-label">供应商</text>
        <text class="spec-value">{{materialInfo.supplier}}</text>
      </view>
      <view class="spec-item">
        <text class="spec-label">保质期</text>
        <text class="spec-value">{{materialInfo.shelfLife}}</text>
      </view>
    </view>
  </view>

  <!-- 库存信息 -->
  <view class="stock-section">
    <view class="section-header">
      <text class="section-title">库存信息</text>
      <text class="update-time">更新: {{stockInfo.updateTime}}</text>
    </view>
    
    <view class="stock-overview">
      <view class="stock-item primary">
        <text class="stock-value">{{stockInfo.currentStock}}</text>
        <text class="stock-label">当前库存</text>
        <text class="stock-unit">{{materialInfo.unit}}</text>
      </view>
      <view class="stock-item">
        <text class="stock-value">{{stockInfo.safetyStock}}</text>
        <text class="stock-label">安全库存</text>
        <text class="stock-unit">{{materialInfo.unit}}</text>
      </view>
      <view class="stock-item">
        <text class="stock-value">{{stockInfo.maxStock}}</text>
        <text class="stock-label">最大库存</text>
        <text class="stock-unit">{{materialInfo.unit}}</text>
      </view>
    </view>
    
    <view class="stock-chart">
      <view class="chart-header">
        <text class="chart-title">库存趋势</text>
        <view class="time-filter">
          <view class="filter-item {{timeRange === '7d' ? 'active' : ''}}" data-range="7d" bindtap="onTimeRangeChange">7天</view>
          <view class="filter-item {{timeRange === '30d' ? 'active' : ''}}" data-range="30d" bindtap="onTimeRangeChange">30天</view>
        </view>
      </view>
      <canvas type="2d" id="stockChart" class="chart-canvas"></canvas>
    </view>
  </view>

  <!-- 出入库记录 -->
  <view class="record-section">
    <view class="section-header">
      <text class="section-title">出入库记录</text>
      <text class="more" bindtap="onViewAllRecords">查看全部</text>
    </view>
    
    <view class="record-list">
      <block wx:for="{{records}}" wx:key="id">
        <view class="record-item" bindtap="onRecordTap" data-id="{{item.id}}">
          <view class="record-icon {{item.type}}">
            <image src="/assets/icons/{{item.type === 'in' ? 'arrow_down' : 'arrow_up'}}.png" mode="aspectFit"></image>
          </view>
          <view class="record-info">
            <text class="record-title">{{item.title}}</text>
            <text class="record-desc">{{item.description}}</text>
            <text class="record-time">{{item.time}}</text>
          </view>
          <view class="record-amount {{item.type}}">
            <text class="amount-value">{{item.type === 'in' ? '+' : '-'}}{{item.amount}}</text>
            <text class="amount-unit">{{materialInfo.unit}}</text>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 预警设置 -->
  <view class="alert-section">
    <view class="section-title">预警设置</view>
    <view class="alert-settings">
      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-label">低库存预警</text>
          <text class="setting-desc">库存低于安全值时提醒</text>
        </view>
        <switch checked="{{alertSettings.lowStock}}" bindchange="onAlertChange" data-type="lowStock"></switch>
      </view>
      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-label">过期预警</text>
          <text class="setting-desc">临近保质期时提醒</text>
        </view>
        <switch checked="{{alertSettings.expiry}}" bindchange="onAlertChange" data-type="expiry"></switch>
      </view>
      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-label">采购建议</text>
          <text class="setting-desc">智能采购时机提醒</text>
        </view>
        <switch checked="{{alertSettings.purchase}}" bindchange="onAlertChange" data-type="purchase"></switch>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <button class="action-btn primary" bindtap="onInStockTap">入库</button>
    <button class="action-btn secondary" bindtap="onOutStockTap">出库</button>
    <button class="action-btn tertiary" bindtap="onPurchaseTap">采购申请</button>
  </view>
</view>
