// pages/production/finance/finance.js
const app = getApp();

Page({
  data: {
    // 当前用户角色
    userRole: 'user',

    // 时间范围选项
    timeRanges: [
      { label: '近一周', value: 'week' },
      { label: '近一月', value: 'month' },
      { label: '近三月', value: 'quarter' },
      { label: '近一年', value: 'year' }
    ],
    activeTimeRange: 'month',
    
    // 财务概览数据
    financeOverview: {
      income: 0,        // 收入
      expense: 0,       // 支出
      profit: 0,        // 利润
      profitMargin: 0   // 利润率
    },
    

    
    // 收入趋势数据
    incomeTrend: [],
    
    // 支出趋势数据
    expenseTrend: [],
    
    // 财务记录列表
    financeRecords: [],
    allFinanceRecords: [], // 存储所有记录用于筛选
    recordFilter: 'all', // 记录筛选类型：all, income, expense

    // AI分析相关
    aiAnalysisResult: null,
    isAnalyzing: false,

    // 报销相关数据
    reimbursementStats: {
      pendingCount: 0,
      myCount: 0,
      monthlyAmount: 0
    },
    recentReimbursements: [],

    loading: true,
    pageNum: 1,
    pageSize: 10,
    hasMore: true,

    // 图表相关
    selectedChartType: '', // 当前选择的图表类型：income, expense, profit, margin
    chartTitle: '',
    chartData: [],
    chartColor: '#4CAF50',

    // 新的统一图表数据
    financeChartData: [],
    financeChartConfig: {
      type: 'line',
      smooth: true,
      showPoints: true,
      showGrid: true,
      animation: true
    },
    financeLegendData: [],
    updateTime: '',

    // 添加记录弹窗
    showAddModal: false,
    newRecord: {
      type: '支出',
      category: '',
      categoryIndex: 0,
      amount: '',
      remark: '',
      date: ''
    },
    categoryOptions: [],
    canSubmit: false,

    // 记录详情弹窗
    showDetailModal: false,
    selectedRecord: {}
  },

  onLoad: function (options) {
    // 获取用户角色
    const userRole = this.getUserRole();

    // 初始化日期为今天
    const today = new Date();
    const dateStr = today.getFullYear() + '-' +
                   String(today.getMonth() + 1).padStart(2, '0') + '-' +
                   String(today.getDate()).padStart(2, '0');

    this.setData({
      userRole: userRole,
      'newRecord.date': dateStr
    });

    // 初始化分类选项
    this.initCategoryOptions();

    // 验证用户权限 - 临时禁用权限验证进行调试

    // 临时注释掉权限验证，让所有用户都能访问
    /*
    if (this.data.userRole !== 'admin' && this.data.userRole !== 'manager') {
      wx.showModal({
        title: '权限不足',
        content: '您没有权限访问财务管理模块',
        showCancel: false,
        success: function() {
          wx.navigateBack();
        }
      });
      return;
    }
    */

    // 页面加载时获取数据
    this.loadData();
  },

  onShow: function () {
    // 页面显示
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.setData({
      pageNum: 1,
      hasMore: true
    }, () => {
      this.loadData(() => {
        wx.stopPullDownRefresh();
      });
    });
  },

  // 上拉加载更多
  onReachBottom: function () {
    if (this.data.hasMore) {
      this.loadMore();
    }
  },

  // 获取用户角色
  getUserRole: function() {
    const app = getApp();
    const globalUserInfo = app.globalData.userInfo || {};
    const storageUserInfo = wx.getStorageSync('userInfo') || {};


    const userInfo = globalUserInfo.username ? globalUserInfo : storageUserInfo;

    return userInfo.role || 'user';
  },

  // 财务管理页面不需要标签页切换功能

  // 加载数据
  loadData: function (callback) {
    const { pageNum, pageSize, activeTimeRange } = this.data;

    // 显示加载状态
    if (pageNum === 1) {
      this.setData({
        loading: true
      });
    }

    // 模拟加载数据
    setTimeout(() => {
      // 根据时间范围生成不同的数据
      const dataByTimeRange = this.generateDataByTimeRange(activeTimeRange);

      // 模拟财务概览数据
      const financeOverview = {
        income: dataByTimeRange.income,
        expense: dataByTimeRange.expense,
        profit: dataByTimeRange.income - dataByTimeRange.expense,
        profitMargin: ((dataByTimeRange.income - dataByTimeRange.expense) / dataByTimeRange.income * 100).toFixed(2)
      };
      

      
      // 模拟收入趋势数据
      const incomeTrend = [
        { month: '1月', value: 85000 },
        { month: '2月', value: 78000 },
        { month: '3月', value: 95000 },
        { month: '4月', value: 88000 },
        { month: '5月', value: 92000 },
        { month: '6月', value: 96000 }
      ];
      
      // 模拟支出趋势数据
      const expenseTrend = [
        { month: '1月', value: 72000 },
        { month: '2月', value: 68000 },
        { month: '3月', value: 80000 },
        { month: '4月', value: 75000 },
        { month: '5月', value: 78500 },
        { month: '6月', value: 81000 }
      ];
      
      // 模拟财务记录列表（带关联业务记录）
      const financeRecords = [
        {
          id: 1,
          type: '收入',
          category: '鹅只销售',
          amount: 25000,
          date: '2023-06-15',
          remark: '成鹅销售',
          canEdit: true,
          relatedRecord: {
            type: '出栏记录',
            description: '批次GS2024001 - 出栏150只成鹅',
            recordId: 'sale_001',
            batchNumber: 'GS2024001',
            quantity: 150,
            weight: 2250,
            buyer: '华联超市'
          }
        },
        {
          id: 2,
          type: '支出',
          category: '饲料费用',
          amount: 8000,
          date: '2023-06-12',
          remark: '购买育肥期饲料',
          canEdit: true,
          relatedRecord: {
            type: '采购记录',
            description: '育肥期饲料采购 - 2吨',
            recordId: 'purchase_001',
            supplier: '正大饲料',
            quantity: 2000,
            unit: 'kg'
          }
        },
        {
          id: 3,
          type: '支出',
          category: '药品费用',
          amount: 3500,
          date: '2023-06-10',
          remark: '疫苗采购',
          canEdit: true,
          relatedRecord: {
            type: '采购记录',
            description: '禽流感疫苗采购',
            recordId: 'purchase_002',
            supplier: '华兽药业',
            quantity: 500,
            unit: '支'
          }
        },
        {
          id: 4,
          type: '收入',
          category: '鹅蛋销售',
          amount: 5000,
          date: '2023-06-08',
          remark: '鹅蛋销售',
          canEdit: true,
          relatedRecord: {
            type: '销售记录',
            description: '鹅蛋销售 - 1000枚',
            recordId: 'egg_sale_001',
            quantity: 1000,
            unit: '枚',
            buyer: '农贸市场'
          }
        },
        {
          id: 5,
          type: '支出',
          category: '人工成本',
          amount: 4000,
          date: '2023-06-05',
          remark: '工人工资',
          canEdit: false
        }
      ];
      
      const hasMore = pageNum === 1 && financeRecords.length === pageSize;

      // 存储所有记录用于筛选
      const allRecords = pageNum === 1 ? financeRecords : this.data.allFinanceRecords.concat(financeRecords);

      this.setData({
        financeOverview: financeOverview,
        incomeTrend: incomeTrend,
        expenseTrend: expenseTrend,
        allFinanceRecords: allRecords,
        hasMore: hasMore,
        loading: false
      });

      // 应用当前筛选
      this.applyRecordFilter();
      
      callback && callback();
    }, 500);
  },

  // 加载更多
  loadMore: function () {
    this.setData({
      pageNum: this.data.pageNum + 1
    }, () => {
      this.loadData();
    });
  },

  // 切换时间范围
  onTimeRangeChange: function (e) {
    const range = e.currentTarget.dataset.range;
    this.setData({
      activeTimeRange: range,
      pageNum: 1,
      hasMore: true
    });

    // 重新加载数据
    this.loadData();
  },

  // 添加财务记录
  onAddRecord: function () {
    wx.showToast({
      title: '添加财务记录功能开发中',
      icon: 'none'
    });
  },

  // 财务记录项点击
  onRecordItemTap: function(e) {
    const record = e.currentTarget.dataset.record;
    this.setData({
      selectedRecord: record,
      showDetailModal: true
    });
  },

  // 关闭详情弹窗
  onCloseDetailModal: function() {
    this.setData({
      showDetailModal: false,
      selectedRecord: {}
    });
  },

  // 查看关联记录
  onViewRelatedRecord: function() {
    const relatedRecord = this.data.selectedRecord.relatedRecord;
    if (!relatedRecord) return;

    // 根据关联记录类型跳转到相应页面
    switch (relatedRecord.type) {
      case '出栏记录':
        wx.navigateTo({
          url: `/pages/production/record-detail?id=${relatedRecord.recordId}&type=sale`
        });
        break;
      case '采购记录':
        wx.navigateTo({
          url: `/pages/production/materials/detail?id=${relatedRecord.recordId}&type=purchase`
        });
        break;
      case '销售记录':
        wx.navigateTo({
          url: `/pages/production/record-detail?id=${relatedRecord.recordId}&type=egg_sale`
        });
        break;
      default:
        wx.showToast({
          title: '暂无关联记录详情',
          icon: 'none'
        });
    }

    // 关闭详情弹窗
    this.onCloseDetailModal();
  },

  // 编辑记录
  onEditRecord: function() {
    const record = this.data.selectedRecord;
    // 将选中的记录数据填充到编辑表单
    this.setData({
      showDetailModal: false,
      showAddModal: true,
      newRecord: {
        type: record.type,
        category: record.category,
        categoryIndex: this.getCategoryIndex(record.category, record.type),
        amount: record.amount.toString(),
        remark: record.remark,
        date: record.date
      }
    });

    // 初始化分类选项
    this.initCategoryOptions();
    this.checkCanSubmit();
  },

  // 获取分类索引
  getCategoryIndex: function(category, type) {
    const options = type === '收入' ?
      ['鹅只销售', '鹅蛋销售', '羽毛销售', '其他收入'] :
      ['饲料费用', '药品费用', '人工成本', '设备维护', '其他支出'];
    return options.indexOf(category) || 0;
  },

  // 查看记录详情（保留原方法名兼容性）
  onViewRecord: function (e) {
    this.onRecordItemTap(e);
  },

  // 财务管理专用功能
  // 导出财务报表
  onExportReport: function() {
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    });
  },

  // 查看详细财务分析
  onViewDetailAnalysis: function() {
    wx.showToast({
      title: '详细分析功能开发中',
      icon: 'none'
    });
  },

  // 初始化分类选项
  initCategoryOptions: function() {
    this.setData({
      categoryOptions: this.data.newRecord.type === '收入' ?
        ['鹅只销售', '鹅蛋销售', '羽毛销售', '其他收入'] :
        ['饲料费用', '药品费用', '人工成本', '设备维护', '其他支出']
    });
  },

  // 根据时间范围生成数据
  generateDataByTimeRange: function(timeRange) {
    const baseData = {
      week: { income: 25000, expense: 18000 },
      month: { income: 92000, expense: 78500 },
      quarter: { income: 280000, expense: 235000 },
      year: { income: 1120000, expense: 950000 }
    };

    return baseData[timeRange] || baseData.month;
  },

  // 财务概览项点击
  onOverviewItemTap: function(e) {
    const type = e.currentTarget.dataset.type;
    let chartTitle = '';
    let chartColor = '#4CAF50';

    if (type === 'income') {
      chartTitle = '收入趋势';
      chartColor = '#4CAF50';
    } else if (type === 'expense') {
      chartTitle = '支出趋势';
      chartColor = '#FF5722';
    } else if (type === 'profit') {
      chartTitle = '利润趋势';
      chartColor = '#2196F3';
    } else if (type === 'margin') {
      chartTitle = '利润率趋势';
      chartColor = '#FF9800';
    }

    this.setData({
      selectedChartType: type,
      chartTitle: chartTitle,
      chartColor: chartColor,
      updateTime: new Date().toLocaleString()
    });

    // 生成新格式的图表数据
    this.generateFinanceChartData(type, this.data.activeTimeRange);
  },

  // 关闭图表
  onCloseChart: function() {
    this.setData({
      selectedChartType: '',
      chartTitle: '',
      chartData: [],
      chartColor: '#4CAF50'
    });
  },

  // 记录筛选切换
  onRecordFilterChange: function(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({
      recordFilter: filter
    });
    this.applyRecordFilter();
  },

  // 应用记录筛选
  applyRecordFilter: function() {
    const { allFinanceRecords, recordFilter } = this.data;
    let filteredRecords = allFinanceRecords;

    if (recordFilter === 'income') {
      filteredRecords = allFinanceRecords.filter(record => record.type === '收入');
    } else if (recordFilter === 'expense') {
      filteredRecords = allFinanceRecords.filter(record => record.type === '支出');
    }

    this.setData({
      financeRecords: filteredRecords
    });
  },

  // AI财务分析
  onAIAnalysis: function() {
    if (this.data.isAnalyzing) {
      wx.showToast({
        title: '正在分析中...',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isAnalyzing: true
    });

    wx.showLoading({
      title: 'AI分析中...'
    });

    this.performAIFinanceAnalysis();
  },

  // 执行AI财务分析
  async performAIFinanceAnalysis() {
    try {
      // 引入AI服务
      const { callAIService } = require('../../../utils/ai-service.js');

      // 构建财务数据分析提示
      const financeData = this.buildFinanceDataForAI();
      const userMessage = `
请分析以下养鹅场的财务数据：

${financeData}

请从以下几个方面进行专业分析：
1. 数据概况：总体财务状况评估
2. 关键指标：重要财务指标分析
3. 趋势分析：收支趋势和发展方向
4. 改进建议：具体的经营改进建议
5. 风险提示：潜在的财务风险和应对策略

请用专业但易懂的语言进行分析，提供具体可操作的建议。
      `.trim();

      // 调用AI财务分析服务
      const result = await callAIService('FINANCE_ANALYSIS', userMessage);

      wx.hideLoading();

      if (result.success) {
        // 解析AI分析结果
        const analysisResult = this.parseAIAnalysisResult(result.data.content);

        this.setData({
          aiAnalysisResult: analysisResult,
          isAnalyzing: false
        });

        wx.showToast({
          title: 'AI分析完成',
          icon: 'success'
        });

      } else {
        // AI分析失败
        wx.showModal({
          title: 'AI分析失败',
          content: result.error.message || '分析服务暂时不可用，请稍后重试',
          showCancel: false
        });

        this.setData({
          isAnalyzing: false
        });
      }

    } catch (error) {
      console.error('AI财务分析错误:', error);

      wx.hideLoading();
      wx.showModal({
        title: '分析失败',
        content: '网络连接异常，请检查网络后重试',
        showCancel: false
      });

      this.setData({
        isAnalyzing: false
      });
    }
  },

  // 构建用于AI分析的财务数据
  buildFinanceDataForAI() {
    const { financeOverview, incomeTrend, expenseTrend, allFinanceRecords, activeTimeRange } = this.data;

    // 计算收支分类统计
    const incomeByCategory = {};
    const expenseByCategory = {};

    allFinanceRecords.forEach(record => {
      if (record.type === '收入') {
        incomeByCategory[record.category] = (incomeByCategory[record.category] || 0) + Math.abs(record.amount);
      } else {
        expenseByCategory[record.category] = (expenseByCategory[record.category] || 0) + Math.abs(record.amount);
      }
    });

    return `
时间范围：${this.getTimeRangeName(activeTimeRange)}

财务概览：
- 总收入：¥${financeOverview.income}
- 总支出：¥${financeOverview.expense}
- 净利润：¥${financeOverview.profit}
- 利润率：${financeOverview.profitMargin}%

收入趋势：${incomeTrend.map(item => `${item.month}月: ¥${item.value}`).join(', ')}

支出趋势：${expenseTrend.map(item => `${item.month}月: ¥${item.value}`).join(', ')}

收入分类统计：
${Object.entries(incomeByCategory).map(([category, amount]) => `- ${category}: ¥${amount}`).join('\n')}

支出分类统计：
${Object.entries(expenseByCategory).map(([category, amount]) => `- ${category}: ¥${amount}`).join('\n')}

最近财务记录（前10条）：
${allFinanceRecords.slice(0, 10).map(record =>
  `${record.date} ${record.type} ${record.category} ¥${Math.abs(record.amount)} ${record.description}`
).join('\n')}
    `.trim();
  },

  // 获取时间范围名称
  getTimeRangeName(rangeId) {
    const ranges = {
      'week': '本周',
      'month': '本月',
      'quarter': '本季度',
      'year': '本年'
    };
    return ranges[rangeId] || '本月';
  },

  // 解析AI分析结果
  parseAIAnalysisResult(content) {
    try {
      const sections = {
        overview: '',
        keyIndicators: '',
        trends: '',
        suggestions: '',
        risks: ''
      };

      const lines = content.split('\n').filter(line => line.trim());
      let currentSection = '';

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();

        // 识别章节标题
        if (line.includes('数据概况') || line.includes('总体') || line.includes('概况')) {
          currentSection = 'overview';
          continue;
        } else if (line.includes('关键指标') || line.includes('重要指标')) {
          currentSection = 'keyIndicators';
          continue;
        } else if (line.includes('趋势分析') || line.includes('发展趋势')) {
          currentSection = 'trends';
          continue;
        } else if (line.includes('改进建议') || line.includes('建议')) {
          currentSection = 'suggestions';
          continue;
        } else if (line.includes('风险提示') || line.includes('风险')) {
          currentSection = 'risks';
          continue;
        }

        // 添加内容到对应章节
        if (currentSection && line && !line.match(/^\d+[\.、]/)) {
          sections[currentSection] += (sections[currentSection] ? '\n' : '') + line;
        }
      }

      // 如果解析失败，使用原始内容
      if (!sections.overview && !sections.keyIndicators) {
        sections.overview = content.substring(0, 500) + (content.length > 500 ? '...' : '');
      }

      return sections;

    } catch (error) {
      console.error('解析AI分析结果失败:', error);
      return {
        overview: content.substring(0, 500) + (content.length > 500 ? '...' : ''),
        keyIndicators: '',
        trends: '',
        suggestions: '',
        risks: ''
      };
    }
  },

  // 关闭AI分析结果
  onCloseAIAnalysis: function() {
    this.setData({
      aiAnalysisResult: null
    });
  },



  // 打开添加记录弹窗
  onAddRecord: function() {
    this.setData({
      showAddModal: true
    });
    this.initCategoryOptions();
  },

  // 关闭添加记录弹窗
  onCloseAddModal: function() {
    this.setData({
      showAddModal: false,
      newRecord: {
        type: '支出',
        category: '',
        categoryIndex: 0,
        amount: '',
        remark: '',
        date: this.data.newRecord.date
      },
      canSubmit: false
    });
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    // 空函数，用于阻止事件冒泡
  },

  // 记录类型改变
  onRecordTypeChange: function(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      'newRecord.type': type,
      'newRecord.category': '',
      'newRecord.categoryIndex': 0
    });
    this.initCategoryOptions();
    this.checkCanSubmit();
  },

  // 分类选择改变
  onCategoryChange: function(e) {
    const index = e.detail.value;
    this.setData({
      'newRecord.categoryIndex': index,
      'newRecord.category': this.data.categoryOptions[index]
    });
    this.checkCanSubmit();
  },

  // 金额输入
  onAmountInput: function(e) {
    this.setData({
      'newRecord.amount': e.detail.value
    });
    this.checkCanSubmit();
  },

  // 备注输入
  onRemarkInput: function(e) {
    this.setData({
      'newRecord.remark': e.detail.value
    });
    this.checkCanSubmit();
  },

  // 日期选择
  onDateChange: function(e) {
    this.setData({
      'newRecord.date': e.detail.value
    });
    this.checkCanSubmit();
  },

  // 检查是否可以提交
  checkCanSubmit: function() {
    const { type, category, amount, date } = this.data.newRecord;
    const canSubmit = type && category && amount && date && parseFloat(amount) > 0;
    this.setData({
      canSubmit: canSubmit
    });
  },

  // 确认添加记录
  onConfirmAdd: function() {
    if (!this.data.canSubmit) {
      return;
    }

    const newRecord = {
      id: Date.now(),
      type: this.data.newRecord.type,
      category: this.data.newRecord.category,
      amount: parseFloat(this.data.newRecord.amount),
      date: this.data.newRecord.date,
      remark: this.data.newRecord.remark || '无'
    };

    // 添加到记录列表
    const financeRecords = [newRecord, ...this.data.financeRecords];

    // 更新财务概览数据
    const financeOverview = { ...this.data.financeOverview };
    if (newRecord.type === '收入') {
      financeOverview.income += newRecord.amount;
    } else {
      financeOverview.expense += newRecord.amount;
    }
    financeOverview.profit = financeOverview.income - financeOverview.expense;
    financeOverview.profitMargin = financeOverview.income > 0 ?
      ((financeOverview.profit / financeOverview.income) * 100).toFixed(2) : 0;

    this.setData({
      financeRecords: financeRecords,
      financeOverview: financeOverview
    });

    wx.showToast({
      title: '添加成功',
      icon: 'success'
    });

    this.onCloseAddModal();
  },

  // 生成财务图表数据
  generateFinanceChartData: function(type, timeRange) {
    const chartData = [];
    const now = new Date();

    // 根据时间范围生成数据点
    let dataCount, timeFormat, timeStep;
    switch (timeRange) {
      case 'week':
        dataCount = 7;
        timeFormat = 'day';
        timeStep = 1;
        break;
      case 'month':
        dataCount = 30;
        timeFormat = 'day';
        timeStep = 1;
        break;
      case 'quarter':
        dataCount = 12;
        timeFormat = 'week';
        timeStep = 7;
        break;
      case 'year':
        dataCount = 12;
        timeFormat = 'month';
        timeStep = 30;
        break;
      default:
        dataCount = 30;
        timeFormat = 'day';
        timeStep = 1;
    }

    // 根据图表类型生成数据
    for (let i = dataCount - 1; i >= 0; i--) {
      const time = new Date(now);
      
      if (timeFormat === 'day') {
        time.setDate(time.getDate() - i * timeStep);
      } else if (timeFormat === 'week') {
        time.setDate(time.getDate() - i * timeStep);
      } else if (timeFormat === 'month') {
        time.setMonth(time.getMonth() - i);
      }

      let value = 0;
      const progress = i / dataCount;

      // 根据类型生成不同的趋势数据
      switch (type) {
        case 'income':
          // 收入趋势：基本稳定，略有增长
          const incomeBase = 15000 + progress * 5000;
          const incomeVariation = Math.random() * 4000 - 2000;
          value = Math.max(0, incomeBase + incomeVariation);
          break;
        case 'expense':
          // 支出趋势：相对稳定
          const expenseBase = 12000 + progress * 3000;
          const expenseVariation = Math.random() * 3000 - 1500;
          value = Math.max(0, expenseBase + expenseVariation);
          break;
        case 'profit':
          // 利润趋势：波动较大
          const profitBase = 3000 + progress * 2000;
          const profitVariation = Math.random() * 2000 - 1000;
          value = profitBase + profitVariation;
          break;
        case 'margin':
          // 利润率趋势：百分比
          const marginBase = 15 + progress * 10;
          const marginVariation = Math.random() * 8 - 4;
          value = Math.max(0, Math.min(100, marginBase + marginVariation));
          break;
      }

      chartData.push({
        time: this.formatFinanceChartTime(time, timeFormat),
        value: Math.round(value * 100) / 100
      });
    }

    // 设置图例数据
    const legendData = [{
      key: type,
      label: this.data.chartTitle,
      color: this.data.chartColor
    }];

    this.setData({
      financeChartData: chartData,
      financeLegendData: legendData
    });
  },

  // 格式化财务图表时间显示
  formatFinanceChartTime: function(date, format) {
    switch (format) {
      case 'day':
        return `${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
      case 'week':
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        return `${(weekStart.getMonth() + 1).toString().padStart(2, '0')}-${weekStart.getDate().toString().padStart(2, '0')}`;
      case 'month':
        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
      default:
        return date.toLocaleDateString();
    }
  },

  // 财务图表时间范围改变
  onFinanceTimeRangeChange: function(e) {
    const timeRange = e.detail.value;
    this.setData({
      activeTimeRange: timeRange
    });
    
    // 重新生成图表数据
    if (this.data.selectedChartType) {
      this.generateFinanceChartData(this.data.selectedChartType, timeRange);
    }
  },

  // 财务图表渲染完成
  onFinanceChartRendered: function(e) {
  },

  // 刷新财务图表
  onRefreshFinanceChart: function() {
    if (this.data.selectedChartType) {
      this.generateFinanceChartData(this.data.selectedChartType, this.data.activeTimeRange);
      wx.showToast({
        title: '数据已刷新',
        icon: 'success',
        duration: 1500
      });
    }
  }
});