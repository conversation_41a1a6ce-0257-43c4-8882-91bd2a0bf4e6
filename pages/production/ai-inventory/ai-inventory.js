// pages/production/ai-inventory/ai-inventory.js
const { production } = require('../../../utils/api.js');
const { aiInventoryService } = require('../../../utils/ai-inventory-service.js');

Page({
  data: {
    currentStep: 'camera', // camera, preview, result, success
    cameraAuthorized: false,
    capturing: false,
    capturedImage: '',
    recognizing: false,
    recognizeProgress: 0,
    
    // 识别结果
    recognitionResult: {
      count: 0,
      confidence: 0,
      timestamp: '',
      model: '',
      details: '',
      warnings: '',
      analysis: {}
    },
    
    // 手动调整
    adjustedCount: 0,
    
    // 表单数据
    batchNumber: '',
    notes: '',
    
    // 保存状态
    saving: false,
    savedRecord: {}
  },

  onLoad: function (options) {
    this.checkCameraPermission();
    this.generateBatchNumber();
  },

  // 检查相机权限
  checkCameraPermission: function () {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.camera']) {
          this.setData({ cameraAuthorized: true });
        } else {
          this.setData({ cameraAuthorized: false });
        }
      }
    });
  },

  // 请求相机权限
  onRequestCameraPermission: function () {
    wx.authorize({
      scope: 'scope.camera',
      success: () => {
        this.setData({ cameraAuthorized: true });
      },
      fail: () => {
        wx.showModal({
          title: '权限申请',
          content: '需要相机权限才能使用AI盘点功能，请在设置中开启',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting();
            }
          }
        });
      }
    });
  },

  // 生成批次编号
  generateBatchNumber: function () {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    
    const batchNumber = `AI${year}${month}${day}${hour}${minute}`;
    this.setData({ batchNumber });
  },

  // 拍照
  onTakePhoto: function () {
    if (this.data.capturing) return;
    
    this.setData({ capturing: true });
    
    const ctx = wx.createCameraContext();
    ctx.takePhoto({
      quality: 'high',
      success: (res) => {
        this.setData({
          capturedImage: res.tempImagePath,
          currentStep: 'preview',
          capturing: false
        });
      },
      fail: (err) => {
        console.error('拍照失败', err);
        wx.showToast({
          title: '拍照失败，请重试',
          icon: 'none'
        });
        this.setData({ capturing: false });
      }
    });
  },

  // 从相册选择
  onSelectFromAlbum: function () {
    wx.chooseImage({
      count: 1,
      sizeType: ['original'],
      sourceType: ['album'],
      success: (res) => {
        this.setData({
          capturedImage: res.tempFilePaths[0],
          currentStep: 'preview'
        });
      }
    });
  },

  // 重新拍照
  onRetakePhoto: function () {
    this.setData({
      currentStep: 'camera',
      capturedImage: '',
      recognitionResult: {
        count: 0,
        confidence: 0,
        timestamp: '',
        model: '',
        details: '',
        warnings: '',
        analysis: {}
      },
      adjustedCount: 0
    });
  },

  // 开始AI识别
  onStartRecognition: function () {
    if (this.data.recognizing) return;
    
    this.setData({ 
      recognizing: true,
      recognizeProgress: 0
    });

    // 模拟识别进度
    this.simulateRecognitionProgress();

    // 上传图片并进行AI识别
    this.uploadAndRecognize();
  },

  // 模拟识别进度
  simulateRecognitionProgress: function () {
    let progress = 0;
    const timer = setInterval(() => {
      progress += Math.random() * 15;
      if (progress >= 95) {
        progress = 95;
        clearInterval(timer);
      }
      this.setData({ recognizeProgress: Math.floor(progress) });
    }, 200);
    
    // 保存timer以便清理
    this.progressTimer = timer;
  },

  // 使用AI服务进行识别
  uploadAndRecognize: function () {
    const imagePath = this.data.capturedImage;

    // 构建识别选项
    const options = {
      minConfidence: 70,
      batchNumber: this.data.batchNumber,
      expectedRange: {
        min: 1,
        max: 1000
      }
    };

    // 调用AI盘点服务
    aiInventoryService.recognizeGooseCount(imagePath, options)
      .then(result => {

        if (result.success) {
          const timestamp = new Date().toLocaleString();
          const recognitionResult = {
            count: result.data.count,
            confidence: result.data.confidence,
            timestamp: timestamp,
            model: result.data.model || 'AI模型',
            details: result.data.details || '',
            warnings: result.data.warnings || '',
            analysis: result.data.analysis || {}
          };

          this.setData({
            recognitionResult,
            adjustedCount: result.data.count,
            recognizing: false,
            recognizeProgress: 100,
            currentStep: 'result'
          });

          // 清理进度定时器
          if (this.progressTimer) {
            clearInterval(this.progressTimer);
          }

          // 如果置信度较低，显示警告
          if (result.data.confidence < 70) {
            wx.showModal({
              title: '识别置信度较低',
              content: `AI识别置信度为${result.data.confidence}%，建议仔细核对结果或重新拍照。`,
              showCancel: false,
              confirmText: '知道了'
            });
          }

        } else {
          // 处理识别失败
          let errorMessage = result.error.message || '识别失败';

          // 如果有备用结果，询问是否使用
          if (result.error.fallbackResult) {
            wx.showModal({
              title: '识别结果不确定',
              content: `AI识别遇到问题，但检测到可能有${result.error.fallbackResult.count}只鹅。是否使用此结果？`,
              success: (res) => {
                if (res.confirm) {
                  const recognitionResult = {
                    count: result.error.fallbackResult.count,
                    confidence: result.error.fallbackResult.confidence,
                    timestamp: new Date().toLocaleString(),
                    model: 'AI模型(备用)',
                    details: result.error.fallbackResult.details,
                    warnings: result.error.fallbackResult.warnings,
                    analysis: {}
                  };

                  this.setData({
                    recognitionResult,
                    adjustedCount: result.error.fallbackResult.count,
                    recognizing: false,
                    recognizeProgress: 100,
                    currentStep: 'result'
                  });

                  if (this.progressTimer) {
                    clearInterval(this.progressTimer);
                  }
                } else {
                  this.handleRecognitionError(errorMessage);
                }
              }
            });
          } else {
            this.handleRecognitionError(errorMessage);
          }
        }
      })
      .catch(error => {
        console.error('AI识别服务调用失败:', error);
        this.handleRecognitionError(error.message || '识别服务调用失败');
      });
  },

  // 处理识别错误
  handleRecognitionError: function (message) {
    this.setData({
      recognizing: false,
      recognizeProgress: 0
    });
    
    if (this.progressTimer) {
      clearInterval(this.progressTimer);
    }
    
    wx.showModal({
      title: '识别失败',
      content: message || '请检查网络连接或重新拍照',
      confirmText: '重新拍照',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.onRetakePhoto();
        }
      }
    });
  },

  // 数量调整
  onDecrease: function () {
    const count = Math.max(0, this.data.adjustedCount - 1);
    this.setData({ adjustedCount: count });
  },

  onIncrease: function () {
    const count = this.data.adjustedCount + 1;
    this.setData({ adjustedCount: count });
  },

  onCountInput: function (e) {
    const count = parseInt(e.detail.value) || 0;
    this.setData({ adjustedCount: Math.max(0, count) });
  },

  // 表单输入
  onBatchNumberInput: function (e) {
    this.setData({ batchNumber: e.detail.value });
  },

  onNotesInput: function (e) {
    this.setData({ notes: e.detail.value });
  },

  // 保存记录
  onSaveRecord: function () {
    if (!this.data.batchNumber.trim()) {
      wx.showToast({
        title: '请输入批次编号',
        icon: 'none'
      });
      return;
    }

    this.setData({ saving: true });

    const recordData = {
      type: 'ai_inventory',
      batchNumber: this.data.batchNumber,
      originalCount: this.data.recognitionResult.count,
      finalCount: this.data.adjustedCount,
      confidence: this.data.recognitionResult.confidence,
      aiModel: this.data.recognitionResult.model,
      imagePath: this.data.capturedImage,
      notes: this.data.notes,
      timestamp: new Date().toISOString()
    };

    // 调用API保存记录
    production.saveAIInventoryRecord(recordData).then(res => {
      if (res.success) {
        const savedRecord = {
          count: this.data.adjustedCount,
          batchNumber: this.data.batchNumber,
          saveTime: new Date().toLocaleString()
        };
        
        this.setData({
          saving: false,
          savedRecord,
          currentStep: 'success'
        });
      } else {
        throw new Error(res.message || '保存失败');
      }
    }).catch(err => {
      console.error('保存记录失败', err);
      this.setData({ saving: false });
      wx.showToast({
        title: err.message || '保存失败，请重试',
        icon: 'none'
      });
    });
  },

  // 继续盘点
  onContinueInventory: function () {
    this.setData({
      currentStep: 'camera',
      capturedImage: '',
      recognitionResult: {
        count: 0,
        confidence: 0,
        timestamp: '',
        model: '',
        details: '',
        warnings: '',
        analysis: {}
      },
      adjustedCount: 0,
      notes: '',
      savedRecord: {}
    });
    this.generateBatchNumber();
  },

  // 查看记录
  onViewRecords: function () {
    wx.navigateTo({
      url: '/pages/production/records/records?type=ai_inventory'
    });
  },

  // 返回
  onGoBack: function () {
    wx.navigateBack();
  },

  // 相机事件
  onCameraError: function (e) {
    console.error('相机错误', e);
    wx.showToast({
      title: '相机启动失败',
      icon: 'none'
    });
  },

  onCameraStop: function (e) {
  },

  onCameraReady: function (e) {
  },

  onUnload: function () {
    // 清理定时器
    if (this.progressTimer) {
      clearInterval(this.progressTimer);
    }
  }
});
