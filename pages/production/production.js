// pages/production/production.js
const { IMAGES, UI, BUSINESS, API } = require('../../constants/index.js');
const request = require('../../utils/request.js');
const app = getApp();

/**
 * 生产管理页面 - 智慧养鹅小程序核心功能页面
 * 功能：环境监控、物料管理、生产记录、报销申请、财务管理
 * 重构完成：WXML组件化、性能优化、最佳实践应用
 */

Page({
  data: {
    activeTab: 0,
    tabs: [],
    allTabs: [
      { id: 0, name: '环境监控' },
      { id: 1, name: '物料管理' },
      { id: 2, name: '生产记录' },
      { id: 3, name: '报销申请' },
      { id: 4, name: '财务管理', requiredRole: ['admin', 'manager'] }
    ],
    // 页面状态
    loading: false,
    refreshing: false,
    lastRefreshTime: 0,
    environmentData: {
      temperature: 25.6,
      humidity: 62,
      pm25: 28,
      light: 850,
      temperatureStatus: 'normal',
      humidityStatus: 'normal',
      pm25Status: 'normal',
      lightStatus: 'normal',
      updateTime: '2024-12-30 14:30:25'
    },
    // 环境趋势数据
    environmentTrends: {
      temperature: [],
      humidity: [],
      pm25: [],
      light: [],
      timeLabels: []
    },

    // 环境阈值配置
    environmentThresholds: {
      temperature: { min: 18, max: 28, optimal: [20, 26] },
      humidity: { min: 40, max: 80, optimal: [50, 70] },
      pm25: { min: 0, max: 75, optimal: [0, 35] },
      light: { min: 200, max: 2000, optimal: [500, 1500] }
    },

    // 环境历史数据
    environmentHistoryData: [],

    // 选中的趋势类型
    selectedTrendType: '',

    // 新组件相关数据
    aiInventoryMode: 'camera',
    currentBatchId: 'BATCH_' + new Date().getTime(),
    aiConfig: {
      confidence: 0.8,
      maxCount: 1000,
      enableMultiAngle: true,
      autoCorrection: true
    },
    inventoryHistory: [],

    // 看板配置
    dashboardTimeRange: '7d',
    chartConfig: {
      showLegend: true,
      showGrid: true,
      showTooltip: true,
      animationDuration: 800,
      colors: ['#4CAF50', '#2196F3', '#FF9800', '#F44336', '#9C27B0']
    },
    analyticsData: [],
    financeData: [],
    financeChartConfig: {
      showLegend: true,
      showGrid: true,
      animationDuration: 600
    },
    refreshInterval: 30,

    // 记录相关
    productionRecords: [],
    recordFilterType: 'all',
    recordDisplayMode: 'timeline',
    recordTimeRange: '7d',
    recordPagination: {
      page: 1,
      pageSize: 20,
      total: 0
    },
    financeRecords: [],
    financeTimeRange: '30d',

    // 表单相关
    showFormModal: false,
    formModalTitle: '',
    formFields: [],

    // 趋势数据
    trendData: [],

    // 图表相关数据
    timeRanges: [
      { label: '近24小时', value: '24h' },
      { label: '近7天', value: '7d' },
      { label: '近30天', value: '30d' }
    ],
    activeTimeRange: '24h',
    
    // 生产图表数据
    productionChartData: [],
    productionChartConfig: {
      type: 'line',
      smooth: true,
      showPoints: true,
      showGrid: true,
      animation: true
    },
    productionLegendData: [],
    currentProductionValues: null,

    // 物料管理相关数据
    materialStats: {
      feedCount: 12,
      medicineCount: 8,
      lowStockItems: 3
    },
    activeMaterialTab: 'all',
    materialTabs: [
      { id: 'all', name: '全部' },
      { id: 'feed', name: '饲料' },
      { id: 'medicine', name: '药品' },
      { id: 'other', name: '其他' }
    ],
    materialList: [
      {
        id: 1,
        name: '雏鹅专用饲料',
        spec: '25kg/袋',
        stock: 120,
        status: 'normal',
        category: 'feed'
      },
      {
        id: 2,
        name: '育肥期饲料',
        spec: '25kg/袋',
        stock: 45,
        status: 'warning',
        category: 'feed'
      },
      {
        id: 3,
        name: '成鹅饲料',
        spec: '25kg/袋',
        stock: 80,
        status: 'normal',
        category: 'feed'
      },
      {
        id: 4,
        name: '抗生素',
        spec: '100ml/瓶',
        stock: 30,
        status: 'danger',
        category: 'medicine'
      },
      {
        id: 5,
        name: '维生素',
        spec: '500g/瓶',
        stock: 25,
        status: 'warning',
        category: 'medicine'
      },
      {
        id: 6,
        name: '消毒液',
        spec: '5L/桶',
        stock: 15,
        status: 'normal',
        category: 'other'
      },
      {
        id: 7,
        name: '饮水器',
        spec: '个',
        stock: 8,
        status: 'danger',
        category: 'other'
      }
    ],
    allMaterials: [], // 保存所有物料的原始数据
    filteredMaterialList: [],
    searchKeyword: '', // 搜索关键词

    // 财务管理相关数据
    financeData: {
      dailyCost: 2580,
      monthlyCost: 45600,
      income: 68900,
      profit: 23300
    },
    activeFinanceTab: 'cost',
    financeTabs: [
      { id: 'cost', name: '成本管理' },
      { id: 'reimbursement', name: '报销审批' }
    ],
    financeRecords: [
      {
        id: 1,
        title: '饲料采购',
        amount: -1200,
        date: '2023-06-15',
        type: 'expense',
        category: 'cost'
      },
      {
        id: 2,
        title: '鹅蛋销售',
        amount: 3500,
        date: '2023-06-14',
        type: 'income',
        category: 'cost'
      },
      {
        id: 3,
        title: '员工差旅费',
        amount: -800,
        date: '2023-06-13',
        type: 'expense',
        category: 'reimbursement',
        status: 'pending'
      },
      {
        id: 4,
        title: '设备维修费',
        amount: -450,
        date: '2023-06-12',
        type: 'expense',
        category: 'reimbursement',
        status: 'approved'
      }
    ],
    filteredFinanceRecords: [],

    // 生产记录相关数据
    activeRecordTab: 'all',
    recordTabs: [
      { id: 'all', name: '全部记录' },
      { id: 'growth', name: '生长记录' },
      { id: 'weight', name: '称重记录' },
      { id: 'sale', name: '出栏记录' }
    ],
    productionRecords: [
      {
        id: 1,
        type: 'growth',
        batch: 'GE2023061501',
        date: '2023-06-15',
        details: {
          weight: 2.5,
          ratio: '2.8:1'
        }
      },
      {
        id: 2,
        type: 'weight',
        batch: 'GE2023060801',
        date: '2023-06-14',
        details: {
          count: 500,
          weight: 2.3
        }
      },
      {
        id: 3,
        type: 'sale',
        batch: 'GE2023060501',
        date: '2023-06-05',
        details: {
          count: 200,
          weight: 3.2
        }
      },
      {
        id: 4,
        type: 'growth',
        batch: 'GE2023050801',
        date: '2023-06-10',
        details: {
          weight: 2.1,
          ratio: '3.0:1'
        }
      },
      {
        id: 5,
        type: 'weight',
        batch: 'GE2023050501',
        date: '2023-06-08',
        details: {
          count: 480,
          weight: 2.0
        }
      }
    ],
    filteredRecords: [],

    // 报销相关数据
    reimbursementStats: {
      myCount: 0,
      monthlyAmount: 0,
      pendingCount: 0
    },
    recentReimbursements: [],

    userRole: 'user', // 默认值，会在onLoad中被正确设置
    loading: true,

    // 表单相关
    showFormModal: false,
    formTitle: '',
    formFields: [],
    formData: {},
    formLoading: false,
    currentFormType: ''
  },

  onLoad: function (options) {
    // 页面加载时初始化标签页和用户角色
    this.initTabs();
    this.setData({
      userRole: this.getUserRole(),
      allMaterials: this.data.materialList // 保存原始物料数据
    });
    
    // 初始化新组件数据
    this.initProductionComponents();
    
    // 初始化过滤数据
    this.initFilteredData();

    // 加载报销数据（所有用户都需要）
    this.loadReimbursementData();
  },

  onShow: function () {
    // 页面显示时加载数据
    this.loadData();
  },

  // 初始化过滤数据
  initFilteredData: function () {
    this.filterMaterialList();
    this.loadFinanceData();
    this.filterRecordList();
    // 更新物料统计数据
    this.setData({
      materialStats: this.calculateMaterialStats()
    });
  },

  // 初始化标签页（根据用户权限）
  initTabs: function() {
    const userRole = this.getUserRole();
    const availableTabs = this.data.allTabs.filter(tab => {
      // 如果没有权限要求，所有用户都可以看到
      if (!tab.requiredRole) return true;

      // 检查角色权限
      if (tab.requiredRole.includes(userRole)) return true;

      return false;
    });

    this.setData({
      tabs: availableTabs
    });
  },

  // 获取用户角色
  getUserRole: function() {
    // 从本地存储或全局数据获取用户角色
    const app = getApp();
    const globalUserInfo = app.globalData.userInfo || {};
    const storageUserInfo = wx.getStorageSync('userInfo') || {};


    const userInfo = globalUserInfo.username ? globalUserInfo : storageUserInfo;

    return userInfo.role || 'user'; // 默认为普通用户
  },

  // 加载报销数据（根据用户角色加载不同数据）
  loadReimbursementData: function() {
    // 防止递归调用
    if (this._loadingReimbursementData) {
      return;
    }
    this._loadingReimbursementData = true;
    
    const userRole = this.getUserRole();

    if (userRole === 'user') {
      // 普通用户：只看自己的报销数据
      const reimbursementStats = {
        myCount: 2,
        monthlyAmount: 850,
        pendingCount: 1
      };

      const recentReimbursements = [
        {
          id: 1,
          title: '差旅费报销',
          category: '差旅费',
          applicant: '我',
          amount: 500,
          date: '2023-06-15',
          status: 'pending',
          statusText: '待审批'
        },
        {
          id: 2,
          title: '办公用品采购',
          category: '办公用品',
          applicant: '我',
          amount: 350,
          date: '2023-06-10',
          status: 'approved',
          statusText: '已通过'
        }
      ];

      // 使用wx.nextTick避免递归更新
      wx.nextTick(() => {
        this.setData({
          reimbursementStats: reimbursementStats,
          recentReimbursements: recentReimbursements
        });
      });
    } else {
      // 管理员：看所有员工的报销数据
      const reimbursementStats = {
        myCount: 0, // 管理员自己的申请
        monthlyAmount: 3250, // 本月所有报销总额
        pendingCount: 5 // 待审批数量
      };

      const recentReimbursements = [
        {
          id: 1,
          title: '差旅费报销',
          category: '差旅费',
          applicant: '张三',
          amount: 500,
          date: '2023-06-15',
          status: 'pending',
          statusText: '待审批'
        },
        {
          id: 2,
          title: '办公用品采购',
          category: '办公用品',
          applicant: '李四',
          amount: 350,
          date: '2023-06-14',
          status: 'pending',
          statusText: '待审批'
        },
        {
          id: 3,
          title: '培训费用',
          category: '培训费',
          applicant: '王五',
          amount: 800,
          date: '2023-06-13',
          status: 'approved',
          statusText: '已通过'
        },
        {
          id: 4,
          title: '设备维修费',
          category: '维修费',
          applicant: '赵六',
          amount: 450,
          date: '2023-06-12',
          status: 'rejected',
          statusText: '已拒绝'
        }
      ];

      // 使用wx.nextTick避免递归更新
      wx.nextTick(() => {
        this.setData({
          reimbursementStats: reimbursementStats,
          recentReimbursements: recentReimbursements
        });
      });
    }
    
    // 重置加载标志
    this._loadingReimbursementData = false;
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.loadData(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载数据
  loadData: function (callback) {
    this.setData({
      loading: true
    });

    // 模拟加载数据
    setTimeout(() => {
      // 模拟环境数据
      const environmentData = {
        temperature: 26.5,
        humidity: 65,
        pm25: 32,
        light: 800,
        updateTime: '2023-06-15 14:30'
      };

      // 计算物料统计数据
      const materialStats = this.calculateMaterialStats();

      // 模拟财务数据
      const financeData = {
        dailyCost: 2650,
        monthlyCost: 78500,
        income: 92000,
        profit: 13500
      };

      // 生成24小时环境趋势数据
      const environmentTrend = this.generateEnvironmentTrend();

      this.setData({
        environmentData: environmentData,
        environmentTrend: environmentTrend,
        materialStats: materialStats,
        financeData: financeData,
        loading: false
      });

      callback && callback();
    }, 500);
  },

  // 切换Tab
  onTabChange: function (e) {
    const index = e.detail.index;
    // 确保index是有效的数字
    if (typeof index === 'number' && index >= 0) {
      // 防止重复设置相同的tab
      if (this.data.activeTab !== index) {
        wx.nextTick(() => {
          this.setData({
            activeTab: index
          });
        });
      }
    } else {
      console.warn('[Production] Invalid tab index:', index);
      return;
    }

    // 根据选中的tab加载对应数据（不再跳转页面）
    switch (index) {
      case 0:
        // 环境监控数据已在loadData中加载
        break;
      case 1:
        // 物料管理数据已在loadData中加载
        break;
      case 2:
        // 财务管理数据已在loadData中加载
        break;
      case 3:
        // 生产记录数据已在loadData中加载
        break;
    }
  },

  // 点击环境监控跳转到详情页
  onEnvironmentDetailTap: function() {
    wx.navigateTo({
      url: '/pages/production/environment/environment'
    });
  },

  // 添加物料
  onAddMaterial: function() {
    wx.showToast({
      title: '添加物料功能开发中',
      icon: 'none'
    });
  },

  // 搜索输入
  onSearchInput: function(e) {
    // 搜索功能
  },

  // 执行搜索
  onSearch: function() {
    wx.showToast({
      title: '搜索功能开发中',
      icon: 'none'
    });
  },

  // 添加财务记录
  onAddFinanceRecord: function() {
    wx.showToast({
      title: '添加财务记录功能开发中',
      icon: 'none'
    });
  },

  // 添加生产记录
  onAddRecord: function() {
    wx.navigateTo({
      url: '/pages/production/records/records'
    });
  },

  // ==================== Tab切换相关方法 ====================

  // 物料管理Tab切换
  onMaterialTabChange: function (e) {
    const tabId = e.currentTarget.dataset.tab;
    this.setData({
      activeMaterialTab: tabId
    });
    this.filterMaterialList();
  },

  // 财务管理Tab切换
  onFinanceTabChange: function (e) {
    const tabId = e.currentTarget.dataset.tab;
    this.setData({
      activeFinanceTab: tabId
    });
    this.loadFinanceData();
  },

  // 生产记录Tab切换
  onRecordTabChange: function (e) {
    const tabId = e.currentTarget.dataset.tab;
    this.setData({
      activeRecordTab: tabId
    });
    this.filterRecordList();
  },

  // ==================== 数据过滤方法 ====================

  // 计算物料统计数据
  calculateMaterialStats: function() {
    const materialList = this.data.materialList;

    const feedCount = materialList.filter(item => item.category === 'feed').length;
    const medicineCount = materialList.filter(item => item.category === 'medicine').length;
    const otherCount = materialList.filter(item => item.category === 'other').length;
    const lowStockItems = materialList.filter(item => item.status === 'danger').length;

    const stats = {
      feedCount: feedCount,
      medicineCount: medicineCount,
      otherCount: otherCount,
      lowStockItems: lowStockItems
    };

    // 保存到本地存储，供个人中心页面使用
    wx.setStorageSync('materialStats', stats);

    return stats;
  },

  // 过滤物料列表
  filterMaterialList: function () {
    const { activeMaterialTab, searchKeyword, allMaterials } = this.data;
    let filteredList = allMaterials || this.data.materialList;

    // 首先根据搜索关键词过滤
    if (searchKeyword) {
      filteredList = filteredList.filter(item => {
        return item.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
               item.spec.toLowerCase().includes(searchKeyword.toLowerCase());
      });
    }

    // 然后根据分类过滤
    if (activeMaterialTab !== 'all') {
      filteredList = filteredList.filter(item => item.category === activeMaterialTab);
    }

    this.setData({
      filteredMaterialList: filteredList
    });

    // 如果搜索结果为空，显示提示
    if (searchKeyword && filteredList.length === 0) {
      wx.showToast({
        title: '未找到相关物料',
        icon: 'none'
      });
    }
  },

  // 加载财务数据
  loadFinanceData: function () {
    const { activeFinanceTab, financeRecords } = this.data;
    let filteredRecords = financeRecords;

    if (activeFinanceTab === 'cost') {
      // 成本管理：显示收入和支出
      filteredRecords = financeRecords.filter(item => item.category === 'cost');
    } else if (activeFinanceTab === 'reimbursement') {
      // 报销审批：显示报销相关记录
      filteredRecords = financeRecords.filter(item => item.category === 'reimbursement');
    }

    this.setData({
      filteredFinanceRecords: filteredRecords
    });
  },

  // 过滤记录列表
  filterRecordList: function () {
    const { activeRecordTab, productionRecords } = this.data;
    let filteredRecords = productionRecords;

    if (activeRecordTab !== 'all') {
      filteredRecords = productionRecords.filter(item => item.type === activeRecordTab);
    }

    this.setData({
      filteredRecords: filteredRecords
    });
  },

  // ==================== 搜索相关方法 ====================

  // 搜索输入
  onSearchInput: function (e) {
    const value = e.detail.value;
    this.setData({
      searchKeyword: value
    });
  },

  // 执行搜索
  onSearch: function () {
    this.filterMaterialList();
  },



  // 生成24小时环境趋势数据
  generateEnvironmentTrend: function() {
    const timeLabels = [];
    const temperature = [];
    const humidity = [];
    const pm25 = [];
    const light = [];

    // 生成24小时的数据点（每2小时一个点）
    for (let i = 0; i < 12; i++) {
      const hour = i * 2;
      timeLabels.push(hour.toString().padStart(2, '0') + ':00');

      // 模拟温度数据 (24-28度之间波动)
      temperature.push(24 + Math.random() * 4 + Math.sin(i * 0.5) * 2);

      // 模拟湿度数据 (60-70%之间波动)
      humidity.push(60 + Math.random() * 10 + Math.cos(i * 0.3) * 5);

      // 模拟PM2.5数据 (20-50之间波动)
      pm25.push(20 + Math.random() * 30 + Math.sin(i * 0.8) * 10);

      // 模拟光照数据 (白天高，夜晚低)
      const isDay = hour >= 6 && hour <= 18;
      light.push(isDay ? 600 + Math.random() * 400 : 50 + Math.random() * 100);
    }

    return {
      timeLabels,
      temperature: temperature.map(v => Math.round(v * 10) / 10),
      humidity: humidity.map(v => Math.round(v)),
      pm25: pm25.map(v => Math.round(v)),
      light: light.map(v => Math.round(v))
    };
  },

  // ==================== 快捷操作相关方法 ====================

  // 添加生产记录
  onAddProductionRecord: function () {
    const that = this;
    wx.showActionSheet({
      itemList: ['生长记录', '称重记录', '出栏记录'],
      success: function (res) {
        let recordType = '';
        switch (res.tapIndex) {
          case 0:
            recordType = 'growth';
            that.showProductionRecordForm('生长记录', recordType);
            break;
          case 1:
            recordType = 'weight';
            that.showProductionRecordForm('称重记录', recordType);
            break;
          case 2:
            recordType = 'sale';
            that.showProductionRecordForm('出栏记录', recordType);
            break;
        }
      }
    });
  },

  // 添加物料记录
  onAddMaterial: function () {
    this.showMaterialForm('添加物料', 'add');
  },

  // 显示生产记录表单
  showProductionRecordForm: function (title, type) {
    let fields = [];

    // 通用字段
    const commonFields = [
      {
        key: 'batch',
        label: '批次号',
        type: 'input',
        placeholder: '请输入批次号',
        required: true
      },
      {
        key: 'date',
        label: '记录日期',
        type: 'date',
        required: true
      }
    ];

    // 根据类型添加特定字段
    if (type === 'growth') {
      fields = [
        ...commonFields,
        {
          key: 'weight',
          label: '平均体重(kg)',
          type: 'input',
          placeholder: '请输入平均体重',
          required: true
        },
        {
          key: 'ratio',
          label: '料肉比',
          type: 'input',
          placeholder: '请输入料肉比',
          required: true
        }
      ];
    } else if (type === 'weight') {
      fields = [
        ...commonFields,
        {
          key: 'count',
          label: '称重数量(只)',
          type: 'input',
          placeholder: '请输入称重数量',
          required: true
        },
        {
          key: 'weight',
          label: '平均体重(kg)',
          type: 'input',
          placeholder: '请输入平均体重',
          required: true
        }
      ];
    } else if (type === 'sale') {
      fields = [
        ...commonFields,
        {
          key: 'count',
          label: '出栏数量(只)',
          type: 'input',
          placeholder: '请输入出栏数量',
          required: true
        },
        {
          key: 'weight',
          label: '平均体重(kg)',
          type: 'input',
          placeholder: '请输入平均体重',
          required: true
        }
      ];
    }

    // 初始化表单数据
    let formData = { type: type };
    
    // 为每个字段设置默认值
    fields.forEach(field => {
      const fieldName = field.key || field.name;
      if (fieldName === 'date') {
        // 默认设置为今天的日期
        const today = new Date();
        const year = today.getFullYear();
        const month = (today.getMonth() + 1).toString().padStart(2, '0');
        const day = today.getDate().toString().padStart(2, '0');
        formData[fieldName] = `${year}-${month}-${day}`;
      } else {
        formData[fieldName] = '';
      }
    });

    this.setData({
      showFormModal: true,
      formTitle: title,
      formFields: fields,
      formData: formData,
      formType: 'production_record'
    });
  },

  // 显示物料表单
  showMaterialForm: function (title, mode, materialData = {}) {
    const fields = [
      {
        key: 'recordDate',
        label: '记录日期',
        type: 'date',
        placeholder: '请选择日期',
        required: true
      },
      {
        key: 'recordTime',
        label: '记录时间',
        type: 'time',
        placeholder: '请选择时间',
        required: true
      },
      {
        key: 'name',
        label: '物料名称',
        type: 'input',
        placeholder: '请输入物料名称',
        required: true
      },
      {
        key: 'category',
        label: '物料类别',
        type: 'picker',
        options: [
          { value: 'feed', label: '饲料' },
          { value: 'medicine', label: '药品' },
          { value: 'other', label: '其他' }
        ],
        required: true
      },
      {
        key: 'spec',
        label: '规格',
        type: 'input',
        placeholder: '请输入规格',
        required: true
      },
      {
        key: 'stock',
        label: '库存数量',
        type: 'input',
        placeholder: '请输入库存数量',
        required: true
      },
      {
        key: 'supplier',
        label: '供应商',
        type: 'input',
        placeholder: '请输入供应商（可选）',
        required: false
      },
      {
        key: 'notes',
        label: '备注',
        type: 'textarea',
        placeholder: '请输入备注信息（可选）',
        required: false
      }
    ];

    // 初始化表单数据
    let formData = mode === 'edit' ? { ...materialData } : {};
    
    // 为新添加记录设置默认时间
    if (mode !== 'edit') {
      const now = new Date();
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const day = now.getDate().toString().padStart(2, '0');
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      
      formData.recordDate = `${year}-${month}-${day}`;
      formData.recordTime = `${hours}:${minutes}`;
    }

    this.setData({
      showFormModal: true,
      formTitle: title,
      formFields: fields,
      formData: formData,
      formType: 'material',
      formMode: mode
    });
  },

  // 显示添加记录表单
  onShowAddRecordForm: function () {
    const that = this;
    wx.showActionSheet({
      itemList: ['生长记录', '称重记录', '出栏记录'],
      success: function (res) {
        let recordType = '';
        switch (res.tapIndex) {
          case 0:
            recordType = 'growth';
            that.showProductionRecordForm('生长记录', recordType);
            break;
          case 1:
            recordType = 'weight';
            that.showProductionRecordForm('称重记录', recordType);
            break;
          case 2:
            recordType = 'sale';
            that.showProductionRecordForm('出栏记录', recordType);
            break;
        }
      }
    });
  },

  // 显示采购申请表单
  onShowPurchaseForm: function () {
    const that = this;
    wx.showActionSheet({
      itemList: ['饲料采购', '药品采购', '其他物料采购'],
      success: function (res) {
        let purchaseType = '';
        switch (res.tapIndex) {
          case 0:
            purchaseType = '饲料采购';
            break;
          case 1:
            purchaseType = '药品采购';
            break;
          case 2:
            purchaseType = '其他物料采购';
            break;
        }
        that.showPurchaseForm(purchaseType);
      }
    });
  },

  // 显示报销申请表单
  onShowReimbursementForm: function () {
    const that = this;
    wx.showActionSheet({
      itemList: ['差旅费报销', '办公用品报销', '医疗费报销', '其他费用报销'],
      success: function (res) {
        let reimbursementType = '';
        switch (res.tapIndex) {
          case 0:
            reimbursementType = '差旅费报销';
            break;
          case 1:
            reimbursementType = '办公用品报销';
            break;
          case 2:
            reimbursementType = '医疗费报销';
            break;
          case 3:
            reimbursementType = '其他费用报销';
            break;
        }
        that.showReimbursementForm(reimbursementType);
      }
    });
  },

  // AI盘点功能
  onAIInventory: function () {
    wx.navigateTo({
      url: '/pages/production/ai-inventory/ai-inventory'
    });
  },

  // 更多操作
  onQuickMore: function () {
    const that = this;
    wx.showActionSheet({
      itemList: ['数据导出', '系统设置', '帮助中心'],
      success: function (res) {
        switch (res.tapIndex) {
          case 0:
            that.onDataExport();
            break;
          case 1:
            that.onSystemSettings();
            break;
          case 2:
            that.onHelpCenter();
            break;
        }
      }
    });
  },

  // 数据导出功能
  onDataExport: function() {
    const that = this;
    wx.showModal({
      title: '数据导出',
      content: '选择要导出的数据类型',
      showCancel: true,
      cancelText: '取消',
      confirmText: '确定',
      success: function(res) {
        if (res.confirm) {
          wx.showActionSheet({
            itemList: ['生产记录', '环境数据', '库存信息', '财务数据'],
            success: function(exportRes) {
              const exportTypes = ['生产记录', '环境数据', '库存信息', '财务数据'];
              const selectedType = exportTypes[exportRes.tapIndex];

              wx.showLoading({
                title: '导出中...',
                mask: true
              });

              // 模拟导出过程
              setTimeout(() => {
                wx.hideLoading();
                wx.showToast({
                  title: `${selectedType}导出成功`,
                  icon: 'success',
                  duration: 2000
                });

                // 这里可以添加实际的文件下载逻辑
              }, 2000);
            }
          });
        }
      }
    });
  },

  // 系统设置功能
  onSystemSettings: function() {
    wx.showModal({
      title: '系统设置',
      content: '是否跳转到系统设置页面？',
      showCancel: true,
      cancelText: '取消',
      confirmText: '确定',
      success: function(res) {
        if (res.confirm) {
          // 跳转到系统设置页面
          wx.navigateTo({
            url: '/pages/settings/settings',
            fail: function() {
              // 如果设置页面不存在，显示设置选项
              wx.showActionSheet({
                itemList: ['通知设置', '数据同步', '账户管理', '关于我们'],
                success: function(settingRes) {
                  const settings = ['通知设置', '数据同步', '账户管理', '关于我们'];
                  const selectedSetting = settings[settingRes.tapIndex];
                  wx.showToast({
                    title: `进入${selectedSetting}`,
                    icon: 'none',
                    duration: 1500
                  });
                }
              });
            }
          });
        }
      }
    });
  },

  // 帮助中心功能
  onHelpCenter: function() {
    wx.showModal({
      title: '帮助中心',
      content: '选择需要帮助的内容',
      showCancel: true,
      cancelText: '取消',
      confirmText: '确定',
      success: function(res) {
        if (res.confirm) {
          wx.showActionSheet({
            itemList: ['使用教程', '常见问题', '联系客服', '意见反馈'],
            success: function(helpRes) {
              switch(helpRes.tapIndex) {
                case 0:
                  // 使用教程
                  wx.showModal({
                    title: '使用教程',
                    content: '1. 点击添加记录可新增生产数据\n2. 环境监控实时显示养殖环境\n3. 库存管理帮助跟踪物资\n4. 财务管理记录收支情况',
                    showCancel: false,
                    confirmText: '知道了'
                  });
                  break;
                case 1:
                  // 常见问题
                  wx.showModal({
                    title: '常见问题',
                    content: 'Q: 如何添加生产记录？\nA: 点击快捷操作中的"添加记录"按钮\n\nQ: 数据如何同步？\nA: 系统会自动同步到云端',
                    showCancel: false,
                    confirmText: '知道了'
                  });
                  break;
                case 2:
                  // 联系客服
                  wx.showModal({
                    title: '联系客服',
                    content: '客服电话：400-123-4567\n工作时间：9:00-18:00\n或扫描二维码添加微信客服',
                    showCancel: false,
                    confirmText: '知道了'
                  });
                  break;
                case 3:
                  // 意见反馈
                  wx.navigateTo({
                    url: '/pages/feedback/feedback',
                    fail: function() {
                      wx.showToast({
                        title: '反馈功能开发中',
                        icon: 'none'
                      });
                    }
                  });
                  break;
              }
            }
          });
        }
      }
    });
  },

  // ==================== 表单显示方法 ====================

  // 显示记录表单
  showRecordForm: function(recordType) {
    let fields = [];
    let formData = {};

    switch (recordType) {
      case '孵化记录':
        fields = [
          { name: 'batch', label: '批次号', type: 'text', placeholder: '请输入批次号' },
          { name: 'date', label: '孵化日期', type: 'date', placeholder: '请选择日期' },
          { name: 'eggCount', label: '种蛋数量', type: 'number', placeholder: '请输入种蛋数量' },
          { name: 'temperature', label: '孵化温度(°C)', type: 'number', placeholder: '请输入温度' },
          { name: 'humidity', label: '湿度(%)', type: 'number', placeholder: '请输入湿度' },
          { name: 'notes', label: '备注', type: 'textarea', placeholder: '请输入备注信息' }
        ];
        formData = {
          batch: '',
          date: '',
          eggCount: '',
          temperature: '37.8',
          humidity: '60',
          notes: ''
        };
        break;
      case '生长记录':
        fields = [
          { name: 'batch', label: '批次号', type: 'text', placeholder: '请输入批次号' },
          { name: 'date', label: '记录日期', type: 'date', placeholder: '请选择日期' },
          { name: 'count', label: '存栏数量', type: 'number', placeholder: '请输入存栏数量' },
          { name: 'weight', label: '平均体重(kg)', type: 'number', placeholder: '请输入平均体重' },
          { name: 'feedConsumption', label: '饲料消耗(kg)', type: 'number', placeholder: '请输入饲料消耗' },
          { name: 'healthStatus', label: '健康状况', type: 'picker', placeholder: '请选择健康状况',
            options: [
              { label: '良好', value: 'good' },
              { label: '一般', value: 'normal' },
              { label: '异常', value: 'abnormal' }
            ]
          },
          { name: 'notes', label: '备注', type: 'textarea', placeholder: '请输入备注信息' }
        ];
        formData = {
          batch: '',
          date: '',
          count: '',
          weight: '',
          feedConsumption: '',
          healthStatus: undefined,
          notes: ''
        };
        break;
      case '出栏记录':
        fields = [
          { name: 'batch', label: '批次号', type: 'text', placeholder: '请输入批次号' },
          { name: 'date', label: '出栏日期', type: 'date', placeholder: '请选择日期' },
          { name: 'count', label: '出栏数量', type: 'number', placeholder: '请输入出栏数量' },
          { name: 'totalWeight', label: '总重量(kg)', type: 'number', placeholder: '请输入总重量' },
          { name: 'avgWeight', label: '平均体重(kg)', type: 'number', placeholder: '请输入平均体重' },
          { name: 'price', label: '单价(元/kg)', type: 'number', placeholder: '请输入单价' },
          { name: 'buyer', label: '购买方', type: 'text', placeholder: '请输入购买方' },
          { name: 'notes', label: '备注', type: 'textarea', placeholder: '请输入备注信息' }
        ];
        formData = {
          batch: '',
          date: '',
          count: '',
          totalWeight: '',
          avgWeight: '',
          price: '',
          buyer: '',
          notes: ''
        };
        break;
    }

    this.setData({
      showFormModal: true,
      formTitle: `添加${recordType}`,
      formFields: fields,
      formData: formData,
      currentFormType: 'record'
    });
  },

  // 显示采购表单
  showPurchaseForm: function(purchaseType) {
    let fields = [];
    let formData = {};

    switch (purchaseType) {
      case '饲料采购':
        fields = [
          { name: 'productName', label: '饲料名称', type: 'text', placeholder: '请输入饲料名称' },
          { name: 'specification', label: '规格', type: 'text', placeholder: '如：25kg/袋' },
          { name: 'quantity', label: '采购数量', type: 'number', placeholder: '请输入数量' },
          { name: 'supplier', label: '供应商', type: 'text', placeholder: '请输入供应商名称' },
          { name: 'unitPrice', label: '单价(元)', type: 'number', placeholder: '请输入单价' },
          { name: 'urgency', label: '紧急程度', type: 'picker', placeholder: '请选择紧急程度',
            options: [
              { label: '普通', value: 'normal' },
              { label: '紧急', value: 'urgent' },
              { label: '特急', value: 'critical' }
            ]
          },
          { name: 'reason', label: '采购原因', type: 'textarea', placeholder: '请输入采购原因' }
        ];
        break;
      case '药品采购':
        fields = [
          { name: 'productName', label: '药品名称', type: 'text', placeholder: '请输入药品名称' },
          { name: 'specification', label: '规格', type: 'text', placeholder: '如：100ml/瓶' },
          { name: 'quantity', label: '采购数量', type: 'number', placeholder: '请输入数量' },
          { name: 'supplier', label: '供应商', type: 'text', placeholder: '请输入供应商名称' },
          { name: 'unitPrice', label: '单价(元)', type: 'number', placeholder: '请输入单价' },
          { name: 'expiryDate', label: '有效期至', type: 'date', placeholder: '请选择有效期' },
          { name: 'urgency', label: '紧急程度', type: 'picker', placeholder: '请选择紧急程度',
            options: [
              { label: '普通', value: 'normal' },
              { label: '紧急', value: 'urgent' },
              { label: '特急', value: 'critical' }
            ]
          },
          { name: 'reason', label: '采购原因', type: 'textarea', placeholder: '请输入采购原因' }
        ];
        break;
      case '其他物料采购':
        fields = [
          { name: 'productName', label: '物料名称', type: 'text', placeholder: '请输入物料名称' },
          { name: 'specification', label: '规格', type: 'text', placeholder: '请输入规格' },
          { name: 'quantity', label: '采购数量', type: 'number', placeholder: '请输入数量' },
          { name: 'supplier', label: '供应商', type: 'text', placeholder: '请输入供应商名称' },
          { name: 'unitPrice', label: '单价(元)', type: 'number', placeholder: '请输入单价' },
          { name: 'category', label: '物料类别', type: 'picker', placeholder: '请选择类别',
            options: [
              { label: '设备用品', value: 'equipment' },
              { label: '清洁用品', value: 'cleaning' },
              { label: '办公用品', value: 'office' },
              { label: '其他', value: 'other' }
            ]
          },
          { name: 'urgency', label: '紧急程度', type: 'picker', placeholder: '请选择紧急程度',
            options: [
              { label: '普通', value: 'normal' },
              { label: '紧急', value: 'urgent' },
              { label: '特急', value: 'critical' }
            ]
          },
          { name: 'reason', label: '采购原因', type: 'textarea', placeholder: '请输入采购原因' }
        ];
        break;
    }

    formData = {
      productName: '',
      specification: '',
      quantity: '',
      supplier: '',
      unitPrice: '',
      urgency: undefined,
      reason: ''
    };

    if (purchaseType === '药品采购') {
      formData.expiryDate = '';
    }

    if (purchaseType === '其他物料采购') {
      formData.category = undefined;
    }

    this.setData({
      showFormModal: true,
      formTitle: `${purchaseType}申请`,
      formFields: fields,
      formData: formData,
      currentFormType: 'purchase'
    });
  },

  // 显示报销表单
  showReimbursementForm: function(reimbursementType) {
    let fields = [
      { name: 'amount', label: '报销金额(元)', type: 'number', placeholder: '请输入报销金额' },
      { name: 'date', label: '发生日期', type: 'date', placeholder: '请选择发生日期' },
      { name: 'description', label: '费用说明', type: 'textarea', placeholder: '请详细说明费用用途' },
      { name: 'receipt', label: '发票/收据', type: 'image', placeholder: '请上传发票或收据照片' }
    ];

    // 根据报销类型添加特定字段
    switch (reimbursementType) {
      case '差旅费报销':
        fields.splice(2, 0,
          { name: 'destination', label: '出差地点', type: 'text', placeholder: '请输入出差地点' },
          { name: 'purpose', label: '出差目的', type: 'text', placeholder: '请输入出差目的' }
        );
        break;
      case '办公用品报销':
        fields.splice(2, 0,
          { name: 'supplier', label: '购买商家', type: 'text', placeholder: '请输入购买商家' },
          { name: 'items', label: '物品清单', type: 'textarea', placeholder: '请列出购买的物品清单' }
        );
        break;
      case '医疗费报销':
        fields.splice(2, 0,
          { name: 'hospital', label: '医疗机构', type: 'text', placeholder: '请输入医疗机构名称' },
          { name: 'patient', label: '就诊人员', type: 'text', placeholder: '请输入就诊人员姓名' }
        );
        break;
    }

    const formData = {
      amount: '',
      date: '',
      description: '',
      receipt: ''
    };

    this.setData({
      showFormModal: true,
      formTitle: `${reimbursementType}申请`,
      formFields: fields,
      formData: formData,
      currentFormType: 'reimbursement'
    });
  },

  // ==================== 表单处理方法 ====================

  // 表单输入变化
  onFormInputChange: function(e) {
    const { field, value } = e.detail;
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 选择器变化
  onFormPickerChange: function(e) {
    const { field, value } = e.detail;
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 日期选择器变化
  onFormDateChange: function(e) {
    const { field, value } = e.detail;
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 时间选择器变化
  onFormTimeChange: function(e) {
    const { field, value } = e.detail;
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 开关变化
  onFormSwitchChange: function(e) {
    const { field, value } = e.detail;
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 图片上传
  onFormImageUpload: function(e) {
    const { field, value } = e.detail;
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 表单确认
  onFormConfirm: function(e) {
    const { formData } = e.detail;
    const { formType } = this.data;

    this.setData({
      formLoading: true
    });

    // 模拟API调用
    setTimeout(() => {
      this.submitForm(formType, formData);
    }, 1000);
  },

  // 表单取消
  onFormCancel: function() {
    this.closeForm();
  },

  // 表单关闭
  onFormClose: function() {
    this.closeForm();
  },

  // 关闭表单
  closeForm: function() {
    this.setData({
      showFormModal: false,
      formTitle: '',
      formFields: [],
      formData: {},
      formLoading: false,
      currentFormType: ''
    });
  },

  // 提交表单
  submitForm: function(formType, formData) {
    let submitPromise;

    switch (formType) {
      case 'production_record':
        submitPromise = this.submitProductionRecord(formData);
        break;
      case 'material':
        submitPromise = this.submitMaterial(formData);
        break;
      case 'purchase':
        submitPromise = this.submitPurchaseRequest(formData);
        break;
      case 'reimbursement':
        submitPromise = this.submitReimbursementRequest(formData);
        break;
      default:
        submitPromise = Promise.reject(new Error('未知的表单类型'));
    }

    submitPromise.then(result => {
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      });

      this.setData({
        formLoading: false
      });

      this.closeForm();

      // 刷新相关数据
      this.refreshData(formType);

    }).catch(error => {
      wx.showToast({
        title: error.message || '提交失败',
        icon: 'none'
      });

      this.setData({
        formLoading: false
      });
    });
  },

  // 刷新数据
  refreshData: function(formType) {
    switch (formType) {
      case 'production_record':
        this.filterRecordList();
        break;
      case 'material':
        this.filterMaterialList();
        break;
      case 'purchase':
        this.loadMaterialData();
        break;
      case 'reimbursement':
        this.loadFinanceData();
        break;
    }
  },

  // 提交生产记录
  submitProductionRecord: function(formData) {
    const api = require('../../utils/api.js');

    return new Promise((resolve, reject) => {
      // 调用V2版本的API
      api.production.createRecordV2(formData).then(result => {
        if (result.success) {
          // 添加到本地记录列表
          const newRecord = result.data;
          const updatedRecords = [newRecord, ...this.data.productionRecords];
          this.setData({
            productionRecords: updatedRecords
          });
          resolve(result);
        } else {
          reject(new Error(result.message || '创建记录失败'));
        }
      }).catch(error => {
        console.error('提交生产记录失败:', error);
        reject(error);
      });
    });
  },

  // 提交物料记录
  submitMaterial: function(formData) {
    const api = require('../../utils/api.js');

    return new Promise((resolve, reject) => {
      // 调用物料管理API
      api.production.createMaterial(formData).then(result => {
        if (result.success) {
          // 添加到本地物料列表
          const newMaterial = result.data;
          const updatedMaterials = [newMaterial, ...this.data.materialList];
          this.setData({
            materialList: updatedMaterials
          });
          resolve(result);
        } else {
          reject(new Error(result.message || '创建物料失败'));
        }
      }).catch(error => {
        console.error('提交物料记录失败:', error);
        reject(error);
      });
    });
  },

  // 提交采购申请
  submitPurchaseRequest: function(formData) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve({ success: true });
      }, 1000);
    });
  },

  // 提交报销申请
  submitReimbursementRequest: function(formData) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve({ success: true });
      }, 1000);
    });
  },

  // 物料统计卡片点击事件
  onMaterialStatsTap: function(e) {
    const type = e.currentTarget.dataset.type;

    // 根据点击的统计类型，切换到对应的物料标签
    switch (type) {
      case 'feed':
        this.setData({
          activeMaterialTab: 'feed'
        });
        break;
      case 'medicine':
        this.setData({
          activeMaterialTab: 'medicine'
        });
        break;
      case 'lowstock':
        // 显示库存不足的物料
        this.setData({
          activeMaterialTab: 'all'
        });
        break;
    }

    // 重新过滤物料列表
    this.filterMaterialList();
  },

  // 查看趋势图
  /**
   * 环境数据趋势查看 - 支持组件事件和原生事件
   */
  onViewTrend: function (e) {
    try {
      // 支持组件事件和原生事件两种格式
      // 组件事件：e.detail.type (environment-data-item组件)
      // 原生事件：e.currentTarget.dataset.type (直接bindtap)
      let type = null;

      if (e.detail && e.detail.type) {
        // 组件事件格式
        type = e.detail.type;
      } else if (e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.type) {
        // 原生事件格式
        type = e.currentTarget.dataset.type;
      }

      // 如果没有type，可能是点击了图表内部的其他元素（时间选择器、Canvas等）
      // 这些点击是正常的，不需要提示错误，直接忽略即可
      if (!type) {
        return;
      }

      
      const typeNames = {
        temperature: '温度',
        humidity: '湿度',
        pm25: 'PM2.5',
        light: '光照'
      };

      const typeName = typeNames[type];
      if (!typeName) return;

      this.setData({
        selectedTrendType: typeName
      });

      // 生成图表数据
      this.generateProductionChartData(type, this.data.activeTimeRange);
      
    } catch (error) {
      console.error('[Production] 查看趋势失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      });
    }
  },

  // 关闭图表
  onCloseChart: function () {
    this.setData({
      selectedTrendType: '',
      trendData: [],
      productionChartData: [],
      productionLegendData: [],
      currentProductionValues: null
    });
  },

  // 显示报销申请表单或查看待审批
  onShowReimbursementForm: function() {
    const userRole = this.getUserRole();
    if (userRole === 'user') {
      // 普通用户：新增报销申请
      wx.navigateTo({
        url: '/pages/production/reimbursement/add/add'
      });
    } else {
      // 管理员：查看待审批申请
      wx.navigateTo({
        url: '/pages/production/reimbursement/reimbursement?filter=pending'
      });
    }
  },

  // 查看我的报销申请或全部申请
  onViewMyReimbursements: function() {
    const userRole = this.getUserRole();
    if (userRole === 'user') {
      // 普通用户：查看我的申请
      wx.navigateTo({
        url: '/pages/production/reimbursement/reimbursement?filter=my'
      });
    } else {
      // 管理员：查看全部申请
      wx.navigateTo({
        url: '/pages/production/reimbursement/reimbursement?filter=all'
      });
    }
  },

  // 点击报销项
  onReimbursementTap: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/production/reimbursement/detail/detail?id=${id}`
    });
  },

  // 生成生产图表数据
  generateProductionChartData: function(type, timeRange) {
    const typeConfigs = {
      temperature: { unit: '°C', color: '#FF6B6B', min: 15, max: 35 },
      humidity: { unit: '%', color: '#4ECDC4', min: 40, max: 90 },
      pm25: { unit: '', color: '#45B7D1', min: 0, max: 100 },
      light: { unit: 'lx', color: '#FFA726', min: 0, max: 2000 }
    };

    const config = typeConfigs[type];
    if (!config) return;

    // 根据时间范围生成不同数量的数据点
    let dataCount, timeFormat, timeStep;
    switch (timeRange) {
      case '24h':
        dataCount = 12; // 每2小时一个点
        timeFormat = 'hour';
        timeStep = 2;
        break;
      case '7d':
        dataCount = 7; // 每天一个点
        timeFormat = 'day';
        timeStep = 1;
        break;
      case '30d':
        dataCount = 15; // 每2天一个点
        timeFormat = 'day';
        timeStep = 2;
        break;
      default:
        dataCount = 12;
        timeFormat = 'hour';
        timeStep = 2;
    }

    // 生成模拟数据
    const chartData = [];
    const now = new Date();
    
    for (let i = dataCount - 1; i >= 0; i--) {
      const time = new Date(now);
      
      if (timeFormat === 'hour') {
        time.setHours(time.getHours() - i * timeStep);
      } else {
        time.setDate(time.getDate() - i * timeStep);
      }

      // 生成符合正常范围的随机值，并添加一些变化趋势
      const baseValue = (config.min + config.max) / 2;
      const variation = (config.max - config.min) * 0.3;
      const trendFactor = Math.sin((i / dataCount) * Math.PI * 2) * 0.2;
      const randomFactor = (Math.random() - 0.5) * 0.6;
      
      let value = baseValue + variation * (trendFactor + randomFactor);
      value = Math.max(config.min, Math.min(config.max, value));

      chartData.push({
        time: timeFormat === 'hour' 
          ? `${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`
          : `${(time.getMonth() + 1).toString().padStart(2, '0')}-${time.getDate().toString().padStart(2, '0')}`,
        value: Math.round(value * 10) / 10
      });
    }

    // 设置图例数据
    const legendData = [{
      key: type,
      label: this.data.selectedTrendType,
      color: config.color
    }];

    // 设置当前值显示
    const currentProductionValues = {
      time: chartData[chartData.length - 1]?.time || '',
      values: [{
        key: type,
        label: this.data.selectedTrendType,
        value: chartData[chartData.length - 1]?.value || 0,
        unit: config.unit,
        color: config.color
      }]
    };

    this.setData({
      productionChartData: chartData,
      productionLegendData: legendData,
      currentProductionValues: currentProductionValues
    });
  },

  // 生产图表时间范围改变
  onProductionTimeRangeChange: function(e) {
    const timeRange = e.detail.value;
    this.setData({
      activeTimeRange: timeRange
    });
    
    // 重新生成图表数据
    if (this.data.selectedTrendType) {
      // 从selectedTrendType反推type
      const typeNames = {
        '温度': 'temperature',
        '湿度': 'humidity',
        'PM2.5': 'pm25',
        '光照': 'light'
      };
      
      const type = typeNames[this.data.selectedTrendType];
      if (type) {
        this.generateProductionChartData(type, timeRange);
      }
    }
  },

  // 生产图表渲染完成
  onProductionChartRendered: function(e) {
  },

  // 刷新生产图表
  onRefreshProductionChart: function() {
    if (this.data.selectedTrendType) {
      // 从selectedTrendType反推type
      const typeNames = {
        '温度': 'temperature',
        '湿度': 'humidity',
        'PM2.5': 'pm25',
        '光照': 'light'
      };
      
      const type = typeNames[this.data.selectedTrendType];
      if (type) {
        this.generateProductionChartData(type, this.data.activeTimeRange);
      }
    }
  },

  // 数据导出事件
  onExportData: function() {
    wx.showActionSheet({
      itemList: ['导出环境数据', '导出物料清单', '导出生产记录', '导出报销记录'],
      success: (res) => {
        const types = ['environment', 'materials', 'production', 'reimbursement'];
        const type = types[res.tapIndex];
        this.exportData(type);
      }
    });
    wx.vibrateShort();
  },

  // 生产设置事件
  onProductionSettings: function() {
    wx.navigateTo({
      url: '/pages/production/settings/settings'
    });
    wx.vibrateShort();
  },

  // 导出数据逻辑
  exportData: function(type) {
    wx.showLoading({
      title: '正在导出...'
    });

    // 模拟导出过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '导出成功',
        icon: 'success'
      });
    }, 2000);
  },

  /**
   * 仪表板数据加载事件处理
   * @param {Object} e 事件对象
   */
  onDashboardDataLoad: function(e) {
    
    // 根据传入的数据类型，触发相应的数据刷新
    const { type } = e.detail || {};
    
    switch (type) {
      case 'environment':
        this.loadEnvironmentData();
        break;
      case 'materials':
        this.loadMaterialData();
        break;
      case 'records':
        this.loadProductionRecords();
        break;
      default:
        // 默认刷新当前标签页数据
        this.refreshCurrentTabData();
    }
  },

  /**
   * 刷新当前标签页数据
   */
  refreshCurrentTabData: function() {
    const { activeTab } = this.data;
    
    switch (activeTab) {
      case 0: // 环境监控
        this.loadEnvironmentData();
        break;
      case 1: // 物料管理
        this.loadMaterialData();
        break;
      case 2: // 生产记录
        this.loadProductionRecords();
        break;
      case 3: // 报销申请
        this.loadReimbursementData();
        break;
      case 4: // 财务管理
        this.loadFinanceData();
        break;
    }
  },

  /**
   * 加载环境数据
   */
  loadEnvironmentData: function() {
    // 这里可以实现环境数据的加载逻辑
    this.generateEnvironmentTrend();
  },

  /**
   * 加载物料数据
   */
  loadMaterialData: function() {
    // 这里可以实现物料数据的加载逻辑
    this.calculateMaterialStats();
  },

  /**
   * 加载生产记录数据
   */
  loadProductionRecords: function() {
    // 这里可以实现生产记录数据的加载逻辑
  },

  /**
   * 加载财务数据
   */
  loadFinanceData: function() {
    // 这里可以实现财务数据的加载逻辑
  },

  /**
   * 环境状态变化事件处理
   * @param {Object} e 事件对象
   */
  onEnvironmentStatusChange: function(e) {
    const { type, value, status, threshold } = e.detail;
    
    // 防止重复更新相同状态
    const statusKey = `${type}Status`;
    const currentStatus = this.data.environmentData && this.data.environmentData[statusKey];
    
    if (currentStatus !== status) {
      // 使用nextTick避免递归更新
      wx.nextTick(() => {
        // 更新环境数据状态
        this.setData({
          [`environmentData.${statusKey}`]: status
        });

        // 触发状态变化的业务逻辑
        this.handleEnvironmentStatusChange(type, status, value);
      });
    }
  },

  /**
   * 处理环境状态变化的业务逻辑
   * @param {string} type 环境类型
   * @param {string} status 状态
   * @param {number} value 数值
   */
  handleEnvironmentStatusChange: function(type, status, value) {
    // 危险状态处理
    if (status === 'danger') {
      this.showEnvironmentAlert(type, value);
    }
    
    // 警告状态处理
    if (status === 'warning') {
      this.showEnvironmentWarning(type, value);
    }

    // 记录状态变化日志
    this.logEnvironmentChange(type, status, value);
  },

  /**
   * 显示环境危险警报
   * @param {string} type 环境类型
   * @param {number} value 数值
   */
  showEnvironmentAlert: function(type, value) {
    const typeNames = {
      temperature: '温度',
      humidity: '湿度',
      pm25: 'PM2.5',
      light: '光照'
    };

    wx.showModal({
      title: '环境危险警报',
      content: `${typeNames[type] || type}值异常：${value}，请立即处理！`,
      confirmText: '立即处理',
      cancelText: '稍后处理',
      success: (res) => {
        if (res.confirm) {
          this.handleEnvironmentEmergency(type);
        }
      }
    });

    // 震动提醒
    wx.vibrateShort({
      type: 'heavy'
    });
  },

  /**
   * 显示环境警告
   * @param {string} type 环境类型
   * @param {number} value 数值
   */
  showEnvironmentWarning: function(type, value) {
    const typeNames = {
      temperature: '温度',
      humidity: '湿度',
      pm25: 'PM2.5',
      light: '光照'
    };

    wx.showToast({
      title: `${typeNames[type] || type}警告：${value}`,
      icon: 'none',
      duration: 3000
    });
  },

  /**
   * 处理环境紧急情况
   * @param {string} type 环境类型
   */
  handleEnvironmentEmergency: function(type) {
    // 根据环境类型执行相应的紧急处理措施
    switch (type) {
      case 'temperature':
        this.adjustTemperature();
        break;
      case 'humidity':
        this.adjustHumidity();
        break;
      case 'pm25':
        this.activateAirPurification();
        break;
      case 'light':
        this.adjustLighting();
        break;
    }
  },

  /**
   * 调节温度
   */
  adjustTemperature: function() {
    wx.showLoading({ title: '调节温度中...' });
    // 模拟调节温度
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '温度调节指令已发送',
        icon: 'success'
      });
    }, 2000);
  },

  /**
   * 调节湿度
   */
  adjustHumidity: function() {
    wx.showLoading({ title: '调节湿度中...' });
    // 模拟调节湿度
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '湿度调节指令已发送',
        icon: 'success'
      });
    }, 2000);
  },

  /**
   * 启动空气净化
   */
  activateAirPurification: function() {
    wx.showLoading({ title: '启动空气净化...' });
    // 模拟启动空气净化
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '空气净化系统已启动',
        icon: 'success'
      });
    }, 2000);
  },

  /**
   * 调节光照
   */
  adjustLighting: function() {
    wx.showLoading({ title: '调节光照中...' });
    // 模拟调节光照
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '光照调节指令已发送',
        icon: 'success'
      });
    }, 2000);
  },

  /**
   * 记录环境变化日志
   * @param {string} type 环境类型
   * @param {string} status 状态
   * @param {number} value 数值
   */
  logEnvironmentChange: function(type, status, value) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      type: type,
      status: status,
      value: value,
      level: status === 'danger' ? 'error' : status === 'warning' ? 'warn' : 'info'
    };

    // 保存到本地日志
    try {
      let logs = wx.getStorageSync('environment_logs') || [];
      logs.unshift(logEntry);
      
      // 只保留最近100条日志
      if (logs.length > 100) {
        logs = logs.slice(0, 100);
      }
      
      wx.setStorageSync('environment_logs', logs);
    } catch (error) {
      console.error('保存环境日志失败:', error);
    }
  },

  /**
   * 初始化生产组件
   */
  initProductionComponents: function() {
    // 初始化图表数据
    this.setData({
      chartData: {
        temperature: [],
        humidity: [],
        production: []
      },
      dashboardConfig: {
        autoRefresh: true,
        refreshInterval: 30000
      }
    });
  },

  /**
   * 仪表板类型变更事件处理
   * @param {Object} e 事件对象
   */
  onDashboardTypeChange: function(e) {
    const { dashboardType } = e.detail || {};
    
    // 更新仪表板类型并刷新数据
    this.setData({
      currentDashboardType: dashboardType || 'overview'
    });
    
    // 根据新的仪表板类型刷新数据
    this.refreshCurrentTabData();
  }
});