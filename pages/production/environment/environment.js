// pages/production/environment/environment.js
const authHelper = require('../../../utils/auth-helper.js');

Page({
  data: {
    // 当前环境数据
    currentData: {
      temperature: 0,
      humidity: 0,
      pm25: 0,
      light: 0,
      updateTime: ''
    },
    
    // 历史数据
    historyData: {
      temperature: [],
      humidity: [],
      pm25: [],
      light: []
    },
    
    // 时间范围选项
    timeRanges: [
      { label: '近24小时', value: '24h' },
      { label: '近7天', value: '7d' },
      { label: '近30天', value: '30d' }
    ],
    activeTimeRange: '24h',
    
    // 报警信息
    alerts: [],
    
    // 正常范围
    normalRanges: {
      temperature: { min: 15, max: 30 },
      humidity: { min: 50, max: 80 },
      pm25: { min: 0, max: 50 },
      light: { min: 200, max: 2000 }
    },
    
    loading: true,

    // 选中的趋势类型
    selectedTrendType: '',

    // 趋势数据
    trendData: [],

    // 图表数据
    chartData: [],
    
    // 图表配置
    chartConfig: {
      type: 'line',
      smooth: true,
      showPoints: true,
      showGrid: true,
      animation: true
    },

    // 图例数据
    legendData: [],

    // 当前图表值
    currentChartValues: null
  },

  onLoad: function (options) {

    // 检查页面权限
    const hasPermission = authHelper.checkPagePermission(options, 'production:environment:read', () => {
      // 权限检查通过，加载数据
      this.loadData();
    });

    if (!hasPermission) {
      return; // 权限检查失败，不继续执行
    }
  },

  onShow: function () {
    // 页面显示
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.loadData(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载数据
  loadData: function (callback) {
    this.setData({
      loading: true
    });

    // 模拟加载数据
    setTimeout(() => {
      // 模拟当前环境数据
      const currentData = {
        temperature: 26.5,
        humidity: 65,
        pm25: 32,
        light: 800,
        updateTime: '2023-06-15 14:30'
      };

      // 模拟历史数据（近24小时，每2小时一个点）
      const historyData = {
        temperature: [
          { time: '14:30', value: 26.5 },
          { time: '12:30', value: 27.2 },
          { time: '10:30', value: 26.8 },
          { time: '08:30', value: 25.9 },
          { time: '06:30', value: 24.5 },
          { time: '04:30', value: 23.8 },
          { time: '02:30', value: 23.2 },
          { time: '00:30', value: 22.9 },
          { time: '22:30', value: 23.5 },
          { time: '20:30', value: 24.2 },
          { time: '18:30', value: 25.1 },
          { time: '16:30', value: 25.8 }
        ],
        humidity: [
          { time: '14:30', value: 65 },
          { time: '12:30', value: 67 },
          { time: '10:30', value: 68 },
          { time: '08:30', value: 70 },
          { time: '06:30', value: 72 },
          { time: '04:30', value: 74 },
          { time: '02:30', value: 75 },
          { time: '00:30', value: 73 },
          { time: '22:30', value: 71 },
          { time: '20:30', value: 69 },
          { time: '18:30', value: 67 },
          { time: '16:30', value: 66 }
        ],
        pm25: [
          { time: '14:30', value: 32 },
          { time: '12:30', value: 35 },
          { time: '10:30', value: 38 },
          { time: '08:30', value: 42 },
          { time: '06:30', value: 45 },
          { time: '04:30', value: 48 },
          { time: '02:30', value: 46 },
          { time: '00:30', value: 44 },
          { time: '22:30', value: 40 },
          { time: '20:30', value: 38 },
          { time: '18:30', value: 35 },
          { time: '16:30', value: 33 }
        ],
        light: [
          { time: '14:30', value: 800 },
          { time: '12:30', value: 1200 },
          { time: '10:30', value: 800 },
          { time: '08:30', value: 400 },
          { time: '06:30', value: 200 },
          { time: '04:30', value: 50 },
          { time: '02:30', value: 20 },
          { time: '00:30', value: 10 },
          { time: '22:30', value: 150 },
          { time: '20:30', value: 300 },
          { time: '18:30', value: 550 },
          { time: '16:30', value: 750 }
        ]
      };

      // 模拟报警信息
      const alerts = [
        { id: 1, type: 'warning', message: '温度过高', time: '2023-06-15 12:30', status: '未处理' },
        { id: 2, type: 'info', message: '湿度过低', time: '2023-06-15 06:30', status: '已处理' }
      ];

      this.setData({
        currentData: currentData,
        historyData: historyData,
        alerts: alerts,
        loading: false
      });

      callback && callback();
    }, 500);
  },

  // 切换时间范围
  onTimeRangeChange: function (e) {
    let range;
    
    // 支持两种调用方式：直接点击和组件事件
    if (e.detail && e.detail.value) {
      range = e.detail.value;
    } else {
      range = e.currentTarget.dataset.range;
    }

    this.setData({
      activeTimeRange: range
    });
    
    // 重新生成对应时间范围的图表数据
    if (this.data.selectedTrendType) {
      this.generateChartData(this.data.selectedTrendType, range);
    }
  },

  // 查看报警详情
  onViewAlert: function (e) {
    const alert = e.currentTarget.dataset.alert;
    wx.showToast({
      title: '查看详情功能开发中',
      icon: 'none'
    });
  },

  // 设置报警阈值
  onSetThreshold: function () {
    wx.showToast({
      title: '设置报警阈值功能开发中',
      icon: 'none'
    });
  },

  // 查看趋势图
  onViewTrend: function (e) {
    const type = e.currentTarget.dataset.type;
    const typeNames = {
      temperature: '温度',
      humidity: '湿度',
      pm25: 'PM2.5',
      light: '光照'
    };

    const typeName = typeNames[type];
    if (!typeName) return;

    this.setData({
      selectedTrendType: typeName
    });

    // 生成图表数据
    this.generateChartData(type, this.data.activeTimeRange);
  },

  // 生成图表数据
  generateChartData: function(type, timeRange) {
    const typeConfigs = {
      temperature: { unit: '°C', color: '#FF6B6B', min: 15, max: 35 },
      humidity: { unit: '%', color: '#4ECDC4', min: 40, max: 90 },
      pm25: { unit: '', color: '#45B7D1', min: 0, max: 100 },
      light: { unit: 'lx', color: '#FFA726', min: 0, max: 2000 }
    };

    const config = typeConfigs[type];
    if (!config) return;

    // 根据时间范围生成不同数量的数据点
    let dataCount, timeFormat, timeStep;
    switch (timeRange) {
      case '24h':
        dataCount = 12; // 每2小时一个点
        timeFormat = 'hour';
        timeStep = 2;
        break;
      case '7d':
        dataCount = 7; // 每天一个点
        timeFormat = 'day';
        timeStep = 1;
        break;
      case '30d':
        dataCount = 15; // 每2天一个点
        timeFormat = 'day';
        timeStep = 2;
        break;
      default:
        dataCount = 12;
        timeFormat = 'hour';
        timeStep = 2;
    }

    // 生成模拟数据
    const chartData = [];
    const now = new Date();
    
    for (let i = dataCount - 1; i >= 0; i--) {
      const time = new Date(now);
      
      if (timeFormat === 'hour') {
        time.setHours(time.getHours() - i * timeStep);
      } else {
        time.setDate(time.getDate() - i * timeStep);
      }

      // 生成符合正常范围的随机值，并添加一些变化趋势
      const baseValue = (config.min + config.max) / 2;
      const variation = (config.max - config.min) * 0.3;
      const trendFactor = Math.sin((i / dataCount) * Math.PI * 2) * 0.2;
      const randomFactor = (Math.random() - 0.5) * 0.6;
      
      let value = baseValue + variation * (trendFactor + randomFactor);
      value = Math.max(config.min, Math.min(config.max, value));

      chartData.push({
        time: timeFormat === 'hour' 
          ? `${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`
          : `${(time.getMonth() + 1).toString().padStart(2, '0')}-${time.getDate().toString().padStart(2, '0')}`,
        value: Math.round(value * 10) / 10
      });
    }

    // 设置图例数据
    const legendData = [{
      key: type,
      label: this.data.selectedTrendType,
      color: config.color
    }];

    // 设置当前值显示
    const currentChartValues = {
      time: chartData[chartData.length - 1]?.time || '',
      values: [{
        key: type,
        label: this.data.selectedTrendType,
        value: chartData[chartData.length - 1]?.value || 0,
        unit: config.unit,
        color: config.color
      }]
    };

    this.setData({
      chartData: chartData,
      legendData: legendData,
      currentChartValues: currentChartValues
    });
  },

  // 图表渲染完成
  onChartRendered: function(e) {
  },

  // 刷新图表
  onRefreshChart: function() {
    if (this.data.selectedTrendType) {
      // 从selectedTrendType反推type
      const typeNames = {
        '温度': 'temperature',
        '湿度': 'humidity',
        'PM2.5': 'pm25',
        '光照': 'light'
      };
      
      const type = typeNames[this.data.selectedTrendType];
      if (type) {
        this.generateChartData(type, this.data.activeTimeRange);
      }
    }
  },

  // 关闭图表
  onCloseChart: function() {
    this.setData({
      selectedTrendType: '',
      chartData: [],
      legendData: [],
      currentChartValues: null
    });
  }
});