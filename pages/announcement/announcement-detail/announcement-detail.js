// pages/announcement/announcement-detail/announcement-detail.js
const { home } = require('../../../utils/api.js');

Page({
  data: {
    announcementId: '',
    announcement: null,
    loading: true,
    error: ''
  },

  onLoad: function (options) {
    if (options.id) {
      this.setData({
        announcementId: options.id
      });
      this.loadAnnouncementDetail();
    } else {
      this.setData({
        loading: false,
        error: '公告ID不存在'
      });
    }
  },

  onShow: function () {
    // 页面显示
  },

  // 加载公告详情
  loadAnnouncementDetail: function () {
    this.setData({
      loading: true,
      error: ''
    });

    // 根据ID生成不同的模拟数据
    const mockAnnouncements = {
      '1': {
        id: '1',
        title: '冬季养鹅注意事项及防疫措施',
        content: `<p>各位养殖户：</p><p>随着冬季的到来，气温逐渐降低，为确保鹅群健康过冬，现将冬季养鹅注意事项通知如下：</p><h3>一、保温措施</h3><p>1. 加强鹅舍保温，及时修补破损部位</p><p>2. 适当增加垫料厚度，保持干燥</p><p>3. 合理控制通风，避免贼风侵袭</p><h3>二、饲养管理</h3><p>1. 适当增加饲料营养浓度</p><p>2. 保证充足的饮水，防止水管结冰</p><p>3. 增加饲喂次数，少量多餐</p><h3>三、疾病防控</h3><p>1. 定期消毒，保持环境卫生</p><p>2. 加强巡查，及时发现异常</p><p>3. 做好疫苗接种工作</p><p>请各位养殖户严格按照要求执行，如有疑问请及时联系技术人员。</p><p style="text-align: right;">技术服务部<br/>2024年12月15日</p>`,
        publishTime: '2024-12-15 09:30',
        isImportant: true,
        attachments: []
      },
      '2': {
        id: '2',
        title: '新品种鹅苗到货通知',
        content: `<p>各位养殖户：</p><p>我们新引进的优质鹅苗已到货，现通知如下：</p><h3>品种信息</h3><p>品种：白鹅优良品种</p><p>数量：5000只</p><p>价格：每只15元</p><h3>预订方式</h3><p>请有需要的养殖户及时联系我们进行预订。</p><p style="text-align: right;">销售部<br/>2024年12月14日</p>`,
        publishTime: '2024-12-14 14:20',
        isImportant: false,
        attachments: []
      }
    };

    const mockAnnouncement = mockAnnouncements[this.data.announcementId] || mockAnnouncements['1'];

    // 模拟网络延迟
    setTimeout(() => {
      this.setData({
        announcement: mockAnnouncement,
        loading: false
      });
    }, 500);

    // 尝试从API获取真实数据
    try {
      home.getAnnouncementDetail && home.getAnnouncementDetail(this.data.announcementId).then(res => {
        if (res && res.success && res.data) {
          this.setData({
            announcement: res.data,
            loading: false
          });
        }
      }).catch(err => {
        // 已经设置了模拟数据
      });
    } catch (error) {
      // 已经设置了模拟数据
    }
  },

  // 下载附件
  onDownloadAttachment: function (e) {
    const { url, name } = e.currentTarget.dataset;
    
    wx.showToast({
      title: '下载功能开发中',
      icon: 'none'
    });
    
    // 实现文件下载功能
    if (attachment.url) {
      wx.downloadFile({
        url: attachment.url,
        success: (res) => {
          if (res.statusCode === 200) {
            wx.openDocument({
              filePath: res.tempFilePath,
              success: () => {
              },
              fail: (err) => {
                console.error('文件打开失败:', err);
                wx.showToast({
                  title: '文件打开失败',
                  icon: 'none'
                });
              }
            });
          }
        },
        fail: (err) => {
          console.error('文件下载失败:', err);
          wx.showToast({
            title: '文件下载失败',
            icon: 'none'
          });
        }
      });
    }
    // wx.downloadFile({
    //   url: url,
    //   success: function(res) {
    //     // 下载成功处理
    //   }
    // });
  },

  // 分享公告
  onShare: function () {
    return {
      title: this.data.announcement.title,
      path: `/pages/announcement/announcement-detail/announcement-detail?id=${this.data.announcementId}`
    };
  },

  // 页面分享
  onShareAppMessage: function () {
    return this.onShare();
  }
});
