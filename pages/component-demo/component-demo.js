// pages/component-demo/component-demo.js
const { performanceMixin } = require('../../utils/performance.js');

/**
 * 高级组件演示页面
 * 展示表单构建器、图表组件、模态弹窗等高级组件的使用方法
 */
Page(Object.assign({}, performanceMixin, {
  data: {
    // 当前标签页
    activeTab: 0,
    tabs: [
      { id: 0, name: '表单组件' },
      { id: 1, name: '图表组件' },
      { id: 2, name: '弹窗组件' }
    ],

    // 表单相关
    showForm: false,
    formSubmitting: false,
    formConfig: {
      fields: [
        {
          name: 'name',
          label: '姓名',
          type: 'text',
          required: true,
          placeholder: '请输入姓名',
          rules: [
            { type: 'minLength', value: 2, message: '姓名至少2个字符' }
          ]
        },
        {
          name: 'phone',
          label: '手机号',
          type: 'text',
          required: true,
          placeholder: '请输入手机号',
          rules: [
            { type: 'phone', message: '请输入正确的手机号' }
          ]
        },
        {
          name: 'email',
          label: '邮箱',
          type: 'text',
          placeholder: '请输入邮箱地址',
          rules: [
            { type: 'email', message: '请输入正确的邮箱地址' }
          ]
        },
        {
          name: 'age',
          label: '年龄',
          type: 'number',
          placeholder: '请输入年龄',
          rules: [
            { type: 'number', message: '年龄必须是数字' }
          ]
        },
        {
          name: 'description',
          label: '描述',
          type: 'textarea',
          placeholder: '请输入详细描述',
          help: '请详细描述您的需求'
        }
      ]
    },
    formData: {
      name: '张三',
      phone: '13812345678',
      email: '<EMAIL>',
      age: 25,
      description: '这是一个演示表单'
    },

    // 图表相关
    chartType: 'line',
    chartTypes: [
      { value: 'line', label: '折线图' },
      { value: 'bar', label: '柱状图' },
      { value: 'pie', label: '饼图' },
      { value: 'area', label: '面积图' }
    ],
    
    // 图表数据
    lineChartData: [
      { label: '1月', value: 120 },
      { label: '2月', value: 132 },
      { label: '3月', value: 101 },
      { label: '4月', value: 134 },
      { label: '5月', value: 90 },
      { label: '6月', value: 230 },
      { label: '7月', value: 210 }
    ],

    barChartData: [
      { label: '产品A', value: 320 },
      { label: '产品B', value: 240 },
      { label: '产品C', value: 180 },
      { label: '产品D', value: 150 },
      { label: '产品E', value: 100 }
    ],

    pieChartData: [
      { label: '健康鹅', value: 65 },
      { label: '待观察', value: 20 },
      { label: '治疗中', value: 10 },
      { label: '其他', value: 5 }
    ],

    areaChartData: [
      { label: '0h', value: 0 },
      { label: '6h', value: 20 },
      { label: '12h', value: 40 },
      { label: '18h', value: 35 },
      { label: '24h', value: 50 }
    ],

    // 弹窗相关
    showDefaultModal: false,
    showConfirmModal: false,
    showAlertModal: false,
    showCustomModal: false,
    modalResult: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
  },

  /**
   * 标签页切换
   */
  onTabChange: function(e) {
    const { current } = e.detail;
    this.safeSetData({
      activeTab: current
    });
  },

  // ======== 表单组件演示 ========

  /**
   * 显示表单
   */
  showFormDemo: function() {
    this.safeSetData({
      showForm: true
    });
  },

  /**
   * 表单提交
   */
  onFormSubmit: function(e) {
    const { formData, isValid, errors } = e.detail;

    if (isValid) {
      this.safeSetData({
        formSubmitting: true
      });

      // 模拟提交过程
      setTimeout(() => {
        this.safeSetData({
          formSubmitting: false,
          showForm: false
        });

        wx.showToast({
          title: '提交成功',
          icon: 'success'
        });
      }, 2000);
    }
  },

  /**
   * 表单取消
   */
  onFormCancel: function() {
    this.safeSetData({
      showForm: false
    });
  },

  /**
   * 表单关闭
   */
  onFormClose: function() {
    this.safeSetData({
      showForm: false
    });
  },

  // ======== 图表组件演示 ========

  /**
   * 切换图表类型
   */
  onChartTypeChange: function(e) {
    const { value } = e.detail;
    this.safeSetData({
      chartType: value
    });
  },

  /**
   * 获取图表数据
   */
  getChartData: function() {
    const { chartType } = this.data;
    const dataMap = {
      line: this.data.lineChartData,
      bar: this.data.barChartData,
      pie: this.data.pieChartData,
      area: this.data.areaChartData
    };
    return dataMap[chartType] || [];
  },

  /**
   * 图表渲染完成
   */
  onChartRendered: function(e) {
    const { type, data } = e.detail;
  },

  /**
   * 图表点击
   */
  onChartTap: function(e) {
    const { x, y, data } = e.detail;
    
    wx.showToast({
      title: '图表被点击',
      icon: 'none'
    });
  },

  /**
   * 刷新图表数据
   */
  refreshChartData: function() {
    // 生成随机数据
    const { chartType } = this.data;
    let newData = [];

    switch (chartType) {
      case 'line':
      case 'area':
        newData = this.data.lineChartData.map(item => ({
          ...item,
          value: Math.floor(Math.random() * 200) + 50
        }));
        this.safeSetData({
          [`${chartType}ChartData`]: newData
        });
        break;
      case 'bar':
        newData = this.data.barChartData.map(item => ({
          ...item,
          value: Math.floor(Math.random() * 300) + 50
        }));
        this.safeSetData({
          barChartData: newData
        });
        break;
      case 'pie':
        const total = 100;
        const values = [
          Math.floor(Math.random() * 40) + 30,
          Math.floor(Math.random() * 20) + 10,
          Math.floor(Math.random() * 15) + 5,
          Math.floor(Math.random() * 10) + 5
        ];
        newData = this.data.pieChartData.map((item, index) => ({
          ...item,
          value: values[index]
        }));
        this.safeSetData({
          pieChartData: newData
        });
        break;
    }

    wx.showToast({
      title: '数据已刷新',
      icon: 'success'
    });
  },

  // ======== 弹窗组件演示 ========

  /**
   * 显示默认弹窗
   */
  showDefaultModalDemo: function() {
    this.safeSetData({
      showDefaultModal: true
    });
  },

  /**
   * 显示确认弹窗
   */
  showConfirmModalDemo: function() {
    this.safeSetData({
      showConfirmModal: true
    });
  },

  /**
   * 显示提示弹窗
   */
  showAlertModalDemo: function() {
    this.safeSetData({
      showAlertModal: true
    });
  },

  /**
   * 显示自定义弹窗
   */
  showCustomModalDemo: function() {
    this.safeSetData({
      showCustomModal: true
    });
  },

  /**
   * 弹窗确认
   */
  onModalConfirm: function(e) {
    this.safeSetData({
      modalResult: '用户点击了确认',
      showDefaultModal: false,
      showConfirmModal: false,
      showAlertModal: false
    });
  },

  /**
   * 弹窗取消
   */
  onModalCancel: function(e) {
    this.safeSetData({
      modalResult: '用户点击了取消',
      showDefaultModal: false,
      showConfirmModal: false
    });
  },

  /**
   * 弹窗关闭
   */
  onModalClose: function(e) {
    this.safeSetData({
      showDefaultModal: false,
      showConfirmModal: false,
      showAlertModal: false,
      showCustomModal: false
    });
  },

  /**
   * 自定义弹窗确认
   */
  onCustomModalConfirm: function() {
    this.safeSetData({
      modalResult: '自定义弹窗确认',
      showCustomModal: false
    });
  },

  /**
   * 清除结果
   */
  clearResult: function() {
    this.safeSetData({
      modalResult: ''
    });
  }
}));