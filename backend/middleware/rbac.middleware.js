/**
 * 基于角色的访问控制中间件 (RBAC)
 * Role-Based Access Control Middleware
 */

const tenantDatabaseManager = require('../models/tenant-database.model');

/**
 * 权限定义
 */
const PERMISSIONS = {
  // 鹅群管理权限
  FLOCK_READ: 'flock:read',
  FLOCK_CREATE: 'flock:create',
  FLOCK_UPDATE: 'flock:update',
  FLOCK_DELETE: 'flock:delete',
  
  // 健康管理权限
  HEALTH_READ: 'health:read',
  HEALTH_CREATE: 'health:create',
  HEALTH_UPDATE: 'health:update',
  HEALTH_DELETE: 'health:delete',
  
  // 生产管理权限
  PRODUCTION_READ: 'production:read',
  PRODUCTION_CREATE: 'production:create',
  PRODUCTION_UPDATE: 'production:update',
  PRODUCTION_DELETE: 'production:delete',
  
  // 用户管理权限
  USER_READ: 'user:read',
  USER_CREATE: 'user:create',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  
  // 商城管理权限
  SHOP_READ: 'shop:read',
  SHOP_CREATE: 'shop:create',
  SHOP_UPDATE: 'shop:update',
  SHOP_DELETE: 'shop:delete',
  
  // 数据导出权限
  DATA_EXPORT: 'data:export',
  
  // AI诊断权限
  AI_DIAGNOSIS: 'ai:diagnosis',
  
  // 系统管理权限
  SYSTEM_ADMIN: 'system:admin',
  
  // 🆕 租户内管理权限 (小程序管理功能)
  TENANT_MANAGEMENT: 'tenant:management',
  STAFF_MANAGE: 'staff:manage',
  APPROVAL_MANAGE: 'approval:manage',
  REPORTS_VIEW: 'reports:view',
  SETTINGS_MANAGE: 'settings:manage',
  
  // 🆕 平台级权限 (SAAS管理后台)
  PLATFORM_ADMIN: 'platform:admin',
  CROSS_TENANT_READ: 'cross-tenant:read',
  TENANT_MANAGE: 'tenant:manage',
  SUBSCRIPTION_MANAGE: 'subscription:manage',
  PLATFORM_ANALYTICS: 'platform:analytics',
  SYSTEM_MONITOR: 'system:monitor'
};

/**
 * 角色权限映射
 */
const ROLE_PERMISSIONS = {
  // 🆕 平台超级管理员 (SAAS平台运营方)
  platform_admin: [
    // 拥有所有平台级权限
    PERMISSIONS.PLATFORM_ADMIN,
    PERMISSIONS.CROSS_TENANT_READ,
    PERMISSIONS.TENANT_MANAGE,
    PERMISSIONS.SUBSCRIPTION_MANAGE,
    PERMISSIONS.PLATFORM_ANALYTICS,
    PERMISSIONS.SYSTEM_MONITOR,
    PERMISSIONS.SYSTEM_ADMIN
  ],
  
  // 🆕 平台运营人员
  platform_operator: [
    PERMISSIONS.CROSS_TENANT_READ,
    PERMISSIONS.TENANT_MANAGE,
    PERMISSIONS.PLATFORM_ANALYTICS
  ],
  
  // 租户拥有者 (企业主)
  owner: [
    // 拥有租户内所有权限
    PERMISSIONS.FLOCK_READ, PERMISSIONS.FLOCK_CREATE, PERMISSIONS.FLOCK_UPDATE, PERMISSIONS.FLOCK_DELETE,
    PERMISSIONS.HEALTH_READ, PERMISSIONS.HEALTH_CREATE, PERMISSIONS.HEALTH_UPDATE, PERMISSIONS.HEALTH_DELETE,
    PERMISSIONS.PRODUCTION_READ, PERMISSIONS.PRODUCTION_CREATE, PERMISSIONS.PRODUCTION_UPDATE, PERMISSIONS.PRODUCTION_DELETE,
    PERMISSIONS.USER_READ, PERMISSIONS.USER_CREATE, PERMISSIONS.USER_UPDATE, PERMISSIONS.USER_DELETE,
    PERMISSIONS.SHOP_READ, PERMISSIONS.SHOP_CREATE, PERMISSIONS.SHOP_UPDATE, PERMISSIONS.SHOP_DELETE,
    PERMISSIONS.DATA_EXPORT,
    PERMISSIONS.AI_DIAGNOSIS,
    PERMISSIONS.SYSTEM_ADMIN,
    // 🆕 租户内管理权限
    PERMISSIONS.TENANT_MANAGEMENT,
    PERMISSIONS.STAFF_MANAGE,
    PERMISSIONS.APPROVAL_MANAGE,
    PERMISSIONS.REPORTS_VIEW,
    PERMISSIONS.SETTINGS_MANAGE
  ],
  
  // 租户管理员 (养殖场管理员)
  admin: [
    PERMISSIONS.FLOCK_READ, PERMISSIONS.FLOCK_CREATE, PERMISSIONS.FLOCK_UPDATE, PERMISSIONS.FLOCK_DELETE,
    PERMISSIONS.HEALTH_READ, PERMISSIONS.HEALTH_CREATE, PERMISSIONS.HEALTH_UPDATE, PERMISSIONS.HEALTH_DELETE,
    PERMISSIONS.PRODUCTION_READ, PERMISSIONS.PRODUCTION_CREATE, PERMISSIONS.PRODUCTION_UPDATE, PERMISSIONS.PRODUCTION_DELETE,
    PERMISSIONS.USER_READ, PERMISSIONS.USER_CREATE, PERMISSIONS.USER_UPDATE,
    PERMISSIONS.SHOP_READ, PERMISSIONS.SHOP_CREATE, PERMISSIONS.SHOP_UPDATE, PERMISSIONS.SHOP_DELETE,
    PERMISSIONS.DATA_EXPORT,
    PERMISSIONS.AI_DIAGNOSIS,
    // 🆕 租户内管理权限
    PERMISSIONS.TENANT_MANAGEMENT,
    PERMISSIONS.STAFF_MANAGE,
    PERMISSIONS.APPROVAL_MANAGE,
    PERMISSIONS.REPORTS_VIEW,
    PERMISSIONS.SETTINGS_MANAGE
  ],
  
  // 部门经理
  manager: [
    PERMISSIONS.FLOCK_READ, PERMISSIONS.FLOCK_CREATE, PERMISSIONS.FLOCK_UPDATE,
    PERMISSIONS.HEALTH_READ, PERMISSIONS.HEALTH_CREATE, PERMISSIONS.HEALTH_UPDATE,
    PERMISSIONS.PRODUCTION_READ, PERMISSIONS.PRODUCTION_CREATE, PERMISSIONS.PRODUCTION_UPDATE,
    PERMISSIONS.USER_READ,
    PERMISSIONS.SHOP_READ,
    PERMISSIONS.DATA_EXPORT,
    PERMISSIONS.AI_DIAGNOSIS,
    // 🆕 部分管理权限
    PERMISSIONS.TENANT_MANAGEMENT,
    PERMISSIONS.APPROVAL_MANAGE,
    PERMISSIONS.REPORTS_VIEW
  ],
  
  // 普通员工
  user: [
    PERMISSIONS.FLOCK_READ,
    PERMISSIONS.HEALTH_READ, PERMISSIONS.HEALTH_CREATE,
    PERMISSIONS.PRODUCTION_READ, PERMISSIONS.PRODUCTION_CREATE,
    PERMISSIONS.SHOP_READ
  ]
};

/**
 * 检查用户是否有指定权限
 * @param {Object} user 用户对象
 * @param {string} permission 权限字符串
 * @returns {boolean} 是否有权限
 */
function hasPermission(user, permission) {
  if (!user || !user.role) {
    return false;
  }
  
  const rolePermissions = ROLE_PERMISSIONS[user.role] || [];
  return rolePermissions.includes(permission);
}

/**
 * 检查用户是否有多个权限中的任意一个
 * @param {Object} user 用户对象
 * @param {Array} permissions 权限数组
 * @returns {boolean} 是否有任意一个权限
 */
function hasAnyPermission(user, permissions) {
  return permissions.some(permission => hasPermission(user, permission));
}

/**
 * 检查用户是否有所有指定权限
 * @param {Object} user 用户对象
 * @param {Array} permissions 权限数组
 * @returns {boolean} 是否有所有权限
 */
function hasAllPermissions(user, permissions) {
  return permissions.every(permission => hasPermission(user, permission));
}

/**
 * 权限验证中间件
 * @param {string|Array} requiredPermissions 必需的权限
 * @param {Object} options 选项 { requireAll: false }
 */
function requirePermission(requiredPermissions, options = {}) {
  const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];
  const requireAll = options.requireAll || false;
  
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '用户未认证',
        code: 'AUTHENTICATION_REQUIRED'
      });
    }
    
    const hasRequiredPermission = requireAll 
      ? hasAllPermissions(req.user, permissions)
      : hasAnyPermission(req.user, permissions);
    
    if (!hasRequiredPermission) {
      return res.status(403).json({
        success: false,
        message: '权限不足',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: permissions,
        userRole: req.user.role
      });
    }
    
    next();
  };
}

/**
 * 资源所有者验证中间件
 * 确保用户只能访问自己的资源
 * @param {string} resourceIdParam 资源ID参数名
 * @param {string} resourceTable 资源表名
 * @param {string} ownerField 所有者字段名，默认为 'userId'
 */
function requireResourceOwner(resourceIdParam, resourceTable, ownerField = 'userId') {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: '用户未认证',
          code: 'AUTHENTICATION_REQUIRED'
        });
      }
      
      const resourceId = req.params[resourceIdParam];
      if (!resourceId) {
        return res.status(400).json({
          success: false,
          message: '缺少资源ID参数',
          code: 'MISSING_RESOURCE_ID'
        });
      }
      
      // 查询资源
      const [resources] = await req.tenantDb.query(
        `SELECT ${ownerField} FROM ${resourceTable} WHERE id = ?`,
        { replacements: [resourceId] }
      );
      
      if (resources.length === 0) {
        return res.status(404).json({
          success: false,
          message: '资源不存在',
          code: 'RESOURCE_NOT_FOUND'
        });
      }
      
      const resource = resources[0];
      
      // 检查是否是资源所有者或管理员
      if (resource[ownerField] !== req.user.id && !hasPermission(req.user, PERMISSIONS.SYSTEM_ADMIN)) {
        return res.status(403).json({
          success: false,
          message: '无权访问此资源',
          code: 'RESOURCE_ACCESS_DENIED'
        });
      }
      
      next();
    } catch (error) {
      console.error('资源所有者验证失败:', error);
      res.status(500).json({
        success: false,
        message: '权限验证失败',
        code: 'PERMISSION_CHECK_ERROR'
      });
    }
  };
}

/**
 * 数据行级安全中间件
 * 自动在查询中添加租户和用户过滤条件
 * @param {Object} options 选项配置
 */
function rowLevelSecurity(options = {}) {
  return (req, res, next) => {
    // 保存原始的查询方法
    const originalQuery = req.tenantDb.query;
    
    // 重写查询方法，自动添加安全过滤
    req.tenantDb.query = function(sql, options = {}) {
      let modifiedSql = sql;
      let modifiedReplacements = options.replacements || [];
      
      // 如果是SELECT查询且包含用户相关表，自动添加用户过滤
      if (sql.toLowerCase().includes('select') && req.user) {
        // 为特定表添加用户过滤
        const userFilterTables = ['flocks', 'health_records', 'production_records'];
        
        userFilterTables.forEach(table => {
          if (sql.toLowerCase().includes(table)) {
            // 如果用户不是管理员，只能查看自己的数据
            if (!hasPermission(req.user, PERMISSIONS.SYSTEM_ADMIN)) {
              if (!sql.toLowerCase().includes('where')) {
                modifiedSql += ` WHERE ${table}.userId = ?`;
                modifiedReplacements.push(req.user.id);
              } else if (!sql.toLowerCase().includes(`${table}.userid`)) {
                modifiedSql += ` AND ${table}.userId = ?`;
                modifiedReplacements.push(req.user.id);
              }
            }
          }
        });
      }
      
      return originalQuery.call(this, modifiedSql, {
        ...options,
        replacements: modifiedReplacements
      });
    };
    
    // 在请求结束后恢复原始方法
    res.on('finish', () => {
      req.tenantDb.query = originalQuery;
    });
    
    next();
  };
}

/**
 * API限流中间件（基于用户角色）
 * @param {Object} limits 各角色的限制配置
 */
function roleBasedRateLimit(limits = {}) {
  const defaultLimits = {
    owner: { windowMs: 15 * 60 * 1000, max: 1000 }, // 15分钟1000次
    admin: { windowMs: 15 * 60 * 1000, max: 500 },  // 15分钟500次
    manager: { windowMs: 15 * 60 * 1000, max: 200 }, // 15分钟200次
    user: { windowMs: 15 * 60 * 1000, max: 100 }     // 15分钟100次
  };
  
  const rateLimits = { ...defaultLimits, ...limits };
  
  return (req, res, next) => {
    if (!req.user) {
      return next();
    }
    
    const userRole = req.user.role || 'user';
    const limit = rateLimits[userRole] || rateLimits.user;
    
    // 这里可以集成实际的限流库，如 express-rate-limit
    // 暂时只记录日志
    
    next();
  };
}

/**
 * 审计日志中间件
 * 记录重要操作的审计日志
 */
function auditLog(action) {
  return async (req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      // 记录审计日志
      const auditEntry = {
        tenantId: req.tenant?.id,
        userId: req.user?.id,
        action: action,
        resource: req.originalUrl,
        method: req.method,
        ip: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent'),
        success: res.statusCode < 400,
        statusCode: res.statusCode,
        timestamp: new Date(),
        requestBody: req.method !== 'GET' ? req.body : null
      };
      
      // 异步记录到数据库
      if (req.tenant?.id) {
        recordAuditLog(auditEntry).catch(error => {
          console.error('记录审计日志失败:', error);
        });
      }
      
      return originalSend.call(this, data);
    };
    
    next();
  };
}

/**
 * 记录审计日志到数据库
 * @param {Object} auditEntry 审计条目
 */
async function recordAuditLog(auditEntry) {
  try {
    const saasDb = tenantDatabaseManager.getSaasConnection();
    
    await saasDb.query(
      `INSERT INTO operation_logs (
        tenantId, userId, action, resource, method, 
        ipAddress, userAgent, success, statusCode, 
        requestData, createdAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      {
        replacements: [
          auditEntry.tenantId,
          auditEntry.userId,
          auditEntry.action,
          auditEntry.resource,
          auditEntry.method,
          auditEntry.ip,
          auditEntry.userAgent,
          auditEntry.success,
          auditEntry.statusCode,
          JSON.stringify(auditEntry.requestBody),
          auditEntry.timestamp
        ]
      }
    );
  } catch (error) {
    console.error('记录审计日志到数据库失败:', error);
  }
}

/**
 * 功能开关中间件
 * 根据租户订阅计划控制功能访问
 * @param {string} feature 功能名称
 */
function requireFeature(feature) {
  return (req, res, next) => {
    if (!req.tenant) {
      return res.status(400).json({
        success: false,
        message: '租户信息缺失',
        code: 'TENANT_REQUIRED'
      });
    }
    
    // 检查租户是否有此功能权限
    const tenantFeatures = req.tenant.features || {};
    
    if (!tenantFeatures[feature]) {
      return res.status(403).json({
        success: false,
        message: '当前订阅计划不支持此功能',
        code: 'FEATURE_NOT_AVAILABLE',
        feature: feature,
        plan: req.tenant.plan
      });
    }
    
    next();
  };
}

/**
 * 🆕 平台管理员身份验证中间件
 * 用于SAAS管理后台的身份验证
 */
function authenticateAdmin(req, res, next) {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: '需要登录'
    });
  }

  // 检查是否为平台管理员
  const platformRoles = ['platform_admin', 'platform_operator'];
  if (!platformRoles.includes(req.user.role)) {
    return res.status(403).json({
      success: false,
      message: '需要平台管理员权限'
    });
  }

  next();
}

/**
 * 🆕 跨租户数据访问权限验证
 * 确保只有平台管理员能访问跨租户数据
 */
function requireCrossTenantAccess(req, res, next) {
  if (!hasPermission(req.user, PERMISSIONS.CROSS_TENANT_READ)) {
    return res.status(403).json({
      success: false,
      message: '无权访问跨租户数据'
    });
  }
  
  next();
}

/**
 * 🆕 租户内管理权限验证
 * 用于小程序管理功能的权限控制
 */
function requireTenantManagement(req, res, next) {
  if (!hasPermission(req.user, PERMISSIONS.TENANT_MANAGEMENT)) {
    return res.status(403).json({
      success: false,
      message: '无权访问管理功能'
    });
  }
  
  next();
}

/**
 * 🆕 员工管理权限验证
 */
function requireStaffManagement(req, res, next) {
  if (!hasPermission(req.user, PERMISSIONS.STAFF_MANAGE)) {
    return res.status(403).json({
      success: false,
      message: '无权管理员工'
    });
  }
  
  next();
}

/**
 * 🆕 审批管理权限验证
 */
function requireApprovalManagement(req, res, next) {
  if (!hasPermission(req.user, PERMISSIONS.APPROVAL_MANAGE)) {
    return res.status(403).json({
      success: false,
      message: '无权管理审批流程'
    });
  }
  
  next();
}

/**
 * 🆕 平台监控权限验证
 */
function requirePlatformMonitoring(req, res, next) {
  if (!hasPermission(req.user, PERMISSIONS.SYSTEM_MONITOR)) {
    return res.status(403).json({
      success: false,
      message: '无权访问系统监控'
    });
  }
  
  next();
}

/**
 * 🆕 租户数据安全检查
 * 确保用户只能访问自己租户的数据（除非是平台管理员）
 */
function ensureTenantDataSecurity(req, res, next) {
  // 平台管理员可以访问所有租户数据
  if (hasPermission(req.user, PERMISSIONS.CROSS_TENANT_READ)) {
    return next();
  }

  // 普通用户必须在租户上下文中
  if (!req.tenant) {
    return res.status(403).json({
      success: false,
      message: '无法确定租户身份'
    });
  }

  // 检查用户是否属于当前租户
  if (req.user.tenantId && req.user.tenantId !== req.tenant.id) {
    return res.status(403).json({
      success: false,
      message: '无权访问其他租户数据'
    });
  }

  next();
}

module.exports = {
  PERMISSIONS,
  ROLE_PERMISSIONS,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  requirePermission,
  requireResourceOwner,
  rowLevelSecurity,
  roleBasedRateLimit,
  auditLog,
  requireFeature,
  recordAuditLog,
  
  // 🆕 新增的权限控制函数
  authenticateAdmin,
  requireCrossTenantAccess,
  requireTenantManagement,
  requireStaffManagement,
  requireApprovalManagement,
  requirePlatformMonitoring,
  ensureTenantDataSecurity
};