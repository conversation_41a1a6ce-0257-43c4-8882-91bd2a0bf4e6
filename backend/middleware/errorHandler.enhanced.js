/**
 * 增强型错误处理中间件
 * Enhanced Error Handling Middleware
 * 
 * 统一的错误处理、请求日志记录和性能监控
 */

const { Logger } = require('../utils/logger');

/**
 * 请求日志中间件
 */
const requestLogger = (req, res, next) => {
  const start = Date.now();
  
  // 记录请求开始
  Logger.apiRequest(req);
  
  // 监听响应结束
  res.on('finish', () => {
    const duration = Date.now() - start;
    Logger.apiResponse(req, res, duration);
  });
  
  next();
};

/**
 * 增强型错误处理中间件
 */
const errorHandler = (err, req, res, next) => {
  // 记录错误详情
  Logger.error('请求处理错误', {
    error: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    tenantId: req.tenant?.id,
    body: req.body,
    params: req.params,
    query: req.query
  });

  // 安全错误响应
  let statusCode = err.statusCode || err.status || 500;
  let message = '服务器内部错误';
  let errorCode = 'INTERNAL_SERVER_ERROR';

  // 根据错误类型设置响应
  if (err.name === 'ValidationError') {
    statusCode = 400;
    message = '数据验证失败';
    errorCode = 'VALIDATION_ERROR';
  } else if (err.name === 'UnauthorizedError' || err.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = '身份验证失败';
    errorCode = 'UNAUTHORIZED';
  } else if (err.name === 'ForbiddenError') {
    statusCode = 403;
    message = '访问被拒绝';
    errorCode = 'FORBIDDEN';
  } else if (err.name === 'NotFoundError') {
    statusCode = 404;
    message = '资源不存在';
    errorCode = 'NOT_FOUND';
  } else if (err.name === 'SequelizeValidationError') {
    statusCode = 400;
    message = '数据验证失败';
    errorCode = 'DATABASE_VALIDATION_ERROR';
  } else if (err.name === 'SequelizeUniqueConstraintError') {
    statusCode = 409;
    message = '数据冲突';
    errorCode = 'DUPLICATE_ENTRY';
  } else if (err.message) {
    message = err.message;
  }

  // 构建错误响应
  const errorResponse = {
    success: false,
    message,
    errorCode,
    timestamp: new Date().toISOString(),
    path: req.originalUrl
  };

  // 开发环境返回详细错误信息
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error = {
      message: err.message,
      stack: err.stack,
      name: err.name
    };
  }

  res.status(statusCode).json(errorResponse);
};

/**
 * 404 处理中间件
 */
const notFoundHandler = (req, res) => {
  Logger.warn('404 - 路由不存在', {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.status(404).json({
    success: false,
    message: '请求的资源不存在',
    errorCode: 'NOT_FOUND',
    timestamp: new Date().toISOString(),
    path: req.originalUrl
  });
};

/**
 * 异步错误捕获包装器
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 自定义错误类
 */
class AppError extends Error {
  constructor(message, statusCode = 500, errorCode = 'INTERNAL_SERVER_ERROR') {
    super(message);
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

class ValidationError extends AppError {
  constructor(message = '数据验证失败') {
    super(message, 400, 'VALIDATION_ERROR');
  }
}

class UnauthorizedError extends AppError {
  constructor(message = '身份验证失败') {
    super(message, 401, 'UNAUTHORIZED');
  }
}

class ForbiddenError extends AppError {
  constructor(message = '访问被拒绝') {
    super(message, 403, 'FORBIDDEN');
  }
}

class NotFoundError extends AppError {
  constructor(message = '资源不存在') {
    super(message, 404, 'NOT_FOUND');
  }
}

module.exports = {
  requestLogger,
  errorHandler,
  notFoundHandler,
  asyncHandler,
  AppError,
  ValidationError,
  UnauthorizedError,
  ForbiddenError,
  NotFoundError,
  Logger
};