#!/usr/bin/env node
// 完整数据库环境搭建脚本

const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
const fs = require('fs');
const path = require('path');

class CompleteDatabaseSetup {
  constructor() {
    this.rootConfig = {
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '', // 如果root有密码，请在这里设置
      charset: 'utf8mb4'
    };
    
    this.dbConfig = {
      host: 'localhost',
      port: 3306,
      user: 'zhihuiyange',
      password: 'zhihuiyange123',
      database: 'zhihuiyange_local',
      charset: 'utf8mb4'
    };
  }

  async createDatabaseAndUser() {
    
    let connection;
    try {
      connection = await mysql.createConnection(this.rootConfig);

      // 创建数据库
      await connection.execute(`CREATE DATABASE IF NOT EXISTS zhihuiyange_local CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);

      // 创建用户
      try {
        await connection.execute(`CREATE USER IF NOT EXISTS 'zhihuiyange'@'localhost' IDENTIFIED BY 'zhihuiyange123'`);
      } catch (error) {
        if (!error.message.includes('already exists')) {
        }
      }

      // 授权
      await connection.execute(`GRANT ALL PRIVILEGES ON zhihuiyange_local.* TO 'zhihuiyange'@'localhost'`);
      await connection.execute(`FLUSH PRIVILEGES`);

    } catch (error) {
      console.error('❌ 数据库创建失败:', error.message);
      throw error;
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }

  async createTables() {
    
    let connection;
    try {
      connection = await mysql.createConnection(this.dbConfig);
      
      // 用户表
      const createUsersTable = `
        CREATE TABLE IF NOT EXISTS users (
          id INT AUTO_INCREMENT PRIMARY KEY,
          username VARCHAR(50) UNIQUE NOT NULL,
          password VARCHAR(255) NOT NULL,
          email VARCHAR(100),
          phone VARCHAR(20),
          role ENUM('admin', 'manager', 'user') DEFAULT 'user',
          farm_name VARCHAR(100),
          avatar VARCHAR(255),
          status ENUM('active', 'inactive') DEFAULT 'active',
          createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;
      
      await connection.execute(createUsersTable);

      // 健康记录表
      const createHealthRecordsTable = `
        CREATE TABLE IF NOT EXISTS health_records (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          goose_id VARCHAR(50),
          symptoms TEXT,
          diagnosis TEXT,
          treatment TEXT,
          status ENUM('healthy', 'sick', 'recovering', 'dead', 'processing', 'completed') DEFAULT 'healthy',
          record_date DATE,
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
          INDEX idx_user_status (user_id, status),
          INDEX idx_record_date (record_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;
      
      await connection.execute(createHealthRecordsTable);

      // 生产记录表
      const createProductionRecordsTable = `
        CREATE TABLE IF NOT EXISTS production_records (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          egg_count INT DEFAULT 0,
          feed_consumption DECIMAL(10,2) DEFAULT 0,
          temperature DECIMAL(5,2),
          humidity DECIMAL(5,2),
          notes TEXT,
          recorded_date DATE,
          weather VARCHAR(50),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
          INDEX idx_user_date (user_id, recorded_date),
          INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;
      
      await connection.execute(createProductionRecordsTable);

      // 库存记录表
      const createInventoryRecordsTable = `
        CREATE TABLE IF NOT EXISTS inventory_records (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          type ENUM('goose', 'egg', 'feed', 'medicine', 'equipment') NOT NULL,
          item_name VARCHAR(100),
          quantity INT NOT NULL,
          unit VARCHAR(20) DEFAULT '只',
          price DECIMAL(10,2),
          operation_type ENUM('in', 'out', 'adjust') DEFAULT 'in',
          record_date DATE,
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
          INDEX idx_user_type (user_id, type),
          INDEX idx_record_date (record_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;
      
      await connection.execute(createInventoryRecordsTable);

      // 任务表
      const createTasksTable = `
        CREATE TABLE IF NOT EXISTS tasks (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          title VARCHAR(200) NOT NULL,
          description TEXT,
          status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
          priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
          due_date DATE,
          completed_at TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
          INDEX idx_user_status (user_id, status),
          INDEX idx_due_date (due_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;
      
      await connection.execute(createTasksTable);

      // 公告表
      const createAnnouncementsTable = `
        CREATE TABLE IF NOT EXISTS announcements (
          id INT AUTO_INCREMENT PRIMARY KEY,
          title VARCHAR(200) NOT NULL,
          content TEXT,
          type ENUM('notice', 'warning', 'info', 'success') DEFAULT 'notice',
          priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
          status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
          author_id INT,
          publish_date TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE SET NULL,
          INDEX idx_status_priority (status, priority),
          INDEX idx_publish_date (publish_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;
      
      await connection.execute(createAnnouncementsTable);

    } catch (error) {
      console.error('❌ 创建数据表失败:', error.message);
      throw error;
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }

  async insertInitialData() {
    
    let connection;
    try {
      connection = await mysql.createConnection(this.dbConfig);
      
      // 创建管理员用户
      const adminPassword = await bcrypt.hash('admin123', 10);
      const managerPassword = await bcrypt.hash('manager123', 10);
      const demoPassword = await bcrypt.hash('demo123', 10);
      
      const insertUsers = `
        INSERT IGNORE INTO users (username, password, email, role, status, createdAt, updatedAt) VALUES
        ('admin', ?, '<EMAIL>', 'admin', 'active', NOW(), NOW()),
        ('manager', ?, '<EMAIL>', 'manager', 'active', NOW(), NOW()),
        ('demo', ?, '<EMAIL>', 'user', 'active', NOW(), NOW()),
        ('test_user', ?, '<EMAIL>', 'user', 'active', NOW(), NOW());
      `;
      
      await connection.execute(insertUsers, [adminPassword, managerPassword, demoPassword, demoPassword]);

      // 获取用户ID
      const [users] = await connection.execute('SELECT id, username FROM users WHERE username IN ("admin", "manager", "demo", "test_user")');
      const userMap = {};
      users.forEach(user => {
        userMap[user.username] = user.id;
      });

      // 插入健康记录
      const insertHealthRecords = `
        INSERT IGNORE INTO health_records (user_id, goose_id, symptoms, diagnosis, treatment, status, record_date, notes) VALUES
        (?, 'G001', '精神不振，食欲下降', '感冒症状', '保温，给予维生素C', 'recovering', '2023-12-01', '需要持续观察'),
        (?, 'G002', '正常', '健康检查', '无需治疗', 'healthy', '2023-12-01', '状态良好'),
        (?, 'G003', '腹泻', '肠胃炎', '给予益生菌，调整饲料', 'processing', '2023-12-02', '已开始治疗'),
        (?, 'G004', '咳嗽', '呼吸道感染', '抗生素治疗', 'processing', '2023-12-03', '症状有所缓解');
      `;
      
      const demoUserId = userMap['demo'];
      await connection.execute(insertHealthRecords, [demoUserId, demoUserId, demoUserId, demoUserId]);

      // 插入生产记录
      const insertProductionRecords = `
        INSERT IGNORE INTO production_records (user_id, egg_count, feed_consumption, temperature, humidity, notes, recorded_date, weather) VALUES
        (?, 120, 45.5, 25.5, 65.0, '天气晴朗，鹅群状态良好', '2023-12-01', '晴天'),
        (?, 115, 46.0, 24.8, 68.0, '有轻微降温，增加了保温措施', '2023-12-02', '多云'),
        (?, 125, 44.8, 26.2, 62.0, '产蛋量有所提升', '2023-12-03', '晴天'),
        (?, 118, 47.2, 23.5, 70.0, '湿度较高，注意通风', '2023-12-04', '阴天'),
        (?, 130, 43.5, 27.0, 58.0, '最佳生产状态', '2023-12-05', '晴天');
      `;
      
      await connection.execute(insertProductionRecords, [demoUserId, demoUserId, demoUserId, demoUserId, demoUserId]);

      // 插入库存记录
      const insertInventoryRecords = `
        INSERT IGNORE INTO inventory_records (user_id, type, item_name, quantity, unit, price, operation_type, record_date, notes) VALUES
        (?, 'goose', '成年鹅', 500, '只', 80.00, 'in', '2023-12-01', '新进鹅群'),
        (?, 'egg', '鹅蛋', 1200, '个', 3.50, 'in', '2023-12-01', '当日产蛋'),
        (?, 'feed', '鹅饲料', 2000, 'kg', 2.80, 'in', '2023-12-01', '月度采购'),
        (?, 'medicine', '维生素C', 50, '瓶', 15.00, 'in', '2023-12-01', '保健用品'),
        (?, 'equipment', '自动饮水器', 20, '个', 120.00, 'in', '2023-12-01', '设备更新');
      `;
      
      await connection.execute(insertInventoryRecords, [demoUserId, demoUserId, demoUserId, demoUserId, demoUserId]);

      // 插入任务
      const insertTasks = `
        INSERT IGNORE INTO tasks (user_id, title, description, status, priority, due_date) VALUES
        (?, '清理鹅舍', '定期清理鹅舍，保持环境卫生', 'pending', 'high', '2023-12-10'),
        (?, '疫苗接种', '为鹅群进行季度疫苗接种', 'in_progress', 'urgent', '2023-12-08'),
        (?, '饲料采购', '采购下月所需饲料', 'pending', 'medium', '2023-12-15'),
        (?, '设备检修', '检查自动化设备运行状态', 'completed', 'medium', '2023-12-05');
      `;
      
      await connection.execute(insertTasks, [demoUserId, demoUserId, demoUserId, demoUserId]);

      // 插入公告
      const insertAnnouncements = `
        INSERT IGNORE INTO announcements (title, content, type, priority, status, author_id, publish_date) VALUES
        ('系统维护通知', '系统将于本周末进行维护升级，请提前做好数据备份。', 'warning', 'high', 'published', ?, NOW()),
        ('新功能上线', '智能盘点功能已正式上线，欢迎大家使用体验。', 'success', 'medium', 'published', ?, NOW()),
        ('养殖技术分享', '冬季养鹅注意事项及保温措施指导。', 'info', 'medium', 'published', ?, NOW());
      `;
      
      const adminUserId = userMap['admin'];
      await connection.execute(insertAnnouncements, [adminUserId, adminUserId, adminUserId]);

    } catch (error) {
      console.error('❌ 插入初始数据失败:', error.message);
      throw error;
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }

  async verifySetup() {
    
    let connection;
    try {
      connection = await mysql.createConnection(this.dbConfig);
      
      // 检查表数量
      const [tables] = await connection.execute('SHOW TABLES');
      
      // 检查用户数据
      const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
      
      // 检查各表数据量
      const tableNames = ['health_records', 'production_records', 'inventory_records', 'tasks', 'announcements'];
      for (const tableName of tableNames) {
        try {
          const [result] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
        } catch (error) {
        }
      }
      
      return true;
    } catch (error) {
      console.error('❌ 验证失败:', error.message);
      return false;
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }

  async setup() {
    );
    
    try {
      // 1. 创建数据库和用户
      await this.createDatabaseAndUser();
      
      // 2. 创建表结构
      await this.createTables();
      
      // 3. 插入初始数据
      await this.insertInitialData();
      
      // 4. 验证设置
      const verified = await this.verifySetup();
      
      if (verified) {
      } else {
        throw new Error('数据库验证失败');
      }
      
    } catch (error) {
      console.error('\n❌ 数据库环境搭建失败:', error.message);
      process.exit(1);
    }
  }
}

// 运行设置
if (require.main === module) {
  const setup = new CompleteDatabaseSetup();
  setup.setup().catch(console.error);
}

module.exports = CompleteDatabaseSetup;
