/**
 * 企业级鹅群管理系统 JavaScript
 * 功能：鹅群列表、搜索、筛选、排序、分页、CRUD操作
 */

class FlockManagement {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 10;
        this.sortBy = 'createdAt';
        this.sortOrder = 'DESC';
        this.filters = {
            search: '',
            breed: '',
            status: '',
            ageGroup: ''
        };
        this.selectedFlocks = new Set();
        this.flocks = [];
        this.totalCount = 0;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadFlocks();
        this.loadStats();
    }

    bindEvents() {
        // 搜索
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.filters.search = e.target.value;
                    this.currentPage = 1;
                    this.loadFlocks();
                }, 300);
            });
        }

        // 筛选器
        document.getElementById('breedFilter')?.addEventListener('change', (e) => {
            this.filters.breed = e.target.value;
            this.currentPage = 1;
            this.loadFlocks();
        });

        document.getElementById('statusFilter')?.addEventListener('change', (e) => {
            this.filters.status = e.target.value;
            this.currentPage = 1;
            this.loadFlocks();
        });

        document.getElementById('ageGroupFilter')?.addEventListener('change', (e) => {
            this.filters.ageGroup = e.target.value;
            this.currentPage = 1;
            this.loadFlocks();
        });

        // 分页大小
        document.getElementById('pageSizeSelect')?.addEventListener('change', (e) => {
            this.pageSize = parseInt(e.target.value);
            this.currentPage = 1;
            this.loadFlocks();
        });

        // 全选
        document.getElementById('selectAll')?.addEventListener('change', (e) => {
            this.toggleSelectAll(e.target.checked);
        });

        // 排序
        document.querySelectorAll('.sortable').forEach(th => {
            th.addEventListener('click', () => {
                const sortField = th.dataset.sort;
                if (this.sortBy === sortField) {
                    this.sortOrder = this.sortOrder === 'ASC' ? 'DESC' : 'ASC';
                } else {
                    this.sortBy = sortField;
                    this.sortOrder = 'DESC';
                }
                this.updateSortIcons();
                this.loadFlocks();
            });
        });
    }

    async loadFlocks() {
        try {
            this.showLoading();
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                sortBy: this.sortBy,
                sortOrder: this.sortOrder,
                ...this.filters
            });

            const response = await fetch(`/api/flocks?${params}`);
            const result = await response.json();

            if (result.success) {
                this.flocks = result.data.items;
                this.totalCount = result.data.pagination.total;
                this.renderFlocks();
                this.renderPagination(result.data.pagination);
                this.updatePaginationInfo(result.data.pagination);
            } else {
                this.showError(result.message || '加载鹅群数据失败');
            }
        } catch (error) {
            console.error('加载鹅群失败:', error);
            this.showError('网络错误，请稍后重试');
        } finally {
            this.hideLoading();
        }
    }

    async loadStats() {
        try {
            const response = await fetch('/api/flocks/stats');
            const result = await response.json();
            
            if (result.success) {
                this.updateStats(result.data);
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    renderFlocks() {
        const tbody = document.getElementById('flocksTableBody');
        if (!tbody) return;

        if (this.flocks.length === 0) {
            this.showEmpty();
            return;
        }

        tbody.innerHTML = this.flocks.map(flock => `
            <tr data-flock-id="${flock.id}">
                <td>
                    <div class="form-check">
                        <input class="form-check-input flock-checkbox" type="checkbox" 
                               value="${flock.id}" onchange="flockManager.toggleFlockSelection(${flock.id}, this.checked)">
                    </div>
                </td>
                <td>
                    <div class="flock-info">
                        <div class="flock-name">${flock.name}</div>
                        <div class="flock-location">${flock.location || '未设置位置'}</div>
                    </div>
                </td>
                <td>
                    <span class="batch-number">${flock.batchNumber}</span>
                </td>
                <td>
                    <span class="breed-badge">${this.getBreedText(flock.breed)}</span>
                </td>
                <td>
                    <div class="count-info">
                        <span class="total-count">${flock.totalCount}</span>
                    </div>
                </td>
                <td>
                    <div class="count-info">
                        <span class="current-count">${flock.currentCount}</span>
                        ${flock.currentCount < flock.totalCount ? 
                            `<small class="text-warning">(-${flock.totalCount - flock.currentCount})</small>` : 
                            ''
                        }
                    </div>
                </td>
                <td>
                    <div class="gender-distribution">
                        <small class="text-primary">♂ ${flock.maleCount}</small>
                        <small class="text-danger">♀ ${flock.femaleCount}</small>
                    </div>
                </td>
                <td>
                    <span class="age-group-badge ${flock.ageGroup}">${this.getAgeGroupText(flock.ageGroup)}</span>
                </td>
                <td>
                    <span class="health-status ${flock.healthStatus}">${this.getHealthStatusText(flock.healthStatus)}</span>
                </td>
                <td>
                    <div class="production-rate">
                        <span class="rate-value">${flock.productionRate || 0}%</span>
                        <div class="rate-bar">
                            <div class="rate-fill" style="width: ${flock.productionRate || 0}%"></div>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="survival-rate">
                        <span class="rate-value">${this.calculateSurvivalRate(flock)}%</span>
                    </div>
                </td>
                <td>
                    <span class="flock-status ${flock.status}">${this.getStatusText(flock.status)}</span>
                </td>
                <td>
                    <div class="established-date">${this.formatDate(flock.establishedDate)}</div>
                    <small class="text-muted">${this.calculateAge(flock.establishedDate)} 天</small>
                </td>
                <td>
                    <div class="action-buttons">
                        <button type="button" class="btn btn-sm btn-outline-primary btn-action" 
                                onclick="flockManager.viewFlock(${flock.id})" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-success btn-action" 
                                onclick="flockManager.editFlock(${flock.id})" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger btn-action" 
                                onclick="flockManager.deleteFlock(${flock.id}, '${flock.name}')" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        this.hideEmpty();
        this.hideError();
    }

    renderPagination(pagination) {
        const paginationEl = document.getElementById('pagination');
        if (!paginationEl) return;

        const { page, pages, hasNext, hasPrev } = pagination;
        let html = '';

        // 上一页
        html += `
            <li class="page-item ${!hasPrev ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="flockManager.goToPage(${page - 1})" ${!hasPrev ? 'tabindex="-1"' : ''}>
                    <i class="bi bi-chevron-left"></i>
                </a>
            </li>
        `;

        // 页码
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(pages, page + 2);

        if (startPage > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="flockManager.goToPage(1)">1</a></li>`;
            if (startPage > 2) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="flockManager.goToPage(${i})">${i}</a>
                </li>
            `;
        }

        if (endPage < pages) {
            if (endPage < pages - 1) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            html += `<li class="page-item"><a class="page-link" href="#" onclick="flockManager.goToPage(${pages})">${pages}</a></li>`;
        }

        // 下一页
        html += `
            <li class="page-item ${!hasNext ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="flockManager.goToPage(${page + 1})" ${!hasNext ? 'tabindex="-1"' : ''}>
                    <i class="bi bi-chevron-right"></i>
                </a>
            </li>
        `;

        paginationEl.innerHTML = html;
    }

    updatePaginationInfo(pagination) {
        const infoEl = document.getElementById('paginationInfo');
        if (!infoEl) return;

        const { page, limit, total } = pagination;
        const start = (page - 1) * limit + 1;
        const end = Math.min(page * limit, total);
        
        infoEl.textContent = `显示第 ${start}-${end} 条，共 ${total} 条记录`;
    }

    updateStats(stats) {
        document.getElementById('totalFlocks').textContent = stats.totalFlocks || 0;
        document.getElementById('totalGeese').textContent = stats.totalGeese || 0;
        document.getElementById('healthyFlocks').textContent = stats.healthyFlocks || 0;
        document.getElementById('avgProduction').textContent = (stats.avgProduction || 0) + '%';
    }

    // 工具方法
    getBreedText(breed) {
        const breedMap = {
            'white_goose': '白鹅',
            'grey_goose': '灰鹅',
            'embden': '埃姆登鹅',
            'toulouse': '图卢兹鹅',
            'chinese': '中国鹅',
            'african': '非洲鹅'
        };
        return breedMap[breed] || breed;
    }

    getAgeGroupText(ageGroup) {
        const ageGroupMap = {
            'young': '幼鹅',
            'adult': '成鹅',
            'breeding': '种鹅',
            'retired': '淘汰'
        };
        return ageGroupMap[ageGroup] || ageGroup;
    }

    getStatusText(status) {
        const statusMap = {
            'active': '活跃',
            'inactive': '非活跃',
            'sold': '已售出',
            'deceased': '已死亡'
        };
        return statusMap[status] || status;
    }

    getHealthStatusText(healthStatus) {
        const healthMap = {
            'excellent': '优秀',
            'good': '良好',
            'fair': '一般',
            'poor': '较差'
        };
        return healthMap[healthStatus] || healthStatus;
    }

    calculateSurvivalRate(flock) {
        return ((flock.currentCount / flock.totalCount) * 100).toFixed(1);
    }

    calculateAge(establishedDate) {
        const now = new Date();
        const established = new Date(establishedDate);
        const diffTime = Math.abs(now - established);
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }

    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN');
    }

    // 状态管理方法
    showLoading() {
        document.getElementById('loadingState')?.style.setProperty('display', 'flex');
        document.getElementById('emptyState')?.style.setProperty('display', 'none');
        document.getElementById('errorState')?.style.setProperty('display', 'none');
    }

    hideLoading() {
        document.getElementById('loadingState')?.style.setProperty('display', 'none');
    }

    showEmpty() {
        document.getElementById('emptyState')?.style.setProperty('display', 'flex');
        document.getElementById('loadingState')?.style.setProperty('display', 'none');
        document.getElementById('errorState')?.style.setProperty('display', 'none');
        document.getElementById('flocksTableBody').innerHTML = '';
    }

    hideEmpty() {
        document.getElementById('emptyState')?.style.setProperty('display', 'none');
    }

    showError(message) {
        const errorState = document.getElementById('errorState');
        const errorMessage = document.getElementById('errorMessage');
        if (errorState && errorMessage) {
            errorMessage.textContent = message;
            errorState.style.setProperty('display', 'flex');
            document.getElementById('loadingState')?.style.setProperty('display', 'none');
            document.getElementById('emptyState')?.style.setProperty('display', 'none');
        }
    }

    hideError() {
        document.getElementById('errorState')?.style.setProperty('display', 'none');
    }

    goToPage(page) {
        if (page < 1) return;
        this.currentPage = page;
        this.loadFlocks();
    }

    updateSortIcons() {
        document.querySelectorAll('.sortable').forEach(th => {
            const icon = th.querySelector('.sort-icon');
            if (th.dataset.sort === this.sortBy) {
                th.classList.add('active');
                icon.className = this.sortOrder === 'ASC' ? 'bi bi-arrow-up sort-icon' : 'bi bi-arrow-down sort-icon';
            } else {
                th.classList.remove('active');
                icon.className = 'bi bi-arrow-down-up sort-icon';
            }
        });
    }

    toggleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.flock-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            this.toggleFlockSelection(parseInt(checkbox.value), checked);
        });
    }

    toggleFlockSelection(flockId, selected) {
        if (selected) {
            this.selectedFlocks.add(flockId);
        } else {
            this.selectedFlocks.delete(flockId);
        }
        this.updateBatchActions();
    }

    updateBatchActions() {
        const batchActions = document.getElementById('batchActions');
        const selectedCount = document.getElementById('selectedCount');
        
        if (this.selectedFlocks.size > 0) {
            batchActions.style.display = 'flex';
            selectedCount.textContent = this.selectedFlocks.size;
        } else {
            batchActions.style.display = 'none';
        }
    }

    clearSelection() {
        this.selectedFlocks.clear();
        document.querySelectorAll('.flock-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
        document.getElementById('selectAll').checked = false;
        this.updateBatchActions();
    }
}

// 全局实例
let flockManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    flockManager = new FlockManagement();
});

// 全局函数
function showCreateFlockModal() {
}

function resetFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('breedFilter').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('ageGroupFilter').value = '';
    
    flockManager.filters = {
        search: '',
        breed: '',
        status: '',
        ageGroup: ''
    };
    flockManager.currentPage = 1;
    flockManager.loadFlocks();
}

function exportFlocks() {
}

// 鹅群相关的其他功能
flockManager.viewFlock = function(flockId) {
};

flockManager.editFlock = function(flockId) {
};

flockManager.deleteFlock = function(flockId, flockName) {
    if (confirm(`确定要删除鹅群 "${flockName}" 吗？此操作不可恢复。`)) {
    }
};
