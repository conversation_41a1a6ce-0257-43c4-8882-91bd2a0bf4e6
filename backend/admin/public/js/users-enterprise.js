/**
 * 企业级用户管理系统 JavaScript
 * 功能：用户列表、搜索、筛选、排序、分页、CRUD操作
 */

class UserManagement {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 10;
        this.sortBy = 'createdAt';
        this.sortOrder = 'DESC';
        this.filters = {
            search: '',
            role: '',
            status: '',
            dateRange: ''
        };
        this.selectedUsers = new Set();
        this.users = [];
        this.totalCount = 0;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadUsers();
        this.loadStats();
    }

    bindEvents() {
        // 搜索
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.filters.search = e.target.value;
                    this.currentPage = 1;
                    this.loadUsers();
                }, 300);
            });
        }

        // 筛选器
        document.getElementById('roleFilter')?.addEventListener('change', (e) => {
            this.filters.role = e.target.value;
            this.currentPage = 1;
            this.loadUsers();
        });

        document.getElementById('statusFilter')?.addEventListener('change', (e) => {
            this.filters.status = e.target.value;
            this.currentPage = 1;
            this.loadUsers();
        });

        document.getElementById('dateFilter')?.addEventListener('change', (e) => {
            this.filters.dateRange = e.target.value;
            this.currentPage = 1;
            this.loadUsers();
        });

        // 分页大小
        document.getElementById('pageSizeSelect')?.addEventListener('change', (e) => {
            this.pageSize = parseInt(e.target.value);
            this.currentPage = 1;
            this.loadUsers();
        });

        // 全选
        document.getElementById('selectAll')?.addEventListener('change', (e) => {
            this.toggleSelectAll(e.target.checked);
        });

        // 排序
        document.querySelectorAll('.sortable').forEach(th => {
            th.addEventListener('click', () => {
                const sortField = th.dataset.sort;
                if (this.sortBy === sortField) {
                    this.sortOrder = this.sortOrder === 'ASC' ? 'DESC' : 'ASC';
                } else {
                    this.sortBy = sortField;
                    this.sortOrder = 'DESC';
                }
                this.updateSortIcons();
                this.loadUsers();
            });
        });
    }

    async loadUsers() {
        try {
            this.showLoading();
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                sortBy: this.sortBy,
                sortOrder: this.sortOrder,
                ...this.filters
            });

            const response = await fetch(`/api/users?${params}`);
            const result = await response.json();

            if (result.success) {
                this.users = result.data.items;
                this.totalCount = result.data.pagination.total;
                this.renderUsers();
                this.renderPagination(result.data.pagination);
                this.updatePaginationInfo(result.data.pagination);
            } else {
                this.showError(result.message || '加载用户数据失败');
            }
        } catch (error) {
            console.error('加载用户失败:', error);
            this.showError('网络错误，请稍后重试');
        } finally {
            this.hideLoading();
        }
    }

    async loadStats() {
        try {
            const response = await fetch('/api/users/stats');
            const result = await response.json();
            
            if (result.success) {
                this.updateStats(result.data);
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    renderUsers() {
        const tbody = document.getElementById('usersTableBody');
        if (!tbody) return;

        if (this.users.length === 0) {
            this.showEmpty();
            return;
        }

        tbody.innerHTML = this.users.map(user => `
            <tr data-user-id="${user.id}">
                <td>
                    <div class="form-check">
                        <input class="form-check-input user-checkbox" type="checkbox" 
                               value="${user.id}" onchange="userManager.toggleUserSelection(${user.id}, this.checked)">
                    </div>
                </td>
                <td>
                    <div class="user-avatar-container">
                        ${user.avatar ? 
                            `<img src="${user.avatar}" class="user-avatar" alt="${user.username}">` :
                            `<div class="user-avatar-placeholder">${user.username.charAt(0).toUpperCase()}</div>`
                        }
                    </div>
                </td>
                <td>
                    <div class="user-info">
                        <div class="user-name">${user.username}</div>
                        <div class="user-id">#${user.id}</div>
                    </div>
                </td>
                <td>
                    <div class="user-email">${user.email || '-'}</div>
                </td>
                <td>
                    <div class="user-real-name">${user.name || '-'}</div>
                </td>
                <td>
                    <div class="user-phone">${user.phone || '-'}</div>
                </td>
                <td>
                    <div class="user-farm">${user.farmName || '-'}</div>
                </td>
                <td>
                    <span class="user-role ${user.role}">${this.getRoleText(user.role)}</span>
                </td>
                <td>
                    <span class="user-status ${user.status}">${this.getStatusText(user.status)}</span>
                </td>
                <td>
                    <div class="user-last-login">
                        ${user.lastLoginAt ? this.formatDateTime(user.lastLoginAt) : '从未登录'}
                    </div>
                </td>
                <td>
                    <div class="user-created-at">${this.formatDateTime(user.createdAt)}</div>
                </td>
                <td>
                    <div class="action-buttons">
                        <button type="button" class="btn btn-sm btn-outline-primary btn-action" 
                                onclick="userManager.viewUser(${user.id})" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-success btn-action" 
                                onclick="userManager.editUser(${user.id})" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger btn-action" 
                                onclick="userManager.deleteUser(${user.id}, '${user.username}')" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        this.hideEmpty();
        this.hideError();
    }

    renderPagination(pagination) {
        const paginationEl = document.getElementById('pagination');
        if (!paginationEl) return;

        const { page, pages, hasNext, hasPrev } = pagination;
        let html = '';

        // 上一页
        html += `
            <li class="page-item ${!hasPrev ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="userManager.goToPage(${page - 1})" ${!hasPrev ? 'tabindex="-1"' : ''}>
                    <i class="bi bi-chevron-left"></i>
                </a>
            </li>
        `;

        // 页码
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(pages, page + 2);

        if (startPage > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="userManager.goToPage(1)">1</a></li>`;
            if (startPage > 2) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="userManager.goToPage(${i})">${i}</a>
                </li>
            `;
        }

        if (endPage < pages) {
            if (endPage < pages - 1) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            html += `<li class="page-item"><a class="page-link" href="#" onclick="userManager.goToPage(${pages})">${pages}</a></li>`;
        }

        // 下一页
        html += `
            <li class="page-item ${!hasNext ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="userManager.goToPage(${page + 1})" ${!hasNext ? 'tabindex="-1"' : ''}>
                    <i class="bi bi-chevron-right"></i>
                </a>
            </li>
        `;

        paginationEl.innerHTML = html;
    }

    updatePaginationInfo(pagination) {
        const infoEl = document.getElementById('paginationInfo');
        if (!infoEl) return;

        const { page, limit, total } = pagination;
        const start = (page - 1) * limit + 1;
        const end = Math.min(page * limit, total);
        
        infoEl.textContent = `显示第 ${start}-${end} 条，共 ${total} 条记录`;
    }

    updateStats(stats) {
        document.getElementById('totalUsers').textContent = stats.totalUsers || 0;
        document.getElementById('activeUsers').textContent = stats.activeUsers || 0;
        document.getElementById('adminUsers').textContent = stats.adminUsers || 0;
        document.getElementById('onlineUsers').textContent = stats.onlineUsers || 0;
    }

    showLoading() {
        document.getElementById('loadingState')?.style.setProperty('display', 'flex');
        document.getElementById('emptyState')?.style.setProperty('display', 'none');
        document.getElementById('errorState')?.style.setProperty('display', 'none');
    }

    hideLoading() {
        document.getElementById('loadingState')?.style.setProperty('display', 'none');
    }

    showEmpty() {
        document.getElementById('emptyState')?.style.setProperty('display', 'flex');
        document.getElementById('loadingState')?.style.setProperty('display', 'none');
        document.getElementById('errorState')?.style.setProperty('display', 'none');
        document.getElementById('usersTableBody').innerHTML = '';
    }

    hideEmpty() {
        document.getElementById('emptyState')?.style.setProperty('display', 'none');
    }

    showError(message) {
        const errorState = document.getElementById('errorState');
        const errorMessage = document.getElementById('errorMessage');
        if (errorState && errorMessage) {
            errorMessage.textContent = message;
            errorState.style.setProperty('display', 'flex');
            document.getElementById('loadingState')?.style.setProperty('display', 'none');
            document.getElementById('emptyState')?.style.setProperty('display', 'none');
        }
    }

    hideError() {
        document.getElementById('errorState')?.style.setProperty('display', 'none');
    }

    goToPage(page) {
        if (page < 1) return;
        this.currentPage = page;
        this.loadUsers();
    }

    getRoleText(role) {
        const roleMap = {
            'admin': '管理员',
            'manager': '经理',
            'user': '普通用户'
        };
        return roleMap[role] || role;
    }

    getStatusText(status) {
        const statusMap = {
            'active': '活跃',
            'inactive': '非活跃',
            'suspended': '已暂停'
        };
        return statusMap[status] || status;
    }

    formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    updateSortIcons() {
        document.querySelectorAll('.sortable').forEach(th => {
            const icon = th.querySelector('.sort-icon');
            if (th.dataset.sort === this.sortBy) {
                th.classList.add('active');
                icon.className = this.sortOrder === 'ASC' ? 'bi bi-arrow-up sort-icon' : 'bi bi-arrow-down sort-icon';
            } else {
                th.classList.remove('active');
                icon.className = 'bi bi-arrow-down-up sort-icon';
            }
        });
    }

    toggleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.user-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            this.toggleUserSelection(parseInt(checkbox.value), checked);
        });
    }

    toggleUserSelection(userId, selected) {
        if (selected) {
            this.selectedUsers.add(userId);
        } else {
            this.selectedUsers.delete(userId);
        }
        this.updateBatchActions();
    }

    updateBatchActions() {
        const batchActions = document.getElementById('batchActions');
        const selectedCount = document.getElementById('selectedCount');
        
        if (this.selectedUsers.size > 0) {
            batchActions.style.display = 'flex';
            selectedCount.textContent = this.selectedUsers.size;
        } else {
            batchActions.style.display = 'none';
        }
    }

    clearSelection() {
        this.selectedUsers.clear();
        document.querySelectorAll('.user-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
        document.getElementById('selectAll').checked = false;
        this.updateBatchActions();
    }
}

// 全局实例
let userManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    userManager = new UserManagement();
});

// 全局函数
function showCreateUserModal() {
    // 实现创建用户模态框
    $('#createUserModal').modal('show');
}

function resetFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('roleFilter').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('dateFilter').value = '';
    
    userManager.filters = {
        search: '',
        role: '',
        status: '',
        dateRange: ''
    };
    userManager.currentPage = 1;
    userManager.loadUsers();
}

function exportUsers() {
    // 导出用户数据为Excel格式
    if (!userManager.users || userManager.users.length === 0) {
        alert('暂无用户数据可导出');
        return;
    }
    
    const csvContent = generateUserCSV(userManager.users);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `用户列表_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 用户相关的其他功能
userManager.viewUser = function(userId) {
    // 显示用户详情模态框
    const user = userManager.users.find(u => u.id === userId);
    if (!user) {
        alert('用户不存在');
        return;
    }
    
    // 创建详情模态框
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <dl class="row">
                        <dt class="col-sm-3">用户名:</dt>
                        <dd class="col-sm-9">${user.username}</dd>
                        <dt class="col-sm-3">邮箱:</dt>
                        <dd class="col-sm-9">${user.email || '未设置'}</dd>
                        <dt class="col-sm-3">角色:</dt>
                        <dd class="col-sm-9">${user.role}</dd>
                        <dt class="col-sm-3">状态:</dt>
                        <dd class="col-sm-9">${user.status === 'active' ? '活跃' : '已禁用'}</dd>
                        <dt class="col-sm-3">创建时间:</dt>
                        <dd class="col-sm-9">${new Date(user.created_at).toLocaleString()}</dd>
                    </dl>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
};

userManager.editUser = function(userId) {
    // 实现编辑用户功能
    const user = userManager.users.find(u => u.id === userId);
    if (!user) {
        alert('用户不存在');
        return;
    }
    
    // 创建编辑模态框
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editUserForm">
                        <div class="mb-3">
                            <label for="editUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="editUsername" value="${user.username}" required>
                        </div>
                        <div class="mb-3">
                            <label for="editEmail" class="form-label">邮箱</label>
                            <input type="email" class="form-control" id="editEmail" value="${user.email || ''}">
                        </div>
                        <div class="mb-3">
                            <label for="editRole" class="form-label">角色</label>
                            <select class="form-control" id="editRole" required>
                                <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>管理员</option>
                                <option value="user" ${user.role === 'user' ? 'selected' : ''}>普通用户</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editStatus" class="form-label">状态</label>
                            <select class="form-control" id="editStatus" required>
                                <option value="active" ${user.status === 'active' ? 'selected' : ''}>活跃</option>
                                <option value="inactive" ${user.status === 'inactive' ? 'selected' : ''}>已禁用</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveUserEdit(${userId})">保存</button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
};

userManager.deleteUser = function(userId, username) {
    if (confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复。`)) {
        // 实现删除用户功能
        fetch(`/admin/api/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('用户删除成功');
                userManager.loadUsers(); // 重新加载用户列表
            } else {
                alert('删除失败: ' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            console.error('删除用户时出错:', error);
            alert('删除失败，请稍后重试');
        });
    }
};

// 辅助函数：生成CSV内容
function generateUserCSV(users) {
    const headers = ['ID', '用户名', '邮箱', '角色', '状态', '创建时间'];
    const csvContent = [
        headers.join(','),
        ...users.map(user => [
            user.id,
            user.username,
            user.email || '',
            user.role,
            user.status === 'active' ? '活跃' : '已禁用',
            new Date(user.created_at).toLocaleString()
        ].join(','))
    ].join('\n');
    
    return '\ufeff' + csvContent; // 添加BOM以支持中文
}

// 保存用户编辑
function saveUserEdit(userId) {
    const form = document.getElementById('editUserForm');
    const formData = new FormData(form);
    const userData = {
        username: document.getElementById('editUsername').value,
        email: document.getElementById('editEmail').value,
        role: document.getElementById('editRole').value,
        status: document.getElementById('editStatus').value
    };
    
    fetch(`/admin/api/users/${userId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('用户更新成功');
            bootstrap.Modal.getInstance(document.querySelector('.modal')).hide();
            userManager.loadUsers(); // 重新加载用户列表
        } else {
            alert('更新失败: ' + (data.message || '未知错误'));
        }
    })
    .catch(error => {
        console.error('更新用户时出错:', error);
        alert('更新失败，请稍后重试');
    });
}
