const ProductionRecord = require('../models/production-record.model');
const ProductionRecordV2 = require('../models/production-record-v2.model');
const { Op } = require('sequelize');
const ExcelJS = require('exceljs');
const moment = require('moment');
const ResponseHelper = require('../utils/response-helper');
const ValidationHelper = require('../utils/validation-helper');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USERNAME || 'zhihuiyange',
  password: process.env.DB_PASSWORD || 'zhihuiyange123',
  database: process.env.DB_NAME || 'zhihuiyange_local',
  charset: 'utf8mb4'
};

// 获取生产记录列表
exports.getRecords = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      startDate = '',
      endDate = '',
      sortBy = 'created_at',
      sortOrder = 'DESC',
      batchNumber = '',
      status = ''
    } = req.query;

    // 验证分页参数
    if (!ValidationHelper.validateNumber(page, 1) || !ValidationHelper.validateNumber(limit, 1, 100)) {
      return ResponseHelper.error(res, '分页参数无效', 400);
    }

    // 验证日期范围
    if (!ValidationHelper.validateDateRange(startDate, endDate)) {
      return ResponseHelper.error(res, '日期范围无效', 400);
    }

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // 构建查询条件
    const whereConditions = { userId: req.user.id };

    // 搜索条件
    if (search) {
      whereConditions[Op.or] = [
        { notes: { [Op.like]: `%${search}%` } },
        { batchNumber: { [Op.like]: `%${search}%` } }
      ];
    }

    // 日期范围筛选
    if (startDate || endDate) {
      whereConditions.recordedDate = {};
      if (startDate) whereConditions.recordedDate[Op.gte] = startDate;
      if (endDate) whereConditions.recordedDate[Op.lte] = endDate;
    }

    // 批次号筛选
    if (batchNumber) {
      whereConditions.batchNumber = batchNumber;
    }

    // 状态筛选
    if (status) {
      whereConditions.status = status;
    }

    // 排序验证
    const allowedSortFields = ['created_at', 'recordedDate', 'eggCount', 'feedConsumption'];
    const sortField = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
    const order = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    const { count, rows } = await ProductionRecord.findAndCountAll({
      where: whereConditions,
      limit: parseInt(limit),
      offset: offset,
      order: [[sortField, order]]
    });

    // 转换数据格式
    const records = rows.map(record => ({
      id: record.id,
      userId: record.userId,
      batchNumber: record.batchNumber || `BATCH-${record.id}`,
      eggCount: record.eggCount,
      feedConsumption: record.feedConsumption,
      temperature: record.temperature,
      humidity: record.humidity,
      notes: record.notes,
      recordedDate: record.recordedDate,
      status: record.status || 'active',
      createdAt: record.created_at,
      updatedAt: record.updated_at
    }));

    const paginatedData = ResponseHelper.paginate(records, page, limit, count);
    return ResponseHelper.success(res, paginatedData, '获取生产记录成功');

  } catch (error) {
    console.error('获取生产记录列表失败:', error);
    return ResponseHelper.error(res, '获取生产记录失败', 500, error.message);
  }
};

// 创建生产记录
exports.createRecord = async (req, res) => {
  try {
    const {
      flockId,
      eggCount,
      eggWeight,
      feedConsumption,
      temperature,
      humidity,
      notes,
      recordedDate,
      mortalityCount,
      healthStatus
    } = req.body;

    // 验证必填字段
    const requiredFields = ['flockId', 'eggCount'];
    const missing = ValidationHelper.validateRequired(requiredFields, req.body);
    if (missing.length > 0) {
      return ResponseHelper.error(res, `缺少必填字段: ${missing.join(', ')}`, 400);
    }

    // 验证数值
    if (!ValidationHelper.validateNumber(eggCount, 0)) {
      return ResponseHelper.error(res, '产蛋数量必须为非负数', 400);
    }

    if (feedConsumption && !ValidationHelper.validateNumber(feedConsumption, 0)) {
      return ResponseHelper.error(res, '饲料消耗量必须为非负数', 400);
    }

    const record = await ProductionRecord.create({
      userId: req.user.id,
      flockId: parseInt(flockId),
      eggCount: parseInt(eggCount) || 0,
      eggWeight: parseFloat(eggWeight) || null,
      feedConsumption: parseFloat(feedConsumption) || 0,
      temperature: parseFloat(temperature) || null,
      humidity: parseFloat(humidity) || null,
      mortalityCount: parseInt(mortalityCount) || 0,
      healthStatus: healthStatus || 'normal',
      notes: notes || '',
      recordedDate: recordedDate || new Date()
    });

    return ResponseHelper.success(res, record, '生产记录创建成功', 201);

  } catch (error) {
    console.error('创建生产记录失败:', error);
    return ResponseHelper.error(res, '创建生产记录失败', 500, error.message);
  }
};

// 更新鹅群统计信息的辅助方法
exports.updateFlockStats = async (flockId) => {
  try {
    // 这里应该根据生产记录更新鹅群的统计信息
    // 比如平均产蛋量、饲料转化率等
  } catch (error) {
    console.error('更新鹅群统计信息失败:', error);
  }
};

// 获取生产记录详情
exports.getRecordById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const record = await ProductionRecord.findOne({
      where: { 
        id: id,
        userId: req.user.id
      }
    });
    
    if (!record) {
      return res.status(404).json({
        success: false,
        message: '生产记录不存在'
      });
    }
    
    res.json({
      success: true,
      data: {
        id: record.id,
        userId: record.userId,
        产蛋数量: record.eggCount,
        饲料消耗: record.feedConsumption,
        温度: record.temperature,
        湿度: record.humidity,
        备注: record.notes,
        记录日期: record.recordedDate,
        createdAt: record.created_at,
        updatedAt: record.updated_at
      }
    });
  } catch (error) {
    console.error('获取生产记录详情失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 更新生产记录
exports.updateRecord = async (req, res) => {
  try {
    const { id } = req.params;
    const { 产蛋数量, 饲料消耗, 温度, 湿度, 备注, 记录日期 } = req.body;
    
    const record = await ProductionRecord.findOne({
      where: { 
        id: id,
        userId: req.user.id
      }
    });
    
    if (!record) {
      return res.status(404).json({
        success: false,
        message: '生产记录不存在'
      });
    }
    
    // 更新记录
    await record.update({
      eggCount: 产蛋数量,
      feedConsumption: 饲料消耗,
      temperature: 温度,
      humidity: 湿度,
      notes: 备注,
      recordedDate: 记录日期
    });
    
    res.json({
      success: true,
      message: '生产记录更新成功',
      data: {
        id: record.id,
        userId: record.userId,
        产蛋数量: record.eggCount,
        饲料消耗: record.feedConsumption,
        温度: record.temperature,
        湿度: record.humidity,
        备注: record.notes,
        记录日期: record.recordedDate,
        createdAt: record.created_at,
        updatedAt: record.updated_at
      }
    });
  } catch (error) {
    console.error('更新生产记录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 删除生产记录
exports.deleteRecord = async (req, res) => {
  try {
    const { id } = req.params;
    
    const record = await ProductionRecord.findByPk(id);
    
    if (!record) {
      return res.status(404).json({
        success: false,
        message: '生产记录不存在'
      });
    }
    
    // 删除记录
    await record.destroy();
    
    res.json({
      success: true,
      message: '生产记录删除成功'
    });
  } catch (error) {
    console.error('删除生产记录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 部分更新生产记录
exports.patchRecord = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    const record = await ProductionRecord.findByPk(id);
    
    if (!record) {
      return res.status(404).json({
        success: false,
        message: '生产记录不存在'
      });
    }
    
    // 更新记录
    await record.update(updateData);
    
    res.json({
      success: true,
      message: '生产记录更新成功',
      data: {
        id: record.id,
        date: record.date,
        batchNumber: record.batchNumber,
        gooseType: record.gooseType,
        quantity: record.quantity,
        weight: record.weight,
        feedConsumption: record.feedConsumption,
        medicineConsumption: record.medicineConsumption,
        mortality: record.mortality,
        eggProduction: record.eggProduction,
        eggWeight: record.eggWeight,
        temperature: record.temperature,
        humidity: record.humidity,
        ventilation: record.ventilation,
        lightHours: record.lightHours,
        createdAt: record.createdAt,
        updatedAt: record.updatedAt
      }
    });
  } catch (error) {
    console.error('部分更新生产记录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 批量创建生产记录
exports.batchCreateRecords = async (req, res) => {
  try {
    const records = req.body.records;
    
    if (!records || !Array.isArray(records) || records.length === 0) {
      return res.status(400).json({
        success: false,
        message: '记录列表不能为空'
      });
    }
    
    // 批量创建记录
    const createdRecords = await ProductionRecord.bulkCreate(records);
    
    res.status(201).json({
      success: true,
      message: `成功创建${createdRecords.length}条生产记录`,
      data: {
        records: createdRecords.map(record => ({
          id: record.id,
          date: record.date,
          batchNumber: record.batchNumber,
          gooseType: record.gooseType,
          quantity: record.quantity,
          weight: record.weight,
          feedConsumption: record.feedConsumption,
          medicineConsumption: record.medicineConsumption,
          mortality: record.mortality,
          eggProduction: record.eggProduction,
          eggWeight: record.eggWeight,
          temperature: record.temperature,
          humidity: record.humidity,
          ventilation: record.ventilation,
          lightHours: record.lightHours,
          createdAt: record.createdAt,
          updatedAt: record.updatedAt
        }))
      }
    });
  } catch (error) {
    console.error('批量创建生产记录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 批量更新生产记录
exports.batchUpdateRecords = async (req, res) => {
  try {
    const updates = req.body.updates;
    
    if (!updates || !Array.isArray(updates) || updates.length === 0) {
      return res.status(400).json({
        success: false,
        message: '更新列表不能为空'
      });
    }
    
    let updatedCount = 0;
    
    // 逐条更新记录
    for (const update of updates) {
      const { id, ...updateData } = update;
      
      const record = await ProductionRecord.findByPk(id);
      
      if (record) {
        await record.update(updateData);
        updatedCount++;
      }
    }
    
    res.json({
      success: true,
      message: `成功更新${updatedCount}条生产记录`,
      data: {
        updatedCount: updatedCount
      }
    });
  } catch (error) {
    console.error('批量更新生产记录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 批量删除生产记录
exports.batchDeleteRecords = async (req, res) => {
  try {
    const ids = req.body.ids;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'ID列表不能为空'
      });
    }
    
    // 批量删除记录
    const deletedCount = await ProductionRecord.destroy({
      where: {
        id: ids
      }
    });
    
    res.json({
      success: true,
      message: `成功删除${deletedCount}条生产记录`,
      data: {
        deletedCount: deletedCount
      }
    });
  } catch (error) {
    console.error('批量删除生产记录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// ================================
// 物料管理相关方法
// ================================

// 获取物料列表
exports.getMaterials = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      category,
      search,
      status,
      sortBy = 'created_at',
      sortOrder = 'desc',
      page = 1,
      limit = 10
    } = req.query;

    const connection = await mysql.createConnection(dbConfig);

    // 构建查询条件
    let whereClause = 'WHERE user_id = ?';
    const queryParams = [userId];

    if (category) {
      whereClause += ' AND category = ?';
      queryParams.push(category);
    }

    if (status) {
      whereClause += ' AND status = ?';
      queryParams.push(status);
    }

    if (search) {
      whereClause += ' AND (name LIKE ? OR code LIKE ? OR supplier LIKE ?)';
      const searchPattern = `%${search}%`;
      queryParams.push(searchPattern, searchPattern, searchPattern);
    }

    // 构建排序
    const validSortFields = ['name', 'code', 'current_stock', 'created_at'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'created_at';
    const order = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
    const orderClause = `ORDER BY ${sortField} ${order}`;

    // 分页
    const offset = (parseInt(page) - 1) * parseInt(limit);
    const limitClause = `LIMIT ${parseInt(limit)} OFFSET ${offset}`;

    // 查询物料列表
    const query = `
      SELECT
        id, name, code, category, specification, unit, supplier, shelf_life,
        current_stock, safety_stock, max_stock, status, created_at, updated_at
      FROM materials
      ${whereClause}
      ${orderClause}
      ${limitClause}
    `;

    const [materials] = await connection.execute(query, queryParams);

    // 查询总数
    const countQuery = `SELECT COUNT(*) as total FROM materials ${whereClause}`;
    const [countResult] = await connection.execute(countQuery, queryParams);
    const total = countResult[0].total;

    // 格式化物料数据
    const formattedMaterials = materials.map(material => ({
      id: material.id,
      name: material.name,
      code: material.code,
      category: material.category,
      categoryText: getCategoryText(material.category),
      specification: material.specification,
      unit: material.unit,
      supplier: material.supplier,
      shelfLife: material.shelf_life,
      currentStock: material.current_stock,
      safetyStock: material.safety_stock,
      maxStock: material.max_stock,
      status: material.status,
      statusText: getStatusText(material.status),
      createTime: material.created_at,
      updateTime: material.updated_at
    }));

    await connection.end();

    res.json({
      success: true,
      data: {
        materials: formattedMaterials,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('获取物料列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取物料列表失败',
      error: error.message
    });
  }
};

// 创建物料
exports.createMaterial = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      name,
      code,
      category,
      specification,
      unit,
      supplier,
      shelf_life,
      current_stock = 0,
      safety_stock = 0,
      max_stock = 0
    } = req.body;

    const connection = await mysql.createConnection(dbConfig);

    // 检查物料编码是否已存在
    const [existingMaterials] = await connection.execute(
      'SELECT id FROM materials WHERE code = ?',
      [code]
    );

    if (existingMaterials.length > 0) {
      await connection.end();
      return res.status(400).json({
        success: false,
        message: '物料编码已存在'
      });
    }

    const query = `
      INSERT INTO materials (user_id, name, code, category, specification, unit, supplier, shelf_life, current_stock, safety_stock, max_stock)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const [result] = await connection.execute(query, [
      userId, name, code, category, specification, unit, supplier, shelf_life, current_stock, safety_stock, max_stock
    ]);

    // 获取创建的物料详情
    const [newMaterial] = await connection.execute(
      'SELECT * FROM materials WHERE id = ?',
      [result.insertId]
    );

    await connection.end();

    res.status(201).json({
      success: true,
      message: '物料创建成功',
      data: {
        id: result.insertId,
        ...newMaterial[0]
      }
    });

  } catch (error) {
    console.error('创建物料失败:', error);
    res.status(500).json({
      success: false,
      message: '创建物料失败',
      error: error.message
    });
  }
};

// 获取物料详情
exports.getMaterialById = async (req, res) => {
  try {
    const userId = req.user.id;
    const materialId = req.params.id;

    const connection = await mysql.createConnection(dbConfig);

    const query = `
      SELECT * FROM materials
      WHERE id = ? AND user_id = ?
    `;

    const [materials] = await connection.execute(query, [materialId, userId]);

    if (materials.length === 0) {
      await connection.end();
      return res.status(404).json({
        success: false,
        message: '物料不存在'
      });
    }

    await connection.end();

    const material = materials[0];
    res.json({
      success: true,
      data: {
        ...material,
        categoryText: getCategoryText(material.category),
        statusText: getStatusText(material.status)
      }
    });

  } catch (error) {
    console.error('获取物料详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取物料详情失败',
      error: error.message
    });
  }
};

// 获取物料库存趋势
exports.getMaterialStockTrends = async (req, res) => {
  try {
    const userId = req.user.id;
    const materialId = req.params.id;
    const { timeRange = '7d' } = req.query;

    const connection = await mysql.createConnection(dbConfig);

    // 检查物料是否存在
    const [materials] = await connection.execute(
      'SELECT id FROM materials WHERE id = ? AND user_id = ?',
      [materialId, userId]
    );

    if (materials.length === 0) {
      await connection.end();
      return res.status(404).json({
        success: false,
        message: '物料不存在'
      });
    }

    // 根据时间范围计算日期
    let days = 7;
    if (timeRange === '30d') days = 30;
    else if (timeRange === '90d') days = 90;

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // 查询库存记录
    const query = `
      SELECT
        DATE(created_at) as date,
        type,
        amount,
        created_at
      FROM material_stock_records
      WHERE material_id = ? AND created_at >= ?
      ORDER BY created_at ASC
    `;

    const [records] = await connection.execute(query, [materialId, startDate]);

    // 计算每日库存变化
    const trendData = [];
    let currentStock = 0;

    // 获取起始库存
    const [currentMaterial] = await connection.execute(
      'SELECT current_stock FROM materials WHERE id = ?',
      [materialId]
    );
    currentStock = currentMaterial[0].current_stock;

    // 计算历史库存
    const dailyChanges = {};
    records.forEach(record => {
      const date = record.date.toISOString().split('T')[0];
      if (!dailyChanges[date]) {
        dailyChanges[date] = 0;
      }
      dailyChanges[date] += record.type === 'in' ? record.amount : -record.amount;
    });

    // 生成趋势数据
    for (let i = days; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      const change = dailyChanges[dateStr] || 0;
      currentStock -= change;

      trendData.push({
        date: dateStr,
        stock: Math.max(0, currentStock),
        change: change
      });
    }

    await connection.end();

    res.json({
      success: true,
      data: {
        trends: trendData,
        timeRange: timeRange
      }
    });

  } catch (error) {
    console.error('获取物料库存趋势失败:', error);
    res.status(500).json({
      success: false,
      message: '获取物料库存趋势失败',
      error: error.message
    });
  }
};

// 获取物料出入库记录
exports.getMaterialRecords = async (req, res) => {
  try {
    const userId = req.user.id;
    const materialId = req.params.id;
    const {
      type,
      page = 1,
      limit = 10
    } = req.query;

    const connection = await mysql.createConnection(dbConfig);

    // 检查物料是否存在
    const [materials] = await connection.execute(
      'SELECT id FROM materials WHERE id = ? AND user_id = ?',
      [materialId, userId]
    );

    if (materials.length === 0) {
      await connection.end();
      return res.status(404).json({
        success: false,
        message: '物料不存在'
      });
    }

    // 构建查询条件
    let whereClause = 'WHERE msr.material_id = ?';
    const queryParams = [materialId];

    if (type) {
      whereClause += ' AND msr.type = ?';
      queryParams.push(type);
    }

    // 分页
    const offset = (parseInt(page) - 1) * parseInt(limit);
    const limitClause = `LIMIT ${parseInt(limit)} OFFSET ${offset}`;

    // 查询出入库记录
    const query = `
      SELECT
        msr.*,
        u.username as operator_name
      FROM material_stock_records msr
      LEFT JOIN users u ON msr.operator_id = u.id
      ${whereClause}
      ORDER BY msr.created_at DESC
      ${limitClause}
    `;

    const [records] = await connection.execute(query, queryParams);

    // 查询总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM material_stock_records msr
      ${whereClause}
    `;
    const [countResult] = await connection.execute(countQuery, queryParams);
    const total = countResult[0].total;

    // 格式化记录数据
    const formattedRecords = records.map(record => ({
      id: record.id,
      type: record.type,
      typeText: record.type === 'in' ? '入库' : '出库',
      amount: record.amount,
      unitPrice: record.unit_price,
      totalPrice: record.total_price,
      description: record.description,
      operatorName: record.operator_name,
      batchNumber: record.batch_number,
      expiryDate: record.expiry_date,
      createTime: record.created_at
    }));

    await connection.end();

    res.json({
      success: true,
      data: {
        records: formattedRecords,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('获取物料出入库记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取物料出入库记录失败',
      error: error.message
    });
  }
};

// 获取物料预警设置
exports.getMaterialAlerts = async (req, res) => {
  try {
    const userId = req.user.id;
    const materialId = req.params.id;

    const connection = await mysql.createConnection(dbConfig);

    // 检查物料是否存在
    const [materials] = await connection.execute(
      'SELECT id FROM materials WHERE id = ? AND user_id = ?',
      [materialId, userId]
    );

    if (materials.length === 0) {
      await connection.end();
      return res.status(404).json({
        success: false,
        message: '物料不存在'
      });
    }

    // 查询预警设置
    const query = `
      SELECT * FROM material_alerts
      WHERE material_id = ?
      ORDER BY alert_type
    `;

    const [alerts] = await connection.execute(query, [materialId]);

    // 格式化预警设置
    const alertSettings = {
      lowStock: false,
      expiry: false,
      purchase: false
    };

    alerts.forEach(alert => {
      alertSettings[alert.alert_type] = {
        enabled: Boolean(alert.is_enabled),
        thresholdValue: alert.threshold_value,
        thresholdDays: alert.threshold_days
      };
    });

    await connection.end();

    res.json({
      success: true,
      data: alertSettings
    });

  } catch (error) {
    console.error('获取物料预警设置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取物料预警设置失败',
      error: error.message
    });
  }
};

// 更新物料预警设置
exports.updateMaterialAlerts = async (req, res) => {
  try {
    const userId = req.user.id;
    const materialId = req.params.id;
    const { lowStock, expiry, purchase } = req.body;

    const connection = await mysql.createConnection(dbConfig);

    // 检查物料是否存在
    const [materials] = await connection.execute(
      'SELECT id FROM materials WHERE id = ? AND user_id = ?',
      [materialId, userId]
    );

    if (materials.length === 0) {
      await connection.end();
      return res.status(404).json({
        success: false,
        message: '物料不存在'
      });
    }

    // 更新预警设置
    const alertTypes = [
      { type: 'lowStock', settings: lowStock },
      { type: 'expiry', settings: expiry },
      { type: 'purchase', settings: purchase }
    ];

    for (const alertType of alertTypes) {
      if (alertType.settings) {
        const query = `
          INSERT INTO material_alerts (material_id, alert_type, is_enabled, threshold_value, threshold_days)
          VALUES (?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
            is_enabled = VALUES(is_enabled),
            threshold_value = VALUES(threshold_value),
            threshold_days = VALUES(threshold_days),
            updated_at = CURRENT_TIMESTAMP
        `;

        await connection.execute(query, [
          materialId,
          alertType.type,
          alertType.settings.enabled || false,
          alertType.settings.thresholdValue || null,
          alertType.settings.thresholdDays || null
        ]);
      }
    }

    await connection.end();

    res.json({
      success: true,
      message: '预警设置更新成功'
    });

  } catch (error) {
    console.error('更新物料预警设置失败:', error);
    res.status(500).json({
      success: false,
      message: '更新物料预警设置失败',
      error: error.message
    });
  }
};

// 辅助函数
function getCategoryText(category) {
  const categoryMap = {
    'feed': '饲料',
    'medicine': '药品',
    'equipment': '设备',
    'other': '其他'
  };
  return categoryMap[category] || category;
}

function getStatusText(status) {
  const statusMap = {
    'normal': '正常',
    'warning': '预警',
    'danger': '危险'
  };
  return statusMap[status] || status;
}

// ==================== AI识别相关方法 ====================

// AI图像识别接口
exports.aiRecognition = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请上传图片文件'
      });
    }

    const imageFile = req.file;
    const recognitionType = req.body.type || 'goose_counting';

    // 调用AI识别服务
    const recognitionResult = await performAIRecognition(imageFile, recognitionType);

    res.json({
      success: true,
      data: recognitionResult,
      message: 'AI识别完成'
    });

  } catch (error) {
    console.error('AI识别失败:', error);
    res.status(500).json({
      success: false,
      message: 'AI识别失败',
      error: error.message
    });
  }
};

// 保存AI盘点记录
exports.saveAIInventoryRecord = async (req, res) => {
  try {
    const {
      batchNumber,
      originalCount,
      finalCount,
      confidence,
      aiModel,
      imagePath,
      notes
    } = req.body;

    // 验证必填字段
    if (!batchNumber || finalCount === undefined) {
      return res.status(400).json({
        success: false,
        message: '批次编号和最终数量为必填项'
      });
    }

    const connection = await mysql.createConnection(dbConfig);

    // 插入AI盘点记录
    const insertQuery = `
      INSERT INTO ai_inventory_records (
        user_id, batch_number, original_count, final_count,
        confidence, ai_model, image_path, notes, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `;

    const [result] = await connection.execute(insertQuery, [
      req.user.id,
      batchNumber,
      originalCount || 0,
      finalCount,
      confidence || 0,
      aiModel || 'GPT-4V',
      imagePath || '',
      notes || ''
    ]);

    await connection.end();

    res.json({
      success: true,
      data: {
        id: result.insertId,
        batchNumber,
        finalCount,
        timestamp: new Date().toISOString()
      },
      message: 'AI盘点记录保存成功'
    });

  } catch (error) {
    console.error('保存AI盘点记录失败:', error);
    res.status(500).json({
      success: false,
      message: '保存AI盘点记录失败',
      error: error.message
    });
  }
};

// 获取AI盘点记录列表
exports.getAIInventoryRecords = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    const connection = await mysql.createConnection(dbConfig);

    // 获取记录总数
    const [countResult] = await connection.execute(
      'SELECT COUNT(*) as total FROM ai_inventory_records WHERE user_id = ?',
      [req.user.id]
    );

    // 获取记录列表
    const [records] = await connection.execute(`
      SELECT
        id, batch_number, original_count, final_count,
        confidence, ai_model, notes, created_at
      FROM ai_inventory_records
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `, [req.user.id, limit, offset]);

    await connection.end();

    res.json({
      success: true,
      data: {
        records: records.map(record => ({
          id: record.id,
          batchNumber: record.batch_number,
          originalCount: record.original_count,
          finalCount: record.final_count,
          confidence: record.confidence,
          aiModel: record.ai_model,
          notes: record.notes,
          createdAt: record.created_at
        })),
        pagination: {
          page,
          limit,
          total: countResult[0].total,
          pages: Math.ceil(countResult[0].total / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取AI盘点记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取AI盘点记录失败',
      error: error.message
    });
  }
};

// 获取AI盘点记录详情
exports.getAIInventoryDetail = async (req, res) => {
  try {
    const recordId = req.params.id;
    const connection = await mysql.createConnection(dbConfig);

    const [records] = await connection.execute(`
      SELECT
        id, batch_number, original_count, final_count,
        confidence, ai_model, image_path, notes, created_at
      FROM ai_inventory_records
      WHERE id = ? AND user_id = ?
    `, [recordId, req.user.id]);

    await connection.end();

    if (records.length === 0) {
      return res.status(404).json({
        success: false,
        message: '记录不存在'
      });
    }

    const record = records[0];
    res.json({
      success: true,
      data: {
        id: record.id,
        batchNumber: record.batch_number,
        originalCount: record.original_count,
        finalCount: record.final_count,
        confidence: record.confidence,
        aiModel: record.ai_model,
        imagePath: record.image_path,
        notes: record.notes,
        createdAt: record.created_at
      }
    });

  } catch (error) {
    console.error('获取AI盘点记录详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取AI盘点记录详情失败',
      error: error.message
    });
  }
};

// AI识别核心函数
async function performAIRecognition(imageFile, recognitionType) {
  try {
    // 这里实现AI识别逻辑
    // 可以调用不同的AI服务：GPT-4V、Claude Vision、YOLO等

    if (recognitionType === 'goose_counting') {
      return await performGooseCounting(imageFile);
    }

    throw new Error('不支持的识别类型');

  } catch (error) {
    console.error('AI识别处理失败:', error);
    throw error;
  }
}

// 鹅群计数识别
async function performGooseCounting(imageFile) {
  try {
    // 模拟AI识别过程
    // 实际项目中这里会调用真实的AI服务

    // 模拟识别延迟
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 模拟识别结果
    const mockCount = Math.floor(Math.random() * 50) + 10; // 10-60只
    const mockConfidence = Math.floor(Math.random() * 20) + 80; // 80-100%

    return {
      count: mockCount,
      confidence: mockConfidence,
      model: 'GPT-4V',
      timestamp: new Date().toISOString(),
      imageSize: imageFile.size,
      imageFormat: imageFile.mimetype
    };

    // 实际实现示例（需要配置相应的AI服务）:
    /*
    // 使用OpenAI GPT-4V
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });

    const base64Image = imageFile.buffer.toString('base64');
    const response = await openai.chat.completions.create({
      model: "gpt-4-vision-preview",
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: "请仔细计算这张图片中鹅的数量。只返回数字，不要其他文字。"
            },
            {
              type: "image_url",
              image_url: {
                url: `data:${imageFile.mimetype};base64,${base64Image}`
              }
            }
          ]
        }
      ],
      max_tokens: 300
    });

    const count = parseInt(response.choices[0].message.content.trim());
    return {
      count: count,
      confidence: 95,
      model: 'GPT-4V',
      timestamp: new Date().toISOString()
    };
    */

  } catch (error) {
    console.error('鹅群计数识别失败:', error);
    throw new Error('AI识别服务暂时不可用，请稍后重试');
  }
}

// ================================
// V2版本生产记录管理 (生长记录、称重记录、出栏记录)
// ================================

// 获取V2生产记录列表
exports.getRecordsV2 = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const type = req.query.type; // 记录类型过滤

    // 构建查询条件
    const whereConditions = { userId: req.user.id };
    if (type && ['growth', 'weight', 'sale'].includes(type)) {
      whereConditions.type = type;
    }

    const { count, rows } = await ProductionRecordV2.findAndCountAll({
      where: whereConditions,
      limit: limit,
      offset: offset,
      order: [['date', 'DESC'], ['created_at', 'DESC']]
    });

    // 转换数据格式
    const records = rows.map(record => ({
      id: record.id,
      type: record.type,
      batch: record.batch,
      date: record.date,
      details: {
        weight: record.averageWeight,
        ratio: record.feedRatio,
        count: record.type === 'weight' ? record.weightCount : record.saleCount
      },
      notes: record.notes,
      status: record.status,
      createdAt: record.created_at,
      updatedAt: record.updated_at
    }));

    res.json({
      success: true,
      data: {
        records: records,
        pagination: {
          page: page,
          limit: limit,
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取V2生产记录列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 创建V2生产记录
exports.createRecordV2 = async (req, res) => {
  try {
    const { type, batch, date, weight, ratio, count, notes } = req.body;

    // 验证必填字段
    if (!type || !batch || !date) {
      return res.status(400).json({
        success: false,
        message: '记录类型、批次号和日期为必填项'
      });
    }

    // 验证记录类型
    if (!['growth', 'weight', 'sale'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: '无效的记录类型'
      });
    }

    // 构建记录数据
    const recordData = {
      userId: req.user.id,
      type: type,
      batch: batch,
      date: date,
      notes: notes || null
    };

    // 根据类型设置特定字段
    if (type === 'growth') {
      recordData.averageWeight = weight ? parseFloat(weight) : null;
      recordData.feedRatio = ratio || null;
    } else if (type === 'weight') {
      recordData.weightCount = count ? parseInt(count) : null;
      recordData.averageWeight = weight ? parseFloat(weight) : null;
    } else if (type === 'sale') {
      recordData.saleCount = count ? parseInt(count) : null;
      recordData.averageWeight = weight ? parseFloat(weight) : null;
    }

    const record = await ProductionRecordV2.create(recordData);

    res.status(201).json({
      success: true,
      message: '生产记录创建成功',
      data: {
        id: record.id,
        type: record.type,
        batch: record.batch,
        date: record.date,
        details: {
          weight: record.averageWeight,
          ratio: record.feedRatio,
          count: record.type === 'weight' ? record.weightCount : record.saleCount
        },
        notes: record.notes,
        status: record.status,
        createdAt: record.created_at,
        updatedAt: record.updated_at
      }
    });
  } catch (error) {
    console.error('创建V2生产记录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// ================================
// 物料管理
// ================================

// 获取物料列表
exports.getMaterials = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const category = req.query.category; // 分类过滤
    const status = req.query.status; // 状态过滤

    // 构建查询条件
    const whereConditions = { userId: req.user.id };
    if (category && ['feed', 'medicine', 'other'].includes(category)) {
      whereConditions.category = category;
    }
    if (status && ['normal', 'warning', 'danger', 'expired'].includes(status)) {
      whereConditions.status = status;
    }

    const { count, rows } = await Material.findAndCountAll({
      where: whereConditions,
      limit: limit,
      offset: offset,
      order: [['created_at', 'DESC']]
    });

    // 转换数据格式
    const materials = rows.map(material => ({
      id: material.id,
      name: material.name,
      category: material.category,
      spec: material.spec,
      stock: material.stock,
      unit: material.unit,
      minStock: material.minStock,
      maxStock: material.maxStock,
      unitPrice: material.unitPrice,
      supplier: material.supplier,
      supplierContact: material.supplierContact,
      purchaseDate: material.purchaseDate,
      expiryDate: material.expiryDate,
      location: material.location,
      description: material.description,
      status: material.status,
      createdAt: material.created_at,
      updatedAt: material.updated_at
    }));

    res.json({
      success: true,
      data: {
        materials: materials,
        pagination: {
          page: page,
          limit: limit,
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取物料列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 创建物料
exports.createMaterial = async (req, res) => {
  try {
    const {
      name,
      category,
      spec,
      stock,
      unit,
      minStock,
      maxStock,
      unitPrice,
      supplier,
      supplierContact,
      purchaseDate,
      expiryDate,
      location,
      description
    } = req.body;

    // 验证必填字段
    if (!name || !category || !spec || stock === undefined) {
      return res.status(400).json({
        success: false,
        message: '物料名称、类别、规格和库存数量为必填项'
      });
    }

    // 验证物料类别
    if (!['feed', 'medicine', 'other'].includes(category)) {
      return res.status(400).json({
        success: false,
        message: '无效的物料类别'
      });
    }

    // 计算物料状态
    let status = 'normal';
    const stockNum = parseInt(stock);
    const minStockNum = minStock ? parseInt(minStock) : 20;

    if (expiryDate && new Date(expiryDate) < new Date()) {
      status = 'expired';
    } else if (stockNum <= 0) {
      status = 'danger';
    } else if (stockNum <= minStockNum) {
      status = 'warning';
    }

    const material = await Material.create({
      userId: req.user.id,
      name: name,
      category: category,
      spec: spec,
      stock: stockNum,
      unit: unit || '个',
      minStock: minStock ? parseInt(minStock) : null,
      maxStock: maxStock ? parseInt(maxStock) : null,
      unitPrice: unitPrice ? parseFloat(unitPrice) : null,
      supplier: supplier || null,
      supplierContact: supplierContact || null,
      purchaseDate: purchaseDate || null,
      expiryDate: expiryDate || null,
      location: location || null,
      description: description || null,
      status: status
    });

    res.status(201).json({
      success: true,
      message: '物料创建成功',
      data: {
        id: material.id,
        name: material.name,
        category: material.category,
        spec: material.spec,
        stock: material.stock,
        unit: material.unit,
        minStock: material.minStock,
        maxStock: material.maxStock,
        unitPrice: material.unitPrice,
        supplier: material.supplier,
        supplierContact: material.supplierContact,
        purchaseDate: material.purchaseDate,
        expiryDate: material.expiryDate,
        location: material.location,
        description: material.description,
        status: material.status,
        createdAt: material.created_at,
        updatedAt: material.updated_at
      }
    });
  } catch (error) {
    console.error('创建物料失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// ================================
// V2版本生产记录管理 (生长记录、称重记录、出栏记录)
// ================================

// 获取V2生产记录列表
exports.getRecordsV2 = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const type = req.query.type; // 记录类型过滤

    // 构建查询条件
    const whereConditions = { userId: req.user.id };
    if (type && ['growth', 'weight', 'sale'].includes(type)) {
      whereConditions.type = type;
    }

    const { count, rows } = await ProductionRecordV2.findAndCountAll({
      where: whereConditions,
      limit: limit,
      offset: offset,
      order: [['date', 'DESC'], ['created_at', 'DESC']]
    });

    // 转换数据格式
    const records = rows.map(record => ({
      id: record.id,
      type: record.type,
      batch: record.batch,
      date: record.date,
      details: {
        weight: record.averageWeight,
        ratio: record.feedRatio,
        count: record.type === 'weight' ? record.weightCount : record.saleCount
      },
      notes: record.notes,
      status: record.status,
      createdAt: record.created_at,
      updatedAt: record.updated_at
    }));

    res.json({
      success: true,
      data: {
        records: records,
        pagination: {
          page: page,
          limit: limit,
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取V2生产记录列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 创建V2生产记录
exports.createRecordV2 = async (req, res) => {
  try {
    const { type, batch, date, weight, ratio, count, notes } = req.body;

    // 验证必填字段
    if (!type || !batch || !date) {
      return res.status(400).json({
        success: false,
        message: '记录类型、批次号和日期为必填项'
      });
    }

    // 验证记录类型
    if (!['growth', 'weight', 'sale'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: '无效的记录类型'
      });
    }

    // 构建记录数据
    const recordData = {
      userId: req.user.id,
      type: type,
      batch: batch,
      date: date,
      notes: notes || null
    };

    // 根据类型设置特定字段
    if (type === 'growth') {
      recordData.averageWeight = weight ? parseFloat(weight) : null;
      recordData.feedRatio = ratio || null;
    } else if (type === 'weight') {
      recordData.weightCount = count ? parseInt(count) : null;
      recordData.averageWeight = weight ? parseFloat(weight) : null;
    } else if (type === 'sale') {
      recordData.saleCount = count ? parseInt(count) : null;
      recordData.averageWeight = weight ? parseFloat(weight) : null;
    }

    const record = await ProductionRecordV2.create(recordData);

    res.status(201).json({
      success: true,
      message: '生产记录创建成功',
      data: {
        id: record.id,
        type: record.type,
        batch: record.batch,
        date: record.date,
        details: {
          weight: record.averageWeight,
          ratio: record.feedRatio,
          count: record.type === 'weight' ? record.weightCount : record.saleCount
        },
        notes: record.notes,
        status: record.status,
        createdAt: record.created_at,
        updatedAt: record.updated_at
      }
    });
  } catch (error) {
    console.error('创建V2生产记录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// ================================
// 物料管理
// ================================

// 获取物料列表
exports.getMaterials = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const category = req.query.category; // 分类过滤
    const status = req.query.status; // 状态过滤

    // 构建查询条件
    const whereConditions = { userId: req.user.id };
    if (category && ['feed', 'medicine', 'other'].includes(category)) {
      whereConditions.category = category;
    }
    if (status && ['normal', 'warning', 'danger', 'expired'].includes(status)) {
      whereConditions.status = status;
    }

    const { count, rows } = await Material.findAndCountAll({
      where: whereConditions,
      limit: limit,
      offset: offset,
      order: [['created_at', 'DESC']]
    });

    // 转换数据格式
    const materials = rows.map(material => ({
      id: material.id,
      name: material.name,
      category: material.category,
      spec: material.spec,
      stock: material.stock,
      unit: material.unit,
      minStock: material.minStock,
      maxStock: material.maxStock,
      unitPrice: material.unitPrice,
      supplier: material.supplier,
      supplierContact: material.supplierContact,
      purchaseDate: material.purchaseDate,
      expiryDate: material.expiryDate,
      location: material.location,
      description: material.description,
      status: material.status,
      createdAt: material.created_at,
      updatedAt: material.updated_at
    }));

    res.json({
      success: true,
      data: {
        materials: materials,
        pagination: {
          page: page,
          limit: limit,
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取物料列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取生产效率分析
exports.getProductionEfficiency = async (req, res) => {
  try {
    // 这里应该从数据库获取真实的生产效率分析数据
    // 暂时返回模拟数据
    const efficiencyData = {
      feedConversionRatio: 2.5,
      eggProductionRate: 85,
      mortalityRate: 2.1,
      averageWeight: 3.2
    };

    res.json({
      success: true,
      data: efficiencyData
    });
  } catch (error) {
    console.error('获取生产效率分析失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取生产趋势数据
exports.getProductionTrends = async (req, res) => {
  try {
    // 这里应该从数据库获取真实的生产趋势数据
    // 暂时返回模拟数据
    const trends = [
      { date: '2023-01', value: 120 },
      { date: '2023-02', value: 125 },
      { date: '2023-03', value: 130 },
      { date: '2023-04', value: 135 },
      { date: '2023-05', value: 140 }
    ];

    res.json({
      success: true,
      data: {
        trends: trends
      }
    });
  } catch (error) {
    console.error('获取生产趋势数据失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取生产记录统计信息
exports.getRecordStats = async (req, res) => {
  try {
    // 这里应该从数据库获取真实的统计信息
    // 暂时返回模拟数据
    const stats = {
      totalRecords: 120,
      avgDailyProduction: 135,
      totalEggProduction: 4000,
      feedConsumption: 2500
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('获取生产记录统计信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 导出生产记录为CSV格式
exports.exportRecords = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 1000;
    const offset = (page - 1) * limit;
    
    // 构建查询条件
    const whereConditions = { userId: req.user.id };
    
    const { rows } = await ProductionRecord.findAndCountAll({
      where: whereConditions,
      limit: limit,
      offset: offset,
      order: [['created_at', 'DESC']]
    });
    
    // 转换字段名以匹配前端期望的格式
    const records = rows.map(record => {
      return {
        id: record.id,
        userId: record.userId,
        产蛋数量: record.eggCount,
        饲料消耗: record.feedConsumption,
        温度: record.temperature,
        湿度: record.humidity,
        备注: record.notes,
        记录日期: record.recordedDate,
        createdAt: record.created_at,
        updatedAt: record.updated_at
      };
    });
    
    // 将数据转换为CSV格式
    const csvData = records.map(record => {
      return Object.values(record).map(value => {
        if (typeof value === 'string') {
          return `"${value}"`;
        }
        return value;
      }).join(',');
    }).join('\n');
    
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=production_records.csv');
    res.send(csvData);
  } catch (error) {
    console.error('导出生产记录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 导出生产记录为Excel格式
exports.exportRecordsExcel = async (req, res) => {
  try {
    // 获取查询参数
    const { startDate, endDate } = req.query;
    
    // 构建查询条件
    const whereClause = {};
    if (startDate || endDate) {
      whereClause.date = {};
      if (startDate) whereClause.date[Op.gte] = startDate;
      if (endDate) whereClause.date[Op.lte] = endDate;
    }
    
    const records = await ProductionRecord.findAll({ where: whereClause });
    
    // 构造Excel内容（简化版，实际项目中可以使用专门的库如xlsx）
    let excelContent = '日期\t批次\t鹅种\t数量\t重量\t饲料消耗\t药品消耗\t死亡数\t产蛋量\t蛋重\t温度\t湿度\t通风\t光照\n';
    records.forEach(record => {
      excelContent += `${record.date}\t${record.batchNumber}\t${record.gooseType}\t${record.quantity}\t${record.weight}\t${record.feedConsumption}\t${record.medicineConsumption}\t${record.mortality}\t${record.eggProduction}\t${record.eggWeight}\t${record.temperature}\t${record.humidity}\t${record.ventilation}\t${record.lightHours}\n`;
    });
    
    res.setHeader('Content-Type', 'application/vnd.ms-excel');
    res.setHeader('Content-Disposition', 'attachment; filename="production-records.xls"');
    res.status(200).send(excelContent);
  } catch (error) {
    console.error('导出生产记录Excel失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 导出生产记录为CSV格式
exports.exportRecordsCSV = async (req, res) => {
  try {
    // 获取查询参数
    const { startDate, endDate } = req.query;
    
    // 构建查询条件
    const whereClause = {};
    if (startDate || endDate) {
      whereClause.date = {};
      if (startDate) whereClause.date[Op.gte] = startDate;
      if (endDate) whereClause.date[Op.lte] = endDate;
    }
    
    const records = await ProductionRecord.findAll({ where: whereClause });
    
    // 构造CSV内容
    let csvContent = '日期,批次,鹅种,数量,重量,饲料消耗,药品消耗,死亡数,产蛋量,蛋重,温度,湿度,通风,光照\n';
    records.forEach(record => {
      csvContent += `"${record.date}","${record.batchNumber}","${record.gooseType}","${record.quantity}","${record.weight}","${record.feedConsumption}","${record.medicineConsumption}","${record.mortality}","${record.eggProduction}","${record.eggWeight}","${record.temperature}","${record.humidity}","${record.ventilation}","${record.lightHours}"\n`;
    });
    
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename="production-records.csv"');
    res.status(200).send(csvContent);
  } catch (error) {
    console.error('导出生产记录CSV失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取成本分析
exports.getCostAnalysis = async (req, res) => {
  try {
    // 这里应该从数据库获取真实的成本分析数据
    // 暂时返回模拟数据
    const costData = {
      totalFeedCost: 5000,
      totalMedicineCost: 800,
      totalLaborCost: 3000,
      totalOtherCost: 1200,
      totalCost: 10000,
      averageCostPerGoose: 50
    };

    res.json({
      success: true,
      data: costData
    });
  } catch (error) {
    console.error('获取成本分析失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取生产预测
exports.getProductionForecast = async (req, res) => {
  try {
    // 这里应该根据历史数据进行生产预测
    // 暂时返回模拟数据
    const forecast = {
      nextWeekEggProduction: 500,
      nextWeekFeedConsumption: 150,
      predictedMortality: 2,
      confidence: 0.85
    };

    res.json({
      success: true,
      data: forecast
    });
  } catch (error) {
    console.error('获取生产预测失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 导出生产报告PDF
exports.exportProductionReportPDF = async (req, res) => {
  try {
    // 这里应该生成真实的PDF报告
    // 暂时返回模拟的PDF内容
    
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', 'attachment; filename="production-report.pdf"');
    res.status(200).send('%PDF-1.4...'); // 简化的PDF内容
  } catch (error) {
    console.error('导出生产报告PDF失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 搜索生产记录
exports.searchRecords = async (req, res) => {
  try {
    const { keyword, startDate, endDate } = req.query;
    
    // 构建查询条件
    const whereClause = {};
    
    if (keyword) {
      whereClause[Op.or] = [
        { batchNumber: { [Op.like]: `%${keyword}%` } },
        { gooseType: { [Op.like]: `%${keyword}%` } }
      ];
    }
    
    if (startDate || endDate) {
      whereClause.date = {};
      if (startDate) whereClause.date[Op.gte] = startDate;
      if (endDate) whereClause.date[Op.lte] = endDate;
    }
    
    const records = await ProductionRecord.findAll({ 
      where: whereClause,
      order: [['date', 'DESC']]
    });
    
    res.json({
      success: true,
      data: {
        records: records.map(record => ({
          id: record.id,
          date: record.date,
          batchNumber: record.batchNumber,
          gooseType: record.gooseType,
          quantity: record.quantity,
          weight: record.weight,
          feedConsumption: record.feedConsumption,
          medicineConsumption: record.medicineConsumption,
          mortality: record.mortality,
          eggProduction: record.eggProduction,
          eggWeight: record.eggWeight,
          temperature: record.temperature,
          humidity: record.humidity,
          ventilation: record.ventilation,
          lightHours: record.lightHours,
          createdAt: record.createdAt,
          updatedAt: record.updatedAt
        }))
      }
    });
  } catch (error) {
    console.error('搜索生产记录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 过滤生产记录
exports.filterRecords = async (req, res) => {
  try {
    const filters = req.body;
    
    // 构建查询条件
    const whereClause = {};
    
    if (filters.batchNumber) {
      whereClause.batchNumber = filters.batchNumber;
    }
    
    if (filters.gooseType) {
      whereClause.gooseType = filters.gooseType;
    }
    
    if (filters.startDate || filters.endDate) {
      whereClause.date = {};
      if (filters.startDate) whereClause.date[Op.gte] = filters.startDate;
      if (filters.endDate) whereClause.date[Op.lte] = filters.endDate;
    }
    
    const records = await ProductionRecord.findAll({ 
      where: whereClause,
      order: [['date', 'DESC']]
    });
    
    res.json({
      success: true,
      data: {
        records: records.map(record => ({
          id: record.id,
          date: record.date,
          batchNumber: record.batchNumber,
          gooseType: record.gooseType,
          quantity: record.quantity,
          weight: record.weight,
          feedConsumption: record.feedConsumption,
          medicineConsumption: record.medicineConsumption,
          mortality: record.mortality,
          eggProduction: record.eggProduction,
          eggWeight: record.eggWeight,
          temperature: record.temperature,
          humidity: record.humidity,
          ventilation: record.ventilation,
          lightHours: record.lightHours,
          createdAt: record.createdAt,
          updatedAt: record.updatedAt
        }))
      }
    });
  } catch (error) {
    console.error('过滤生产记录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取记录类型
exports.getRecordTypes = async (req, res) => {
  try {
    // 返回预定义的记录类型
    const types = [
      { id: 'daily', name: '日常记录' },
      { id: 'feeding', name: '喂养记录' },
      { id: 'health', name: '健康记录' },
      { id: 'egg', name: '产蛋记录' },
      { id: 'sale', name: '销售记录' }
    ];
    
    res.json({
      success: true,
      data: {
        types: types
      }
    });
  } catch (error) {
    console.error('获取记录类型失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取记录模板
exports.getRecordTemplates = async (req, res) => {
  try {
    // 返回预定义的记录模板
    const templates = [
      { 
        id: 1, 
        name: '日常生产记录模板', 
        type: 'daily',
        fields: ['date', 'batchNumber', 'gooseType', 'quantity', 'weight']
      },
      { 
        id: 2, 
        name: '喂养记录模板', 
        type: 'feeding',
        fields: ['date', 'feedType', 'feedConsumption', 'waterConsumption']
      }
    ];
    
    res.json({
      success: true,
      data: {
        templates: templates
      }
    });
  } catch (error) {
    console.error('获取记录模板失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 提交记录审批
exports.submitRecordForApproval = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 这里应该更新记录状态为待审批
    // 暂时直接返回成功
    
    res.json({
      success: true,
      message: '记录已提交审批'
    });
  } catch (error) {
    console.error('提交记录审批失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 审批记录（通过）
exports.approveRecord = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 这里应该更新记录状态为已审批
    // 暂时直接返回成功
    
    res.json({
      success: true,
      message: '记录已审批通过'
    });
  } catch (error) {
    console.error('审批记录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 拒绝记录审批
exports.rejectRecord = async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    
    // 这里应该更新记录状态为已拒绝
    // 暂时直接返回成功
    
    res.json({
      success: true,
      message: '记录审批已拒绝'
    });
  } catch (error) {
    console.error('拒绝记录审批失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取记录关联的环境数据
exports.getRecordEnvironment = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 这里应该从数据库获取记录关联的环境数据
    // 暂时返回模拟数据
    const environment = {
      temperature: 25.5,
      humidity: 60,
      pm25: 30,
      lightHours: 12,
      ventilation: '良好'
    };
    
    res.json({
      success: true,
      data: {
        environment: environment
      }
    });
  } catch (error) {
    console.error('获取记录关联环境数据失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取记录关联的通知
exports.getRecordNotifications = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 这里应该从数据库获取记录关联的通知
    // 暂时返回模拟数据
    const notifications = [
      { 
        id: 1, 
        title: '记录更新提醒', 
        content: '记录已更新，请查看',
        date: new Date()
      }
    ];
    
    res.json({
      success: true,
      data: {
        notifications: notifications
      }
    });
  } catch (error) {
    console.error('获取记录关联通知失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取记录修改历史
exports.getRecordHistory = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 这里应该从数据库获取记录的修改历史
    // 暂时返回模拟数据
    const history = [
      { 
        id: 1, 
        version: 'v1',
        modifiedBy: '张三',
        modifiedAt: new Date(),
        changes: '创建记录'
      },
      { 
        id: 2, 
        version: 'v2',
        modifiedBy: '李四',
        modifiedAt: new Date(),
        changes: '更新饲料消耗量'
      }
    ];
    
    res.json({
      success: true,
      data: {
        history: history
      }
    });
  } catch (error) {
    console.error('获取记录修改历史失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取物料列表
exports.getMaterials = async (req, res) => {
  try {
    // 这里应该从数据库获取物料列表
    // 暂时返回模拟数据
    const materials = [
      { id: 1, name: '饲料A', category: '饲料', quantity: 100 },
      { id: 2, name: '药品B', category: '药品', quantity: 50 },
      { id: 3, name: '设备C', category: '设备', quantity: 10 }
    ];
    
    res.json({
      success: true,
      data: {
        materials: materials
      }
    });
  } catch (error) {
    console.error('获取物料列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 创建物料
exports.createMaterial = async (req, res) => {
  try {
    const { name, category, quantity } = req.body;
    
    // 这里应该创建物料
    // 暂时直接返回成功
    
    res.status(201).json({
      success: true,
      message: '物料创建成功'
    });
  } catch (error) {
    console.error('创建物料失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 更新物料
exports.updateMaterial = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, category, quantity } = req.body;
    
    // 这里应该更新物料
    // 暂时直接返回成功
    
    res.json({
      success: true,
      message: '物料更新成功'
    });
  } catch (error) {
    console.error('更新物料失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 删除物料
exports.deleteMaterial = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 这里应该删除物料
    // 暂时直接返回成功
    
    res.json({
      success: true,
      message: '物料删除成功'
    });
  } catch (error) {
    console.error('删除物料失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取物料分类列表
exports.getMaterialCategories = async (req, res) => {
  try {
    // 这里应该从数据库获取物料分类列表
    // 暂时返回模拟数据
    const categories = [
      { id: 1, name: '饲料', type: 'feed' },
      { id: 2, name: '药品', type: 'medicine' },
      { id: 3, name: '设备', type: 'equipment' }
    ];
    
    res.json({
      success: true,
      data: {
        categories: categories
      }
    });
  } catch (error) {
    console.error('获取物料分类列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 创建物料分类
exports.createMaterialCategory = async (req, res) => {
  try {
    const { name, type } = req.body;
    
    // 这里应该创建物料分类
    // 暂时直接返回成功
    
    res.status(201).json({
      success: true,
      message: '物料分类创建成功'
    });
  } catch (error) {
    console.error('创建物料分类失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 更新物料分类
exports.updateMaterialCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, type } = req.body;
    
    // 这里应该更新物料分类
    // 暂时直接返回成功
    
    res.json({
      success: true,
      message: '物料分类更新成功'
    });
  } catch (error) {
    console.error('更新物料分类失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 删除物料分类
exports.deleteMaterialCategory = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 这里应该删除物料分类
    // 暂时直接返回成功
    
    res.json({
      success: true,
      message: '物料分类删除成功'
    });
  } catch (error) {
    console.error('删除物料分类失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 更新物料分类
exports.updateMaterialCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, type } = req.body;
    
    // 这里应该更新物料分类
    // 暂时直接返回成功
    
    res.json({
      success: true,
      message: '物料分类更新成功'
    });
  } catch (error) {
    console.error('更新物料分类失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 恢复记录到指定版本
exports.restoreRecordVersion = async (req, res) => {
  try {
    const { id, version } = req.params;
    
    // 这里应该从数据库恢复记录到指定版本
    // 暂时直接返回成功
    
    res.json({
      success: true,
      message: `记录已恢复到版本 ${version}`
    });
  } catch (error) {
    console.error('恢复记录版本失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 数据质量检查
exports.checkDataQuality = async (req, res) => {
  try {
    // 这里应该执行数据质量检查
    // 暂时返回模拟结果
    const qualityReport = {
      totalRecords: 100,
      validRecords: 95,
      invalidRecords: 5,
      completeness: 95,
      accuracy: 98,
      consistency: 97
    };
    
    res.json({
      success: true,
      data: {
        qualityReport: qualityReport
      }
    });
  } catch (error) {
    console.error('数据质量检查失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 数据清理建议
exports.getDataCleaningSuggestions = async (req, res) => {
  try {
    // 这里应该提供数据清理建议
    // 暂时返回模拟建议
    const suggestions = [
      { 
        id: 1, 
        type: 'duplicate', 
        description: '发现重复记录', 
        count: 3,
        suggestion: '删除重复记录'
      },
      { 
        id: 2, 
        type: 'incomplete', 
        description: '发现不完整记录', 
        count: 5,
        suggestion: '补充缺失字段'
      }
    ];
    
    res.json({
      success: true,
      data: {
        suggestions: suggestions
      }
    });
  } catch (error) {
    console.error('获取数据清理建议失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 验证记录数据
exports.validateRecordData = async (req, res) => {
  try {
    const recordData = req.body;
    
    // 这里应该执行数据验证逻辑
    // 暂时直接返回验证成功
    
    res.json({
      success: true,
      message: '数据验证通过',
      data: {
        isValid: true
      }
    });
  } catch (error) {
    console.error('验证记录数据失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 执行质量检查
exports.performQualityCheck = async (req, res) => {
  try {
    // 这里应该执行质量检查逻辑
    // 暂时返回模拟结果
    
    const checkResult = {
      passed: true,
      issues: [],
      score: 95
    };
    
    res.json({
      success: true,
      data: {
        checkResult: checkResult
      }
    });
  } catch (error) {
    console.error('执行质量检查失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 设置记录提醒
exports.setRecordReminders = async (req, res) => {
  try {
    const { id } = req.params;
    const { time, message } = req.body;
    
    // 这里应该设置真实的提醒
    // 暂时直接返回成功
    
    res.json({
      success: true,
      message: '提醒设置成功'
    });
  } catch (error) {
    console.error('设置记录提醒失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取待审批记录列表
exports.getPendingApprovalRecords = async (req, res) => {
  try {
    // 这里应该从数据库获取待审批的记录
    // 暂时返回模拟数据
    const records = [
      { 
        id: 1, 
        batchNumber: 'B001', 
        date: '2023-05-01', 
        status: 'pending',
        submitter: '张三',
        submittedAt: new Date()
      },
      { 
        id: 2, 
        batchNumber: 'B002', 
        date: '2023-05-02', 
        status: 'pending',
        submitter: '李四',
        submittedAt: new Date()
      }
    ];
    
    res.json({
      success: true,
      data: {
        records: records
      }
    });
  } catch (error) {
    console.error('获取待审批记录列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取记录关联的物料使用情况
exports.getRecordMaterials = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 这里应该从数据库获取记录关联的物料使用情况
    // 暂时返回模拟数据
    const materials = [
      { 
        id: 1, 
        name: '饲料A', 
        type: 'feed',
        quantity: 100,
        unit: 'kg',
        cost: 500
      },
      { 
        id: 2, 
        name: '药品B', 
        type: 'medicine',
        quantity: 10,
        unit: '瓶',
        cost: 200
      }
    ];
    
    res.json({
      success: true,
      data: {
        materials: materials
      }
    });
  } catch (error) {
    console.error('获取记录关联的物料使用情况失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取记录关联的成本明细
exports.getRecordCosts = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 这里应该从数据库获取记录关联的成本明细
    // 暂时返回模拟数据
    const costs = {
      feedCost: 500,
      medicineCost: 200,
      laborCost: 300,
      otherCost: 100,
      totalCost: 1100
    };
    
    res.json({
      success: true,
      data: {
        costs: costs
      }
    });
  } catch (error) {
    console.error('获取记录关联成本明细失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};
