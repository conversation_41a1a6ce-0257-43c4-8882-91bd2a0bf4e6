#!/usr/bin/env node
// 插入测试数据脚本

const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');

class TestDataInserter {
  constructor() {
    this.dbConfig = {
      host: 'localhost',
      port: 3306,
      user: 'zhihuiyange',
      password: 'zhihuiyange123',
      database: 'zhihuiyange_local',
      charset: 'utf8mb4'
    };
  }

  async insertTestUsers() {
    
    let connection;
    try {
      connection = await mysql.createConnection(this.dbConfig);
      
      // 生成密码哈希
      const adminPassword = await bcrypt.hash('admin123', 10);
      const managerPassword = await bcrypt.hash('manager123', 10);
      const demoPassword = await bcrypt.hash('demo123', 10);
      
      // 插入测试用户
      const insertUsers = `
        INSERT IGNORE INTO users (username, password, email, role, createdAt, updatedAt) VALUES
        ('admin', ?, '<EMAIL>', 'admin', NOW(), NOW()),
        ('manager', ?, '<EMAIL>', 'manager', NOW(), NOW()),
        ('demo', ?, '<EMAIL>', 'user', NOW(), NOW());
      `;
      
      await connection.execute(insertUsers, [adminPassword, managerPassword, demoPassword]);
      
      // 获取用户ID
      const [users] = await connection.execute('SELECT id, username FROM users WHERE username IN ("admin", "manager", "demo")');
      users.forEach(user => {
        `);
      });
      
      return users;
    } catch (error) {
      console.error('❌ 插入用户失败:', error.message);
      throw error;
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }

  async insertTestHealthRecords(userId = 3) {
    
    let connection;
    try {
      connection = await mysql.createConnection(this.dbConfig);
      
      const insertHealthRecords = `
        INSERT IGNORE INTO health_records (user_id, goose_id, symptoms, diagnosis, treatment, status, created_at, updated_at) VALUES
        (?, 'G001', '精神不振，食欲下降', '感冒症状', '保温，给予维生素C', 'processing', NOW(), NOW()),
        (?, 'G002', '正常', '健康检查', '无需治疗', 'completed', NOW(), NOW()),
        (?, 'G003', '腹泻', '肠胃炎', '给予益生菌，调整饲料', 'processing', NOW(), NOW());
      `;
      
      await connection.execute(insertHealthRecords, [userId, userId, userId]);
      
    } catch (error) {
      console.error('❌ 插入健康记录失败:', error.message);
      throw error;
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }

  async insertTestProductionRecords(userId = 3) {
    
    let connection;
    try {
      connection = await mysql.createConnection(this.dbConfig);
      
      // 检查生产记录表结构
      const [columns] = await connection.execute('DESCRIBE production_records');
      columns.forEach(col => {
      });
      
      const insertProductionRecords = `
        INSERT IGNORE INTO production_records (user_id, egg_count, feed_consumption, temperature, humidity, notes, recorded_date, created_at, updated_at) VALUES
        (?, 120, 45.5, 25.5, 65.0, '天气晴朗，鹅群状态良好', '2023-12-01', NOW(), NOW()),
        (?, 115, 46.0, 24.8, 68.0, '有轻微降温，增加了保温措施', '2023-12-02', NOW(), NOW()),
        (?, 125, 44.8, 26.2, 62.0, '产蛋量有所提升', '2023-12-03', NOW(), NOW());
      `;
      
      await connection.execute(insertProductionRecords, [userId, userId, userId]);
      
    } catch (error) {
      console.error('❌ 插入生产记录失败:', error.message);
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }

  async checkExistingData() {
    
    let connection;
    try {
      connection = await mysql.createConnection(this.dbConfig);
      
      // 检查用户数据
      const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
      
      // 检查健康记录数据
      const [healthRecords] = await connection.execute('SELECT COUNT(*) as count FROM health_records');
      
      // 检查生产记录数据
      try {
        const [productionRecords] = await connection.execute('SELECT COUNT(*) as count FROM production_records');
      } catch (error) {
      }
      
    } catch (error) {
      console.error('❌ 检查数据失败:', error.message);
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }

  async insertAllTestData() {
    
    try {
      // 1. 检查现有数据
      await this.checkExistingData();
      
      // 2. 插入测试用户
      const users = await this.insertTestUsers();
      
      // 3. 获取demo用户ID
      const demoUser = users.find(u => u.username === 'demo');
      const userId = demoUser ? demoUser.id : 3;
      
      // 4. 插入健康记录
      await this.insertTestHealthRecords(userId);
      
      // 5. 插入生产记录
      await this.insertTestProductionRecords(userId);
      
      
    } catch (error) {
      console.error('\n❌ 插入测试数据失败:', error.message);
      process.exit(1);
    }
  }
}

// 运行插入
if (require.main === module) {
  const inserter = new TestDataInserter();
  inserter.insertAllTestData().catch(console.error);
}

module.exports = TestDataInserter;
