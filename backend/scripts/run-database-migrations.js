/**
 * 数据库迁移执行脚本
 * 统一数据库字段命名为snake_case
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

class DatabaseMigration {
  constructor() {
    this.config = {
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'zhihuiyange',
      password: process.env.DB_PASSWORD || 'zhihuiyange123',
      database: process.env.DB_NAME || 'zhihuiyange_local',
      charset: 'utf8mb4'
    };
  }

  async connect() {
    try {
      this.connection = await mysql.createConnection(this.config);
    } catch (error) {
      console.error('❌ 数据库连接失败:', error.message);
      throw error;
    }
  }

  async disconnect() {
    if (this.connection) {
      await this.connection.end();
    }
  }

  async executeMigration(migrationFile) {
    try {
      const migrationPath = path.join(__dirname, '../migrations', migrationFile);
      const sqlContent = await fs.readFile(migrationPath, 'utf8');
      
      // 分割SQL语句（处理多个语句）
      const statements = sqlContent
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      
      for (const statement of statements) {
        if (statement.trim()) {
          await this.connection.execute(statement);
          }...`);
        }
      }
      
    } catch (error) {
      console.error(`❌ 迁移 ${migrationFile} 执行失败:`, error.message);
      throw error;
    }
  }

  async createMigrationsTable() {
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS migrations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        migration_name VARCHAR(255) NOT NULL UNIQUE,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_migration_name (migration_name)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;
    
    await this.connection.execute(createTableSQL);
  }

  async isMigrationExecuted(migrationName) {
    const [rows] = await this.connection.execute(
      'SELECT id FROM migrations WHERE migration_name = ?',
      [migrationName]
    );
    return rows.length > 0;
  }

  async recordMigration(migrationName) {
    await this.connection.execute(
      'INSERT INTO migrations (migration_name) VALUES (?)',
      [migrationName]
    );
  }

  async runAllMigrations() {
    const migrationFiles = [
      '001-create-flocks-table.sql',
      '002-standardize-inventory-table.sql', 
      '003-standardize-users-table.sql',
      '004-merge-material-inventory-models.sql',
      '005-add-database-constraints.sql'
    ];


    for (const migrationFile of migrationFiles) {
      const migrationName = migrationFile.replace('.sql', '');
      
      if (await this.isMigrationExecuted(migrationName)) {
        continue;
      }

      await this.executeMigration(migrationFile);
      await this.recordMigration(migrationName);
    }

  }

  async verifyMigrations() {
    
    try {
      // 验证flocks表
      const [flockColumns] = await this.connection.execute(
        "SHOW COLUMNS FROM flocks"
      );
      
      // 验证inventory表
      const [inventoryColumns] = await this.connection.execute(
        "SHOW COLUMNS FROM inventory"
      );
      
      // 验证users表
      const [userColumns] = await this.connection.execute(
        "SHOW COLUMNS FROM users"
      );
      
      // 检查snake_case字段
      const snakeCaseFields = [
        'user_id', 'batch_number', 'total_count', 'current_count',
        'created_at', 'updated_at', 'current_stock', 'min_stock'
      ];
      
      for (const field of snakeCaseFields) {
        try {
          await this.connection.execute(`SELECT ${field} FROM flocks LIMIT 1`);
        } catch (error) {
          try {
            await this.connection.execute(`SELECT ${field} FROM inventory LIMIT 1`);
          } catch (error2) {
            try {
              await this.connection.execute(`SELECT ${field} FROM users LIMIT 1`);
            } catch (error3) {
            }
          }
        }
      }
      
    } catch (error) {
      console.error('❌ 验证过程中出错:', error.message);
    }
  }
}

// 主执行函数
async function main() {
  const migration = new DatabaseMigration();
  
  try {
    await migration.connect();
    await migration.createMigrationsTable();
    await migration.runAllMigrations();
    await migration.verifyMigrations();
  } catch (error) {
    console.error('❌ 迁移过程失败:', error.message);
    process.exit(1);
  } finally {
    await migration.disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = DatabaseMigration;