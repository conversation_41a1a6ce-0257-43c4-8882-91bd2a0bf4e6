// backend/scripts/sync-new-tables.js
// 同步新增的数据库表

const sequelize = require('../config/database');
const bcrypt = require('bcryptjs');

// 导入所有模型
const User = require('../models/user.model');
const HealthRecord = require('../models/health-record.model');
const ProductionRecord = require('../models/production-record.model');
const AIConfig = require('../models/ai-config.model');
const AIUsageStats = require('../models/ai-usage-stats.model');
const Inventory = require('../models/inventory.model');
const Announcement = require('../models/announcement.model');
const KnowledgeBase = require('../models/knowledge-base.model');

async function syncDatabase() {
  try {
    await sequelize.authenticate();

    
    // 同步所有表（alter: true 会更新现有表结构）
    await sequelize.sync({ alter: true });
    

    // 初始化AI配置数据
    await initializeAIConfig();
    
    // 初始化库存数据
    await initializeInventory();
    
    // 初始化公告数据
    await initializeAnnouncements();
    
    // 初始化知识库数据
    await initializeKnowledgeBase();

    
  } catch (error) {
    console.error('❌ 数据库同步失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 初始化AI配置数据
async function initializeAIConfig() {
  
  const defaultConfigs = [
    {
      provider: 'zhipu',
      name: '智谱AI',
      apiKey: 'your-zhipu-api-key', // 需要替换为真实密钥
      baseUrl: 'https://open.bigmodel.cn/api/paas/v4',
      models: {
        chat: 'glm-4-flash',
        vision: 'glm-4v-flash'
      },
      maxTokens: 4096,
      temperature: 0.7,
      enabled: true,
      isDefault: true,
      priority: 10
    },
    {
      provider: 'siliconflow',
      name: '硅基流动',
      apiKey: 'your-siliconflow-api-key', // 需要替换为真实密钥
      baseUrl: 'https://api.siliconflow.cn/v1',
      models: {
        chat: 'Qwen/Qwen2.5-7B-Instruct',
        vision: 'Qwen/Qwen2.5-VL-7B-Instruct'
      },
      maxTokens: 4096,
      temperature: 0.7,
      enabled: true,
      isDefault: false,
      priority: 5
    }
  ];

  for (const config of defaultConfigs) {
    await AIConfig.findOrCreate({
      where: { provider: config.provider },
      defaults: config
    });
  }
  
}

// 初始化库存数据
async function initializeInventory() {
  
  const defaultInventory = [
    {
      name: '鹅饲料',
      category: 'feed',
      specification: '25kg/袋',
      unit: '袋',
      currentStock: 50,
      minStock: 10,
      maxStock: 200,
      unitPrice: 85.00,
      supplier: '优质饲料供应商',
      location: '仓库A区'
    },
    {
      name: '维生素C',
      category: 'medicine',
      specification: '500g/瓶',
      unit: '瓶',
      currentStock: 20,
      minStock: 5,
      maxStock: 50,
      unitPrice: 25.00,
      supplier: '兽药供应商',
      location: '药品储存室'
    }
  ];

  for (const item of defaultInventory) {
    await Inventory.findOrCreate({
      where: { name: item.name, category: item.category },
      defaults: item
    });
  }
  
}

// 初始化公告数据
async function initializeAnnouncements() {
  
  const defaultAnnouncements = [
    {
      title: '欢迎使用智慧养鹅管理系统',
      content: '智慧养鹅管理系统已正式上线，为您提供全方位的养殖管理服务。',
      type: 'general',
      status: 'published',
      publishTime: new Date(),
      isTop: true,
      priority: 10,
      createdBy: 1
    }
  ];

  for (const announcement of defaultAnnouncements) {
    await Announcement.findOrCreate({
      where: { title: announcement.title },
      defaults: announcement
    });
  }
  
}

// 初始化知识库数据
async function initializeKnowledgeBase() {
  
  const defaultKnowledge = [
    {
      title: '鹅的基本饲养管理',
      content: '鹅是水禽，具有适应性强、生长快、肉质好等特点。在饲养过程中需要注意以下几个方面...',
      category: 'breeding',
      tags: ['饲养', '管理', '基础知识'],
      keywords: '鹅 饲养 管理 基础',
      difficulty: 'beginner',
      readTime: 5,
      status: 'published',
      isRecommended: true,
      publishTime: new Date(),
      createdBy: 1
    },
    {
      title: '常见鹅病防治',
      content: '鹅在养殖过程中可能遇到的常见疾病及其防治方法...',
      category: 'disease',
      tags: ['疾病', '防治', '健康'],
      keywords: '鹅病 防治 疾病 健康',
      difficulty: 'intermediate',
      readTime: 8,
      status: 'published',
      isRecommended: true,
      publishTime: new Date(),
      createdBy: 1
    }
  ];

  for (const knowledge of defaultKnowledge) {
    await KnowledgeBase.findOrCreate({
      where: { title: knowledge.title },
      defaults: knowledge
    });
  }
  
}

// 运行同步
if (require.main === module) {
  syncDatabase();
}

module.exports = { syncDatabase };
