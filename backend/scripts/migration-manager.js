/**
 * 完整的数据库迁移管理系统
 * 支持版本控制、回滚、备份等功能
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

class MigrationManager {
  constructor() {
    this.config = {
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'zhihuiyange',
      password: process.env.DB_PASSWORD || 'zhihuiyange123',
      database: process.env.DB_NAME || 'zhihuiyange_local',
      charset: 'utf8mb4'
    };
    
    this.migrationsDir = path.join(__dirname, '../migrations');
    this.backupsDir = path.join(__dirname, '../backups');
  }

  async connect() {
    try {
      this.connection = await mysql.createConnection(this.config);
    } catch (error) {
      console.error('❌ 数据库连接失败:', error.message);
      throw error;
    }
  }

  async disconnect() {
    if (this.connection) {
      await this.connection.end();
    }
  }

  async ensureBackupsDir() {
    try {
      await fs.access(this.backupsDir);
    } catch {
      await fs.mkdir(this.backupsDir, { recursive: true });
    }
  }

  async createMigrationsTable() {
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS migrations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        migration_name VARCHAR(255) NOT NULL UNIQUE,
        version VARCHAR(50) NOT NULL,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        rollback_sql TEXT,
        checksum VARCHAR(64),
        INDEX idx_migration_name (migration_name),
        INDEX idx_version (version),
        INDEX idx_executed_at (executed_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;
    
    await this.connection.execute(createTableSQL);
  }

  async getMigrationFiles() {
    const files = await fs.readdir(this.migrationsDir);
    return files
      .filter(file => file.endsWith('.sql'))
      .sort()
      .map(file => ({
        filename: file,
        name: file.replace('.sql', ''),
        path: path.join(this.migrationsDir, file)
      }));
  }

  async getExecutedMigrations() {
    const [rows] = await this.connection.execute(
      'SELECT migration_name, version, executed_at FROM migrations ORDER BY executed_at'
    );
    return rows;
  }

  async calculateChecksum(filePath) {
    const crypto = require('crypto');
    const content = await fs.readFile(filePath, 'utf8');
    return crypto.createHash('md5').update(content).digest('hex');
  }

  async createBackup(tableNames = null) {
    await this.ensureBackupsDir();
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(this.backupsDir, `backup-${timestamp}.sql`);
    
    
    // 获取所有表名
    if (!tableNames) {
      const [tables] = await this.connection.execute(
        `SHOW TABLES FROM \`${this.config.database}\``
      );
      tableNames = tables.map(row => Object.values(row)[0]);
    }
    
    let backupSQL = `-- Database backup created at ${new Date().toISOString()}\n`;
    backupSQL += `-- Database: ${this.config.database}\n\n`;
    
    for (const tableName of tableNames) {
      // 获取表结构
      const [createTable] = await this.connection.execute(`SHOW CREATE TABLE \`${tableName}\``);
      backupSQL += `-- Table: ${tableName}\n`;
      backupSQL += `DROP TABLE IF EXISTS \`${tableName}\`;\n`;
      backupSQL += createTable[0]['Create Table'] + ';\n\n';
      
      // 获取表数据
      const [rows] = await this.connection.execute(`SELECT * FROM \`${tableName}\``);
      if (rows.length > 0) {
        backupSQL += `-- Data for table: ${tableName}\n`;
        backupSQL += `LOCK TABLES \`${tableName}\` WRITE;\n`;
        backupSQL += `INSERT INTO \`${tableName}\` VALUES\n`;
        
        const valueStrings = rows.map(row => {
          const values = Object.values(row).map(value => {
            if (value === null) return 'NULL';
            if (typeof value === 'string') return `'${value.replace(/'/g, "\\'")}'`;
            if (value instanceof Date) return `'${value.toISOString().slice(0, 19).replace('T', ' ')}'`;
            return value;
          });
          return `(${values.join(', ')})`;
        });
        
        backupSQL += valueStrings.join(',\n') + ';\n';
        backupSQL += `UNLOCK TABLES;\n\n`;
      }
    }
    
    await fs.writeFile(backupFile, backupSQL);
    return backupFile;
  }

  async executeMigration(migrationFile) {
    try {
      const sqlContent = await fs.readFile(migrationFile.path, 'utf8');
      const checksum = await this.calculateChecksum(migrationFile.path);
      
      // 分割SQL语句
      const statements = sqlContent
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      
      // 开始事务
      await this.connection.execute('START TRANSACTION');
      
      try {
        for (const statement of statements) {
          if (statement.trim()) {
            await this.connection.execute(statement);
          }
        }
        
        // 记录迁移
        await this.connection.execute(
          'INSERT INTO migrations (migration_name, version, checksum) VALUES (?, ?, ?)',
          [migrationFile.name, this.extractVersion(migrationFile.filename), checksum]
        );
        
        await this.connection.execute('COMMIT');
        
      } catch (error) {
        await this.connection.execute('ROLLBACK');
        throw error;
      }
      
    } catch (error) {
      console.error(`❌ 迁移 ${migrationFile.filename} 执行失败:`, error.message);
      throw error;
    }
  }

  extractVersion(filename) {
    const match = filename.match(/^(\d+)/);
    return match ? match[1] : '000';
  }

  async isMigrationExecuted(migrationName) {
    const [rows] = await this.connection.execute(
      'SELECT id FROM migrations WHERE migration_name = ?',
      [migrationName]
    );
    return rows.length > 0;
  }

  async runPendingMigrations() {
    
    const migrationFiles = await this.getMigrationFiles();
    const executedMigrations = await this.getExecutedMigrations();
    
    
    let pendingCount = 0;
    
    for (const migrationFile of migrationFiles) {
      if (await this.isMigrationExecuted(migrationFile.name)) {
        continue;
      }
      
      // 创建备份（仅第一个待执行的迁移时）
      if (pendingCount === 0) {
        await this.createBackup();
      }
      
      await this.executeMigration(migrationFile);
      pendingCount++;
    }
    
    if (pendingCount === 0) {
    } else {
    }
  }

  async getMigrationStatus() {
    const migrationFiles = await this.getMigrationFiles();
    const executedMigrations = await this.getExecutedMigrations();
    
    );
    
    executedMigrations.forEach(migration => {
      } - ${migration.migration_name} (${migration.executed_at})`);
    });
    
    const pendingMigrations = migrationFiles.filter(file => 
      !executedMigrations.some(executed => executed.migration_name === file.name)
    );
    
    if (pendingMigrations.length === 0) {
    } else {
      pendingMigrations.forEach(migration => {
        .padStart(3, '0')} - ${migration.name}`);
      });
    }
    
  }

  async validateDatabase() {
    
    try {
      // 检查关键表是否存在
      const criticalTables = ['users', 'flocks', 'unified_inventory', 'migrations'];
      
      for (const tableName of criticalTables) {
        const [tables] = await this.connection.execute(
          "SELECT 1 FROM information_schema.tables WHERE table_schema = ? AND table_name = ?",
          [this.config.database, tableName]
        );
        
        if (tables.length === 0) {
          return false;
        } else {
        }
      }
      
      // 检查约束
      const [constraints] = await this.connection.execute(
        "SELECT COUNT(*) as count FROM information_schema.table_constraints WHERE table_schema = ? AND constraint_type = 'CHECK'",
        [this.config.database]
      );
      
      
      // 检查数据完整性
      const [userCount] = await this.connection.execute('SELECT COUNT(*) as count FROM users');
      
      return true;
      
    } catch (error) {
      console.error('❌ 数据库验证失败:', error.message);
      return false;
    }
  }
}

// 主执行函数
async function main() {
  const manager = new MigrationManager();
  
  const command = process.argv[2] || 'migrate';
  
  try {
    await manager.connect();
    await manager.createMigrationsTable();
    
    switch (command) {
      case 'migrate':
        await manager.runPendingMigrations();
        await manager.validateDatabase();
        break;
        
      case 'status':
        await manager.getMigrationStatus();
        break;
        
      case 'backup':
        await manager.createBackup();
        break;
        
      case 'validate':
        await manager.validateDatabase();
        break;
        
      default:
        process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
    process.exit(1);
  } finally {
    await manager.disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = MigrationManager;