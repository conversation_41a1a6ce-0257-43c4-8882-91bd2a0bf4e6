#!/usr/bin/env node
// backend/scripts/init-complete-database.js
// 完整的数据库初始化脚本

const sequelize = require('../config/database');
const bcrypt = require('bcryptjs');

// 导入所有模型
const User = require('../models/user.model');
const HealthRecord = require('../models/health-record.model');
const ProductionRecord = require('../models/production-record.model');
const AIConfig = require('../models/ai-config.model');
const AIUsageStats = require('../models/ai-usage-stats.model');
const Inventory = require('../models/inventory.model');
const Announcement = require('../models/announcement.model');
const KnowledgeBase = require('../models/knowledge-base.model');

async function initializeDatabase() {
  try {
    
    // 1. 测试数据库连接
    await sequelize.authenticate();

    // 2. 同步数据库表
    await sequelize.sync({ force: false, alter: true });

    // 3. 初始化用户数据
    await initializeUsers();
    
    // 4. 初始化AI配置数据
    await initializeAIConfig();
    
    // 5. 初始化库存数据
    await initializeInventory();
    
    // 6. 初始化公告数据
    await initializeAnnouncements();
    
    // 7. 初始化知识库数据
    await initializeKnowledgeBase();
    
    // 8. 初始化示例数据
    await initializeSampleData();

    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// 初始化用户数据
async function initializeUsers() {
  
  const users = [
    {
      username: 'admin',
      password: 'admin123',
      email: '<EMAIL>',
      role: 'admin',
      nickname: '系统管理员'
    },
    {
      username: 'manager',
      password: 'manager123',
      email: '<EMAIL>',
      role: 'manager',
      nickname: '业务经理'
    },
    {
      username: 'demo',
      password: 'demo123',
      email: '<EMAIL>',
      role: 'user',
      nickname: '演示用户'
    }
  ];

  for (const userData of users) {
    const hashedPassword = await bcrypt.hash(userData.password, 10);
    await User.findOrCreate({
      where: { username: userData.username },
      defaults: {
        ...userData,
        password: hashedPassword
      }
    });
  }
  
}

// 初始化AI配置数据
async function initializeAIConfig() {
  
  const aiConfigs = [
    {
      provider: 'zhipu',
      name: '智谱AI',
      apiKey: 'your-zhipu-api-key-here', // 请替换为真实的API密钥
      baseUrl: 'https://open.bigmodel.cn/api/paas/v4',
      models: {
        chat: 'glm-4-flash',
        vision: 'glm-4v-flash'
      },
      maxTokens: 4096,
      temperature: 0.7,
      enabled: true,
      isDefault: true,
      priority: 10,
      createdBy: 1
    },
    {
      provider: 'siliconflow',
      name: '硅基流动',
      apiKey: 'your-siliconflow-api-key-here', // 请替换为真实的API密钥
      baseUrl: 'https://api.siliconflow.cn/v1',
      models: {
        chat: 'Qwen/Qwen2.5-7B-Instruct',
        vision: 'Qwen/Qwen2.5-VL-7B-Instruct'
      },
      maxTokens: 4096,
      temperature: 0.7,
      enabled: true,
      isDefault: false,
      priority: 5,
      createdBy: 1
    }
  ];

  for (const config of aiConfigs) {
    await AIConfig.findOrCreate({
      where: { provider: config.provider },
      defaults: config
    });
  }
  
}

// 初始化库存数据
async function initializeInventory() {
  
  const inventoryItems = [
    {
      name: '优质鹅饲料',
      category: 'feed',
      specification: '25kg/袋',
      unit: '袋',
      currentStock: 50,
      minStock: 10,
      maxStock: 200,
      unitPrice: 85.00,
      supplier: '优质饲料供应商',
      supplierContact: '13800138000',
      location: '仓库A区',
      status: 'active',
      createdBy: 1
    },
    {
      name: '维生素C',
      category: 'medicine',
      specification: '500g/瓶',
      unit: '瓶',
      currentStock: 20,
      minStock: 5,
      maxStock: 50,
      unitPrice: 25.00,
      supplier: '兽药供应商',
      supplierContact: '13900139000',
      location: '药品储存室',
      status: 'active',
      createdBy: 1
    },
    {
      name: '自动饮水器',
      category: 'equipment',
      specification: '不锈钢材质',
      unit: '个',
      currentStock: 15,
      minStock: 3,
      maxStock: 30,
      unitPrice: 120.00,
      supplier: '设备供应商',
      supplierContact: '13700137000',
      location: '设备仓库',
      status: 'active',
      createdBy: 1
    }
  ];

  for (const item of inventoryItems) {
    await Inventory.findOrCreate({
      where: { name: item.name, category: item.category },
      defaults: item
    });
  }
  
}

// 初始化公告数据
async function initializeAnnouncements() {
  
  const announcements = [
    {
      title: '欢迎使用智慧养鹅管理系统',
      content: '智慧养鹅管理系统已正式上线！本系统集成了AI智能盘点、健康管理、生产记录等功能，为您提供全方位的养殖管理服务。如有任何问题，请联系技术支持。',
      type: 'general',
      status: 'published',
      publishTime: new Date(),
      isTop: true,
      priority: 10,
      createdBy: 1
    },
    {
      title: 'AI智能盘点功能上线',
      content: 'AI智能盘点功能已正式上线，支持通过拍照自动识别鹅群数量，大大提高盘点效率。请在生产管理页面体验此功能。',
      type: 'update',
      status: 'published',
      publishTime: new Date(),
      isTop: false,
      priority: 5,
      createdBy: 1
    }
  ];

  for (const announcement of announcements) {
    await Announcement.findOrCreate({
      where: { title: announcement.title },
      defaults: announcement
    });
  }
  
}

// 初始化知识库数据
async function initializeKnowledgeBase() {
  
  const knowledgeItems = [
    {
      title: '鹅的基本饲养管理技术',
      content: '鹅是水禽，具有适应性强、生长快、肉质好等特点。在饲养过程中需要注意以下几个方面：\n\n1. 饲料管理：选择优质饲料，合理搭配营养\n2. 环境控制：保持适宜的温度和湿度\n3. 疾病预防：定期检查，及时发现问题\n4. 清洁卫生：保持养殖环境清洁',
      category: 'breeding',
      tags: ['饲养', '管理', '基础知识'],
      keywords: '鹅 饲养 管理 基础 技术',
      difficulty: 'beginner',
      readTime: 5,
      status: 'published',
      isRecommended: true,
      publishTime: new Date(),
      createdBy: 1
    },
    {
      title: '常见鹅病防治指南',
      content: '鹅在养殖过程中可能遇到的常见疾病及其防治方法：\n\n1. 鹅瘟：病毒性疾病，需要疫苗预防\n2. 鹅副粘病毒病：呼吸道疾病，注意通风\n3. 大肠杆菌病：细菌性疾病，保持环境卫生\n4. 寄生虫病：定期驱虫，保持清洁',
      category: 'disease',
      tags: ['疾病', '防治', '健康', '预防'],
      keywords: '鹅病 防治 疾病 健康 预防 治疗',
      difficulty: 'intermediate',
      readTime: 8,
      status: 'published',
      isRecommended: true,
      publishTime: new Date(),
      createdBy: 1
    }
  ];

  for (const knowledge of knowledgeItems) {
    await KnowledgeBase.findOrCreate({
      where: { title: knowledge.title },
      defaults: knowledge
    });
  }
  
}

// 初始化示例数据
async function initializeSampleData() {
  
  // 创建一些示例健康记录
  const healthRecords = [
    {
      batchNumber: 'BATCH001',
      checkDate: new Date(),
      healthStatus: 'healthy',
      temperature: 38.5,
      weight: 3.2,
      appetite: 'good',
      activity: 'active',
      symptoms: '',
      treatment: '',
      notes: '整体健康状况良好',
      createdBy: 1
    }
  ];

  for (const record of healthRecords) {
    await HealthRecord.findOrCreate({
      where: { batchNumber: record.batchNumber, checkDate: record.checkDate },
      defaults: record
    });
  }

  // 创建一些示例生产记录
  const productionRecords = [
    {
      batchNumber: 'BATCH001',
      recordDate: new Date(),
      type: 'growth',
      quantity: 100,
      weight: 320,
      feedConsumption: 25,
      notes: '生长情况良好',
      createdBy: 1
    }
  ];

  for (const record of productionRecords) {
    await ProductionRecord.findOrCreate({
      where: { batchNumber: record.batchNumber, recordDate: record.recordDate },
      defaults: record
    });
  }
  
}

// 如果直接运行此脚本
if (require.main === module) {
  initializeDatabase();
}

module.exports = { initializeDatabase };
