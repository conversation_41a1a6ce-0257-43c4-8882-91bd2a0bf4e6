#!/usr/bin/env node

/**
 * 智慧养鹅系统 - 创建缺失数据表脚本
 * 执行SQL脚本创建所有缺失的核心数据表
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'zhihuiyange',
  password: process.env.DB_PASSWORD || 'zhihuiyange123',
  database: process.env.DB_NAME || 'zhihuiyange_local',
  multipleStatements: true
};

async function createMissingTables() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);

    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, 'create-missing-tables.sql');
    
    const sqlContent = await fs.readFile(sqlFilePath, 'utf8');

    // 执行SQL脚本
    const [results] = await connection.execute(sqlContent);

    // 验证表是否创建成功
    const tables = [
      'tasks',
      'announcements', 
      'announcement_reads',
      'price_records',
      'price_subscriptions',
      'inventory_records',
      'inventory_alerts',
      'materials',
      'material_stock_records',
      'material_alerts'
    ];

    for (const table of tables) {
      try {
        const [rows] = await connection.execute(`SHOW TABLES LIKE '${table}'`);
        if (rows.length > 0) {
          
          // 检查表结构
          const [columns] = await connection.execute(`DESCRIBE ${table}`);
          
          // 检查数据量
          const [count] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
        } else {
        }
      } catch (error) {
      }
    }

    tables.forEach((table, index) => {
    });

  } catch (error) {
    console.error('❌ 执行过程中出现错误:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 主函数
async function main() {
  );
  
  try {
    await createMissingTables();
    process.exit(0);
  } catch (error) {
    console.error('\n❌ 操作失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { createMissingTables };
