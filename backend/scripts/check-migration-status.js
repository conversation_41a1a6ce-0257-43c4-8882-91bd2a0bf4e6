/**
 * 快速检查迁移状态脚本
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

async function checkMigrationStatus() {
  const config = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'zhihuiyange',
    password: process.env.DB_PASSWORD || 'zhihuiyange123',
    database: process.env.DB_NAME || 'zhihuiyange_local',
    charset: 'utf8mb4'
  };

  let connection;
  
  try {
    
    connection = await mysql.createConnection(config);
    
    // 检查migrations表是否存在
    const [tables] = await connection.execute(
      "SELECT 1 FROM information_schema.tables WHERE table_schema = ? AND table_name = 'migrations'",
      [config.database]
    );
    
    if (tables.length === 0) {
      return;
    }
    
    // 获取已执行的迁移
    const [executedMigrations] = await connection.execute(
      'SELECT migration_name, version, executed_at FROM migrations ORDER BY executed_at'
    );
    
    // 获取迁移文件
    const migrationsDir = path.join(__dirname, '../migrations');
    const files = await fs.readdir(migrationsDir);
    const migrationFiles = files
      .filter(file => file.endsWith('.sql'))
      .sort()
      .map(file => file.replace('.sql', ''));
    
    
    // 检查待执行的迁移
    const executedNames = new Set(executedMigrations.map(m => m.migration_name));
    const pendingMigrations = migrationFiles.filter(name => !executedNames.has(name));
    
    if (pendingMigrations.length > 0) {
      pendingMigrations.forEach(migration => {
      });
    } else {
    }
    
    // 检查数据库完整性
    
    // 检查关键表
    const criticalTables = ['users', 'flocks', 'unified_inventory'];
    for (const tableName of criticalTables) {
      const [tableExists] = await connection.execute(
        "SELECT 1 FROM information_schema.tables WHERE table_schema = ? AND table_name = ?",
        [config.database, tableName]
      );
      
      if (tableExists.length > 0) {
        const [rowCount] = await connection.execute(`SELECT COUNT(*) as count FROM \`${tableName}\``);
      } else {
      }
    }
    
    // 检查约束
    const [constraints] = await connection.execute(
      "SELECT COUNT(*) as count FROM information_schema.table_constraints WHERE table_schema = ? AND constraint_type = 'CHECK'",
      [config.database]
    );
    
    // 检查系统配置（如果存在）
    try {
      const [configCount] = await connection.execute('SELECT COUNT(*) as count FROM system_config');
    } catch {
    }
    
  } catch (error) {
    console.error('❌ 检查过程出错:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkMigrationStatus();
}

module.exports = checkMigrationStatus;