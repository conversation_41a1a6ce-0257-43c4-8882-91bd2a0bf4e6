#!/usr/bin/env node
// backend/scripts/migrate-field-names.js
// 字段命名规范迁移脚本

const sequelize = require('../config/database');
const { QueryInterface } = require('sequelize');

class FieldNameMigration {
  constructor() {
    this.queryInterface = sequelize.getQueryInterface();
  }

  /**
   * 执行字段命名迁移
   */
  async migrate() {
    
    try {
      // 1. 备份数据库
      await this.backupDatabase();
      
      // 2. 迁移用户表字段
      await this.migrateUserFields();
      
      // 3. 迁移健康记录表字段
      await this.migrateHealthRecordFields();
      
      // 4. 迁移生产记录表字段
      await this.migrateProductionRecordFields();
      
      // 5. 迁移库存表字段
      await this.migrateInventoryFields();
      
      // 6. 迁移公告表字段
      await this.migrateAnnouncementFields();
      
      // 7. 迁移知识库表字段
      await this.migrateKnowledgeBaseFields();
      
      // 8. 验证迁移结果
      await this.validateMigration();
      
      
    } catch (error) {
      console.error('❌ 迁移失败:', error);
      await this.rollback();
      throw error;
    }
  }

  /**
   * 备份数据库
   */
  async backupDatabase() {
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = `zhihuiyange_backup_${timestamp}`;
    
    // 这里可以添加数据库备份逻辑
  }

  /**
   * 迁移用户表字段
   */
  async migrateUserFields() {
    
    const tableName = 'users';
    
    // 检查表是否存在
    const tableExists = await this.queryInterface.showAllTables();
    if (!tableExists.includes(tableName)) {
      return;
    }
    
    // 添加新字段（如果不存在）
    try {
      await this.queryInterface.addColumn(tableName, 'farm_name', {
        type: sequelize.Sequelize.STRING(100),
        allowNull: true,
        comment: '农场名称'
      });
    } catch (error) {
      if (!error.message.includes('Duplicate column name')) {
        throw error;
      }
    }
    
    try {
      await this.queryInterface.addColumn(tableName, 'phone', {
        type: sequelize.Sequelize.STRING(20),
        allowNull: true,
        comment: '联系电话'
      });
    } catch (error) {
      if (!error.message.includes('Duplicate column name')) {
        throw error;
      }
    }
  }

  /**
   * 迁移健康记录表字段
   */
  async migrateHealthRecordFields() {
    
    const tableName = 'health_records';
    
    // 检查并添加健康状态字段
    try {
      await this.queryInterface.addColumn(tableName, 'health_status', {
        type: sequelize.Sequelize.ENUM('healthy', 'sick', 'recovering', 'dead'),
        allowNull: true,
        defaultValue: 'healthy',
        comment: '健康状态'
      });
    } catch (error) {
      if (!error.message.includes('Duplicate column name')) {
      }
    }
    
    // 更新现有记录的健康状态
    await sequelize.query(`
      UPDATE ${tableName} 
      SET health_status = CASE 
        WHEN status = 'completed' THEN 'healthy'
        WHEN status = 'processing' THEN 'sick'
        ELSE 'healthy'
      END
      WHERE health_status IS NULL
    `);
    
  }

  /**
   * 迁移生产记录表字段
   */
  async migrateProductionRecordFields() {
    
    const tableName = 'production_records';
    
    // 添加批次号字段
    try {
      await this.queryInterface.addColumn(tableName, 'batch_number', {
        type: sequelize.Sequelize.STRING(50),
        allowNull: true,
        comment: '批次号'
      });
    } catch (error) {
      if (!error.message.includes('Duplicate column name')) {
      }
    }
    
    // 为现有记录生成批次号
    await sequelize.query(`
      UPDATE ${tableName} 
      SET batch_number = CONCAT('BATCH_', DATE_FORMAT(recorded_date, '%Y%m%d'), '_', id)
      WHERE batch_number IS NULL
    `);
    
  }

  /**
   * 迁移库存表字段
   */
  async migrateInventoryFields() {
    
    const tableName = 'inventory_records';
    
    // 检查表是否存在
    const tableExists = await this.queryInterface.showAllTables();
    if (!tableExists.includes(tableName)) {
      return;
    }
    
    // 添加供应商字段
    try {
      await this.queryInterface.addColumn(tableName, 'supplier', {
        type: sequelize.Sequelize.STRING(100),
        allowNull: true,
        comment: '供应商'
      });
    } catch (error) {
      if (!error.message.includes('Duplicate column name')) {
      }
    }
  }

  /**
   * 迁移公告表字段
   */
  async migrateAnnouncementFields() {
    
    const tableName = 'announcements';
    
    // 检查表是否存在
    const tableExists = await this.queryInterface.showAllTables();
    if (!tableExists.includes(tableName)) {
      return;
    }
    
    // 添加发布状态字段
    try {
      await this.queryInterface.addColumn(tableName, 'publish_status', {
        type: sequelize.Sequelize.ENUM('draft', 'published', 'archived'),
        allowNull: false,
        defaultValue: 'published',
        comment: '发布状态'
      });
    } catch (error) {
      if (!error.message.includes('Duplicate column name')) {
      }
    }
  }

  /**
   * 迁移知识库表字段
   */
  async migrateKnowledgeBaseFields() {
    
    const tableName = 'knowledge_base';
    
    // 检查表是否存在
    const tableExists = await this.queryInterface.showAllTables();
    if (!tableExists.includes(tableName)) {
      return;
    }
    
    // 添加浏览次数字段
    try {
      await this.queryInterface.addColumn(tableName, 'view_count', {
        type: sequelize.Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '浏览次数'
      });
    } catch (error) {
      if (!error.message.includes('Duplicate column name')) {
      }
    }
  }

  /**
   * 验证迁移结果
   */
  async validateMigration() {
    
    const tables = ['users', 'health_records', 'production_records'];
    
    for (const table of tables) {
      try {
        const columns = await this.queryInterface.describeTable(table);
        .length} 个字段`);
      } catch (error) {
      }
    }
  }

  /**
   * 回滚迁移
   */
  async rollback() {
    // 这里可以添加回滚逻辑
  }
}

// 执行迁移
async function main() {
  const migration = new FieldNameMigration();
  
  try {
    await sequelize.authenticate();
    
    await migration.migrate();
    
  } catch (error) {
    console.error('迁移失败:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = FieldNameMigration;
