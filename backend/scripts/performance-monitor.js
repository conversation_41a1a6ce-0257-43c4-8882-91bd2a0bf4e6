#!/usr/bin/env node

/**
 * 智慧养鹅系统 - 数据库性能监控脚本
 * 监控数据库性能指标，生成性能报告
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USERNAME || 'zhihuiyange',
  password: process.env.DB_PASSWORD || 'zhihuiyange123',
  database: process.env.DB_NAME || 'zhihuiyange_local',
  charset: 'utf8mb4'
};

class PerformanceMonitor {
  constructor() {
    this.connection = null;
    this.reportData = {
      timestamp: new Date(),
      tableStats: [],
      indexStats: [],
      slowQueries: [],
      recommendations: []
    };
  }

  async connect() {
    this.connection = await mysql.createConnection(dbConfig);
  }

  async disconnect() {
    if (this.connection) {
      await this.connection.end();
    }
  }

  /**
   * 获取表统计信息
   */
  async getTableStats() {
    
    const query = `
      SELECT 
        TABLE_NAME,
        TABLE_ROWS,
        DATA_LENGTH,
        INDEX_LENGTH,
        DATA_FREE,
        AUTO_INCREMENT,
        CREATE_TIME,
        UPDATE_TIME,
        TABLE_COLLATION
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? 
        AND TABLE_NAME IN (
          'tasks', 'announcements', 'announcement_reads', 
          'price_records', 'price_subscriptions',
          'inventory_records', 'inventory_alerts',
          'materials', 'material_stock_records', 'material_alerts'
        )
      ORDER BY DATA_LENGTH DESC
    `;

    const [tables] = await this.connection.execute(query, [dbConfig.database]);
    
    this.reportData.tableStats = tables.map(table => ({
      tableName: table.TABLE_NAME,
      rowCount: table.TABLE_ROWS,
      dataSize: this.formatBytes(table.DATA_LENGTH),
      indexSize: this.formatBytes(table.INDEX_LENGTH),
      totalSize: this.formatBytes(table.DATA_LENGTH + table.INDEX_LENGTH),
      freeSpace: this.formatBytes(table.DATA_FREE),
      autoIncrement: table.AUTO_INCREMENT,
      createTime: table.CREATE_TIME,
      updateTime: table.UPDATE_TIME,
      collation: table.TABLE_COLLATION
    }));

  }

  /**
   * 获取索引统计信息
   */
  async getIndexStats() {
    
    const query = `
      SELECT 
        TABLE_NAME,
        INDEX_NAME,
        COLUMN_NAME,
        CARDINALITY,
        SUB_PART,
        PACKED,
        NULLABLE,
        INDEX_TYPE,
        COMMENT
      FROM INFORMATION_SCHEMA.STATISTICS 
      WHERE TABLE_SCHEMA = ? 
        AND TABLE_NAME IN (
          'tasks', 'announcements', 'announcement_reads', 
          'price_records', 'price_subscriptions',
          'inventory_records', 'inventory_alerts',
          'materials', 'material_stock_records', 'material_alerts'
        )
      ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX
    `;

    const [indexes] = await this.connection.execute(query, [dbConfig.database]);
    
    // 按表和索引分组
    const indexGroups = {};
    indexes.forEach(index => {
      const key = `${index.TABLE_NAME}.${index.INDEX_NAME}`;
      if (!indexGroups[key]) {
        indexGroups[key] = {
          tableName: index.TABLE_NAME,
          indexName: index.INDEX_NAME,
          indexType: index.INDEX_TYPE,
          columns: [],
          cardinality: index.CARDINALITY
        };
      }
      indexGroups[key].columns.push({
        columnName: index.COLUMN_NAME,
        cardinality: index.CARDINALITY,
        nullable: index.NULLABLE
      });
    });

    this.reportData.indexStats = Object.values(indexGroups);
    .length} 个索引的统计信息`);
  }

  /**
   * 检查慢查询
   */
  async checkSlowQueries() {
    
    try {
      // 检查慢查询日志是否启用
      const [slowLogStatus] = await this.connection.execute('SHOW VARIABLES LIKE "slow_query_log"');
      const [longQueryTime] = await this.connection.execute('SHOW VARIABLES LIKE "long_query_time"');
      
      this.reportData.slowQueries = {
        enabled: slowLogStatus[0]?.Value === 'ON',
        threshold: parseFloat(longQueryTime[0]?.Value || 10),
        message: slowLogStatus[0]?.Value === 'ON' ? 
          '慢查询日志已启用' : 
          '建议启用慢查询日志以监控性能'
      };
      
    } catch (error) {
      this.reportData.slowQueries = {
        enabled: false,
        error: error.message
      };
    }
  }

  /**
   * 生成性能建议
   */
  generateRecommendations() {
    
    const recommendations = [];

    // 检查表大小
    this.reportData.tableStats.forEach(table => {
      const dataSizeBytes = this.parseBytes(table.dataSize);
      const indexSizeBytes = this.parseBytes(table.indexSize);
      
      if (dataSizeBytes > 100 * 1024 * 1024) { // 100MB
        recommendations.push({
          type: 'table_size',
          table: table.tableName,
          message: `表 ${table.tableName} 数据量较大 (${table.dataSize})，建议考虑分区或归档历史数据`,
          priority: 'medium'
        });
      }

      if (indexSizeBytes > dataSizeBytes * 0.5) {
        recommendations.push({
          type: 'index_size',
          table: table.tableName,
          message: `表 ${table.tableName} 索引大小 (${table.indexSize}) 超过数据大小的50%，建议检查索引使用情况`,
          priority: 'low'
        });
      }

      if (table.freeSpace && this.parseBytes(table.freeSpace) > 10 * 1024 * 1024) { // 10MB
        recommendations.push({
          type: 'fragmentation',
          table: table.tableName,
          message: `表 ${table.tableName} 存在碎片空间 (${table.freeSpace})，建议执行 OPTIMIZE TABLE`,
          priority: 'medium'
        });
      }
    });

    // 检查索引基数
    this.reportData.indexStats.forEach(index => {
      if (index.cardinality < 10 && index.indexName !== 'PRIMARY') {
        recommendations.push({
          type: 'low_cardinality',
          table: index.tableName,
          index: index.indexName,
          message: `索引 ${index.tableName}.${index.indexName} 基数较低 (${index.cardinality})，可能效果不佳`,
          priority: 'low'
        });
      }
    });

    // 慢查询建议
    if (!this.reportData.slowQueries.enabled) {
      recommendations.push({
        type: 'slow_query_log',
        message: '建议启用慢查询日志以监控数据库性能',
        priority: 'high'
      });
    }

    this.reportData.recommendations = recommendations;
  }

  /**
   * 生成性能报告
   */
  async generateReport() {
    
    const reportContent = `
# 智慧养鹅系统数据库性能报告

**生成时间**: ${this.reportData.timestamp.toLocaleString('zh-CN')}

## 📊 表统计信息

| 表名 | 行数 | 数据大小 | 索引大小 | 总大小 | 碎片空间 |
|------|------|----------|----------|--------|----------|
${this.reportData.tableStats.map(table => 
  `| ${table.tableName} | ${table.rowCount || 0} | ${table.dataSize} | ${table.indexSize} | ${table.totalSize} | ${table.freeSpace || '0 B'} |`
).join('\n')}

## 🔍 索引统计信息

${this.reportData.indexStats.map(index => 
  `### ${index.tableName}.${index.indexName}
- **类型**: ${index.indexType}
- **基数**: ${index.cardinality}
- **列**: ${index.columns.map(col => col.columnName).join(', ')}`
).join('\n\n')}

## 🐌 慢查询状态

- **启用状态**: ${this.reportData.slowQueries.enabled ? '✅ 已启用' : '❌ 未启用'}
- **阈值**: ${this.reportData.slowQueries.threshold || 'N/A'} 秒
- **说明**: ${this.reportData.slowQueries.message || 'N/A'}

## 💡 性能建议

${this.reportData.recommendations.length > 0 ? 
  this.reportData.recommendations.map((rec, index) => 
    `${index + 1}. **${rec.priority.toUpperCase()}** - ${rec.message}`
  ).join('\n') : 
  '✅ 暂无性能问题'
}

## 📈 总体评估

- **总表数**: ${this.reportData.tableStats.length}
- **总索引数**: ${this.reportData.indexStats.length}
- **建议数量**: ${this.reportData.recommendations.length}
- **高优先级建议**: ${this.reportData.recommendations.filter(r => r.priority === 'high').length}

---
*报告由智慧养鹅系统性能监控工具自动生成*
`;

    // 保存报告到文件
    const reportPath = path.join(__dirname, `performance-report-${Date.now()}.md`);
    await fs.writeFile(reportPath, reportContent, 'utf8');
    
    return reportPath;
  }

  /**
   * 执行性能监控
   */
  async monitor() {
    try {
      await this.connect();
      await this.getTableStats();
      await this.getIndexStats();
      await this.checkSlowQueries();
      this.generateRecommendations();
      const reportPath = await this.generateReport();
      
      
      return this.reportData;
    } catch (error) {
      console.error('❌ 性能监控失败:', error);
      throw error;
    } finally {
      await this.disconnect();
    }
  }

  // 辅助方法
  formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  parseBytes(sizeStr) {
    const match = sizeStr.match(/^([\d.]+)\s*([KMGT]?B)$/);
    if (!match) return 0;
    
    const value = parseFloat(match[1]);
    const unit = match[2];
    const multipliers = { 'B': 1, 'KB': 1024, 'MB': 1024*1024, 'GB': 1024*1024*1024 };
    
    return value * (multipliers[unit] || 1);
  }
}

// 主函数
async function main() {
  );
  
  const monitor = new PerformanceMonitor();
  
  try {
    await monitor.monitor();
    process.exit(0);
  } catch (error) {
    console.error('\n❌ 监控失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = PerformanceMonitor;
