/**
 * 运行数据库约束迁移脚本
 * 执行 005-add-database-constraints.sql 中的所有约束
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'smart_goose',
  multipleStatements: true,
  charset: 'utf8mb4'
};

/**
 * 执行约束迁移
 */
async function runConstraintsMigration() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);

    // 读取迁移文件
    const migrationPath = path.join(__dirname, '../migrations/005-add-database-constraints.sql');
    
    const migrationSQL = await fs.readFile(migrationPath, 'utf8');

    // 开始事务
    await connection.beginTransaction();

    try {
      // 执行迁移SQL
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      let successCount = 0;
      let skipCount = 0;
      const errors = [];

      for (let i = 0; i < statements.length; i++) {
        const statement = statements[i];
        
        // 跳过注释和空语句
        if (statement.startsWith('--') || statement.length < 5) {
          skipCount++;
          continue;
        }

        try {
          await connection.execute(statement);
          successCount++;
        } catch (error) {
          // 对于某些可能失败的约束（如表不存在），记录但不中断
          if (error.code === 'ER_NO_SUCH_TABLE' || 
              error.code === 'ER_DUP_KEYNAME' || 
              error.code === 'ER_CHECK_CONSTRAINT_DUP_NAME') {
            skipCount++;
          } else {
            errors.push({
              statement: statement.substring(0, 100) + '...',
              error: error.message
            });
            console.error(`❌ 错误: ${error.message}`);
          }
        }
      }

      // 提交事务
      await connection.commit();

      // 输出结果摘要

      if (errors.length > 0) {
        errors.forEach((err, index) => {
        });
      }

      // 执行数据完整性检查
      try {
        const [validationResults] = await connection.execute('CALL ValidateDataIntegrity()');
        if (Array.isArray(validationResults) && validationResults.length > 0) {
        } else {
        }
      } catch (validationError) {
      }

    } catch (error) {
      // 回滚事务
      await connection.rollback();
      console.error('❌ 迁移失败，事务已回滚:', error.message);
      throw error;
    }

  } catch (error) {
    console.error('❌ 约束迁移失败:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

/**
 * 检查现有约束
 */
async function checkExistingConstraints() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    
    
    // 检查CHECK约束
    const [checkConstraints] = await connection.execute(`
      SELECT 
        TABLE_NAME,
        CONSTRAINT_NAME,
        CHECK_CLAUSE
      FROM information_schema.CHECK_CONSTRAINTS 
      WHERE CONSTRAINT_SCHEMA = ?
      ORDER BY TABLE_NAME, CONSTRAINT_NAME
    `, [dbConfig.database]);

    if (checkConstraints.length > 0) {
    } else {
    }

    // 检查外键约束
    const [foreignKeys] = await connection.execute(`
      SELECT 
        TABLE_NAME,
        CONSTRAINT_NAME,
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM information_schema.KEY_COLUMN_USAGE 
      WHERE CONSTRAINT_SCHEMA = ? 
        AND REFERENCED_TABLE_NAME IS NOT NULL
      ORDER BY TABLE_NAME, CONSTRAINT_NAME
    `, [dbConfig.database]);

    if (foreignKeys.length > 0) {
    }

    // 检查唯一约束
    const [uniqueConstraints] = await connection.execute(`
      SELECT 
        TABLE_NAME,
        CONSTRAINT_NAME,
        CONSTRAINT_TYPE
      FROM information_schema.TABLE_CONSTRAINTS 
      WHERE CONSTRAINT_SCHEMA = ? 
        AND CONSTRAINT_TYPE IN ('UNIQUE', 'PRIMARY KEY')
      ORDER BY TABLE_NAME, CONSTRAINT_NAME
    `, [dbConfig.database]);

    if (uniqueConstraints.length > 0) {
    }

  } catch (error) {
    console.error('❌ 检查约束失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

/**
 * 主函数
 */
async function main() {
  
  const args = process.argv.slice(2);
  
  if (args.includes('--check-only')) {
    await checkExistingConstraints();
    return;
  }

  // 检查现有约束
  await checkExistingConstraints();
  
  // 执行迁移
  await runConstraintsMigration();
  
}

// 处理未捕获的错误
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 主函数执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  runConstraintsMigration,
  checkExistingConstraints
};