require('dotenv').config();
const { Sequelize } = require('sequelize');
const User = require('../models/user.model');
const HealthRecord = require('../models/health-record.model');
const ProductionRecord = require('../models/production-record.model');

// 创建Sequelize实例
const sequelize = new Sequelize(
  process.env.DB_NAME || 'zhihuiyange_local',
  process.env.DB_USERNAME || 'zhihuiyange',
  process.env.DB_PASSWORD || 'zhihuiyange123',
  {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    dialect: 'mysql',
    logging: console.log
  }
);

// 重新定义模型以使用新连接
User.init(User.rawAttributes, {
  sequelize: sequelize,
  modelName: 'User',
  tableName: 'users',
  timestamps: true,
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
});

HealthRecord.init(HealthRecord.rawAttributes, {
  sequelize: sequelize,
  modelName: 'HealthRecord',
  tableName: 'health_records',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

ProductionRecord.init(ProductionRecord.rawAttributes, {
  sequelize: sequelize,
  modelName: 'ProductionRecord',
  tableName: 'production_records',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// 同步数据库表结构
async function syncDatabase() {
  try {
    
    // 验证数据库连接
    await sequelize.authenticate();
    
    // 同步所有模型
    await sequelize.sync({ alter: true });
    
    // 关闭连接
    await sequelize.close();
  } catch (error) {
    console.error('同步数据库表结构失败:', error);
    process.exit(1);
  }
}

syncDatabase();