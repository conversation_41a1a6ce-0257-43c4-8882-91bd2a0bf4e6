#!/usr/bin/env node

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USERNAME || 'zhihuiyange',
  password: process.env.DB_PASSWORD || 'zhihuiyange123',
  database: process.env.DB_NAME || 'zhihuiyange_local',
  charset: 'utf8mb4'
};

async function setupAIInventoryTable() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    
    const sqlFile = path.join(__dirname, 'create-ai-inventory-table.sql');
    const sqlContent = fs.readFileSync(sqlFile, 'utf8');
    
    // 分割SQL语句
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);
    
    for (const statement of statements) {
      if (statement.trim()) {
        await connection.execute(statement);
        + '...');
      }
    }
    
    
    // 验证表是否创建成功
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'ai_inventory_records'"
    );
    
    if (tables.length > 0) {
      
      // 查看表结构
      const [columns] = await connection.execute(
        "DESCRIBE ai_inventory_records"
      );
      
      columns.forEach(col => {
        ` : ''}`);
      });
      
      // 查看示例数据
      const [records] = await connection.execute(
        "SELECT COUNT(*) as count FROM ai_inventory_records"
      );
      
      
    } else {
    }
    
  } catch (error) {
    console.error('❌ 设置失败:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 执行设置
if (require.main === module) {
  setupAIInventoryTable();
}

module.exports = setupAIInventoryTable;
