const request = require('supertest');
const app = require('../app');
const sequelize = require('../config/database');

describe('API集成测试', () => {
  let authToken;
  let testUserId;

  beforeAll(async () => {
    // 确保数据库连接
    await sequelize.authenticate();
    
    // 创建测试用户并获取token
    const registerResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        username: 'testuser_' + Date.now(),
        password: 'testpass123',
        email: '<EMAIL>',
        phone: '13800138000'
      });

    if (registerResponse.status === 201) {
      authToken = registerResponse.body.data.token;
      testUserId = registerResponse.body.data.user.id;
    } else {
      // 如果注册失败，尝试登录
      const loginResponse = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: 'demo',
          password: 'demo123'
        });
      
      authToken = loginResponse.body.data.token;
      testUserId = loginResponse.body.data.user.id;
    }
  });

  describe('认证API测试', () => {
    it('应该能够注册新用户', async () => {
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send({
          username: 'newuser_' + Date.now(),
          password: 'newpass123',
          email: '<EMAIL>',
          phone: '13900139000'
        });

      expect([201, 400]).toContain(response.status);
      if (response.status === 201) {
        expect(response.body.success).toBe(true);
        expect(response.body.data.token).toBeDefined();
      }
    });

    it('应该能够登录用户', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: 'demo',
          password: 'demo123'
        });

      expect([200, 401]).toContain(response.status);
      if (response.status === 200) {
        expect(response.body.success).toBe(true);
        expect(response.body.data.token).toBeDefined();
      }
    });
  });

  describe('生产记录API测试', () => {
    let recordId;

    it('应该能够创建生产记录', async () => {
      if (!authToken) {
        return;
      }

      const response = await request(app)
        .post('/api/v1/production-records')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          产蛋数量: 100,
          饲料消耗: 50,
          温度: 25,
          湿度: 60,
          备注: 'API测试记录',
          记录日期: '2023-12-01'
        });

      expect([201, 401]).toContain(response.status);
      if (response.status === 201) {
        expect(response.body.success).toBe(true);
        recordId = response.body.data.id;
      }
    });

    it('应该能够获取生产记录列表', async () => {
      if (!authToken) {
        return;
      }

      const response = await request(app)
        .get('/api/v1/production-records')
        .set('Authorization', `Bearer ${authToken}`);

      expect([200, 401]).toContain(response.status);
      if (response.status === 200) {
        expect(response.body.success).toBe(true);
        expect(Array.isArray(response.body.data.records)).toBe(true);
      }
    });

    it('应该能够获取单个生产记录', async () => {
      if (!authToken || !recordId) {
        return;
      }

      const response = await request(app)
        .get(`/api/v1/production-records/${recordId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect([200, 404, 401]).toContain(response.status);
      if (response.status === 200) {
        expect(response.body.success).toBe(true);
        expect(response.body.data.id).toBe(recordId);
      }
    });

    it('应该能够更新生产记录', async () => {
      if (!authToken || !recordId) {
        return;
      }

      const response = await request(app)
        .put(`/api/v1/production-records/${recordId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          产蛋数量: 120,
          饲料消耗: 55,
          温度: 26,
          湿度: 65,
          备注: '更新的API测试记录',
          记录日期: '2023-12-02'
        });

      expect([200, 404, 401]).toContain(response.status);
      if (response.status === 200) {
        expect(response.body.success).toBe(true);
      }
    });
  });

  describe('健康记录API测试', () => {
    it('应该能够获取健康记录列表', async () => {
      if (!authToken) {
        return;
      }

      const response = await request(app)
        .get('/api/v1/health-records')
        .set('Authorization', `Bearer ${authToken}`);

      expect([200, 401]).toContain(response.status);
      if (response.status === 200) {
        expect(response.body.success).toBe(true);
      }
    });
  });

  describe('库存管理API测试', () => {
    it('应该能够获取库存概览', async () => {
      if (!authToken) {
        return;
      }

      const response = await request(app)
        .get('/api/v1/inventory/overview')
        .set('Authorization', `Bearer ${authToken}`);

      expect([200, 401]).toContain(response.status);
      if (response.status === 200) {
        expect(response.body.success).toBe(true);
      }
    });
  });

  describe('错误处理测试', () => {
    it('应该拒绝无效的认证token', async () => {
      const response = await request(app)
        .get('/api/v1/production-records')
        .set('Authorization', 'Bearer invalid_token');

      expect(response.status).toBe(401);
    });

    it('应该处理不存在的路由', async () => {
      const response = await request(app)
        .get('/api/v1/nonexistent-endpoint');

      expect(response.status).toBe(404);
    });
  });
});
