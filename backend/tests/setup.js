/**
 * 测试环境配置
 * Jest测试框架的全局配置和设置
 */

const path = require('path');

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.API_BASE_URL = 'http://localhost:3001/api/v1';
process.env.ADMIN_PORT = '3003';

// 模拟console.log以减少测试输出噪音
const originalConsole = { ...console };
global.console = {
  ...console,
  log: jest.fn(() => {}),
  info: jest.fn(() => {}),
  warn: jest.fn(() => {}),
  error: jest.fn((...args) => {
    // 只在真正的错误时输出
    if (process.env.DEBUG_TESTS) {
      originalConsole.error(...args);
    }
  })
};

// 全局测试工具函数
global.testUtils = {
  // 创建模拟请求对象
  createMockRequest: (overrides = {}) => ({
    params: {},
    query: {},
    body: {},
    headers: {},
    session: { user: { id: 1, username: 'testuser' }, token: 'test-token' },
    ip: '127.0.0.1',
    get: jest.fn(),
    ...overrides
  }),

  // 创建模拟响应对象
  createMockResponse: (overrides = {}) => {
    const res = {
      status: jest.fn(() => res),
      json: jest.fn(() => res),
      send: jest.fn(() => res),
      render: jest.fn(() => res),
      redirect: jest.fn(() => res),
      set: jest.fn(() => res),
      locals: {},
      ...overrides
    };
    return res;
  },

  // 创建模拟next函数
  createMockNext: () => jest.fn(),

  // 等待异步操作完成
  waitFor: (ms = 0) => new Promise(resolve => setTimeout(resolve, ms)),

  // 生成测试数据
  generateTestData: {
    user: (overrides = {}) => ({
      id: 1,
      username: 'testuser',
      email: '<EMAIL>',
      name: 'Test User',
      role: 'user',
      status: 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...overrides
    }),

    flock: (overrides = {}) => ({
      id: 1,
      name: 'Test Flock',
      breed: 'white_goose',
      totalCount: 100,
      currentCount: 95,
      maleCount: 30,
      femaleCount: 65,
      ageGroup: 'adult',
      status: 'active',
      establishedDate: new Date().toISOString(),
      location: 'Test Location',
      description: 'Test flock for testing',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...overrides
    }),

    healthRecord: (overrides = {}) => ({
      id: 1,
      flockId: 1,
      checkType: 'routine',
      checkDate: new Date().toISOString(),
      symptoms: 'Normal behavior',
      temperature: 40.5,
      weight: 3.2,
      result: 'healthy',
      diagnosis: 'All birds appear healthy',
      treatment: 'No treatment required',
      notes: 'Regular check completed',
      checkerId: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...overrides
    }),

    productionRecord: (overrides = {}) => ({
      id: 1,
      flockId: 1,
      recordDate: new Date().toISOString().split('T')[0],
      eggCount: 45,
      feedConsumption: 12.5,
      waterConsumption: 25.0,
      mortality: 0,
      notes: 'Good production day',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...overrides
    }),

    apiResponse: (data = null, success = true, message = 'Success') => ({
      success,
      data,
      message,
      timestamp: new Date().toISOString()
    }),

    paginatedResponse: (items = [], total = null, page = 1, limit = 10) => ({
      success: true,
      data: {
        items,
        pagination: {
          page,
          limit,
          total: total !== null ? total : items.length,
          totalPages: Math.ceil((total !== null ? total : items.length) / limit),
          hasNext: page < Math.ceil((total !== null ? total : items.length) / limit),
          hasPrev: page > 1
        }
      },
      message: 'Success',
      timestamp: new Date().toISOString()
    })
  },

  // API Mock助手
  mockApiService: {
    success: (data = null) => Promise.resolve({
      success: true,
      data,
      message: 'Success'
    }),

    error: (message = 'Error', status = 400) => Promise.reject({
      success: false,
      status,
      message,
      data: null
    }),

    networkError: () => Promise.reject({
      type: 'NETWORK_ERROR',
      message: 'Network connection failed',
      originalError: new Error('Network error')
    })
  },

  // 断言助手
  expectSuccessResponse: (mockRes, expectedData = null) => {
    expect(mockRes.json).toHaveBeenCalledWith(
      expect.objectContaining({
        success: true,
        data: expectedData !== null ? expectedData : expect.anything(),
        timestamp: expect.any(String)
      })
    );
  },

  expectErrorResponse: (mockRes, expectedStatus = 400, expectedMessage = null) => {
    expect(mockRes.status).toHaveBeenCalledWith(expectedStatus);
    expect(mockRes.json).toHaveBeenCalledWith(
      expect.objectContaining({
        success: false,
        message: expectedMessage || expect.any(String),
        timestamp: expect.any(String)
      })
    );
  },

  expectValidationError: (mockRes, expectedErrors = null) => {
    expect(mockRes.status).toHaveBeenCalledWith(400);
    expect(mockRes.json).toHaveBeenCalledWith(
      expect.objectContaining({
        success: false,
        message: '数据验证失败',
        data: expectedErrors || expect.any(Object)
      })
    );
  }
};

// 全局beforeAll和afterAll钩子
beforeAll(async () => {
  // 全局测试前置处理
});

afterAll(async () => {
  // 全局测试后置清理
});

// 全局beforeEach和afterEach钩子
beforeEach(() => {
  // 每个测试前清理所有mock
  jest.clearAllMocks();
});

afterEach(() => {
  // 每个测试后的清理工作
  // 恢复所有mock状态
  jest.restoreAllMocks();
});

// 处理未捕获的Promise rejection
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

// 扩展Jest匹配器
expect.extend({
  // 检查是否为有效的日期字符串
  toBeValidDateString(received) {
    const date = new Date(received);
    const pass = !isNaN(date.getTime()) && typeof received === 'string';
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid date string`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid date string`,
        pass: false
      };
    }
  },

  // 检查是否为有效的API响应格式
  toBeValidApiResponse(received) {
    const hasSuccess = typeof received.success === 'boolean';
    const hasTimestamp = typeof received.timestamp === 'string';
    const hasMessage = typeof received.message === 'string';
    
    const pass = hasSuccess && hasTimestamp && hasMessage;
    
    if (pass) {
      return {
        message: () => `expected ${JSON.stringify(received)} not to be a valid API response`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${JSON.stringify(received)} to be a valid API response with success, timestamp, and message fields`,
        pass: false
      };
    }
  },

  // 检查分页响应格式
  toBeValidPaginatedResponse(received) {
    const isValidApiResponse = this.utils.matcherHint('toBeValidApiResponse');
    
    if (!received.success || !received.data) {
      return {
        message: () => `expected response to be successful with data`,
        pass: false
      };
    }

    const { data } = received;
    const hasItems = Array.isArray(data.items);
    const hasPagination = data.pagination && 
      typeof data.pagination.page === 'number' &&
      typeof data.pagination.limit === 'number' &&
      typeof data.pagination.total === 'number';

    const pass = hasItems && hasPagination;

    if (pass) {
      return {
        message: () => `expected response not to be a valid paginated response`,
        pass: true
      };
    } else {
      return {
        message: () => `expected response to be a valid paginated response with items array and pagination object`,
        pass: false
      };
    }
  }
});

module.exports = {};