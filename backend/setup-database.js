#!/usr/bin/env node
// 数据库初始化脚本

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

class DatabaseSetup {
  constructor() {
    this.rootConfig = {
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '', // 如果root有密码，请在这里设置
      charset: 'utf8mb4'
    };
    
    this.dbConfig = {
      host: 'localhost',
      port: 3306,
      user: 'zhihuiyange',
      password: 'zhihuiyange123',
      database: 'zhihuiyange_local',
      charset: 'utf8mb4'
    };
  }

  async createDatabase() {
    
    let connection;
    try {
      // 尝试连接MySQL
      connection = await mysql.createConnection(this.rootConfig);

      // 创建数据库
      await connection.execute(`CREATE DATABASE IF NOT EXISTS zhihuiyange_local CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);

      // 创建用户（如果不存在）
      try {
        await connection.execute(`CREATE USER IF NOT EXISTS 'zhihuiyange'@'localhost' IDENTIFIED BY 'zhihuiyange123'`);
      } catch (error) {
        if (error.code !== 'ER_CANNOT_USER') {
        }
      }

      // 授权
      await connection.execute(`GRANT ALL PRIVILEGES ON zhihuiyange_local.* TO 'zhihuiyange'@'localhost'`);
      await connection.execute(`FLUSH PRIVILEGES`);

    } catch (error) {
      console.error('❌ 数据库创建失败:', error.message);
      
      if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      }
      
      throw error;
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }

  async testConnection() {
    
    let connection;
    try {
      connection = await mysql.createConnection(this.dbConfig);
      
      // 测试查询
      const [rows] = await connection.execute('SELECT 1 as test');
      
      return true;
    } catch (error) {
      console.error('❌ 数据库连接测试失败:', error.message);
      return false;
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }

  async createTables() {
    
    let connection;
    try {
      connection = await mysql.createConnection(this.dbConfig);
      
      // 创建用户表
      const createUsersTable = `
        CREATE TABLE IF NOT EXISTS users (
          id INT AUTO_INCREMENT PRIMARY KEY,
          username VARCHAR(50) UNIQUE NOT NULL,
          password VARCHAR(255) NOT NULL,
          email VARCHAR(100),
          phone VARCHAR(20),
          role ENUM('admin', 'manager', 'user') DEFAULT 'user',
          farm_name VARCHAR(100),
          createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;
      
      await connection.execute(createUsersTable);

      // 创建健康记录表
      const createHealthRecordsTable = `
        CREATE TABLE IF NOT EXISTS health_records (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          goose_id VARCHAR(50),
          symptoms TEXT,
          diagnosis TEXT,
          treatment TEXT,
          status ENUM('healthy', 'sick', 'recovering', 'dead') DEFAULT 'healthy',
          record_date DATE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;
      
      await connection.execute(createHealthRecordsTable);

      // 创建生产记录表
      const createProductionRecordsTable = `
        CREATE TABLE IF NOT EXISTS production_records (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          egg_count INT DEFAULT 0,
          feed_consumption DECIMAL(10,2) DEFAULT 0,
          temperature DECIMAL(5,2),
          humidity DECIMAL(5,2),
          notes TEXT,
          recorded_date DATE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;
      
      await connection.execute(createProductionRecordsTable);

      // 创建库存记录表
      const createInventoryRecordsTable = `
        CREATE TABLE IF NOT EXISTS inventory_records (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          type ENUM('goose', 'egg', 'feed') NOT NULL,
          quantity INT NOT NULL,
          unit VARCHAR(20) DEFAULT '只',
          record_date DATE,
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;
      
      await connection.execute(createInventoryRecordsTable);

    } catch (error) {
      console.error('❌ 创建数据表失败:', error.message);
      throw error;
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }

  async insertTestData() {
    
    let connection;
    try {
      connection = await mysql.createConnection(this.dbConfig);
      
      // 插入测试用户（适配现有表结构）
      const insertUsers = `
        INSERT IGNORE INTO users (username, password, email, role) VALUES
        ('admin', '$2a$10$rOzJqQjQjQjQjQjQjQjQjOzJqQjQjQjQjQjQjQjQjQjQjQjQjQjQjQ', '<EMAIL>', 'admin'),
        ('manager', '$2a$10$rOzJqQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQ', '<EMAIL>', 'manager'),
        ('demo', '$2a$10$rOzJqQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQjQ', '<EMAIL>', 'user');
      `;
      
      await connection.execute(insertUsers);

      // 插入测试健康记录
      const insertHealthRecords = `
        INSERT IGNORE INTO health_records (user_id, goose_id, symptoms, diagnosis, treatment, status, record_date) VALUES
        (3, 'G001', '精神不振，食欲下降', '感冒', '保温，给予维生素C', 'recovering', '2023-12-01'),
        (3, 'G002', '正常', '健康检查', '无需治疗', 'healthy', '2023-12-01'),
        (3, 'G003', '腹泻', '肠胃炎', '给予益生菌，调整饲料', 'recovering', '2023-12-02');
      `;
      
      await connection.execute(insertHealthRecords);

      // 插入测试生产记录
      const insertProductionRecords = `
        INSERT IGNORE INTO production_records (user_id, egg_count, feed_consumption, temperature, humidity, notes, recorded_date) VALUES
        (3, 120, 45.5, 25.5, 65.0, '天气晴朗，鹅群状态良好', '2023-12-01'),
        (3, 115, 46.0, 24.8, 68.0, '有轻微降温，增加了保温措施', '2023-12-02'),
        (3, 125, 44.8, 26.2, 62.0, '产蛋量有所提升', '2023-12-03');
      `;
      
      await connection.execute(insertProductionRecords);

      // 插入测试库存记录
      const insertInventoryRecords = `
        INSERT IGNORE INTO inventory_records (user_id, type, quantity, unit, record_date, notes) VALUES
        (3, 'goose', 500, '只', '2023-12-01', '成年鹅存栏'),
        (3, 'egg', 1200, '个', '2023-12-01', '鹅蛋库存'),
        (3, 'feed', 2000, 'kg', '2023-12-01', '饲料库存');
      `;
      
      await connection.execute(insertInventoryRecords);

    } catch (error) {
      console.error('❌ 插入测试数据失败:', error.message);
      throw error;
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }

  async setup() {
    
    try {
      // 1. 创建数据库和用户
      await this.createDatabase();
      
      // 2. 测试连接
      const connected = await this.testConnection();
      if (!connected) {
        throw new Error('数据库连接失败');
      }
      
      // 3. 创建表
      await this.createTables();
      
      // 4. 插入测试数据
      await this.insertTestData();
      
      
    } catch (error) {
      console.error('\n❌ 数据库初始化失败:', error.message);
      process.exit(1);
    }
  }
}

// 运行初始化
if (require.main === module) {
  const setup = new DatabaseSetup();
  setup.setup().catch(console.error);
}

module.exports = DatabaseSetup;
