/**
 * 智慧养鹅SAAS平台 - 最小化服务器版本
 * 专用于测试增强版系统功能
 */

const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');

// 加载环境变量
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;
const HOST = process.env.HOST || '0.0.0.0';

// 基础中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 根路径
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: '智慧养鹅SAAS平台 - 最小化版本',
    version: '1.0.0-minimal',
    timestamp: new Date().toISOString()
  });
});

// 健康检查
app.get('/api/v1/health/check', (req, res) => {
  res.json({
    success: true,
    message: '健康检查通过',
    server: 'minimal',
    timestamp: new Date().toISOString()
  });
});

// 基础认证路由（占位符）
app.post('/api/v1/auth/login', (req, res) => {
  res.json({
    success: true,
    data: {
      token: 'demo-token-' + Date.now(),
      user: { id: 1, name: '测试用户' }
    },
    message: '登录成功（演示模式）'
  });
});

// 用户信息路由（占位符）
app.get('/api/v1/auth/userinfo', (req, res) => {
  res.json({
    success: true,
    data: {
      id: 1,
      name: '测试用户',
      farmName: '智慧养鹅演示农场',
      inventoryCount: 1250,
      healthRate: 95,
      environmentStatus: '优'
    },
    message: '用户信息获取成功'
  });
});

// 首页数据路由（占位符）
app.get('/api/v1/home/<USER>', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: 1,
        title: '增强版系统测试公告',
        content: '新的日志和错误处理系统已就绪！',
        createdAt: new Date().toISOString(),
        isImportant: true
      }
    ],
    message: '公告获取成功'
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
    path: req.originalUrl
  });
});

// 错误处理
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? error.message : 'Internal Server Error'
  });
});

// 启动服务器
const server = app.listen(PORT, HOST, () => {
  console.log(`🚀 智慧养鹅SAAS平台（最小化版本）已启动`);
  console.log(`📍 访问地址: http://${HOST}:${PORT}`);
  console.log(`⏰ 启动时间: ${new Date().toLocaleString()}`);
  console.log(`🎯 用途: 测试增强版网络请求系统`);
});

// 设置服务器超时
server.timeout = 30000;

module.exports = app;