/**
 * 统一日志管理工具
 * Smart Goose SAAS Platform - Unified Logging System
 * 
 * 提供统一的日志记录、格式化和输出管理
 * 支持不同日志级别、结构化日志和环境适配
 */

const winston = require('winston');
const path = require('path');

// 创建日志目录
const logDir = path.join(__dirname, '..', 'logs');

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

// 开发环境控制台格式
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`;
    if (Object.keys(meta).length > 0) {
      log += '\n' + JSON.stringify(meta, null, 2);
    }
    return log;
  })
);

// 创建 Winston Logger 实例
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: { service: 'smart-goose-saas' },
  transports: [
    // 错误日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // 综合日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  ],
});

// 开发环境添加控制台输出
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat
  }));
}

/**
 * 封装日志方法
 */
class Logger {
  static info(message, meta = {}) {
    logger.info(message, meta);
  }

  static error(message, meta = {}) {
    logger.error(message, meta);
  }

  static warn(message, meta = {}) {
    logger.warn(message, meta);
  }

  static debug(message, meta = {}) {
    logger.debug(message, meta);
  }

  static verbose(message, meta = {}) {
    logger.verbose(message, meta);
  }

  /**
   * 记录 API 请求日志
   */
  static apiRequest(req, meta = {}) {
    const logData = {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
      tenantId: req.tenant?.id,
      ...meta
    };
    logger.info('API Request', logData);
  }

  /**
   * 记录 API 响应日志
   */
  static apiResponse(req, res, responseTime, meta = {}) {
    const logData = {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      responseTime: responseTime + 'ms',
      userId: req.user?.id,
      tenantId: req.tenant?.id,
      ...meta
    };
    logger.info('API Response', logData);
  }

  /**
   * 记录数据库操作日志
   */
  static database(operation, meta = {}) {
    logger.info(`Database: ${operation}`, meta);
  }

  /**
   * 记录安全相关日志
   */
  static security(event, meta = {}) {
    logger.warn(`Security: ${event}`, meta);
  }

  /**
   * 记录业务操作日志
   */
  static business(action, meta = {}) {
    logger.info(`Business: ${action}`, meta);
  }
}

module.exports = { logger, Logger };