// backend/routes/oa.routes.js

/**
 * OA系统路由
 * 处理所有OA相关的API路由
 */

const express = require('express');
const router = express.Router();
const oaController = require('../controllers/oa.controller');
const { verifyToken } = require('../middleware/auth.middleware');
const { validatePermission } = require('../middleware/permission.middleware');

// 应用认证中间件
router.use(verifyToken);

// OA系统基础接口
router.get('/stats', 
  validatePermission('oa_access'), 
  oaController.getOAStats
);

router.get('/activities/recent', 
  validatePermission('oa_access'), 
  oaController.getRecentActivities
);

// 财务相关接口
router.get('/finance/overview', 
  validatePermission('finance_view'), 
  oaController.getFinanceOverview
);

router.get('/finance/trend', 
  validatePermission('finance_view'), 
  oaController.getFinanceTrend
);

router.get('/finance/transactions/recent', 
  validatePermission('finance_view'), 
  oaController.getRecentTransactions
);

// 财务报表接口
router.get('/finance/reports', 
  validatePermission('finance_view'), 
  oaController.getFinanceReports
);

router.post('/finance/reports/export', 
  validatePermission('finance_export'), 
  oaController.exportFinanceReport
);

// 采购管理接口
router.post('/purchase/requests', 
  validatePermission('purchase_create'), 
  oaController.createPurchaseRequest
);

router.get('/purchase/requests', 
  validatePermission('purchase_view'), 
  oaController.getPurchaseRequestList
);

router.get('/purchase/requests/:id', 
  validatePermission('purchase_view'), 
  oaController.getPurchaseRequestDetail
);

router.put('/purchase/requests/:id', 
  validatePermission('purchase_edit'), 
  oaController.updatePurchaseRequest
);

router.post('/purchase/requests/:id/cancel', 
  validatePermission('purchase_cancel'), 
  oaController.cancelPurchaseRequest
);

// 报销管理接口
router.post('/reimbursements',
  validatePermission('reimbursement:create'),
  oaController.createReimbursement
);

router.get('/reimbursements',
  validatePermission('reimbursement:read'),
  oaController.getReimbursements
);

router.get('/reimbursements/:id',
  validatePermission('reimbursement:read'),
  oaController.getReimbursementDetail
);

router.post('/reimbursements/:id/submit',
  validatePermission('reimbursement:create'),
  oaController.submitReimbursement
);

router.post('/reimbursements/:id/approve',
  validatePermission('approval:process'),
  oaController.approveReimbursement
);

router.get('/reimbursements/statistics',
  validatePermission('reimbursement:read'),
  oaController.getReimbursementStatistics
);

// 审批流程接口
router.get('/approvals/pending',
  validatePermission('approval:read'),
  oaController.getPendingApprovals
);

router.get('/approvals/history',
  validatePermission('approval:read'),
  oaController.getApprovalHistory
);

router.get('/approvals/history/statistics',
  validatePermission('approval:read'),
  oaController.getApprovalHistoryStatistics
);

router.get('/approvals/statistics',
  validatePermission('approval:read'),
  oaController.getApprovalStatistics
);

router.post('/approvals/:id/process',
  validatePermission('approval:process'),
  oaController.processApproval
);

// 流程模板管理接口
router.get('/workflows/templates',
  validatePermission('workflow:manage'),
  oaController.getWorkflowTemplates
);

router.get('/workflows/templates/statistics',
  validatePermission('workflow:manage'),
  oaController.getWorkflowTemplateStatistics
);

router.post('/workflows/templates',
  validatePermission('workflow:manage'),
  oaController.createWorkflowTemplate
);

router.put('/workflows/templates/:id',
  validatePermission('workflow:manage'),
  oaController.updateWorkflowTemplate
);

router.delete('/workflows/templates/:id',
  validatePermission('workflow:manage'),
  oaController.deleteWorkflowTemplate
);

router.post('/workflows/templates/:id/copy',
  validatePermission('workflow:manage'),
  oaController.copyWorkflowTemplate
);

router.put('/workflows/templates/:id/status',
  validatePermission('workflow:manage'),
  oaController.toggleWorkflowTemplateStatus
);

// 权限管理接口
router.get('/permissions',
  validatePermission('system:permission:manage'),
  oaController.getPermissions
);

router.get('/permissions/roles',
  validatePermission('system:role:manage'),
  oaController.getRoles
);

router.get('/permissions/roles/statistics',
  validatePermission('system:role:manage'),
  oaController.getRoleStatistics
);

router.post('/permissions/roles',
  validatePermission('system:role:manage'),
  oaController.createRole
);

router.put('/permissions/roles/:id',
  validatePermission('system:role:manage'),
  oaController.updateRole
);

router.delete('/permissions/roles/:id',
  validatePermission('system:role:manage'),
  oaController.deleteRole
);

router.put('/permissions/roles/:id/status',
  validatePermission('system:role:manage'),
  oaController.toggleRoleStatus
);

router.get('/permissions/roles/:id/permissions',
  validatePermission('system:role:manage'),
  oaController.getRolePermissions
);

router.put('/permissions/roles/:id/permissions',
  validatePermission('system:role:manage'),
  oaController.updateRolePermissions
);

router.get('/permissions/users/:userId/permissions',
  validatePermission('system:user:manage'),
  oaController.getUserPermissions
);

// 用户权限管理接口
router.get('/permissions/users',
  validatePermission('system:user:manage'),
  oaController.getUsersForPermission
);

router.get('/permissions/users/statistics',
  validatePermission('system:user:manage'),
  oaController.getUserPermissionStatistics
);

router.get('/permissions/users/:userId/roles',
  validatePermission('system:user:manage'),
  oaController.getUserRoles
);

router.put('/permissions/users/:userId/roles',
  validatePermission('system:user:manage'),
  oaController.updateUserRoles
);

// 部门管理接口
router.get('/departments',
  validatePermission('system:department:manage'),
  oaController.getDepartments
);

// 工作流模板管理接口
router.get('/workflows/templates',
  validatePermission('approval_workflow_manage'),
  oaController.getWorkflowTemplates
);

router.post('/workflows/templates',
  validatePermission('approval_workflow_manage'),
  oaController.createWorkflowTemplate
);

router.put('/workflows/templates/:id',
  validatePermission('approval_workflow_manage'),
  oaController.updateWorkflowTemplate
);

// 文件上传接口
router.post('/upload',
  validatePermission('oa_access'),
  oaController.uploadFile
);

module.exports = router;