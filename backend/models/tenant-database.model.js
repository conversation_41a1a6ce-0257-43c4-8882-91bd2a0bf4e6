/**
 * 租户数据库连接管理模型
 * Tenant Database Connection Management Model
 */

const { Sequelize } = require('sequelize');
const path = require('path');

class TenantDatabaseManager {
  constructor() {
    this.connections = new Map(); // 存储租户数据库连接
    this.saasConnection = null; // SAAS平台数据库连接
  }

  /**
   * 初始化SAAS平台数据库连接
   */
  async initSaasConnection() {
    try {
      this.saasConnection = new Sequelize({
        dialect: 'mysql',
        host: process.env.SAAS_DB_HOST || 'localhost',
        port: process.env.SAAS_DB_PORT || 3306,
        database: process.env.SAAS_DB_NAME || 'smart_goose_saas',
        username: process.env.SAAS_DB_USER || 'root',
        password: process.env.SAAS_DB_PASSWORD || '',
        logging: process.env.NODE_ENV === 'development' ? console.log : false,
        pool: {
          max: 10,
          min: 0,
          acquire: 30000,
          idle: 10000
        },
        timezone: '+08:00'
      });

      await this.saasConnection.authenticate();
      return this.saasConnection;
    } catch (error) {
      console.error('❌ SAAS平台数据库连接失败:', error);
      throw error;
    }
  }

  /**
   * 为指定租户创建数据库连接
   * @param {Object} tenantInfo 租户信息
   * @returns {Sequelize} 数据库连接实例
   */
  async createTenantConnection(tenantInfo) {
    const tenantCode = tenantInfo.tenantCode;
    
    if (this.connections.has(tenantCode)) {
      return this.connections.get(tenantCode);
    }

    try {
      // 根据租户配置创建数据库名称
      const dbName = `smart_goose_${tenantCode.toLowerCase()}`;
      
      const connection = new Sequelize({
        dialect: 'mysql',
        host: tenantInfo.dbHost || process.env.DB_HOST || 'localhost',
        port: tenantInfo.dbPort || process.env.DB_PORT || 3306,
        database: dbName,
        username: tenantInfo.dbUser || process.env.DB_USER || 'root',
        password: tenantInfo.dbPassword || process.env.DB_PASSWORD || '',
        logging: process.env.NODE_ENV === 'development' ? console.log : false,
        pool: {
          max: 5,
          min: 0,
          acquire: 30000,
          idle: 10000
        },
        timezone: '+08:00'
      });

      await connection.authenticate();
      this.connections.set(tenantCode, connection);
      
      return connection;
    } catch (error) {
      console.error(`❌ 租户 ${tenantCode} 数据库连接失败:`, error);
      throw error;
    }
  }

  /**
   * 获取租户数据库连接
   * @param {string} tenantCode 租户代码
   * @returns {Sequelize} 数据库连接实例
   */
  getTenantConnection(tenantCode) {
    return this.connections.get(tenantCode);
  }

  /**
   * 获取SAAS平台数据库连接
   * @returns {Sequelize} SAAS平台数据库连接
   */
  getSaasConnection() {
    return this.saasConnection;
  }

  /**
   * 关闭指定租户的数据库连接
   * @param {string} tenantCode 租户代码
   */
  async closeTenantConnection(tenantCode) {
    const connection = this.connections.get(tenantCode);
    if (connection) {
      await connection.close();
      this.connections.delete(tenantCode);
    }
  }

  /**
   * 关闭所有数据库连接
   */
  async closeAllConnections() {
    // 关闭所有租户连接
    for (const [tenantCode, connection] of this.connections) {
      await connection.close();
    }
    this.connections.clear();

    // 关闭SAAS平台连接
    if (this.saasConnection) {
      await this.saasConnection.close();
    }
  }

  /**
   * 初始化租户数据库结构
   * @param {string} tenantCode 租户代码
   */
  async initTenantDatabase(tenantCode) {
    try {
      const connection = this.getTenantConnection(tenantCode);
      if (!connection) {
        throw new Error(`租户 ${tenantCode} 数据库连接不存在`);
      }

      // 读取并执行初始化SQL脚本
      const fs = require('fs');
      const initSqlPath = path.join(__dirname, '..', '..', 'database', 'init.sql');
      const initSql = fs.readFileSync(initSqlPath, 'utf8');
      
      // 分割SQL语句并执行
      const statements = initSql.split(';').filter(stmt => stmt.trim());
      for (const statement of statements) {
        if (statement.trim()) {
          await connection.query(statement);
        }
      }

    } catch (error) {
      console.error(`❌ 租户 ${tenantCode} 数据库初始化失败:`, error);
      throw error;
    }
  }

  /**
   * 检查租户数据库健康状态
   * @param {string} tenantCode 租户代码
   * @returns {Object} 健康状态信息
   */
  async checkTenantDatabaseHealth(tenantCode) {
    try {
      const connection = this.getTenantConnection(tenantCode);
      if (!connection) {
        return { healthy: false, error: '数据库连接不存在' };
      }

      await connection.authenticate();
      
      // 执行简单查询测试
      const [results] = await connection.query('SELECT 1 as test');
      
      return {
        healthy: true,
        latency: Date.now(),
        version: await this.getDatabaseVersion(connection)
      };
    } catch (error) {
      return {
        healthy: false,
        error: error.message
      };
    }
  }

  /**
   * 获取数据库版本信息
   * @param {Sequelize} connection 数据库连接
   * @returns {string} 数据库版本
   */
  async getDatabaseVersion(connection) {
    try {
      const [results] = await connection.query('SELECT VERSION() as version');
      return results[0].version;
    } catch (error) {
      return 'Unknown';
    }
  }
}

// 创建单例实例
const tenantDatabaseManager = new TenantDatabaseManager();

module.exports = tenantDatabaseManager;