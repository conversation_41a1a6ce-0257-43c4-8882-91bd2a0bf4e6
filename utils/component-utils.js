/**
 * 小程序组件工具函数
 * 统一处理组件属性验证和数据更新，避免兼容性问题
 */

/**
 * 标准化字符串属性，确保类型安全
 * @param {*} value - 输入值
 * @param {string} defaultValue - 默认值
 * @return {string} 规范化后的字符串值
 */
function normalizeStringProperty(value, defaultValue = '') {
  // 处理null、undefined、'null'、'undefined'等异常值
  if (value === null || value === undefined || 
      value === 'null' || value === 'undefined' || 
      (typeof value === 'string' && value.trim() === '')) {
    return defaultValue;
  }
  
  // 确保返回字符串类型
  return String(value);
}

/**
 * 标准化数字属性，确保类型安全
 * @param {*} value - 输入值
 * @param {number} defaultValue - 默认值
 * @return {number} 规范化后的数字值
 */
function normalizeNumberProperty(value, defaultValue = 0) {
  // 处理null、undefined、'null'、'undefined'等异常值
  if (value === null || value === undefined || 
      value === 'null' || value === 'undefined' || 
      value === '') {
    return defaultValue;
  }
  
  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
}

/**
 * 标准化布尔属性，确保类型安全
 * @param {*} value - 输入值
 * @param {boolean} defaultValue - 默认值
 * @return {boolean} 规范化后的布尔值
 */
function normalizeBooleanProperty(value, defaultValue = false) {
  // 处理null、undefined、'null'、'undefined'等异常值
  if (value === null || value === undefined || 
      value === 'null' || value === 'undefined' || 
      value === '') {
    return defaultValue;
  }
  
  // 字符串布尔值处理
  if (typeof value === 'string') {
    const lowercaseValue = value.toLowerCase();
    if (lowercaseValue === 'true' || lowercaseValue === '1') {
      return true;
    }
    if (lowercaseValue === 'false' || lowercaseValue === '0') {
      return false;
    }
  }
  
  return Boolean(value);
}

/**
 * 安全的setData，避免递归更新
 * @param {object} component - 组件实例
 * @param {object} data - 要设置的数据
 * @param {function} callback - 回调函数
 */
function safeSetData(component, data, callback) {
  if (!component || !component.setData || !data) {
    console.warn('[ComponentUtils] Invalid component or data for setData');
    return;
  }
  
  // 使用wx.nextTick避免递归更新
  wx.nextTick(() => {
    component.setData(data, callback);
  });
}

/**
 * 防抖的setData，避免频繁更新
 * @param {object} component - 组件实例
 * @param {string} key - 防抖的唯一标识
 * @param {object} data - 要设置的数据
 * @param {number} delay - 延迟时间，默认100ms
 */
function debounceSetData(component, key, data, delay = 100) {
  if (!component._debounceTimers) {
    component._debounceTimers = {};
  }
  
  // 清除之前的定时器
  if (component._debounceTimers[key]) {
    clearTimeout(component._debounceTimers[key]);
  }
  
  // 设置新的定时器
  component._debounceTimers[key] = setTimeout(() => {
    safeSetData(component, data);
    delete component._debounceTimers[key];
  }, delay);
}

/**
 * 创建属性观察器，自动处理类型转换和递归更新问题
 * @param {string} type - 属性类型：'string'、'number'、'boolean'
 * @param {*} defaultValue - 默认值
 * @param {function} customHandler - 自定义处理函数
 * @return {function} 观察器函数
 */
function createPropertyObserver(type, defaultValue, customHandler) {
  return function(newVal, oldVal) {
    let normalizedValue;
    
    switch (type) {
      case 'string':
        normalizedValue = normalizeStringProperty(newVal, defaultValue);
        break;
      case 'number':
        normalizedValue = normalizeNumberProperty(newVal, defaultValue);
        break;
      case 'boolean':
        normalizedValue = normalizeBooleanProperty(newVal, defaultValue);
        break;
      default:
        normalizedValue = newVal;
    }
    
    // 执行自定义处理逻辑
    if (typeof customHandler === 'function') {
      customHandler.call(this, normalizedValue, oldVal);
    }
  };
}

/**
 * 混入组件工具方法到组件实例
 * @param {object} component - 组件配置对象
 * @return {object} 增强后的组件配置
 */
function mixinComponentUtils(component) {
  if (!component.methods) {
    component.methods = {};
  }
  
  // 添加工具方法到组件methods中
  Object.assign(component.methods, {
    _normalizeStringProperty: normalizeStringProperty,
    _normalizeNumberProperty: normalizeNumberProperty,
    _normalizeBooleanProperty: normalizeBooleanProperty,
    _safeSetData: function(data, callback) {
      safeSetData(this, data, callback);
    },
    _debounceSetData: function(key, data, delay) {
      debounceSetData(this, key, data, delay);
    }
  });
  
  return component;
}

module.exports = {
  normalizeStringProperty,
  normalizeNumberProperty,
  normalizeBooleanProperty,
  safeSetData,
  debounceSetData,
  createPropertyObserver,
  mixinComponentUtils
};