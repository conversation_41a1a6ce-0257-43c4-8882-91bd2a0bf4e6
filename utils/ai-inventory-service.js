// utils/ai-inventory-service.js
// AI智能盘点服务 - 专门用于鹅群数量识别

const { callImageRecognition } = require('./ai-service.js');
const { getAIConfig } = require('./ai-config.js');

/**
 * AI智能盘点服务类
 */
class AIInventoryService {
  constructor() {
    this.config = null;
  }

  /**
   * 获取AI配置
   * @returns {Promise<Object>} AI配置
   */
  async getConfig() {
    if (!this.config) {
      this.config = await getAIConfig('AI_INVENTORY_COUNTING');
    }
    return this.config;
  }

  /**
   * 识别图像中的鹅群数量
   * @param {string} imagePath - 图像路径
   * @param {Object} options - 识别选项
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeGooseCount(imagePath, options = {}) {
    try {
      
      // 将图像转换为base64
      const imageBase64 = await this.imageToBase64(imagePath);
      
      // 构建识别提示词
      const prompt = this.buildCountingPrompt(options);
      
      // 调用AI图像识别服务
      const result = await this.callAIRecognition(imageBase64, prompt);
      
      if (result.success) {
        // 解析识别结果
        const parsedResult = this.parseRecognitionResult(result.data.content);
        
        if (parsedResult.success) {
          return {
            success: true,
            data: {
              ...parsedResult.data,
              model: result.data.model,
              provider: result.data.provider,
              usage: result.data.usage
            }
          };
        } else {
          return parsedResult;
        }
      } else {
        throw new Error(result.error.message);
      }
      
    } catch (error) {
      console.error('AI盘点识别失败:', error);
      return {
        success: false,
        error: {
          message: error.message || 'AI识别失败',
          type: 'RECOGNITION_ERROR',
          details: error
        }
      };
    }
  }

  /**
   * 将图像转换为base64格式
   * @param {string} imagePath - 图像路径
   * @returns {Promise<string>} base64字符串
   */
  async imageToBase64(imagePath) {
    return new Promise((resolve, reject) => {
      wx.getFileSystemManager().readFile({
        filePath: imagePath,
        encoding: 'base64',
        success: (res) => {
          resolve(res.data);
        },
        fail: (err) => {
          console.error('图像转换base64失败:', err);
          reject(new Error('图像读取失败: ' + err.errMsg));
        }
      });
    });
  }

  /**
   * 构建数量统计的提示词
   * @param {Object} options - 选项
   * @returns {string} 提示词
   */
  buildCountingPrompt(options = {}) {
    let prompt = `请仔细分析这张图片，统计其中鹅的数量。`;

    // 根据选项添加特定要求
    if (options.minConfidence) {
      prompt += `\n要求置信度达到${options.minConfidence}%以上。`;
    }

    if (options.batchNumber) {
      prompt += `\n这是批次号为${options.batchNumber}的鹅群。`;
    }

    if (options.expectedRange) {
      prompt += `\n预期数量范围：${options.expectedRange.min}-${options.expectedRange.max}只。`;
    }

    prompt += `\n\n请特别注意：
1. 仔细区分鹅和其他禽类（鸭子、鸡等）
2. 只统计活体鹅，排除任何非生物对象
3. 对于部分遮挡的鹅，根据可见特征进行判断
4. 如果图片质量不佳，请在分析中说明并降低置信度

请严格按照系统提示中的JSON格式返回结果。`;

    return prompt;
  }

  /**
   * 调用AI识别服务
   * @param {string} imageBase64 - base64图像数据
   * @param {string} prompt - 提示词
   * @returns {Promise<Object>} AI响应结果
   */
  async callAIRecognition(imageBase64, prompt) {
    try {
      const result = await callImageRecognition(imageBase64, prompt);
      return result;
    } catch (error) {
      console.error('AI识别服务调用失败:', error);
      throw error;
    }
  }

  /**
   * 解析AI识别结果
   * @param {string} aiResponse - AI响应文本
   * @returns {Object} 解析后的结果
   */
  parseRecognitionResult(aiResponse) {
    try {
      
      let result;
      
      // 尝试提取JSON部分
      const jsonMatch = aiResponse.match(/```json\s*([\s\S]*?)\s*```/) || 
                       aiResponse.match(/```\s*([\s\S]*?)\s*```/) ||
                       aiResponse.match(/\{[\s\S]*\}/);
      
      if (jsonMatch) {
        const jsonStr = jsonMatch[1] || jsonMatch[0];
        result = JSON.parse(jsonStr);
      } else {
        // 如果没有找到JSON格式，尝试直接解析
        const cleanResponse = aiResponse.trim();
        if (cleanResponse.startsWith('{') && cleanResponse.endsWith('}')) {
          result = JSON.parse(cleanResponse);
        } else {
          throw new Error('无法找到有效的JSON响应');
        }
      }

      // 验证结果格式
      const validationResult = this.validateRecognitionResult(result);
      if (!validationResult.valid) {
        throw new Error(validationResult.error);
      }

      return {
        success: true,
        data: {
          count: Math.floor(result.count),
          confidence: Math.floor(result.confidence),
          details: result.details || '',
          warnings: result.warnings || '',
          analysis: result.analysis || {},
          timestamp: new Date().toISOString(),
          rawResponse: aiResponse
        }
      };

    } catch (error) {
      console.error('解析AI响应失败:', error);
      console.error('原始响应:', aiResponse);
      
      // 尝试从响应中提取数字信息作为备用方案
      const fallbackResult = this.extractFallbackResult(aiResponse);
      
      return {
        success: false,
        error: {
          message: `解析AI响应失败: ${error.message}`,
          type: 'PARSE_ERROR',
          rawResponse: aiResponse,
          fallbackResult: fallbackResult
        }
      };
    }
  }

  /**
   * 验证识别结果格式
   * @param {Object} result - 识别结果
   * @returns {Object} 验证结果
   */
  validateRecognitionResult(result) {
    if (typeof result !== 'object' || result === null) {
      return { valid: false, error: '结果必须是对象' };
    }

    if (typeof result.count !== 'number' || result.count < 0 || !Number.isInteger(result.count)) {
      return { valid: false, error: 'count必须是非负整数' };
    }

    if (typeof result.confidence !== 'number' || result.confidence < 0 || result.confidence > 100) {
      return { valid: false, error: 'confidence必须是0-100的数字' };
    }

    return { valid: true };
  }

  /**
   * 从响应中提取备用结果
   * @param {string} response - AI响应
   * @returns {Object|null} 备用结果
   */
  extractFallbackResult(response) {
    try {
      // 尝试提取数字信息
      const countMatch = response.match(/(\d+)\s*只/);
      const confidenceMatch = response.match(/置信度[：:]\s*(\d+)%/) || 
                             response.match(/confidence[：:]\s*(\d+)/i);

      if (countMatch) {
        return {
          count: parseInt(countMatch[1]),
          confidence: confidenceMatch ? parseInt(confidenceMatch[1]) : 50,
          details: '从文本中提取的备用结果',
          warnings: '原始JSON解析失败，使用文本提取结果'
        };
      }
    } catch (error) {
      console.error('提取备用结果失败:', error);
    }
    
    return null;
  }

  /**
   * 批量识别多张图片
   * @param {Array} imagePaths - 图片路径数组
   * @param {Object} options - 识别选项
   * @returns {Promise<Array>} 识别结果数组
   */
  async batchRecognize(imagePaths, options = {}) {
    const results = [];
    
    for (let i = 0; i < imagePaths.length; i++) {
      const imagePath = imagePaths[i];
      
      try {
        const result = await this.recognizeGooseCount(imagePath, {
          ...options,
          batchIndex: i + 1,
          totalBatches: imagePaths.length
        });
        
        results.push({
          index: i,
          imagePath: imagePath,
          ...result
        });
        
        // 添加延迟避免API限制
        if (i < imagePaths.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
      } catch (error) {
        results.push({
          index: i,
          imagePath: imagePath,
          success: false,
          error: error.message
        });
      }
    }
    
    return results;
  }

  /**
   * 获取识别统计信息
   * @param {Array} results - 识别结果数组
   * @returns {Object} 统计信息
   */
  getRecognitionStats(results) {
    const stats = {
      total: results.length,
      successful: 0,
      failed: 0,
      totalCount: 0,
      averageConfidence: 0,
      highConfidenceCount: 0, // 置信度>80%的数量
      lowConfidenceCount: 0   // 置信度<60%的数量
    };

    let confidenceSum = 0;
    
    results.forEach(result => {
      if (result.success) {
        stats.successful++;
        stats.totalCount += result.data.count;
        confidenceSum += result.data.confidence;
        
        if (result.data.confidence > 80) {
          stats.highConfidenceCount++;
        } else if (result.data.confidence < 60) {
          stats.lowConfidenceCount++;
        }
      } else {
        stats.failed++;
      }
    });

    if (stats.successful > 0) {
      stats.averageConfidence = Math.round(confidenceSum / stats.successful);
    }

    return stats;
  }
}

// 创建单例实例
const aiInventoryService = new AIInventoryService();

module.exports = {
  AIInventoryService,
  aiInventoryService
};
