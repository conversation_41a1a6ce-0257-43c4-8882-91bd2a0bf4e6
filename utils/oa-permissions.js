/**
 * OA系统权限管理工具
 * 提供细粒度的权限控制和检查机制
 */

// 权限常量定义
const PERMISSIONS = {
  // 基础权限
  VIEW_OA: 'view_oa',                    // 查看OA系统
  
  // 申请权限
  CREATE_PURCHASE: 'create_purchase',      // 创建采购申请
  CREATE_REIMBURSEMENT: 'create_reimbursement', // 创建报销申请
  CREATE_LEAVE: 'create_leave',           // 创建请假申请
  
  // 审批权限
  APPROVE_PURCHASE: 'approve_purchase',    // 审批采购申请
  APPROVE_REIMBURSEMENT: 'approve_reimbursement', // 审批报销申请
  APPROVE_LEAVE: 'approve_leave',         // 审批请假申请
  
  // 管理权限
  MANAGE_USERS: 'manage_users',           // 用户管理
  MANAGE_ROLES: 'manage_roles',           // 角色管理
  MANAGE_WORKFLOW: 'manage_workflow',     // 工作流管理
  MANAGE_NOTIFICATIONS: 'manage_notifications', // 通知管理
  
  // 财务权限
  VIEW_FINANCE: 'view_finance',           // 查看财务数据
  MANAGE_FINANCE: 'manage_finance',       // 管理财务数据
  EXPORT_FINANCE: 'export_finance',       // 导出财务数据
  
  // 系统权限
  SYSTEM_ADMIN: 'system_admin',           // 系统管理员
  DATA_EXPORT: 'data_export'              // 数据导出
};

// 角色定义
const ROLES = {
  EMPLOYEE: 'employee',                   // 普通员工
  MANAGER: 'manager',                     // 部门经理
  FINANCE: 'finance',                     // 财务人员
  ADMIN: 'admin',                         // 系统管理员
  SUPER_ADMIN: 'super_admin'              // 超级管理员
};

// 角色权限映射
const ROLE_PERMISSIONS = {
  [ROLES.EMPLOYEE]: [
    PERMISSIONS.VIEW_OA,
    PERMISSIONS.CREATE_PURCHASE,
    PERMISSIONS.CREATE_REIMBURSEMENT,
    PERMISSIONS.CREATE_LEAVE
  ],
  
  [ROLES.MANAGER]: [
    PERMISSIONS.VIEW_OA,
    PERMISSIONS.CREATE_PURCHASE,
    PERMISSIONS.CREATE_REIMBURSEMENT,
    PERMISSIONS.CREATE_LEAVE,
    PERMISSIONS.APPROVE_PURCHASE,
    PERMISSIONS.APPROVE_REIMBURSEMENT,
    PERMISSIONS.APPROVE_LEAVE,
    PERMISSIONS.VIEW_FINANCE
  ],
  
  [ROLES.FINANCE]: [
    PERMISSIONS.VIEW_OA,
    PERMISSIONS.CREATE_PURCHASE,
    PERMISSIONS.CREATE_REIMBURSEMENT,
    PERMISSIONS.CREATE_LEAVE,
    PERMISSIONS.APPROVE_REIMBURSEMENT,
    PERMISSIONS.VIEW_FINANCE,
    PERMISSIONS.MANAGE_FINANCE,
    PERMISSIONS.EXPORT_FINANCE
  ],
  
  [ROLES.ADMIN]: [
    PERMISSIONS.VIEW_OA,
    PERMISSIONS.CREATE_PURCHASE,
    PERMISSIONS.CREATE_REIMBURSEMENT,
    PERMISSIONS.CREATE_LEAVE,
    PERMISSIONS.APPROVE_PURCHASE,
    PERMISSIONS.APPROVE_REIMBURSEMENT,
    PERMISSIONS.APPROVE_LEAVE,
    PERMISSIONS.MANAGE_USERS,
    PERMISSIONS.MANAGE_ROLES,
    PERMISSIONS.MANAGE_WORKFLOW,
    PERMISSIONS.MANAGE_NOTIFICATIONS,
    PERMISSIONS.VIEW_FINANCE,
    PERMISSIONS.MANAGE_FINANCE,
    PERMISSIONS.EXPORT_FINANCE,
    PERMISSIONS.DATA_EXPORT
  ],
  
  [ROLES.SUPER_ADMIN]: Object.values(PERMISSIONS)
};

/**
 * 权限管理类
 */
class OAPermissionManager {
  constructor() {
    this.userPermissions = new Set();
    this.userRoles = [];
    this.initialized = false;
  }

  /**
   * 初始化用户权限
   */
  async init() {
    try {
      const userInfo = this.getCurrentUserInfo();
      if (!userInfo) {
        console.warn('未找到用户信息，使用默认权限');
        this.setDefaultPermissions();
        this.initialized = true;
        return;
      }


      // 尝试从后台获取用户权限和角色
      try {
        await this.loadUserPermissions(userInfo.id || userInfo.userId || 'default_user');
      } catch (apiError) {
        console.warn('API权限加载失败，使用默认权限:', apiError.message);
        this.setDefaultPermissions();
      }
      
      this.initialized = true;
    } catch (error) {
      console.error('权限初始化失败:', error);
      // 使用默认权限
      this.setDefaultPermissions();
      this.initialized = true;
    }
  }

  /**
   * 获取当前用户信息
   */
  getCurrentUserInfo() {
    try {
      const app = getApp();
      return app?.globalData?.userInfo || wx.getStorageSync('userInfo');
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }

  /**
   * 从服务器加载用户权限
   */
  async loadUserPermissions(userId) {
    try {
      const token = wx.getStorageSync('token');
      
      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: getApp().globalData.apiBaseUrl + `/api/v1/oa/permissions/user/${userId}`,
          method: 'GET',
          header: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });

      if (res.data.success) {
        const { roles, permissions } = res.data.data;
        this.userRoles = roles || [];
        
        // 合并角色权限和特殊权限
        const allPermissions = new Set();
        
        // 添加角色权限
        this.userRoles.forEach(role => {
          const rolePermissions = ROLE_PERMISSIONS[role] || [];
          rolePermissions.forEach(permission => allPermissions.add(permission));
        });
        
        // 添加特殊权限
        (permissions || []).forEach(permission => allPermissions.add(permission));
        
        this.userPermissions = allPermissions;
      } else {
        throw new Error(res.data.message || '获取权限失败');
      }
    } catch (error) {
      console.error('加载用户权限失败:', error);
      throw error;
    }
  }

  /**
   * 设置默认权限（用于离线或出错时）
   */
  setDefaultPermissions() {
    // 开发测试期间，如果权限加载失败，给予管理员权限以便测试
    // 生产环境中应该给予最小权限
    const isDebugMode = wx.getStorageSync('debugMode') === 'true' || true; // 当前为开发模式
    
    if (isDebugMode) {
      console.warn('权限加载失败，使用默认管理员权限（开发模式）');
      this.userRoles = [ROLES.ADMIN];
      this.userPermissions = new Set(ROLE_PERMISSIONS[ROLES.ADMIN]);
    } else {
      console.warn('权限加载失败，使用默认员工权限');
      this.userRoles = [ROLES.EMPLOYEE];
      this.userPermissions = new Set(ROLE_PERMISSIONS[ROLES.EMPLOYEE]);
    }
  }

  /**
   * 检查是否有指定权限
   */
  hasPermission(permission) {
    if (!this.initialized) {
      console.warn('权限系统未初始化，请先调用 init() 方法');
      // 在未初始化时，为了开发方便，临时返回true
      return true;
    }
    
    const hasPermission = this.userPermissions.has(permission);
    
    return hasPermission;
  }

  /**
   * 检查是否有指定角色
   */
  hasRole(role) {
    if (!this.initialized) {
      console.warn('权限系统未初始化，请先调用 init() 方法');
      return false;
    }
    
    return this.userRoles.includes(role);
  }

  /**
   * 检查是否有任意一个权限
   */
  hasAnyPermission(permissions) {
    return permissions.some(permission => this.hasPermission(permission));
  }

  /**
   * 检查是否有所有权限
   */
  hasAllPermissions(permissions) {
    return permissions.every(permission => this.hasPermission(permission));
  }

  /**
   * 获取用户所有权限
   */
  getAllPermissions() {
    return Array.from(this.userPermissions);
  }

  /**
   * 获取用户所有角色
   */
  getAllRoles() {
    return [...this.userRoles];
  }

  /**
   * 权限装饰器 - 用于页面权限检查
   */
  requirePermission(permission, errorMessage = '您没有执行此操作的权限') {
    if (!this.hasPermission(permission)) {
      wx.showModal({
        title: '权限不足',
        content: errorMessage,
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return false;
    }
    return true;
  }

  /**
   * 角色装饰器 - 用于页面角色检查
   */
  requireRole(role, errorMessage = '您的角色权限不足以访问此页面') {
    if (!this.hasRole(role)) {
      wx.showModal({
        title: '权限不足',
        content: errorMessage,
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return false;
    }
    return true;
  }

  /**
   * 批量权限检查 - 用于复杂权限逻辑
   */
  checkPermissions(config) {
    const {
      anyOf = [],          // 需要任意一个权限
      allOf = [],          // 需要所有权限
      anyRole = [],        // 需要任意一个角色
      allRoles = [],       // 需要所有角色
      onSuccess = () => {},  // 成功回调
      onFailed = () => {}    // 失败回调
    } = config;

    let hasPermission = true;

    // 检查 anyOf 权限
    if (anyOf.length > 0 && !this.hasAnyPermission(anyOf)) {
      hasPermission = false;
    }

    // 检查 allOf 权限
    if (allOf.length > 0 && !this.hasAllPermissions(allOf)) {
      hasPermission = false;
    }

    // 检查 anyRole 角色
    if (anyRole.length > 0 && !anyRole.some(role => this.hasRole(role))) {
      hasPermission = false;
    }

    // 检查 allRoles 角色
    if (allRoles.length > 0 && !allRoles.every(role => this.hasRole(role))) {
      hasPermission = false;
    }

    if (hasPermission) {
      onSuccess();
    } else {
      onFailed();
    }

    return hasPermission;
  }

  /**
   * 刷新权限 - 用于权限变更后重新获取
   */
  async refresh() {
    this.initialized = false;
    await this.init();
  }
}

// 创建全局实例
const oaPermissionManager = new OAPermissionManager();

// 页面权限检查混入
const PermissionMixin = {
  onLoad() {
    // 确保权限系统已初始化
    if (!oaPermissionManager.initialized) {
      oaPermissionManager.init().then(() => {
        this.onPermissionReady && this.onPermissionReady();
      });
    } else {
      this.onPermissionReady && this.onPermissionReady();
    }
  },

  // 权限检查方法
  hasPermission(permission) {
    return oaPermissionManager.hasPermission(permission);
  },

  hasRole(role) {
    return oaPermissionManager.hasRole(role);
  },

  requirePermission(permission, errorMessage) {
    return oaPermissionManager.requirePermission(permission, errorMessage);
  },

  requireRole(role, errorMessage) {
    return oaPermissionManager.requireRole(role, errorMessage);
  },

  checkPermissions(config) {
    return oaPermissionManager.checkPermissions(config);
  }
};

module.exports = {
  PERMISSIONS,
  ROLES,
  ROLE_PERMISSIONS,
  OAPermissionManager,
  oaPermissionManager,
  PermissionMixin
};