/**
 * 性能监控工具类
 * 基于微信小程序性能监控API实现
 * 监控页面加载时间、内存使用、网络请求等
 */

class PerformanceMonitor {
  constructor(options = {}) {
    this.options = {
      enableMemoryMonitor: false, // 默认关闭内存监控以减少开销
      enableNetworkMonitor: true,
      enablePagePerformance: true,
      reportInterval: 60000, // 增加到60秒上报一次，减少频率
      maxMetricsCount: 50, // 限制指标数量，防止内存泄露
      ...options
    };
    
    this.metrics = {
      pageLoad: {},
      memory: {},
      network: {},
      errors: []
    };
    
    this.init();
  }

  /**
   * 初始化监控
   */
  init() {
    if (this.options.enablePagePerformance) {
      this.initPagePerformanceMonitor();
    }
    
    if (this.options.enableMemoryMonitor) {
      this.initMemoryMonitor();
    }
    
    if (this.options.enableNetworkMonitor) {
      this.initNetworkMonitor();
    }

    // 启动定时上报
    this.startReporting();
  }

  /**
   * 页面性能监控
   */
  initPagePerformanceMonitor() {
    // 监控页面切换性能
    const originalNavigateTo = wx.navigateTo;
    const originalRedirectTo = wx.redirectTo;
    const originalSwitchTab = wx.switchTab;

    wx.navigateTo = (options) => {
      const startTime = Date.now();
      const originalSuccess = options.success;
      
      options.success = (res) => {
        this.recordPageTransition('navigateTo', startTime, options.url);
        originalSuccess && originalSuccess(res);
      };
      
      return originalNavigateTo(options);
    };

    wx.redirectTo = (options) => {
      const startTime = Date.now();
      const originalSuccess = options.success;
      
      options.success = (res) => {
        this.recordPageTransition('redirectTo', startTime, options.url);
        originalSuccess && originalSuccess(res);
      };
      
      return originalRedirectTo(options);
    };

    wx.switchTab = (options) => {
      const startTime = Date.now();
      const originalSuccess = options.success;
      
      options.success = (res) => {
        this.recordPageTransition('switchTab', startTime, options.url);
        originalSuccess && originalSuccess(res);
      };
      
      return originalSwitchTab(options);
    };
  }

  /**
   * 内存监控
   */
  initMemoryMonitor() {
    if (!wx.getPerformance) return;

    const performance = wx.getPerformance();
    
    setInterval(() => {
      if (performance.getEntries) {
        const entries = performance.getEntries();
        this.recordMemoryUsage(entries);
      }
    }, 10000); // 每10秒检查一次内存
  }

  /**
   * 网络监控
   */
  initNetworkMonitor() {
    const originalRequest = wx.request;
    
    wx.request = (options) => {
      const startTime = Date.now();
      const originalSuccess = options.success;
      const originalFail = options.fail;
      
      options.success = (res) => {
        this.recordNetworkRequest('success', startTime, options.url, res);
        originalSuccess && originalSuccess(res);
      };
      
      options.fail = (err) => {
        this.recordNetworkRequest('fail', startTime, options.url, err);
        originalFail && originalFail(err);
      };
      
      return originalRequest(options);
    };
  }

  /**
   * 记录页面切换性能
   */
  recordPageTransition(type, startTime, url) {
    const duration = Date.now() - startTime;
    const key = `${type}_${url}`;
    
    if (!this.metrics.pageLoad[key]) {
      this.metrics.pageLoad[key] = {
        type,
        url,
        count: 0,
        totalDuration: 0,
        avgDuration: 0,
        maxDuration: 0,
        minDuration: Infinity
      };
    }
    
    const metric = this.metrics.pageLoad[key];
    metric.count++;
    metric.totalDuration += duration;
    metric.avgDuration = metric.totalDuration / metric.count;
    metric.maxDuration = Math.max(metric.maxDuration, duration);
    metric.minDuration = Math.min(metric.minDuration, duration);
    
    // 如果页面加载时间超过3秒，记录为慢页面
    if (duration > 3000) {
      console.warn(`慢页面警告: ${url} 加载耗时 ${duration}ms`);
      this.recordError('slow_page', { url, duration, type });
    }
  }

  /**
   * 记录内存使用情况
   */
  recordMemoryUsage(entries) {
    const now = Date.now();
    
    // 分析内存相关的性能条目
    const memoryEntries = entries.filter(entry => 
      entry.entryType === 'memory' || entry.name.includes('memory')
    );
    
    if (memoryEntries.length > 0) {
      this.metrics.memory[now] = {
        timestamp: now,
        entries: memoryEntries.map(entry => ({
          name: entry.name,
          value: entry.value || entry.duration,
          type: entry.entryType
        }))
      };
    }
  }

  /**
   * 记录网络请求性能
   */
  recordNetworkRequest(status, startTime, url, response) {
    const duration = Date.now() - startTime;
    const key = url.split('?')[0]; // 移除查询参数
    
    if (!this.metrics.network[key]) {
      this.metrics.network[key] = {
        url: key,
        successCount: 0,
        failCount: 0,
        totalDuration: 0,
        avgDuration: 0,
        maxDuration: 0,
        errors: []
      };
    }
    
    const metric = this.metrics.network[key];
    
    if (status === 'success') {
      metric.successCount++;
      metric.totalDuration += duration;
      metric.avgDuration = metric.totalDuration / metric.successCount;
      metric.maxDuration = Math.max(metric.maxDuration, duration);
      
      // 检查响应时间
      if (duration > 5000) {
        console.warn(`慢接口警告: ${key} 响应耗时 ${duration}ms`);
        this.recordError('slow_api', { url: key, duration, response });
      }
    } else {
      metric.failCount++;
      metric.errors.push({
        timestamp: Date.now(),
        error: response,
        duration
      });
      
      this.recordError('api_error', { url: key, duration, error: response });
    }
  }

  /**
   * 记录错误
   */
  recordError(type, data) {
    this.metrics.errors.push({
      type,
      timestamp: Date.now(),
      data
    });
    
    // 限制错误记录数量
    if (this.metrics.errors.length > 100) {
      this.metrics.errors = this.metrics.errors.slice(-50);
    }
  }

  /**
   * 开始性能监控页面
   */
  startPageMonitor(pageName) {
    const startTime = Date.now();
    
    return {
      // 记录页面加载完成
      onReady: () => {
        const loadTime = Date.now() - startTime;
        this.recordPageLoad(pageName, loadTime, 'ready');
      },
      
      // 记录页面首次渲染
      onShow: () => {
        const showTime = Date.now() - startTime;
        this.recordPageLoad(pageName, showTime, 'show');
      },
      
      // 记录自定义性能点
      mark: (eventName) => {
        const markTime = Date.now() - startTime;
        this.recordPageLoad(pageName, markTime, eventName);
      }
    };
  }

  /**
   * 记录页面加载性能
   */
  recordPageLoad(pageName, duration, event) {
    const key = `${pageName}_${event}`;
    
    if (!this.metrics.pageLoad[key]) {
      this.metrics.pageLoad[key] = {
        page: pageName,
        event,
        count: 0,
        totalDuration: 0,
        avgDuration: 0,
        maxDuration: 0,
        minDuration: Infinity
      };
    }
    
    const metric = this.metrics.pageLoad[key];
    metric.count++;
    metric.totalDuration += duration;
    metric.avgDuration = metric.totalDuration / metric.count;
    metric.maxDuration = Math.max(metric.maxDuration, duration);
    metric.minDuration = Math.min(metric.minDuration, duration);
  }

  /**
   * 启动定时上报
   */
  startReporting() {
    setInterval(() => {
      this.reportMetrics();
    }, this.options.reportInterval);
  }

  /**
   * 上报性能数据
   */
  reportMetrics() {
    const report = {
      timestamp: Date.now(),
      pageLoad: Object.values(this.metrics.pageLoad),
      network: Object.values(this.metrics.network),
      memory: Object.values(this.metrics.memory).slice(-10), // 只取最近10条
      errors: this.metrics.errors.slice(-20), // 只取最近20条错误
      deviceInfo: this.getDeviceInfo()
    };
    
    // 上报到服务器（示例）
    if (this.options.reportUrl) {
      wx.request({
        url: this.options.reportUrl,
        method: 'POST',
        data: report,
        fail: (err) => {
          console.error('性能数据上报失败:', err);
        }
      });
    }
    
    // 输出到控制台（开发环境）
    if (this.options.debug) {
    }
  }

  /**
   * 获取设备信息
   */
  getDeviceInfo() {
    const systemInfo = wx.getSystemInfoSync();
    return {
      brand: systemInfo.brand,
      model: systemInfo.model,
      system: systemInfo.system,
      platform: systemInfo.platform,
      version: systemInfo.version,
      SDKVersion: systemInfo.SDKVersion
    };
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    return {
      ...this.metrics,
      summary: this.generateSummary()
    };
  }

  /**
   * 生成性能摘要
   */
  generateSummary() {
    const pageLoadTimes = Object.values(this.metrics.pageLoad)
      .filter(p => p.event === 'ready')
      .map(p => p.avgDuration);
    
    const networkSuccess = Object.values(this.metrics.network)
      .reduce((acc, n) => acc + n.successCount, 0);
    const networkFail = Object.values(this.metrics.network)
      .reduce((acc, n) => acc + n.failCount, 0);
    
    return {
      avgPageLoadTime: pageLoadTimes.length > 0 
        ? pageLoadTimes.reduce((a, b) => a + b, 0) / pageLoadTimes.length 
        : 0,
      slowPagesCount: pageLoadTimes.filter(t => t > 3000).length,
      networkSuccessRate: networkSuccess + networkFail > 0 
        ? (networkSuccess / (networkSuccess + networkFail)) * 100 
        : 100,
      totalErrors: this.metrics.errors.length,
      recentErrors: this.metrics.errors.slice(-5)
    };
  }
}

// 创建全局性能监控实例
const performanceMonitor = new PerformanceMonitor({
  enableMemoryMonitor: true,
  enableNetworkMonitor: true,
  enablePagePerformance: true,
  debug: __DEV__ || false,
  reportInterval: 60000 // 1分钟上报一次
});

module.exports = {
  PerformanceMonitor,
  performanceMonitor
};