/**
 * 无障碍访问增强工具
 * 基于WCAG 2.1 AA标准实现
 * 支持屏幕阅读器、键盘导航、高对比度模式
 */

class AccessibilityHelper {
  constructor() {
    this.isEnabled = true;
    this.announcements = [];
    this.focusHistory = [];
    this.init();
  }

  /**
   * 初始化无障碍功能
   */
  init() {
    this.detectAccessibilityFeatures();
    this.setupKeyboardNavigation();
    this.setupScreenReaderSupport();
  }

  /**
   * 检测用户的无障碍需求
   */
  detectAccessibilityFeatures() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      
      // 检测是否开启了高对比度模式
      this.highContrast = systemInfo.theme === 'dark';
      
      // 检测字体大小设置
      this.fontScale = systemInfo.fontSizeSetting || 1;
      
      // 检测是否使用辅助功能
      this.hasAccessibilityFeatures = this.fontScale > 1 || this.highContrast;
      
      if (this.hasAccessibilityFeatures) {
        this.enableEnhancedSupport();
      }
    } catch (error) {
      console.warn('无法检测无障碍功能:', error);
    }
  }

  /**
   * 启用增强无障碍支持
   */
  enableEnhancedSupport() {
    // 增加默认的触摸目标大小
    this.minTouchTargetSize = 88; // rpx
    
    // 启用更明显的焦点指示
    this.enhancedFocus = true;
    
    // 增加动画持续时间
    this.slowMotion = true;
  }

  /**
   * 设置键盘导航
   */
  setupKeyboardNavigation() {
    // 微信小程序主要通过触摸交互，但可以为组件设置焦点管理
    this.focusableElements = [];
    this.currentFocusIndex = -1;
  }

  /**
   * 设置屏幕阅读器支持
   */
  setupScreenReaderSupport() {
    // 为重要操作提供语音反馈
    this.setupVoiceAnnouncements();
  }

  /**
   * 语音播报设置
   */
  setupVoiceAnnouncements() {
    // 微信小程序不直接支持TTS，但可以通过vibrate等方式提供反馈
    this.canVibrate = wx.canUse && wx.canUse('vibrateLong');
  }

  /**
   * 播报文本（通过震动模式模拟）
   */
  announce(text, priority = 'polite') {
    if (!this.isEnabled) return;

    const announcement = {
      text,
      priority,
      timestamp: Date.now()
    };

    this.announcements.push(announcement);
    
    // 限制播报队列长度
    if (this.announcements.length > 10) {
      this.announcements.shift();
    }

    // 根据优先级处理
    if (priority === 'assertive') {
      this.handleAssertiveAnnouncement(text);
    } else {
      this.handlePoliteAnnouncement(text);
    }

  }

  /**
   * 处理紧急播报
   */
  handleAssertiveAnnouncement(text) {
    // 立即震动提醒
    if (this.canVibrate) {
      wx.vibrateShort({
        type: 'heavy'
      });
    }

    // 显示toast
    wx.showToast({
      title: text,
      icon: 'none',
      duration: 3000
    });
  }

  /**
   * 处理礼貌播报
   */
  handlePoliteAnnouncement(text) {
    // 轻微震动
    if (this.canVibrate) {
      wx.vibrateShort({
        type: 'light'
      });
    }
  }

  /**
   * 设置焦点
   */
  setFocus(elementId, options = {}) {
    if (!this.isEnabled) return;

    const element = {
      id: elementId,
      timestamp: Date.now(),
      ...options
    };

    this.focusHistory.push(element);
    
    // 限制焦点历史长度
    if (this.focusHistory.length > 20) {
      this.focusHistory.shift();
    }

    // 播报焦点变化
    if (options.announce !== false) {
      const text = options.label || options.ariaLabel || `焦点移动到${elementId}`;
      this.announce(text, 'polite');
    }

  }

  /**
   * 获取上一个焦点
   */
  getPreviousFocus() {
    return this.focusHistory[this.focusHistory.length - 2];
  }

  /**
   * 创建无障碍标签
   */
  createAriaLabel(text, context = '') {
    if (context) {
      return `${context}，${text}`;
    }
    return text;
  }

  /**
   * 创建状态描述
   */
  createStateDescription(state, options = {}) {
    const stateTexts = {
      loading: '正在加载',
      error: '发生错误',
      success: '操作成功',
      warning: '请注意',
      selected: '已选择',
      expanded: '已展开',
      collapsed: '已折叠',
      checked: '已勾选',
      unchecked: '未勾选',
      disabled: '不可用',
      required: '必填项'
    };

    const description = stateTexts[state] || state;
    
    if (options.value) {
      return `${description}，值为${options.value}`;
    }
    
    if (options.count) {
      return `${description}，共${options.count}项`;
    }
    
    return description;
  }

  /**
   * 检查颜色对比度
   */
  checkColorContrast(foreground, background) {
    // 简化的对比度计算
    const luminance1 = this.getLuminance(foreground);
    const luminance2 = this.getLuminance(background);
    
    const brightest = Math.max(luminance1, luminance2);
    const darkest = Math.min(luminance1, luminance2);
    
    const contrast = (brightest + 0.05) / (darkest + 0.05);
    
    return {
      ratio: contrast,
      passAA: contrast >= 4.5, // WCAG AA标准
      passAAA: contrast >= 7.0  // WCAG AAA标准
    };
  }

  /**
   * 获取颜色亮度
   */
  getLuminance(color) {
    // 简化的亮度计算（实际应用中需要更精确的计算）
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;
    
    return 0.299 * r + 0.587 * g + 0.114 * b;
  }

  /**
   * 验证触摸目标大小
   */
  validateTouchTargetSize(width, height) {
    const minSize = this.minTouchTargetSize || 44; // rpx
    
    return {
      width: width >= minSize,
      height: height >= minSize,
      valid: width >= minSize && height >= minSize,
      recommendation: minSize
    };
  }

  /**
   * 生成无障碍属性
   */
  generateAriaAttributes(options = {}) {
    const attributes = {};
    
    if (options.label) {
      attributes['aria-label'] = options.label;
    }
    
    if (options.role) {
      attributes.role = options.role;
    }
    
    if (options.describedBy) {
      attributes['aria-describedby'] = options.describedBy;
    }
    
    if (options.expanded !== undefined) {
      attributes['aria-expanded'] = options.expanded.toString();
    }
    
    if (options.selected !== undefined) {
      attributes['aria-selected'] = options.selected.toString();
    }
    
    if (options.disabled !== undefined) {
      attributes['aria-disabled'] = options.disabled.toString();
    }
    
    if (options.hidden !== undefined) {
      attributes['aria-hidden'] = options.hidden.toString();
    }
    
    if (options.live) {
      attributes['aria-live'] = options.live;
    }
    
    return attributes;
  }

  /**
   * 页面跳转时的无障碍处理
   */
  handlePageNavigation(from, to, title) {
    this.announce(`正在跳转到${title || to}页面`, 'polite');
    
    // 清除当前页面的焦点历史
    this.focusHistory = [];
    
    // 记录导航历史
  }

  /**
   * 表单验证的无障碍处理
   */
  handleFormValidation(fieldName, isValid, errorMessage) {
    if (!isValid && errorMessage) {
      this.announce(`${fieldName}验证失败：${errorMessage}`, 'assertive');
    } else if (isValid) {
      this.announce(`${fieldName}验证通过`, 'polite');
    }
  }

  /**
   * 数据加载状态的无障碍处理
   */
  handleLoadingState(isLoading, content = '') {
    if (isLoading) {
      this.announce(`正在加载${content}`, 'polite');
    } else {
      this.announce(`${content}加载完成`, 'polite');
    }
  }

  /**
   * 启用/禁用无障碍功能
   */
  toggle(enabled) {
    this.isEnabled = enabled;
  }

  /**
   * 获取无障碍使用报告
   */
  getUsageReport() {
    return {
      isEnabled: this.isEnabled,
      hasAccessibilityFeatures: this.hasAccessibilityFeatures,
      announcementCount: this.announcements.length,
      focusHistoryCount: this.focusHistory.length,
      recentAnnouncements: this.announcements.slice(-5),
      recentFocus: this.focusHistory.slice(-5)
    };
  }
}

// 创建全局实例
const accessibilityHelper = new AccessibilityHelper();

// 为Page和Component添加无障碍混入
const AccessibilityMixin = {
  // 页面无障碍增强
  onShow() {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const title = currentPage.data.title || this.data.navigationBarTitleText || '页面';
    
    accessibilityHelper.handlePageNavigation(
      pages.length > 1 ? pages[pages.length - 2].route : '',
      currentPage.route,
      title
    );
  },

  // 设置焦点的辅助方法
  setA11yFocus(elementId, options = {}) {
    accessibilityHelper.setFocus(elementId, options);
  },

  // 播报信息的辅助方法
  announce(text, priority = 'polite') {
    accessibilityHelper.announce(text, priority);
  },

  // 表单验证辅助方法
  validateA11y(fieldName, isValid, errorMessage) {
    accessibilityHelper.handleFormValidation(fieldName, isValid, errorMessage);
  },

  // 加载状态辅助方法
  setLoadingA11y(isLoading, content = '') {
    accessibilityHelper.handleLoadingState(isLoading, content);
  }
};

module.exports = {
  AccessibilityHelper,
  accessibilityHelper,
  AccessibilityMixin
};