/**
 * 增强版网络请求模块
 * 基于现有的request.js，添加新的日志系统和错误处理
 * 保持100%向下兼容，渐进式增强
 */

// 引入现有的request系统
const originalRequest = require('./request.js');

// 引入新的日志系统（仅在前端环境使用时需要处理）
let Logger = null;
try {
  // 在微信小程序环境中，这个require会失败，我们用console替代
  Logger = require('../backend/utils/logger').Logger;
} catch (e) {
  // 前端环境使用console，但格式化输出
  Logger = {
    info: (message, data) => console.log(`[INFO] ${message}`, data),
    warn: (message, data) => console.warn(`[WARN] ${message}`, data),
    error: (message, data) => console.error(`[ERROR] ${message}`, data),
    debug: (message, data) => console.log(`[DEBUG] ${message}`, data)
  };
}

/**
 * 增强的错误处理
 * @param {Error} error 错误对象
 * @param {string} context 错误上下文
 * @param {Object} options 选项
 */
function enhancedErrorHandler(error, context = 'unknown', options = {}) {
  // 记录详细错误信息
  Logger.error('前端网络请求错误', {
    context,
    error: error.message || error,
    stack: error.stack,
    statusCode: error.statusCode,
    timestamp: new Date().toISOString(),
    url: options.url,
    method: options.method
  });

  // 用户友好的错误提示
  let userMessage = '操作失败，请稍后重试';
  
  if (error.type === 'NETWORK_ERROR') {
    userMessage = '网络连接异常，请检查网络';
  } else if (error.type === 'TIMEOUT_ERROR') {
    userMessage = '请求超时，请稍后重试';
  } else if (error.message && error.message.length < 20) {
    userMessage = error.message;
  }

  // 使用原有的showError方法
  if (options.showUserError !== false) {
    originalRequest.showError(userMessage);
  }

  return error;
}

/**
 * 增强的请求日志
 * @param {string} url 请求URL
 * @param {string} method 请求方法
 * @param {Object} data 请求数据
 * @param {Object} response 响应数据
 */
function logRequest(url, method, data, response) {
  Logger.info('API请求记录', {
    url,
    method,
    requestData: data,
    responseStatus: response?.status || 'unknown',
    timestamp: new Date().toISOString()
  });
}

/**
 * 增强的成功处理
 * @param {Object} response 响应数据
 * @param {string} context 上下文
 */
function enhancedSuccessHandler(response, context) {
  Logger.info('API请求成功', {
    context,
    timestamp: new Date().toISOString()
  });
  return response;
}

/**
 * 包装原有的request方法，添加增强功能
 */
function enhancedRequest(options) {
  const startTime = Date.now();
  
  // 记录请求开始
  Logger.debug('开始API请求', {
    url: options.url,
    method: options.method,
    data: options.data
  });

  return originalRequest.request(options)
    .then(response => {
      // 记录成功
      const duration = Date.now() - startTime;
      Logger.info('API请求成功', {
        url: options.url,
        method: options.method,
        duration: `${duration}ms`,
        status: 'success'
      });
      
      return response;
    })
    .catch(error => {
      // 使用增强的错误处理
      const duration = Date.now() - startTime;
      const enhancedError = enhancedErrorHandler(error, options.url, {
        ...options,
        duration: `${duration}ms`
      });
      
      throw enhancedError;
    });
}

/**
 * 增强的GET请求
 */
function get(url, data = {}, options = {}) {
  return enhancedRequest({
    url,
    method: 'GET',
    data,
    showLoading: true,
    showError: true,
    ...options
  });
}

/**
 * 增强的POST请求
 */
function post(url, data = {}, options = {}) {
  return enhancedRequest({
    url,
    method: 'POST',
    data,
    showLoading: true,
    showError: true,
    ...options
  });
}

/**
 * 增强的PUT请求
 */
function put(url, data = {}, options = {}) {
  return enhancedRequest({
    url,
    method: 'PUT',
    data,
    showLoading: true,
    showError: true,
    ...options
  });
}

/**
 * 增强的DELETE请求
 */
function del(url, data = {}, options = {}) {
  return enhancedRequest({
    url,
    method: 'DELETE',
    data,
    showLoading: true,
    showError: true,
    ...options
  });
}

// 导出接口 - 保持与原有request.js完全兼容
module.exports = {
  // 保留所有原有接口
  ...originalRequest,
  
  // 增强的方法
  get,
  post,
  put,
  del,
  request: enhancedRequest,
  
  // 新增的工具方法
  enhancedErrorHandler,
  logRequest,
  enhancedSuccessHandler,
  Logger
};