/**
 * Shop商城模块性能优化工具
 * 符合微信小程序最佳实践
 */

// 图片懒加载管理器
class ImageLazyLoadManager {
  constructor() {
    this.observer = null;
    this.loadedImages = new Set();
    this.initObserver();
  }

  initObserver() {
    // 微信小程序支持IntersectionObserver
    this.observer = wx.createIntersectionObserver(null, {
      thresholds: [0.01]
    });
  }

  // 观察图片元素
  observe(selector, callback) {
    if (!this.observer) return;
    
    this.observer.relativeTo('.products-grid').observe(selector, (res) => {
      if (res.intersectionRatio > 0) {
        callback(res);
        this.observer.unobserve(selector);
      }
    });
  }

  // 清理观察器
  disconnect() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}

// 数据缓存管理器
class ShopCacheManager {
  constructor() {
    this.cache = new Map();
    this.expireTime = 5 * 60 * 1000; // 5分钟过期
  }

  // 设置缓存
  set(key, data, customExpire = null) {
    const expire = customExpire || this.expireTime;
    const item = {
      data,
      timestamp: Date.now(),
      expire
    };
    this.cache.set(key, item);
    
    // 同时存储到本地存储
    try {
      wx.setStorageSync(`shop_cache_${key}`, item);
    } catch (e) {
      console.warn('缓存写入失败:', e);
    }
  }

  // 获取缓存
  get(key) {
    let item = this.cache.get(key);
    
    // 如果内存中没有，尝试从本地存储获取
    if (!item) {
      try {
        item = wx.getStorageSync(`shop_cache_${key}`);
        if (item) {
          this.cache.set(key, item);
        }
      } catch (e) {
        console.warn('缓存读取失败:', e);
        return null;
      }
    }

    if (!item) return null;

    // 检查是否过期
    if (Date.now() - item.timestamp > item.expire) {
      this.remove(key);
      return null;
    }

    return item.data;
  }

  // 删除缓存
  remove(key) {
    this.cache.delete(key);
    try {
      wx.removeStorageSync(`shop_cache_${key}`);
    } catch (e) {
      console.warn('缓存删除失败:', e);
    }
  }

  // 清空所有缓存
  clear() {
    this.cache.clear();
    try {
      const info = wx.getStorageInfoSync();
      info.keys.forEach(key => {
        if (key.startsWith('shop_cache_')) {
          wx.removeStorageSync(key);
        }
      });
    } catch (e) {
      console.warn('缓存清空失败:', e);
    }
  }
}

// 商品列表虚拟滚动
class VirtualScrollManager {
  constructor(options = {}) {
    this.itemHeight = options.itemHeight || 400; // 每个商品卡片高度(rpx)
    this.containerHeight = options.containerHeight || 1200; // 容器高度(rpx)
    this.buffer = options.buffer || 2; // 缓冲区项目数
    this.visibleCount = Math.ceil(this.containerHeight / this.itemHeight) + this.buffer * 2;
  }

  // 计算可见区域的商品
  getVisibleItems(scrollTop, allItems) {
    const startIndex = Math.floor(scrollTop / this.itemHeight) - this.buffer;
    const endIndex = startIndex + this.visibleCount;
    
    const safeStartIndex = Math.max(0, startIndex);
    const safeEndIndex = Math.min(allItems.length, endIndex);
    
    return {
      visibleItems: allItems.slice(safeStartIndex, safeEndIndex),
      startIndex: safeStartIndex,
      offsetY: safeStartIndex * this.itemHeight
    };
  }
}

// 搜索防抖管理器
class SearchDebounceManager {
  constructor(delay = 300) {
    this.delay = delay;
    this.timer = null;
  }

  // 防抖搜索
  search(keyword, callback) {
    if (this.timer) {
      clearTimeout(this.timer);
    }
    
    this.timer = setTimeout(() => {
      callback(keyword);
    }, this.delay);
  }

  // 清除定时器
  clear() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }
}

// 商品数据预加载器
class ProductPreloader {
  constructor() {
    this.preloadQueue = [];
    this.preloading = false;
  }

  // 添加预加载任务
  addPreloadTask(url, priority = 'normal') {
    const task = { url, priority, timestamp: Date.now() };
    
    if (priority === 'high') {
      this.preloadQueue.unshift(task);
    } else {
      this.preloadQueue.push(task);
    }
    
    this.processQueue();
  }

  // 处理预加载队列
  async processQueue() {
    if (this.preloading || this.preloadQueue.length === 0) return;
    
    this.preloading = true;
    
    while (this.preloadQueue.length > 0) {
      const task = this.preloadQueue.shift();
      try {
        await this.preloadImage(task.url);
      } catch (e) {
        console.warn('图片预加载失败:', task.url, e);
      }
      
      // 控制预加载频率，避免影响主要功能
      await this.delay(50);
    }
    
    this.preloading = false;
  }

  // 预加载单张图片
  preloadImage(url) {
    return new Promise((resolve, reject) => {
      const image = wx.createImage();
      image.onload = resolve;
      image.onerror = reject;
      image.src = url;
    });
  }

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 性能监控器
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      pageLoadTime: 0,
      apiResponseTime: {},
      imageLoadTime: {},
      userInteractions: []
    };
  }

  // 记录页面加载时间
  recordPageLoad(startTime) {
    this.metrics.pageLoadTime = Date.now() - startTime;
  }

  // 记录API响应时间
  recordApiResponse(apiName, startTime) {
    const responseTime = Date.now() - startTime;
    this.metrics.apiResponseTime[apiName] = responseTime;
  }

  // 记录图片加载时间
  recordImageLoad(imageUrl, startTime) {
    const loadTime = Date.now() - startTime;
    this.metrics.imageLoadTime[imageUrl] = loadTime;
    
    if (loadTime > 3000) {
      console.warn('图片加载较慢:', imageUrl, loadTime + 'ms');
    }
  }

  // 记录用户交互
  recordUserInteraction(actionType, target) {
    this.metrics.userInteractions.push({
      actionType,
      target,
      timestamp: Date.now()
    });
  }

  // 获取性能报告
  getPerformanceReport() {
    return {
      ...this.metrics,
      summary: {
        avgApiResponse: this.calculateAverage(Object.values(this.metrics.apiResponseTime)),
        slowImages: Object.entries(this.metrics.imageLoadTime)
          .filter(([url, time]) => time > 2000)
          .map(([url, time]) => ({ url, time }))
      }
    };
  }

  calculateAverage(numbers) {
    if (numbers.length === 0) return 0;
    return numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
  }
}

// 导出工具类
module.exports = {
  ImageLazyLoadManager,
  ShopCacheManager,
  VirtualScrollManager,
  SearchDebounceManager,
  ProductPreloader,
  PerformanceMonitor
};