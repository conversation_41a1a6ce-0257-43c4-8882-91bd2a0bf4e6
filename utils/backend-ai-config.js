// utils/backend-ai-config.js
// 后台AI配置管理工具

/**
 * 后台AI配置管理器
 */
class BackendAIConfigManager {
  constructor() {
    this.configCache = null;
    this.cacheExpiry = 0;
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
  }

  /**
   * 从后台获取AI配置
   * @returns {Promise<Object>} AI配置对象
   */
  async fetchAIConfig() {
    try {
      // 尝试从后台API获取配置
      const response = await this.makeRequest('/api/v1/ai-config');

      if (response && response.success) {
        const configData = this.transformConfigData(response.data);
        this.configCache = configData;
        this.cacheExpiry = Date.now() + this.cacheTimeout;

        // 同时保存到本地存储作为备份
        wx.setStorageSync('backend_ai_config', {
          data: configData,
          timestamp: Date.now()
        });

        return configData;
      } else {
        throw new Error(response?.message || '获取AI配置失败');
      }
    } catch (error) {
      console.error('从后台获取AI配置失败:', error);

      // 尝试从本地存储获取备份配置
      return this.getLocalBackupConfig();
    }
  }

  /**
   * 获取本地备份配置
   * @returns {Object|null} 本地备份的配置
   */
  getLocalBackupConfig() {
    try {
      const backup = wx.getStorageSync('backend_ai_config');
      if (backup && backup.data) {
        // 检查备份是否过期（24小时）
        const backupAge = Date.now() - backup.timestamp;
        if (backupAge < 24 * 60 * 60 * 1000) {
          return backup.data;
        }
      }
    } catch (error) {
      console.error('获取本地备份配置失败:', error);
    }
    
    return null;
  }

  /**
   * 获取AI配置（带缓存）
   * @returns {Promise<Object>} AI配置对象
   */
  async getAIConfig() {
    // 检查缓存是否有效
    if (this.configCache && Date.now() < this.cacheExpiry) {
      return this.configCache;
    }

    // 缓存过期或不存在，重新获取
    return await this.fetchAIConfig();
  }

  /**
   * 获取特定服务商的API密钥
   * @param {string} provider - 服务商名称
   * @returns {Promise<string|null>} API密钥
   */
  async getProviderApiKey(provider) {
    try {
      const config = await this.getAIConfig();
      
      if (config && config.providers && config.providers[provider]) {
        return config.providers[provider].apiKey;
      }
    } catch (error) {
      console.error(`获取${provider}的API密钥失败:`, error);
    }
    
    return null;
  }

  /**
   * 获取AI服务配置
   * @param {string} provider - 服务商名称
   * @returns {Promise<Object|null>} 服务配置
   */
  async getProviderConfig(provider) {
    try {
      const config = await this.getAIConfig();
      
      if (config && config.providers && config.providers[provider]) {
        return config.providers[provider];
      }
    } catch (error) {
      console.error(`获取${provider}的配置失败:`, error);
    }
    
    return null;
  }

  /**
   * 检查AI服务是否启用
   * @param {string} provider - 服务商名称
   * @returns {Promise<boolean>} 是否启用
   */
  async isProviderEnabled(provider) {
    try {
      const config = await this.getProviderConfig(provider);
      return config && config.enabled === true;
    } catch (error) {
      console.error(`检查${provider}启用状态失败:`, error);
      return false;
    }
  }

  /**
   * 获取默认服务商
   * @returns {Promise<string|null>} 默认服务商名称
   */
  async getDefaultProvider() {
    try {
      const config = await this.getAIConfig();
      return config && config.defaultProvider ? config.defaultProvider : null;
    } catch (error) {
      console.error('获取默认服务商失败:', error);
      return null;
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.configCache = null;
    this.cacheExpiry = 0;
  }

  /**
   * 刷新配置
   * @returns {Promise<Object>} 最新配置
   */
  async refreshConfig() {
    this.clearCache();
    return await this.fetchAIConfig();
  }

  /**
   * 发起HTTP请求
   * @param {string} url - 请求URL
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应数据
   */
  async makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
      const app = getApp();
      const baseUrl = app?.globalData?.baseUrl || 'http://localhost:3000';
      const token = wx.getStorageSync('token');

      wx.request({
        url: baseUrl + url,
        method: options.method || 'GET',
        data: options.data,
        header: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          ...options.headers
        },
        success: (res) => {
          resolve(res.data);
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  }

  /**
   * 转换后台配置数据格式
   * @param {Array} configArray - 后台配置数组
   * @returns {Object} 转换后的配置对象
   */
  transformConfigData(configArray) {
    const result = {
      defaultProvider: null,
      providers: {}
    };

    configArray.forEach(config => {
      if (config.enabled) {
        result.providers[config.provider] = {
          enabled: config.enabled,
          apiKey: config.apiKey, // 注意：这里的apiKey已经是脱敏的
          baseUrl: config.baseUrl,
          models: config.models || {},
          maxTokens: config.maxTokens,
          temperature: config.temperature
        };

        if (config.isDefault) {
          result.defaultProvider = config.provider;
        }
      }
    });

    return result;
  }

  /**
   * 获取AI服务使用统计
   * @returns {Promise<Object|null>} 使用统计
   */
  async getUsageStats() {
    try {
      // 暂时返回null，后续可以实现真实的API调用
      return null;
    } catch (error) {
      console.error('获取AI使用统计失败:', error);
      return null;
    }
  }

  /**
   * 上报AI服务使用情况
   * @param {Object} usageData - 使用数据
   * @returns {Promise<boolean>} 是否成功
   */
  async reportUsage(usageData) {
    try {
      // 暂时只记录日志，后续可以实现真实的API调用
      console.log('上报AI使用情况:', {
        provider: usageData.provider,
        model: usageData.model,
        tokens: usageData.tokens,
        cost: usageData.cost,
        success: usageData.success,
        timestamp: new Date().toISOString()
      });
      return true;
    } catch (error) {
      console.error('上报AI使用情况失败:', error);
      return false;
    }
  }
}

// 创建单例实例
const backendAIConfigManager = new BackendAIConfigManager();

/**
 * 获取后台配置的API密钥（供ai-config.js使用）
 * @param {string} providerName - 服务商名称
 * @returns {Promise<string|null>} API密钥
 */
async function getBackendApiKey(providerName) {
  const providerMap = {
    'SILICONFLOW': 'siliconflow',
    'ZHIPU': 'zhipu',
    'OPENAI': 'openai',
    'QIANWEN': 'qianwen'
  };
  
  const backendProviderKey = providerMap[providerName];
  if (backendProviderKey) {
    return await backendAIConfigManager.getProviderApiKey(backendProviderKey);
  }
  
  return null;
}

module.exports = {
  BackendAIConfigManager,
  backendAIConfigManager,
  getBackendApiKey
};
