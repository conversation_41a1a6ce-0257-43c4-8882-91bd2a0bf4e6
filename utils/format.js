// utils/format.js
// 通用格式化工具函数

/**
 * 格式化金额
 * @param {number} amount 金额
 * @param {string} currency 货币符号
 * @returns {string} 格式化后的金额
 */
function formatCurrency(amount, currency = '¥') {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return currency + '0.00';
  }
  
  return currency + Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
}

/**
 * 格式化日期
 * @param {Date|string} date 日期对象或日期字符串
 * @param {string} format 格式化模式
 * @returns {string} 格式化后的日期
 */
function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return '-';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return '-';
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hour = String(d.getHours()).padStart(2, '0');
  const minute = String(d.getMinutes()).padStart(2, '0');
  const second = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second);
}

/**
 * 格式化百分比
 * @param {number} value 数值
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的百分比
 */
function formatPercent(value, decimals = 2) {
  if (value === null || value === undefined || isNaN(value)) {
    return '0.00%';
  }
  
  return Number(value).toFixed(decimals) + '%';
}

/**
 * 格式化数量
 * @param {number} number 数量
 * @param {string} unit 单位
 * @returns {string} 格式化后的数量
 */
function formatNumber(number, unit = '') {
  if (number === null || number === undefined || isNaN(number)) {
    return '0' + unit;
  }
  
  return Number(number).toLocaleString('zh-CN') + unit;
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的文件大小
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化时间段
 * @param {Date} startDate 开始日期
 * @param {Date} endDate 结束日期
 * @returns {string} 格式化后的时间段
 */
function formatDateRange(startDate, endDate) {
  if (!startDate || !endDate) return '-';
  
  const start = formatDate(startDate, 'YYYY-MM-DD');
  const end = formatDate(endDate, 'YYYY-MM-DD');
  
  return `${start} 至 ${end}`;
}

/**
 * 格式化电话号码
 * @param {string} phone 电话号码
 * @returns {string} 格式化后的电话号码
 */
function formatPhone(phone) {
  if (!phone) return '-';
  
  // 移除非数字字符
  const cleaned = phone.replace(/\D/g, '');
  
  // 手机号格式化
  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3');
  }
  
  // 固定电话格式化
  if (cleaned.length === 10) {
    return cleaned.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3');
  }
  
  return phone;
}

/**
 * 格式化状态
 * @param {string} status 状态值
 * @param {Object} statusMap 状态映射表
 * @returns {string} 格式化后的状态
 */
function formatStatus(status, statusMap = {}) {
  return statusMap[status] || status || '-';
}

/**
 * 格式化时间距现在多久
 * @param {Date|string} date 日期
 * @returns {string} 相对时间
 */
function formatTimeAgo(date) {
  if (!date) return '-';
  
  const now = new Date();
  const target = new Date(date);
  const diffMs = now - target;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  
  if (diffSec < 60) {
    return '刚刚';
  } else if (diffMin < 60) {
    return `${diffMin}分钟前`;
  } else if (diffHour < 24) {
    return `${diffHour}小时前`;
  } else if (diffDay < 30) {
    return `${diffDay}天前`;
  } else {
    return formatDate(date, 'YYYY-MM-DD');
  }
}

/**
 * 格式化中文数字
 * @param {number} num 数字
 * @returns {string} 中文数字
 */
function formatChineseNumber(num) {
  const cnNums = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  const cnUnits = ['', '十', '百', '千', '万'];
  
  if (num === 0) return '零';
  if (num < 10) return cnNums[num];
  if (num < 100) {
    const tens = Math.floor(num / 10);
    const ones = num % 10;
    if (tens === 1) {
      return '十' + (ones > 0 ? cnNums[ones] : '');
    }
    return cnNums[tens] + '十' + (ones > 0 ? cnNums[ones] : '');
  }
  
  // 复杂数字简化处理
  return num.toString();
}

module.exports = {
  formatCurrency,
  formatDate,
  formatPercent,
  formatNumber,
  formatFileSize,
  formatDateRange,
  formatPhone,
  formatStatus,
  formatTimeAgo,
  formatChineseNumber,

  // 从util.js合并的工具函数
  debounce,
  throttle,
  deepClone,
  validator
};

/**
 * 防抖函数
 * @param {Function} fn 执行函数
 * @param {number} delay 延迟时间
 */
function debounce(fn, delay = 300) {
  let timer = null;
  return function() {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, arguments);
      timer = null;
    }, delay);
  };
}

/**
 * 节流函数
 * @param {Function} fn 执行函数
 * @param {number} delay 延迟时间
 */
function throttle(fn, delay = 300) {
  let timer = null;
  return function() {
    if (!timer) {
      timer = setTimeout(() => {
        fn.apply(this, arguments);
        timer = null;
      }, delay);
    }
  };
}

/**
 * 深度克隆对象
 * @param {any} obj 要克隆的对象
 */
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if (typeof obj === 'object') {
    const cloned = {};
    Object.keys(obj).forEach(key => {
      cloned[key] = deepClone(obj[key]);
    });
    return cloned;
  }
}

/**
 * 数据验证工具
 */
const validator = {
  // 验证手机号
  isMobile: (value) => /^1[3-9]\d{9}$/.test(value),

  // 验证邮箱
  isEmail: (value) => /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value),

  // 验证身份证号
  isIdCard: (value) => /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value),

  // 验证是否为空
  isEmpty: (value) => value === null || value === undefined || value === '' || (Array.isArray(value) && value.length === 0)
};