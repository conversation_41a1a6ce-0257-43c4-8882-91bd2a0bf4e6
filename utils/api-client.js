/**
 * 类型安全的API客户端
 * 基于express-zod-api生成的类型定义，为小程序提供类型安全的API调用
 */

const { API_BASE_URLS, API_ENDPOINTS } = require('../constants/api.constants.js');
const { UI } = require('../constants/ui.constants.js');
const tenantConfig = require('./tenant-config.js');

/**
 * API响应状态枚举
 */
const ApiStatus = {
  SUCCESS: 'success',
  ERROR: 'error'
};

/**
 * HTTP状态码
 */
const HttpStatus = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500
};

/**
 * API错误类
 */
class ApiError extends Error {
  constructor(message, status, code, issues = []) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.code = code;
    this.issues = issues;
  }
}

/**
 * 类型安全的API客户端类
 */
class TypeSafeApiClient {
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || this.getBaseUrl();
    this.timeout = options.timeout || 10000;
    this.headers = options.headers || {};
    this.retries = options.retries || 2;
    this.retryDelay = options.retryDelay || 1000;
    
    // 绑定方法
    this.request = this.request.bind(this);
    this.get = this.get.bind(this);
    this.post = this.post.bind(this);
    this.put = this.put.bind(this);
    this.patch = this.patch.bind(this);
    this.delete = this.delete.bind(this);
  }

  /**
   * 获取基础URL
   */
  getBaseUrl() {
    try {
      const config = tenantConfig.getTenantConfig();
      if (config && config.apiBaseUrl) {
        return config.apiBaseUrl;
      }
      
      const app = getApp();
      if (app && app.globalData && app.globalData.baseUrl) {
        return app.globalData.baseUrl;
      }
      
      return API_BASE_URLS.DEVELOPMENT;
    } catch (e) {
      console.warn('获取baseUrl失败，使用默认值');
      return API_BASE_URLS.DEVELOPMENT;
    }
  }

  /**
   * 获取请求头
   */
  getHeaders(customHeaders = {}) {
    const headers = {
      'Content-Type': 'application/json',
      ...this.headers,
      ...customHeaders
    };

    // 添加认证头
    try {
      const token = wx.getStorageSync('token');
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    } catch (e) {
      console.warn('获取token失败');
    }

    // 添加租户头
    try {
      const config = tenantConfig.getTenantConfig();
      if (config && config.tenantCode) {
        headers['X-Tenant-Code'] = config.tenantCode;
      }
    } catch (e) {
      console.warn('获取租户信息失败');
    }

    return headers;
  }

  /**
   * 构建完整URL
   */
  buildUrl(path, params = {}) {
    let url = this.baseUrl + path;
    
    // 替换路径参数
    Object.keys(params).forEach(key => {
      url = url.replace(`:${key}`, encodeURIComponent(params[key]));
    });
    
    return url;
  }

  /**
   * 构建查询字符串
   */
  buildQuery(query = {}) {
    const params = new URLSearchParams();
    
    Object.keys(query).forEach(key => {
      const value = query[key];
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(item => params.append(key, String(item)));
        } else if (typeof value === 'object') {
          Object.keys(value).forEach(subKey => {
            if (value[subKey] !== undefined && value[subKey] !== null) {
              params.append(`${key}[${subKey}]`, String(value[subKey]));
            }
          });
        } else {
          params.append(key, String(value));
        }
      }
    });
    
    return params.toString();
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 执行HTTP请求
   */
  async request(method, path, options = {}) {
    const {
      params = {},
      query = {},
      body,
      headers: customHeaders = {},
      timeout = this.timeout,
      retries = this.retries
    } = options;

    const url = this.buildUrl(path, params);
    const queryString = this.buildQuery(query);
    const finalUrl = queryString ? `${url}?${queryString}` : url;
    
    const headers = this.getHeaders(customHeaders);
    
    const requestOptions = {
      url: finalUrl,
      method: method.toUpperCase(),
      header: headers,
      timeout,
      dataType: 'json',
      responseType: 'text'
    };

    if (body && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
      requestOptions.data = body;
    }

    // 请求重试逻辑
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        console.log('发起请求: ' + finalUrl + (attempt > 0 ? ' (重试 ' + attempt + ')' : ''));
        
        const response = await new Promise((resolve, reject) => {
          wx.request({
            ...requestOptions,
            success: resolve,
            fail: reject
          });
        });

        return this.handleResponse(response);
        
      } catch (error) {
        console.error(`[API] 请求失败 (尝试 ${attempt + 1}/${retries + 1}):`, error);
        
        if (attempt === retries) {
          throw new ApiError(
            '网络请求失败，请检查网络连接',
            'network_error',
            0,
            [{ message: error.errMsg || '网络错误' }]
          );
        }
        
        // 等待后重试
        await this.delay(this.retryDelay * (attempt + 1));
      }
    }
  }

  /**
   * 处理响应
   */
  handleResponse(response) {
    const { statusCode, data } = response;
    
    // 解析响应数据
    let parsedData;
    try {
      parsedData = typeof data === 'string' ? JSON.parse(data) : data;
    } catch (e) {
      throw new ApiError(
        '响应数据格式错误',
        'parse_error',
        statusCode,
        [{ message: '无法解析响应数据' }]
      );
    }

    // 检查HTTP状态码
    if (statusCode >= 200 && statusCode < 300) {
      // 检查业务状态
      if (parsedData.status === ApiStatus.SUCCESS) {
        return parsedData.data;
      } else if (parsedData.status === ApiStatus.ERROR) {
        throw new ApiError(
          parsedData.message || '业务操作失败',
          'business_error',
          parsedData.code || statusCode,
          parsedData.issues || []
        );
      } else {
        // 向下兼容旧版API格式
        return parsedData;
      }
    } else if (statusCode >= 400 && statusCode < 500) {
      throw new ApiError(
        parsedData.message || '请求参数错误',
        'client_error',
        statusCode,
        parsedData.issues || []
      );
    } else if (statusCode >= 500) {
      throw new ApiError(
        parsedData.message || '服务器内部错误',
        'server_error',
        statusCode,
        parsedData.issues || []
      );
    } else {
      throw new ApiError(
        '未知错误',
        'unknown_error',
        statusCode,
        []
      );
    }
  }

  /**
   * GET请求
   */
  async get(path, options = {}) {
    return this.request('GET', path, options);
  }

  /**
   * POST请求
   */
  async post(path, body, options = {}) {
    return this.request('POST', path, { ...options, body });
  }

  /**
   * PUT请求
   */
  async put(path, body, options = {}) {
    return this.request('PUT', path, { ...options, body });
  }

  /**
   * PATCH请求
   */
  async patch(path, body, options = {}) {
    return this.request('PATCH', path, { ...options, body });
  }

  /**
   * DELETE请求
   */
  async delete(path, options = {}) {
    return this.request('DELETE', path, options);
  }
}

/**
 * 鹅群管理API
 */
class FlockApi {
  constructor(client) {
    this.client = client;
  }

  /**
   * 获取鹅群列表
   */
  async getList(params = {}) {
    return this.client.get('/v2/flocks', { query: params });
  }

  /**
   * 创建鹅群
   */
  async create(data) {
    return this.client.post('/v2/flocks/create', data);
  }

  /**
   * 获取鹅群详情
   */
  async getById(id) {
    return this.client.get('/v2/flocks/:id', { params: { id } });
  }

  /**
   * 更新鹅群
   */
  async update(id, data) {
    return this.client.patch('/v2/flocks/:id/update', data, { params: { id } });
  }

  /**
   * 删除鹅群
   */
  async delete(id) {
    return this.client.delete('/v2/flocks/:id/delete', { params: { id } });
  }
}

/**
 * 健康管理API
 */
class HealthApi {
  constructor(client) {
    this.client = client;
  }

  /**
   * 获取健康记录列表
   */
  async getRecords(params = {}) {
    return this.client.get('/v2/health/records', { query: params });
  }

  /**
   * 创建健康记录
   */
  async createRecord(data) {
    return this.client.post('/v2/health/records/create', data);
  }

  /**
   * 获取健康记录详情
   */
  async getRecordById(id) {
    return this.client.get('/v2/health/records/:id', { params: { id } });
  }

  /**
   * 更新健康记录
   */
  async updateRecord(id, data) {
    return this.client.patch('/v2/health/records/:id/update', data, { params: { id } });
  }

  /**
   * 删除健康记录
   */
  async deleteRecord(id) {
    return this.client.delete('/v2/health/records/:id/delete', { params: { id } });
  }

  /**
   * 获取健康统计
   */
  async getStats(params = {}) {
    return this.client.get('/v2/health/stats', { query: params });
  }
}

/**
 * 生产管理API
 */
class ProductionApi {
  constructor(client) {
    this.client = client;
  }

  /**
   * 获取生产记录列表
   */
  async getRecords(params = {}) {
    return this.client.get('/v2/production/records', { query: params });
  }

  /**
   * 创建生产记录
   */
  async createRecord(data) {
    return this.client.post('/v2/production/records/create', data);
  }

  /**
   * 获取生产记录详情
   */
  async getRecordById(id) {
    return this.client.get('/v2/production/records/:id', { params: { id } });
  }

  /**
   * 更新生产记录
   */
  async updateRecord(id, data) {
    return this.client.patch('/v2/production/records/:id/update', data, { params: { id } });
  }

  /**
   * 删除生产记录
   */
  async deleteRecord(id) {
    return this.client.delete('/v2/production/records/:id/delete', { params: { id } });
  }

  /**
   * 获取生产统计
   */
  async getStats(params = {}) {
    return this.client.get('/v2/production/stats', { query: params });
  }

  /**
   * 获取生产趋势
   */
  async getTrends(params = {}) {
    return this.client.get('/v2/production/trends', { query: params });
  }
}

/**
 * 完整的API客户端
 */
class ApiClient {
  constructor(options = {}) {
    this.client = new TypeSafeApiClient(options);
    
    // 初始化各个API模块
    this.flock = new FlockApi(this.client);
    this.health = new HealthApi(this.client);
    this.production = new ProductionApi(this.client);
  }

  /**
   * 健康检查
   */
  async healthCheck() {
    return this.client.get('/health');
  }

  /**
   * 获取原始客户端（用于自定义请求）
   */
  getRawClient() {
    return this.client;
  }

  /**
   * 设置认证令牌
   */
  setToken(token) {
    this.client.headers['Authorization'] = `Bearer ${token}`;
  }

  /**
   * 移除认证令牌
   */
  removeToken() {
    delete this.client.headers['Authorization'];
  }

  /**
   * 设置租户代码
   */
  setTenantCode(tenantCode) {
    this.client.headers['X-Tenant-Code'] = tenantCode;
  }

  /**
   * 移除租户代码
   */
  removeTenantCode() {
    delete this.client.headers['X-Tenant-Code'];
  }
}

// 创建默认API客户端实例
const apiClient = new ApiClient();

/**
 * 创建新的API客户端实例
 */
function createApiClient(options = {}) {
  return new ApiClient(options);
}

/**
 * 错误处理辅助函数
 */
function handleApiError(error, showToast = true) {
  console.error('[API] 请求错误:', error);
  
  let message = '操作失败，请稍后重试';
  
  if (error instanceof ApiError) {
    switch (error.status) {
      case 'network_error':
        message = '网络连接失败，请检查网络';
        break;
      case 'client_error':
        message = error.message || '请求参数错误';
        break;
      case 'server_error':
        message = '服务器错误，请稍后重试';
        break;
      case 'business_error':
        message = error.message || '操作失败';
        break;
      default:
        message = error.message || '未知错误';
    }
  }
  
  if (showToast) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }
  
  return message;
}

/**
 * 创建带有统一错误处理的API调用包装器
 */
function withErrorHandling(apiCall, options = {}) {
  return async (...args) => {
    try {
      return await apiCall(...args);
    } catch (error) {
      const message = handleApiError(error, options.showToast !== false);
      
      if (options.throwError !== false) {
        throw error;
      }
      
      return { error: true, message };
    }
  };
}

// 导出所有内容
module.exports = {
  // 类
  ApiClient,
  TypeSafeApiClient,
  FlockApi,
  HealthApi,
  ProductionApi,
  ApiError,
  
  // 实例
  apiClient,
  
  // 工厂函数
  createApiClient,
  
  // 工具函数
  handleApiError,
  withErrorHandling,
  
  // 常量
  ApiStatus,
  HttpStatus
};