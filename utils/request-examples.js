/**
 * 网络请求使用示例
 * 展示如何使用优化后的网络请求功能
 */

const { request, get, post, showLoading, hideLoading, showError, showSuccess } = require('./request.js');

// 示例1: 基础请求（不显示loading）
function basicRequest() {
  return get('/health/records')
    .then(data => {
      return data;
    })
    .catch(error => {
      console.error('请求失败:', error);
      throw error;
    });
}

// 示例2: 带loading的请求
function requestWithLoading() {
  return get('/health/records', {}, {
    showLoading: true,
    loadingText: '正在加载健康记录...'
  })
    .then(data => {
      showSuccess('加载成功');
      return data;
    })
    .catch(error => {
      // 错误已经在request中处理，这里可以做额外处理
      console.error('加载失败:', error);
      throw error;
    });
}

// 示例3: 启用重试的请求
function requestWithRetry() {
  return request({
    url: '/production/environment',
    method: 'GET',
    showLoading: true,
    loadingText: '获取环境数据...',
    enableRetry: true,
    maxRetries: 3
  })
    .then(data => {
      showSuccess('数据获取成功');
      return data;
    })
    .catch(error => {
      console.error('获取环境数据失败:', error);
      throw error;
    });
}

// 示例4: 不显示错误提示的请求
function silentRequest() {
  return request({
    url: '/auth/userinfo',
    method: 'GET',
    showError: false  // 不显示错误提示
  })
    .then(data => {
      return data;
    })
    .catch(error => {
      // 手动处理错误
      if (error.type === 'AUTH_ERROR') {
      } else {
        showError('获取用户信息失败');
      }
      throw error;
    });
}

// 示例5: 表单提交
function submitForm(formData) {
  return post('/health/records', formData, {
    showLoading: true,
    loadingText: '正在保存...',
    enableRetry: false  // 表单提交通常不重试
  })
    .then(data => {
      showSuccess('保存成功');
      return data;
    })
    .catch(error => {
      // 根据错误类型给出不同提示
      if (error.type === 'BUSINESS_ERROR') {
        showError(error.message || '保存失败，请检查输入信息');
      } else if (error.type === 'NETWORK_ERROR') {
        showError('网络异常，请检查网络连接后重试');
      }
      throw error;
    });
}

// 示例6: 文件上传
function uploadFile(filePath) {
  // 显示上传进度
  showLoading('正在上传...', true);
  
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: 'http://localhost:3001/api/v1/upload',
      filePath: filePath,
      name: 'file',
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('token')
      },
      success: (res) => {
        hideLoading();
        try {
          const data = JSON.parse(res.data);
          if (data.success) {
            showSuccess('上传成功');
            resolve(data);
          } else {
            showError(data.message || '上传失败');
            reject(new Error(data.message || '上传失败'));
          }
        } catch (e) {
          showError('上传响应解析失败');
          reject(e);
        }
      },
      fail: (error) => {
        hideLoading();
        showError('上传失败，请检查网络连接');
        reject(error);
      }
    });
  });
}

// 示例7: 批量请求
async function batchRequests() {
  showLoading('正在加载数据...', true);
  
  try {
    // 并发请求多个接口
    const [healthData, productionData, profileData] = await Promise.all([
      get('/health/records', {}, { showError: false }),
      get('/production/records', {}, { showError: false }),
      get('/profile/settings', {}, { showError: false })
    ]);
    
    hideLoading();
    showSuccess('数据加载完成');
    
    return {
      health: healthData,
      production: productionData,
      profile: profileData
    };
  } catch (error) {
    hideLoading();
    showError('数据加载失败，请稍后重试');
    throw error;
  }
}

// 示例8: 页面数据加载模式
function loadPageData(pageInstance) {
  // 设置页面loading状态
  pageInstance.setData({
    loading: true,
    error: null
  });
  
  return get('/home/<USER>', {}, {
    showLoading: false,  // 使用页面自己的loading状态
    showError: false     // 使用页面自己的错误处理
  })
    .then(data => {
      pageInstance.setData({
        loading: false,
        data: data
      });
      return data;
    })
    .catch(error => {
      pageInstance.setData({
        loading: false,
        error: error.message || '加载失败'
      });
      throw error;
    });
}

// 示例9: 搜索防抖请求
let searchTimer = null;
function searchWithDebounce(keyword, callback) {
  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer);
  }
  
  // 设置新的定时器
  searchTimer = setTimeout(() => {
    get('/search', { keyword }, {
      showLoading: false,
      showError: false
    })
      .then(data => {
        callback(null, data);
      })
      .catch(error => {
        callback(error, null);
      });
  }, 300); // 300ms防抖
}

// 示例10: 错误重试提示
function requestWithRetryPrompt(url, options = {}) {
  return request({
    ...options,
    url,
    enableRetry: false,  // 先不自动重试
    showError: false     // 不自动显示错误
  })
    .catch(error => {
      // 如果是网络错误，询问用户是否重试
      if (error.type === 'NETWORK_ERROR' || error.type === 'TIMEOUT_ERROR') {
        return new Promise((resolve, reject) => {
          wx.showModal({
            title: '网络异常',
            content: '网络连接失败，是否重试？',
            confirmText: '重试',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                // 用户选择重试
                requestWithRetryPrompt(url, options)
                  .then(resolve)
                  .catch(reject);
              } else {
                reject(error);
              }
            }
          });
        });
      } else {
        // 其他错误直接显示
        showError(error.message);
        throw error;
      }
    });
}

module.exports = {
  basicRequest,
  requestWithLoading,
  requestWithRetry,
  silentRequest,
  submitForm,
  uploadFile,
  batchRequests,
  loadPageData,
  searchWithDebounce,
  requestWithRetryPrompt
};
