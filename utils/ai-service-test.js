/**
 * AI服务测试文件
 * 用于测试AI服务的各种功能
 */

const { callAIService, callImageRecognition, batchAIRequests } = require('./ai-service.js');

/**
 * 测试健康诊断功能
 */
async function testHealthDiagnosis() {
  
  const symptoms = `
我的鹅出现以下症状：
1. 精神萎靡，不爱活动
2. 食欲减退，进食量明显下降
3. 拉稀，粪便呈黄绿色
4. 眼睛有分泌物
5. 体温似乎偏高

请帮忙诊断可能的疾病。
  `;

  try {
    const result = await callAIService('HEALTH_DIAGNOSIS', symptoms);
    
    if (result.success) {
      + '...');
    } else {
    }
  } catch (error) {
  }
}

/**
 * 测试知识问答功能
 */
async function testKnowledgeQA() {
  
  const question = '鹅的最佳饲养密度是多少？如何根据不同生长阶段调整饲养密度？';

  try {
    const result = await callAIService('KNOWLEDGE_QA', question);
    
    if (result.success) {
      + '...');
    } else {
    }
  } catch (error) {
  }
}

/**
 * 测试财务分析功能
 */
async function testFinanceAnalysis() {
  
  const financeData = `
本月财务数据：
总收入：¥45,000
- 鹅只销售：¥35,000
- 鹅蛋销售：¥8,000
- 其他收入：¥2,000

总支出：¥28,000
- 饲料费用：¥15,000
- 药品费用：¥3,000
- 人工成本：¥6,000
- 设备维护：¥2,000
- 其他支出：¥2,000

净利润：¥17,000
利润率：37.8%

请分析这个月的财务状况并给出建议。
  `;

  try {
    const result = await callAIService('FINANCE_ANALYSIS', financeData);
    
    if (result.success) {
      + '...');
    } else {
    }
  } catch (error) {
  }
}

/**
 * 测试内容推荐功能
 */
async function testContentRecommendation() {
  
  const userProfile = `
用户最近关注的内容：
- 查看了小鹅瘟防治文章
- 搜索了饲料配方相关内容
- 关注了鹅舍建设话题

请推荐相关的养殖知识内容。
  `;

  try {
    const result = await callAIService('CONTENT_RECOMMENDATION', userProfile);
    
    if (result.success) {
      + '...');
    } else {
    }
  } catch (error) {
  }
}

/**
 * 测试批量请求功能
 */
async function testBatchRequests() {
  
  const requests = [
    {
      id: 'req1',
      scenario: 'KNOWLEDGE_QA',
      message: '鹅的正常体温是多少？',
      options: { maxTokens: 500 }
    },
    {
      id: 'req2',
      scenario: 'KNOWLEDGE_QA',
      message: '如何预防鹅的常见疾病？',
      options: { maxTokens: 500 }
    },
    {
      id: 'req3',
      scenario: 'KNOWLEDGE_QA',
      message: '鹅蛋的营养价值如何？',
      options: { maxTokens: 500 }
    }
  ];

  try {
    const results = await batchAIRequests(requests);
    
    results.forEach((result, index) => {
      if (result.success) {
        }...`);
      } else {
      }
    });
  } catch (error) {
  }
}

/**
 * 测试图像识别功能（模拟）
 */
async function testImageRecognition() {
  
  // 模拟base64图像数据（实际使用时需要真实的图像数据）
  const mockImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
  const description = '这是一只鹅的照片，请分析它的健康状况';

  try {
    // 注意：这里会失败，因为我们使用的是模拟数据
    const result = await callImageRecognition(mockImageBase64, description);
    
    if (result.success) {
      + '...');
    } else {
    }
  } catch (error) {
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  
  const startTime = Date.now();
  
  try {
    await testHealthDiagnosis();
    await testKnowledgeQA();
    await testFinanceAnalysis();
    await testContentRecommendation();
    await testBatchRequests();
    await testImageRecognition();
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    }秒`);
    
  } catch (error) {
  }
}

/**
 * 测试配置验证
 */
function testConfiguration() {
  
  try {
    const { getAIConfig, validateApiKey, getAvailableScenarios } = require('./ai-config.js');
    
    // 测试场景配置
    const scenarios = getAvailableScenarios();
    
    // 测试每个场景的配置
    scenarios.forEach(scenario => {
      try {
        const config = getAIConfig(scenario);
        
        // 验证API密钥格式
        const isValidKey = validateApiKey(config.provider.name.toUpperCase(), config.apiKey);
        
      } catch (error) {
      }
    });
    
  } catch (error) {
  }
}

// 如果直接运行此文件，执行所有测试
if (typeof module !== 'undefined' && require.main === module) {
  
  testConfiguration();
  runAllTests();
}

module.exports = {
  testHealthDiagnosis,
  testKnowledgeQA,
  testFinanceAnalysis,
  testContentRecommendation,
  testBatchRequests,
  testImageRecognition,
  runAllTests,
  testConfiguration
};
