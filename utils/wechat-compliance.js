/**
 * 微信小程序规范合规工具
 * 确保所有功能符合微信小程序最佳实践和审核要求
 */

// 微信小程序API安全封装
class WeChatAPIWrapper {
  
  // 安全的网络请求
  static request(options) {
    return new Promise((resolve, reject) => {
      // 检查网络状态
      wx.getNetworkType({
        success: (res) => {
          if (res.networkType === 'none') {
            reject(new Error('网络连接不可用'));
            return;
          }
          
          // 添加请求头和超时设置
          const requestOptions = {
            ...options,
            timeout: options.timeout || 10000,
            header: {
              'content-type': 'application/json',
              ...options.header
            },
            success: (res) => {
              if (res.statusCode >= 200 && res.statusCode < 300) {
                resolve(res.data);
              } else {
                reject(new Error(`请求失败: ${res.statusCode}`));
              }
            },
            fail: reject
          };
          
          wx.request(requestOptions);
        },
        fail: reject
      });
    });
  }

  // 安全的图片预览
  static previewImage(options) {
    const { current, urls } = options;
    
    // 过滤有效的图片URL
    const validUrls = urls.filter(url => 
      url && (url.startsWith('http') || url.startsWith('https') || url.startsWith('/'))
    );
    
    if (validUrls.length === 0) {
      wx.showToast({
        title: '没有可预览的图片',
        icon: 'none'
      });
      return;
    }

    wx.previewImage({
      current: validUrls.includes(current) ? current : validUrls[0],
      urls: validUrls
    });
  }

  // 安全的分享
  static shareProduct(productInfo) {
    return {
      title: productInfo.title || '智慧养鹅商城',
      path: `/packages/shop/goods-detail?id=${productInfo.id}`,
      imageUrl: productInfo.image || '/images/share-default.jpg'
    };
  }

  // 安全的支付调用
  static requestPayment(paymentData) {
    return new Promise((resolve, reject) => {
      // 验证支付参数
      const requiredFields = ['timeStamp', 'nonceStr', 'package', 'signType', 'paySign'];
      const missingFields = requiredFields.filter(field => !paymentData[field]);
      
      if (missingFields.length > 0) {
        reject(new Error(`缺少支付参数: ${missingFields.join(', ')}`));
        return;
      }

      wx.requestPayment({
        ...paymentData,
        success: resolve,
        fail: (err) => {
          if (err.errMsg.includes('cancel')) {
            reject(new Error('用户取消支付'));
          } else {
            reject(new Error('支付失败'));
          }
        }
      });
    });
  }

  // 安全的本地存储
  static setStorage(key, data) {
    try {
      // 检查数据大小（微信小程序本地存储限制10MB）
      const dataStr = JSON.stringify(data);
      if (dataStr.length > 1024 * 1024) { // 1MB警告
        console.warn('存储数据较大:', dataStr.length / 1024, 'KB');
      }
      
      wx.setStorageSync(key, data);
    } catch (e) {
      console.error('存储失败:', e);
      throw new Error('数据存储失败');
    }
  }

  static getStorage(key) {
    try {
      return wx.getStorageSync(key);
    } catch (e) {
      console.error('读取存储失败:', e);
      return null;
    }
  }
}

// 用户行为合规检查
class UserBehaviorCompliance {
  
  // 检查用户授权状态
  static checkUserAuth() {
    return new Promise((resolve) => {
      wx.getSetting({
        success: (res) => {
          resolve({
            userInfo: res.authSetting['scope.userInfo'],
            userLocation: res.authSetting['scope.userLocation'],
            writePhotosAlbum: res.authSetting['scope.writePhotosAlbum']
          });
        },
        fail: () => resolve({})
      });
    });
  }

  // 请求用户授权
  static requestAuth(scope) {
    return new Promise((resolve, reject) => {
      wx.authorize({
        scope: `scope.${scope}`,
        success: () => resolve(true),
        fail: () => {
          // 如果授权失败，引导用户到设置页面
          wx.showModal({
            title: '授权提示',
            content: '需要相关权限才能正常使用功能，是否前往设置？',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    resolve(settingRes.authSetting[`scope.${scope}`] || false);
                  }
                });
              } else {
                resolve(false);
              }
            }
          });
        }
      });
    });
  }

  // 安全的用户反馈
  static showUserFeedback(type, message) {
    const feedbackMethods = {
      success: () => wx.showToast({ title: message, icon: 'success' }),
      error: () => wx.showToast({ title: message, icon: 'error' }),
      loading: () => wx.showLoading({ title: message }),
      modal: (title) => wx.showModal({ title, content: message })
    };

    const method = feedbackMethods[type];
    if (method) {
      method(message);
    }
  }
}

// 内容合规检查
class ContentCompliance {
  
  // 敏感词过滤
  static filterSensitiveWords(content) {
    // 这里应该连接后端的敏感词检测API
    // 暂时使用简单的本地检查
    const sensitiveWords = ['违法', '色情', '暴力', '赌博'];
    let filteredContent = content;
    
    sensitiveWords.forEach(word => {
      const regex = new RegExp(word, 'gi');
      filteredContent = filteredContent.replace(regex, '*'.repeat(word.length));
    });
    
    return filteredContent;
  }

  // 检查图片内容合规
  static async checkImageCompliance(imageUrl) {
    try {
      // 调用微信内容安全API
      const result = await WeChatAPIWrapper.request({
        url: '/api/check-image-security',
        method: 'POST',
        data: { imageUrl }
      });
      
      return result.isCompliant;
    } catch (e) {
      console.warn('图片合规检查失败:', e);
      return true; // 检查失败时默认通过
    }
  }

  // 商品信息合规检查
  static validateProductInfo(productInfo) {
    const errors = [];
    
    // 检查必填字段
    if (!productInfo.title || productInfo.title.trim().length === 0) {
      errors.push('商品标题不能为空');
    }
    
    if (!productInfo.price || productInfo.price <= 0) {
      errors.push('商品价格必须大于0');
    }
    
    if (!productInfo.images || productInfo.images.length === 0) {
      errors.push('商品图片不能为空');
    }
    
    // 检查标题长度
    if (productInfo.title && productInfo.title.length > 60) {
      errors.push('商品标题不能超过60个字符');
    }
    
    // 检查价格合理性
    if (productInfo.price > 999999) {
      errors.push('商品价格不能超过999999元');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// 性能合规工具
class PerformanceCompliance {
  
  // 检查页面性能指标
  static checkPagePerformance() {
    const performance = wx.getPerformance();
    const entries = performance.getEntries();
    
    return {
      navigationStart: entries.find(e => e.entryType === 'navigation')?.startTime || 0,
      loadComplete: entries.find(e => e.entryType === 'loadComplete')?.startTime || 0,
      firstRender: entries.find(e => e.entryType === 'firstRender')?.startTime || 0
    };
  }

  // 内存使用检查
  static checkMemoryUsage() {
    try {
      const memoryInfo = wx.getSystemInfoSync();
      const usedMemory = memoryInfo.benchmarkLevel || 0;
      
      if (usedMemory > 80) {
        console.warn('内存使用率较高:', usedMemory + '%');
        return { isOptimal: false, usage: usedMemory };
      }
      
      return { isOptimal: true, usage: usedMemory };
    } catch (e) {
      return { isOptimal: true, usage: 0 };
    }
  }

  // 网络请求优化建议
  static optimizeNetworkRequests(requestHistory) {
    const suggestions = [];
    
    // 检查并发请求数量
    const concurrentRequests = requestHistory.filter(r => 
      Date.now() - r.timestamp < 1000
    ).length;
    
    if (concurrentRequests > 5) {
      suggestions.push('并发请求过多，建议使用请求队列');
    }
    
    // 检查重复请求
    const duplicateRequests = requestHistory.reduce((acc, req) => {
      acc[req.url] = (acc[req.url] || 0) + 1;
      return acc;
    }, {});
    
    Object.entries(duplicateRequests).forEach(([url, count]) => {
      if (count > 3) {
        suggestions.push(`URL ${url} 请求过于频繁，建议添加缓存`);
      }
    });
    
    return suggestions;
  }
}

// 导出合规工具
module.exports = {
  WeChatAPIWrapper,
  UserBehaviorCompliance,
  ContentCompliance,
  PerformanceCompliance
};