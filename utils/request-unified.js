/**
 * 统一网络请求封装 - 替代直接使用wx.request
 * Unified Network Request Module
 * 
 * 替换项目中所有直接使用wx.request的地方
 * 提供统一的错误处理、loading管理、重试机制
 */

const { API, CONFIG, BUSINESS } = require('../constants/index.js');

/**
 * 请求配置
 */
const DEFAULT_CONFIG = {
  timeout: 10000,
  enableLoading: true,
  enableRetry: true,
  maxRetries: 3,
  retryDelay: 1000,
  enableErrorToast: true
};

/**
 * 错误代码映射
 */
const ERROR_CODES = {
  NETWORK_ERROR: -1,
  TIMEOUT: -2,
  SERVER_ERROR: -3,
  AUTH_FAILED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  VALIDATION_ERROR: 400
};

/**
 * 全局请求拦截器
 */
let requestInterceptors = [];
let responseInterceptors = [];

/**
 * 统一请求方法
 */
class RequestManager {
  
  constructor() {
    this.baseUrl = this.getBaseUrl();
    this.defaultHeaders = this.getDefaultHeaders();
    this.requestCount = 0;
  }

  /**
   * 获取基础URL
   */
  getBaseUrl() {
    try {
      const app = getApp();
      if (app && app.globalData && app.globalData.baseUrl) {
        return app.globalData.baseUrl;
      }
      return CONFIG.API.BASE_URL || 'http://localhost:3000';
    } catch (error) {
      console.warn('获取baseUrl失败，使用默认值');
      return 'http://localhost:3000';
    }
  }

  /**
   * 获取默认请求头
   */
  getDefaultHeaders() {
    try {
      const token = wx.getStorageSync('token');
      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      
      return headers;
    } catch (error) {
      console.warn('获取默认headers失败');
      return {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };
    }
  }

  /**
   * 执行请求拦截器
   */
  async runRequestInterceptors(config) {
    for (let interceptor of requestInterceptors) {
      config = await interceptor(config);
    }
    return config;
  }

  /**
   * 执行响应拦截器
   */
  async runResponseInterceptors(response) {
    for (let interceptor of responseInterceptors) {
      response = await interceptor(response);
    }
    return response;
  }

  /**
   * 显示loading
   */
  showLoading(config) {
    if (config.enableLoading && this.requestCount === 0) {
      wx.showLoading({
        title: config.loadingText || '加载中...',
        mask: true
      });
    }
    this.requestCount++;
  }

  /**
   * 隐藏loading
   */
  hideLoading() {
    this.requestCount--;
    if (this.requestCount <= 0) {
      this.requestCount = 0;
      wx.hideLoading();
    }
  }

  /**
   * 错误处理
   */
  handleError(error, config) {
    console.error('请求错误:', error);
    
    if (config.enableErrorToast) {
      let message = '网络请求失败';
      
      if (error.statusCode) {
        switch (error.statusCode) {
          case 401:
            message = '登录已过期，请重新登录';
            this.handleAuthError();
            break;
          case 403:
            message = '没有权限访问';
            break;
          case 404:
            message = '请求的资源不存在';
            break;
          case 500:
            message = '服务器内部错误';
            break;
          default:
            message = error.data?.message || '请求失败';
        }
      } else if (error.errMsg) {
        if (error.errMsg.includes('timeout')) {
          message = '请求超时，请检查网络';
        } else if (error.errMsg.includes('fail')) {
          message = '网络连接失败';
        }
      }
      
      wx.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      });
    }
    
    return Promise.reject(error);
  }

  /**
   * 处理认证错误
   */
  handleAuthError() {
    try {
      wx.removeStorageSync('token');
      wx.removeStorageSync('userInfo');
      
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/login/login'
        });
      }, 1500);
    } catch (error) {
      console.error('处理认证错误失败:', error);
    }
  }

  /**
   * 重试机制
   */
  async retryRequest(config, retryCount = 0) {
    if (retryCount >= config.maxRetries) {
      throw new Error('重试次数超过限制');
    }

    try {
      return await this.executeRequest(config);
    } catch (error) {
      if (config.enableRetry && this.shouldRetry(error)) {
        await this.delay(config.retryDelay);
        return this.retryRequest(config, retryCount + 1);
      }
      throw error;
    }
  }

  /**
   * 判断是否应该重试
   */
  shouldRetry(error) {
    // 网络错误或超时错误可以重试
    return error.errMsg && (
      error.errMsg.includes('timeout') ||
      error.errMsg.includes('fail')
    );
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 执行实际请求
   */
  executeRequest(config) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: config.url,
        method: config.method || 'GET',
        data: config.data,
        header: { ...this.defaultHeaders, ...config.header },
        timeout: config.timeout,
        success: (res) => {
          this.hideLoading();
          
          // 根据业务逻辑判断请求是否成功
          if (res.statusCode >= 200 && res.statusCode < 300) {
            // 检查业务状态码
            if (res.data && res.data.success === false) {
              reject({
                statusCode: res.statusCode,
                data: res.data,
                errMsg: res.data.message || '业务处理失败'
              });
            } else {
              resolve(res);
            }
          } else {
            reject({
              statusCode: res.statusCode,
              data: res.data,
              errMsg: `HTTP ${res.statusCode}`
            });
          }
        },
        fail: (error) => {
          this.hideLoading();
          reject(error);
        }
      });
    });
  }

  /**
   * 主请求方法
   */
  async request(options) {
    // 合并配置
    const config = {
      ...DEFAULT_CONFIG,
      ...options
    };

    // 处理URL
    if (!config.url.startsWith('http')) {
      config.url = this.baseUrl + config.url;
    }

    // 显示loading
    if (config.enableLoading) {
      this.showLoading(config);
    }

    try {
      // 执行请求拦截器
      const finalConfig = await this.runRequestInterceptors(config);
      
      // 执行请求（带重试）
      let response;
      if (finalConfig.enableRetry) {
        response = await this.retryRequest(finalConfig);
      } else {
        response = await this.executeRequest(finalConfig);
      }
      
      // 执行响应拦截器
      response = await this.runResponseInterceptors(response);
      
      return response;
    } catch (error) {
      return this.handleError(error, config);
    }
  }

  /**
   * GET请求
   */
  get(url, params = {}, options = {}) {
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    const finalUrl = queryString ? `${url}?${queryString}` : url;
    
    return this.request({
      url: finalUrl,
      method: 'GET',
      ...options
    });
  }

  /**
   * POST请求
   */
  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    });
  }

  /**
   * PUT请求
   */
  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    });
  }

  /**
   * DELETE请求
   */
  delete(url, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      ...options
    });
  }
}

// 创建请求实例
const requestManager = new RequestManager();

/**
 * 拦截器管理
 */
const interceptors = {
  request: {
    use(fn) {
      requestInterceptors.push(fn);
    }
  },
  response: {
    use(fn) {
      responseInterceptors.push(fn);
    }
  }
};

/**
 * 导出接口
 */
module.exports = {
  request: requestManager.request.bind(requestManager),
  get: requestManager.get.bind(requestManager),
  post: requestManager.post.bind(requestManager),
  put: requestManager.put.bind(requestManager),
  delete: requestManager.delete.bind(requestManager),
  interceptors,
  ERROR_CODES
};