/**
 * 统一日志管理系统
 * 提供分级日志记录，生产环境自动禁用调试信息
 */

const { API } = require('../constants/index.js');

class Logger {
  constructor() {
    this.isDevelopment = API.CURRENT_ENV === API.ENV.DEVELOPMENT;
    this.isDebugMode = wx.getStorageSync('debug_mode') === true;
  }

  /**
   * 错误日志 - 所有环境都会记录
   */
  error(message, data = null, context = '') {
    const logData = this.formatLog('ERROR', message, data, context);
    console.error(logData.formatted);
    
    // 生产环境中上报错误到监控系统
    if (!this.isDevelopment) {
      this.reportError(logData);
    }
  }

  /**
   * 警告日志 - 所有环境都会记录
   */
  warn(message, data = null, context = '') {
    const logData = this.formatLog('WARN', message, data, context);
    console.warn(logData.formatted);
  }

  /**
   * 信息日志 - 开发环境或开启调试模式时记录
   */
  info(message, data = null, context = '') {
    if (this.isDevelopment || this.isDebugMode) {
      const logData = this.formatLog('INFO', message, data, context);
      console.log(logData.formatted);
    }
  }

  /**
   * 调试日志 - 仅开发环境记录
   */
  debug(message, data = null, context = '') {
    if (this.isDevelopment) {
      const logData = this.formatLog('DEBUG', message, data, context);
      console.log(logData.formatted);
    }
  }

  /**
   * 网络请求日志
   */
  network(url, method, status, duration, context = '') {
    const message = `${method} ${url} - ${status} (${duration}ms)`;
    if (status >= 400) {
      this.error(message, null, context);
    } else if (this.isDevelopment) {
      this.debug(message, null, context);
    }
  }

  /**
   * 用户行为日志
   */
  action(action, page, data = null) {
    const message = `用户操作: ${action} - 页面: ${page}`;
    this.info(message, data, 'USER_ACTION');
  }

  /**
   * 性能日志
   */
  performance(label, duration, data = null) {
    const message = `性能监控: ${label} - ${duration}ms`;
    if (duration > 3000) {
      this.warn(message, data, 'PERFORMANCE');
    } else if (this.isDevelopment) {
      this.debug(message, data, 'PERFORMANCE');
    }
  }

  /**
   * 格式化日志
   */
  formatLog(level, message, data, context) {
    const timestamp = new Date().toISOString();
    const contextStr = context ? `[${context}] ` : '';
    const formatted = `[${timestamp}] [${level}] ${contextStr}${message}`;
    
    const logData = {
      timestamp,
      level,
      message,
      context,
      data,
      formatted
    };

    if (data) {
      console.log('附加数据:', data);
    }

    return logData;
  }

  /**
   * 上报错误到监控系统
   */
  reportError(logData) {
    try {
      // 这里可以集成第三方错误监控服务
      // 如：Sentry, Bugsnag 等
      wx.request({
        url: `${API.BASE_URL}/api/v1/logging/error`,
        method: 'POST',
        data: {
          level: logData.level,
          message: logData.message,
          context: logData.context,
          data: logData.data,
          timestamp: logData.timestamp,
          platform: 'wechat-miniprogram',
          version: wx.getAccountInfoSync().miniProgram.version || 'dev'
        },
        fail: () => {
          // 静默失败，避免循环错误
        }
      });
    } catch (error) {
      // 静默失败
    }
  }

  /**
   * 开启/关闭调试模式
   */
  setDebugMode(enabled) {
    this.isDebugMode = enabled;
    wx.setStorageSync('debug_mode', enabled);
  }

  /**
   * 清理本地日志缓存
   */
  clearCache() {
    wx.removeStorageSync('debug_mode');
  }
}

// 创建全局日志实例
const logger = new Logger();

// 导出日志方法
module.exports = {
  logger,
  error: (message, data, context) => logger.error(message, data, context),
  warn: (message, data, context) => logger.warn(message, data, context),
  info: (message, data, context) => logger.info(message, data, context),
  debug: (message, data, context) => logger.debug(message, data, context),
  network: (url, method, status, duration, context) => logger.network(url, method, status, duration, context),
  action: (action, page, data) => logger.action(action, page, data),
  performance: (label, duration, data) => logger.performance(label, duration, data)
};