/**
 * 页面通用混入 - 统一页面初始化逻辑
 * Page Common Mixin
 * 
 * 提供统一的页面初始化、错误处理、性能监控
 */

// 注意：这里需要根据实际logger文件位置调整路径
// const { Logger } = require('./logger');
const performance = require('./performance');

/**
 * 页面基础混入
 */
const pageBaseMixin = {
  
  /**
   * 页面数据
   */
  data: {
    loading: false,
    error: null,
    retryCount: 0,
    pageReady: false
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    this.pageStartTime = Date.now();
    this.setData({ loading: true });
    
    // 记录页面访问
    Logger.business('页面访问', {
      page: getCurrentPages().slice(-1)[0].route,
      options,
      userId: this.getUserId()
    });

    // 调用子页面的初始化方法
    if (this.initPage && typeof this.initPage === 'function') {
      this.safeCall(() => this.initPage(options));
    }
  },

  /**
   * 页面显示
   */
  onShow() {
    // 记录页面性能
    if (this.pageStartTime) {
      const loadTime = Date.now() - this.pageStartTime;
      performance.recordPageLoad(getCurrentPages().slice(-1)[0].route, loadTime);
    }

    // 调用子页面的显示方法
    if (this.onPageShow && typeof this.onPageShow === 'function') {
      this.safeCall(() => this.onPageShow());
    }
  },

  /**
   * 页面准备完成
   */
  onReady() {
    this.setData({ 
      loading: false, 
      pageReady: true 
    });

    // 调用子页面的准备完成方法
    if (this.onPageReady && typeof this.onPageReady === 'function') {
      this.safeCall(() => this.onPageReady());
    }
  },

  /**
   * 页面隐藏
   */
  onHide() {
    // 调用子页面的隐藏方法
    if (this.onPageHide && typeof this.onPageHide === 'function') {
      this.safeCall(() => this.onPageHide());
    }
  },

  /**
   * 页面卸载
   */
  onUnload() {
    // 清理定时器和监听器
    this.cleanup();

    // 调用子页面的卸载方法
    if (this.onPageUnload && typeof this.onPageUnload === 'function') {
      this.safeCall(() => this.onPageUnload());
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    if (this.refreshPage && typeof this.refreshPage === 'function') {
      this.safeCall(() => this.refreshPage());
    } else {
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (this.loadMore && typeof this.loadMore === 'function') {
      this.safeCall(() => this.loadMore());
    }
  },

  /**
   * 安全调用方法
   */
  safeCall(fn) {
    try {
      const result = fn();
      if (result && typeof result.catch === 'function') {
        result.catch(error => this.handleError(error));
      }
      return result;
    } catch (error) {
      this.handleError(error);
    }
  },

  /**
   * 错误处理
   */
  handleError(error) {
    Logger.error('页面错误', {
      page: getCurrentPages().slice(-1)[0].route,
      error: error.message || error,
      stack: error.stack,
      userId: this.getUserId()
    });

    this.setData({
      error: error.message || '发生未知错误',
      loading: false
    });

    // 显示错误提示
    if (this.data.retryCount < 3) {
      wx.showToast({
        title: '加载失败，点击重试',
        icon: 'none',
        duration: 2000
      });
    } else {
      wx.showModal({
        title: '加载失败',
        content: '页面加载失败，请检查网络后重试',
        showCancel: false,
        confirmText: '重新加载',
        success: () => {
          this.retryLoad();
        }
      });
    }
  },

  /**
   * 重试加载
   */
  retryLoad() {
    this.setData({
      retryCount: this.data.retryCount + 1,
      error: null,
      loading: true
    });

    if (this.initPage && typeof this.initPage === 'function') {
      this.safeCall(() => this.initPage());
    }
  },

  /**
   * 获取用户ID
   */
  getUserId() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      return userInfo?.id || 'anonymous';
    } catch (error) {
      return 'anonymous';
    }
  },

  /**
   * 显示加载状态
   */
  showLoading(text = '加载中...') {
    this.setData({ loading: true });
    wx.showLoading({
      title: text,
      mask: true
    });
  },

  /**
   * 隐藏加载状态
   */
  hideLoading() {
    this.setData({ loading: false });
    wx.hideLoading();
  },

  /**
   * 显示成功提示
   */
  showSuccess(message = '操作成功') {
    wx.showToast({
      title: message,
      icon: 'success',
      duration: 2000
    });
  },

  /**
   * 显示错误提示
   */
  showError(message = '操作失败') {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 页面跳转
   */
  navigateTo(url, params = {}) {
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    const finalUrl = queryString ? `${url}?${queryString}` : url;
    
    wx.navigateTo({
      url: finalUrl,
      fail: (error) => {
        Logger.warn('页面跳转失败', { url: finalUrl, error });
        this.showError('页面跳转失败');
      }
    });
  },

  /**
   * 页面重定向
   */
  redirectTo(url, params = {}) {
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    const finalUrl = queryString ? `${url}?${queryString}` : url;
    
    wx.redirectTo({
      url: finalUrl,
      fail: (error) => {
        Logger.warn('页面重定向失败', { url: finalUrl, error });
        this.showError('页面跳转失败');
      }
    });
  },

  /**
   * 清理资源
   */
  cleanup() {
    // 清理定时器
    if (this.timers) {
      this.timers.forEach(timer => clearTimeout(timer));
      this.timers = [];
    }

    // 清理间隔器
    if (this.intervals) {
      this.intervals.forEach(interval => clearInterval(interval));
      this.intervals = [];
    }
  },

  /**
   * 添加定时器
   */
  addTimer(callback, delay) {
    if (!this.timers) {
      this.timers = [];
    }
    const timer = setTimeout(callback, delay);
    this.timers.push(timer);
    return timer;
  },

  /**
   * 添加间隔器
   */
  addInterval(callback, interval) {
    if (!this.intervals) {
      this.intervals = [];
    }
    const intervalId = setInterval(callback, interval);
    this.intervals.push(intervalId);
    return intervalId;
  }
};

/**
 * 应用混入到页面
 */
function createPage(pageConfig) {
  // 合并混入和页面配置
  const mergedConfig = Object.assign({}, pageBaseMixin, pageConfig);
  
  // 处理生命周期方法的合并
  ['onLoad', 'onShow', 'onReady', 'onHide', 'onUnload', 'onPullDownRefresh', 'onReachBottom'].forEach(method => {
    if (pageConfig[method] && typeof pageConfig[method] === 'function') {
      const originalMethod = pageConfig[method];
      const mixinMethod = pageBaseMixin[method];
      
      mergedConfig[method] = function(...args) {
        // 先执行混入的方法
        if (mixinMethod) {
          mixinMethod.apply(this, args);
        }
        // 再执行页面自定义的方法
        return originalMethod.apply(this, args);
      };
    }
  });

  return Page(mergedConfig);
}

module.exports = {
  pageBaseMixin,
  createPage
};