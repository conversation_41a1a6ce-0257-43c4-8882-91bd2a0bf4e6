#!/bin/bash
# 智慧养鹅SAAS平台 - 迁移启动脚本
# Smart Goose SAAS Platform - Migration Startup Script

echo "🚀 开始智慧养鹅SAAS平台代码迁移..."
echo "=============================================="

# 检查Node.js环境
echo "📋 检查环境要求..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js"
    exit 1
fi

NODE_VERSION=$(node -v)
echo "✅ Node.js版本: $NODE_VERSION"

# 检查数据库连接
echo "🗄️ 测试数据库连接..."
node -e "
const { testConnection } = require('./backend/config/database');
testConnection().then(success => {
  if (success) {
    console.log('✅ 数据库连接正常');
    process.exit(0);
  } else {
    console.log('❌ 数据库连接失败');
    process.exit(1);
  }
}).catch(() => {
  console.log('⚠️ 数据库配置需要检查');
  process.exit(0);
});
" || echo "⚠️ 数据库连接需要配置"

# 验证新工具
echo "🔧 验证新创建的工具..."

# 测试日志系统
echo "测试日志系统..."
node -e "
const { Logger } = require('./backend/utils/logger');
Logger.info('迁移脚本测试', { timestamp: new Date() });
console.log('✅ 日志系统正常');
" && echo "✅ 日志系统验证通过" || echo "❌ 日志系统需要检查"

# 检查关键文件语法
echo "检查新文件语法..."
node -c backend/utils/logger.js && echo "✅ logger.js 语法正确" || echo "❌ logger.js 语法错误"
node -c backend/middleware/errorHandler.enhanced.js && echo "✅ errorHandler.enhanced.js 语法正确" || echo "❌ errorHandler.enhanced.js 语法错误"
node -c utils/request-unified.js && echo "✅ request-unified.js 语法正确" || echo "❌ request-unified.js 语法错误"
node -c utils/page-mixin.js && echo "✅ page-mixin.js 语法正确" || echo "❌ page-mixin.js 语法错误"

echo ""
echo "🎯 下一步建议："
echo "=============================================="
echo "1. 📖 阅读 NEXT_STEPS_GUIDE.md 了解详细步骤"
echo "2. 📝 查看 MIGRATION_EXAMPLES.md 学习迁移示例"
echo "3. 🔄 开始迁移核心页面（建议从首页开始）"
echo "4. 🧪 每迁移一个页面都要进行测试"
echo ""
echo "📋 快速开始命令："
echo "=============================================="
echo "# 启动开发服务器"
echo "npm run dev"
echo ""
echo "# 启动管理后台"
echo "npm run start:admin"
echo ""
echo "# 运行测试"
echo "npm test"
echo ""
echo "🔗 重要文档："
echo "- README.md - 项目完整文档"
echo "- COMPREHENSIVE_CODE_REVIEW_REPORT.md - 详细审查报告"
echo "- CODE_REVIEW_SUMMARY.md - 总结报告"
echo ""
echo "✨ 迁移准备完成！开始编码吧！"