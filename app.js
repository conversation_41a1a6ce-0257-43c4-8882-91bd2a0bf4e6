// 导入统一的API配置
const { BASE_URLS, API_VERSIONS, ENV } = require('./constants/api.constants.js');

App({
  onLaunch: function () {
    // 初始化应用

    // 禁用实时上报功能以避免开发环境警告
    if (typeof wx.reportRealtimeAction === 'function') {
      const originalReportRealtimeAction = wx.reportRealtimeAction;
      wx.reportRealtimeAction = function() {
        // 在开发环境中静默忽略
        if (__wxConfig && __wxConfig.debug) {
          return;
        }
        return originalReportRealtimeAction.apply(this, arguments);
      };
    }

    // 延迟检查登录状态，确保应用完全初始化
    setTimeout(() => {
      const token = wx.getStorageSync('access_token'); // 统一使用access_token
      if (token) {
        // 验证token有效性
        this.validateToken();
      }
    }, 100);
  },

  // 验证token有效性
  validateToken: function() {
    try {
      const { auth } = require('./utils/api.js');
      auth.getUserInfo().then(res => {
        if (res.success) {
          this.globalData.userInfo = res.data;
          // 同时保存到本地存储
          wx.setStorageSync('userInfo', res.data);
        } else {
          // token无效，清除本地存储
          wx.removeStorageSync('access_token'); // 统一使用access_token
          wx.removeStorageSync('refresh_token');
          wx.removeStorageSync('user_info');
        }
      }).catch((err) => {
        // 尝试从本地存储获取用户信息
        const localUserInfo = wx.getStorageSync('user_info'); // 统一使用user_info
        if (localUserInfo) {
          this.globalData.userInfo = localUserInfo;
        }
      });
    } catch (error) {
      console.error('validateToken方法执行失败:', error);
    }
  },
  
  globalData: {
    // API配置 - 使用统一的常量配置
    baseUrl: BASE_URLS[ENV.DEVELOPMENT] + API_VERSIONS.V1, // 主要API端点
    apiBaseUrl: BASE_URLS[ENV.DEVELOPMENT], // 基础API地址

    // 环境配置
    currentEnv: ENV.DEVELOPMENT, // 当前环境

    // 用户信息
    userInfo: {},
    token: '',
    userRole: 'user', // 默认角色

    // 应用配置
    appVersion: '2.6.0', // 与constants中保持一致

    // AI配置相关
    aiConfig: null, // 从后台获取的AI配置
    aiConfigLastUpdate: null // AI配置最后更新时间
  },
  
  // 检查权限的方法
  checkPermission(requiredRoles) {
    const userRole = this.globalData.userRole;
    return requiredRoles.includes(userRole);
  },

  // 统一的登录状态检查方法
  isLoggedIn() {
    const token = wx.getStorageSync('access_token');
    const userInfo = wx.getStorageSync('user_info');
    return !!(token && userInfo);
  },

  // 统一的登录状态清除方法
  clearLoginState() {
    wx.removeStorageSync('access_token');
    wx.removeStorageSync('refresh_token');
    wx.removeStorageSync('user_info');
    this.globalData.userInfo = {};
    this.globalData.token = '';
  },

  // 统一的登录跳转方法
  redirectToLogin() {
    wx.reLaunch({
      url: '/pages/login/login'
    });
  }
});