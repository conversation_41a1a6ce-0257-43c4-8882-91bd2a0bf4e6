// components/trend-chart/trend-chart.js
const { mixinComponentUtils, createPropertyObserver } = require('../../utils/component-utils.js');
const componentConfig = {
  /**
   * 组件属性
   */
  properties: {
    // 图表标题
    title: {
      type: String,
      value: '数据趋势',
      observer: createPropertyObserver('string', '数据趋势')
    },
    // 副标题
    subtitle: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 图表数据
    chartData: {
      type: Array,
      value: [],
      observer: 'onDataChange'
    },
    // 图表类型配置
    chartConfig: {
      type: Object,
      value: {
        type: 'line', // line, area, multi-line
        smooth: true,
        showPoints: true,
        showGrid: true,
        animation: true
      }
    },
    // 时间范围选项
    timeRanges: {
      type: Array,
      value: [
        { label: '近24小时', value: '24h' },
        { label: '近7天', value: '7d' },
        { label: '近30天', value: '30d' }
      ]
    },
    // 当前时间范围
    activeTimeRange: {
      type: String,
      value: '24h',
      observer: createPropertyObserver('string', '24h')
    },
    // 是否显示时间筛选器
    showTimeFilter: {
      type: Boolean,
      value: true
    },
    // 图例数据
    legendData: {
      type: Array,
      value: []
    },
    // 是否显示当前值
    showCurrentValues: {
      type: Boolean,
      value: false
    },
    // 当前值数据
    currentValues: {
      type: Object,
      value: null
    },
    // 是否显示底部信息
    showFooter: {
      type: Boolean,
      value: false
    },
    // 更新时间
    updateTime: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 空状态文本
    emptyText: {
      type: String,
      value: '暂无数据',
      observer: createPropertyObserver('string', '暂无数据')
    },
    // 是否显示刷新按钮
    showRefresh: {
      type: Boolean,
      value: false
    },
    // 是否允许导出
    allowExport: {
      type: Boolean,
      value: false
    },
    // 是否允许全屏
    allowFullscreen: {
      type: Boolean,
      value: false
    },
    // Canvas尺寸
    canvasWidth: {
      type: Number,
      value: 350
    },
    canvasHeight: {
      type: Number,
      value: 250
    }
  },

  /**
   * 组件数据
   */
  data: {
    // 组件唯一ID
    componentId: '',
    // Canvas上下文
    ctx: null,
    // 是否正在加载
    loading: false,
    // 提示框相关
    showTooltip: false,
    tooltipX: 0,
    tooltipY: 0,
    tooltipData: null,
    // 图表内边距
    padding: {
      top: 40,
      right: 30,
      bottom: 60,
      left: 60
    },
    // 主题色彩
    colors: [
      '#0066CC', '#52C41A', '#FAAD14', '#FF4D4F', 
      '#722ED1', '#13C2C2', '#FA8C16', '#EB2F96'
    ]
  },

  /**
   * 数据监听器
   */
  observers: {
    'chartData': function(chartData) {
      if (chartData && chartData.length > 0 && this.data.ctx) {
        // 延迟渲染确保DOM更新完成
        setTimeout(() => {
          this.renderChart();
        }, 100);
      }
    },
    'activeTimeRange': function(timeRange) {
      if (this.data.chartData && this.data.chartData.length > 0 && this.data.ctx) {
        setTimeout(() => {
          this.renderChart();
        }, 100);
      }
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 生成唯一组件ID
      this.setData({
        componentId: 'chart_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
      });
    },

    ready() {
      // 初始化Canvas
      this.initCanvas();
    },

    detached() {
      // 清理资源
      if (this.data.ctx) {
        this.data.ctx = null;
      }
    }
  },

  /**
   * 组件方法
   */
  methods: {
    /**
     * 初始化Canvas
     */
    initCanvas() {
      const canvasId = `trendChart-${this.data.componentId}`;
      const ctx = wx.createCanvasContext(canvasId, this);
      this.setData({
        ctx: ctx
      });

      // 延迟渲染，确保组件完全加载
      setTimeout(() => {
        if (this.data.chartData && this.data.chartData.length > 0) {
          this.renderChart();
        }
      }, 200);
    },

    /**
     * 数据变化处理
     */
    onDataChange() {
      if (this.data.ctx && this.data.chartData && this.data.chartData.length > 0) {
        this.renderChart();
      }
    },

    /**
     * 渲染图表
     */
    renderChart() {
      if (!this.data.ctx || !this.data.chartData || this.data.chartData.length === 0) {
        return;
      }

      const ctx = this.data.ctx;
      const { chartData, canvasWidth, canvasHeight, padding, colors, chartConfig } = this.data;

      // 清空画布
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);

      // 计算图表区域
      const chartWidth = canvasWidth - padding.left - padding.right;
      const chartHeight = canvasHeight - padding.top - padding.bottom;

      // 处理数据
      const processedData = this.processChartData(chartData);
      
      if (!processedData || processedData.length === 0) {
        return;
      }

      // 计算数值范围
      const { minValue, maxValue } = this.calculateValueRange(processedData);

      // 绘制网格（如果启用）
      if (chartConfig.showGrid !== false) {
        this.drawGrid(ctx, padding, chartWidth, chartHeight, processedData, minValue, maxValue);
      }

      // 绘制坐标轴
      this.drawAxes(ctx, padding, chartWidth, chartHeight, processedData, minValue, maxValue);

      // 绘制数据线
      if (chartConfig.type === 'multi-line') {
        this.drawMultiLines(ctx, padding, chartWidth, chartHeight, processedData, minValue, maxValue);
      } else {
        this.drawSingleLine(ctx, padding, chartWidth, chartHeight, processedData, minValue, maxValue);
      }

      // 提交绘制
      ctx.draw();

      // 触发渲染完成事件
      this.triggerEvent('rendered', {
        data: processedData,
        range: { minValue, maxValue }
      });
    },

    /**
     * 处理图表数据
     */
    processChartData(data) {
      if (!Array.isArray(data)) return [];
      
      return data.map((item, index) => ({
        ...item,
        index,
        // 确保有必要的字段
        time: item.time || item.label || item.x || `点${index + 1}`,
        value: typeof item.value === 'number' ? item.value : (item.y || 0),
        // 支持多条线的数据
        values: item.values || [{ key: 'value', value: item.value || item.y || 0 }]
      }));
    },

    /**
     * 计算数值范围
     */
    calculateValueRange(data) {
      let minValue = Infinity;
      let maxValue = -Infinity;

      data.forEach(item => {
        if (item.values && Array.isArray(item.values)) {
          // 多条线数据
          item.values.forEach(val => {
            const value = typeof val === 'object' ? val.value : val;
            if (typeof value === 'number') {
              minValue = Math.min(minValue, value);
              maxValue = Math.max(maxValue, value);
            }
          });
        } else {
          // 单条线数据
          const value = item.value;
          if (typeof value === 'number') {
            minValue = Math.min(minValue, value);
            maxValue = Math.max(maxValue, value);
          }
        }
      });

      // 处理异常情况
      if (minValue === Infinity) minValue = 0;
      if (maxValue === -Infinity) maxValue = 100;
      if (minValue === maxValue) {
        minValue = maxValue - 10;
        maxValue = maxValue + 10;
      }

      // 添加一些缓冲空间
      const range = maxValue - minValue;
      const buffer = range * 0.1;
      
      return {
        minValue: minValue - buffer,
        maxValue: maxValue + buffer
      };
    },

    /**
     * 绘制网格
     */
    drawGrid(ctx, padding, width, height, data, minValue, maxValue) {
      ctx.setStrokeStyle('#F0F0F0');
      ctx.setLineWidth(1);

      // 绘制水平网格线
      const ySteps = 5;
      for (let i = 0; i <= ySteps; i++) {
        const y = padding.top + (height / ySteps) * i;
        ctx.beginPath();
        ctx.moveTo(padding.left, y);
        ctx.lineTo(padding.left + width, y);
        ctx.stroke();
      }

      // 绘制垂直网格线
      const xSteps = Math.min(data.length - 1, 6); // 最多6条垂直线
      for (let i = 0; i <= xSteps; i++) {
        const x = padding.left + (width / xSteps) * i;
        ctx.beginPath();
        ctx.moveTo(x, padding.top);
        ctx.lineTo(x, padding.top + height);
        ctx.stroke();
      }
    },

    /**
     * 绘制坐标轴
     */
    drawAxes(ctx, padding, width, height, data, minValue, maxValue) {
      ctx.setFillStyle('#666666');
      ctx.setFontSize(12);
      ctx.setTextAlign('center');

      // Y轴标签
      const ySteps = 5;
      ctx.setTextAlign('right');
      for (let i = 0; i <= ySteps; i++) {
        const value = maxValue - ((maxValue - minValue) / ySteps) * i;
        const y = padding.top + (height / ySteps) * i;
        const displayValue = this.formatValue(value);
        ctx.fillText(displayValue, padding.left - 10, y + 4);
      }

      // X轴标签
      ctx.setTextAlign('center');
      const labelStep = Math.max(1, Math.floor(data.length / 6)); // 最多显示6个标签
      data.forEach((item, index) => {
        if (index % labelStep === 0 || index === data.length - 1) {
          const x = padding.left + (width / Math.max(data.length - 1, 1)) * index;
          const y = padding.top + height + 20;
          const displayTime = this.formatTime(item.time);
          ctx.fillText(displayTime, x, y);
        }
      });
    },

    /**
     * 绘制单条线
     */
    drawSingleLine(ctx, padding, width, height, data, minValue, maxValue) {
      const { chartConfig, colors } = this.data;
      const color = colors[0];
      
      ctx.setStrokeStyle(color);
      ctx.setLineWidth(3);
      ctx.setLineCap('round');
      ctx.setLineJoin('round');

      // 绘制折线
      ctx.beginPath();
      data.forEach((item, index) => {
        const x = padding.left + (width / Math.max(data.length - 1, 1)) * index;
        const y = padding.top + height - ((item.value - minValue) / (maxValue - minValue)) * height;

        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          if (chartConfig.smooth) {
            // 平滑曲线
            const prevItem = data[index - 1];
            const prevX = padding.left + (width / Math.max(data.length - 1, 1)) * (index - 1);
            const prevY = padding.top + height - ((prevItem.value - minValue) / (maxValue - minValue)) * height;
            const cpX = (prevX + x) / 2;
            ctx.quadraticCurveTo(cpX, prevY, x, y);
          } else {
            ctx.lineTo(x, y);
          }
        }
      });
      ctx.stroke();

      // 绘制数据点
      if (chartConfig.showPoints !== false) {
        ctx.setFillStyle(color);
        data.forEach((item, index) => {
          const x = padding.left + (width / Math.max(data.length - 1, 1)) * index;
          const y = padding.top + height - ((item.value - minValue) / (maxValue - minValue)) * height;
          
          // 绘制方形数据点（遵循微信小程序Canvas规范）
          const pointSize = 8; // 4*2，保持视觉大小一致
          ctx.fillRect(x - pointSize/2, y - pointSize/2, pointSize, pointSize);
        });
      }

      // 绘制面积（如果是area类型）
      if (chartConfig.type === 'area') {
        ctx.setFillStyle(color + '20'); // 添加透明度
        ctx.beginPath();
        data.forEach((item, index) => {
          const x = padding.left + (width / Math.max(data.length - 1, 1)) * index;
          const y = padding.top + height - ((item.value - minValue) / (maxValue - minValue)) * height;
          
          if (index === 0) {
            ctx.moveTo(x, padding.top + height);
            ctx.lineTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
          
          if (index === data.length - 1) {
            ctx.lineTo(x, padding.top + height);
          }
        });
        ctx.closePath();
        ctx.fill();
      }
    },

    /**
     * 绘制多条线
     */
    drawMultiLines(ctx, padding, width, height, data, minValue, maxValue) {
      const { colors, legendData } = this.data;
      
      if (!data[0] || !data[0].values) {
        return;
      }

      // 获取所有数据系列的键
      const seriesKeys = data[0].values.map(v => v.key || v.label || 'value');
      
      seriesKeys.forEach((seriesKey, seriesIndex) => {
        const color = colors[seriesIndex % colors.length];
        
        ctx.setStrokeStyle(color);
        ctx.setLineWidth(2);
        ctx.setLineCap('round');
        
        ctx.beginPath();
        data.forEach((item, index) => {
          const seriesValue = item.values.find(v => (v.key || v.label || 'value') === seriesKey);
          if (!seriesValue) return;
          
          const value = typeof seriesValue === 'object' ? seriesValue.value : seriesValue;
          const x = padding.left + (width / Math.max(data.length - 1, 1)) * index;
          const y = padding.top + height - ((value - minValue) / (maxValue - minValue)) * height;

          if (index === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        });
        ctx.stroke();

        // 绘制数据点
        ctx.setFillStyle(color);
        data.forEach((item, index) => {
          const seriesValue = item.values.find(v => (v.key || v.label || 'value') === seriesKey);
          if (!seriesValue) return;
          
          const value = typeof seriesValue === 'object' ? seriesValue.value : seriesValue;
          const x = padding.left + (width / Math.max(data.length - 1, 1)) * index;
          const y = padding.top + height - ((value - minValue) / (maxValue - minValue)) * height;
          
          // 绘制方形数据点（遵循微信小程序Canvas规范）
          const pointSize = 6; // 3*2，保持视觉大小一致
          ctx.fillRect(x - pointSize/2, y - pointSize/2, pointSize, pointSize);
        });
      });
    },

    /**
     * 格式化数值
     */
    formatValue(value) {
      if (typeof value !== 'number') return '0';
      
      if (Math.abs(value) >= 1000000) {
        return (value / 1000000).toFixed(1) + 'M';
      } else if (Math.abs(value) >= 1000) {
        return (value / 1000).toFixed(1) + 'K';
      } else if (value % 1 === 0) {
        return value.toString();
      } else {
        return value.toFixed(1);
      }
    },

    /**
     * 格式化时间
     */
    formatTime(time) {
      if (!time) return '';
      
      // 如果是时间字符串，尝试格式化
      if (typeof time === 'string') {
        if (time.includes(':')) {
          return time; // 已经是时:分格式
        }
        if (time.includes('-')) {
          // 日期格式，提取月日
          const parts = time.split('-');
          if (parts.length >= 2) {
            return `${parts[1]}/${parts[2] || ''}`.replace('//', '/');
          }
        }
      }
      
      return time.toString();
    },

    /**
     * 时间范围改变
     */
    onTimeRangeChange(e) {
      const value = e.currentTarget.dataset.value;
      this.setData({
        activeTimeRange: value
      });
      
      this.triggerEvent('timeRangeChange', {
        value: value,
        range: this.data.timeRanges.find(r => r.value === value)
      });
    },

    /**
     * 触摸开始
     */
    onTouchStart(e) {
      // 阻止事件冒泡，避免触发父级的onViewTrend事件
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      this.touchStartTime = Date.now();
      this.touchStartX = e.touches[0].x;
    },

    /**
     * 触摸移动
     */
    onTouchMove(e) {
      // 阻止事件冒泡
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      // 移动时不处理，避免意外触发
    },

    /**
     * 触摸结束 - 实现点击显示数据点信息
     */
    onTouchEnd(e) {
      // 阻止事件冒泡，避免触发父级的onViewTrend事件
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      
      const touchEndTime = Date.now();
      const touchDuration = touchEndTime - this.touchStartTime;
      const touchEndX = e.changedTouches[0].x;
      const touchDistance = Math.abs(touchEndX - this.touchStartX);
      
      // 只有在短时间内且移动距离小的情况下才认为是点击
      if (touchDuration < 300 && touchDistance < 10) {
        this.handleTouch(e.changedTouches[0]);
      }
    },

    /**
     * 处理触摸事件 - 点击显示数据点信息
     */
    handleTouch(touch) {
      if (!this.data.chartData || this.data.chartData.length === 0) return;

      const { chartData, padding, canvasWidth, canvasHeight, legendData } = this.data;
      
      // 计算触摸点对应的数据索引
      const chartWidth = canvasWidth - padding.left - padding.right;
      const touchX = touch.x - padding.left;
      
      if (touchX < 0 || touchX > chartWidth) {
        this.setData({ showTooltip: false });
        return;
      }
      
      const dataIndex = Math.round((touchX / chartWidth) * (chartData.length - 1));
      
      if (dataIndex >= 0 && dataIndex < chartData.length) {
        const dataPoint = chartData[dataIndex];
        
        // 如果点击的是相同的数据点，则隐藏tooltip
        if (this.data.showTooltip && this.data.tooltipData && this.data.tooltipData.time === dataPoint.time) {
          this.setData({
            showTooltip: false,
            tooltipData: null
          });
          return;
        }
        
        // 构建提示框数据
        const tooltipData = {
          time: dataPoint.time,
          values: []
        };

        if (dataPoint.values && Array.isArray(dataPoint.values)) {
          // 多条线数据
          tooltipData.values = dataPoint.values.map((item, index) => ({
            key: item.key || item.label || `数据${index + 1}`,
            label: item.label || item.key || `数据${index + 1}`,
            value: this.formatValue(item.value),
            unit: item.unit || '',
            color: this.data.colors[index % this.data.colors.length]
          }));
        } else {
          // 单条线数据
          const label = legendData && legendData[0] ? legendData[0].label : '数值';
          const color = legendData && legendData[0] ? legendData[0].color : this.data.colors[0];
          tooltipData.values = [{
            key: 'value',
            label: label,
            value: this.formatValue(dataPoint.value),
            unit: '',
            color: color
          }];
        }

        // 优化tooltip位置 - 避免遮挡图表
        let tooltipX = touch.x;
        let tooltipY = 10; // 固定在顶部，避免遮挡图表
        
        // 确保tooltip不超出画布边界
        const tooltipWidth = 160; // 预估tooltip宽度
        if (tooltipX + tooltipWidth > canvasWidth) {
          tooltipX = canvasWidth - tooltipWidth - 10;
        }
        if (tooltipX < 10) {
          tooltipX = 10;
        }

        this.setData({
          showTooltip: true,
          tooltipX: tooltipX,
          tooltipY: tooltipY,
          tooltipData: tooltipData
        });
      }
    },

    /**
     * 刷新数据
     */
    onRefresh() {
      this.triggerEvent('refresh');
    },

    /**
     * 导出图表
     */
    onExport() {
      this.triggerEvent('export');
    },

    /**
     * 全屏显示
     */
    onFullscreen() {
      this.triggerEvent('fullscreen');
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);