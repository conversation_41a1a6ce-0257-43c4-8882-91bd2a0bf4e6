// components/production/production-material-card/production-material-card.js
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');

/**
 * Production物料管理卡片组件
 * 功能：库存管理、分类展示、预警系统、快捷操作
 * 设计：现代化物料管理界面，支持多种物料类型和状态指示
 */

const componentConfig = {
  options: {
    addGlobalClass: true,
    multipleSlots: true
  },

  properties: {
    // 物料信息
    material: {
      type: Object,
      value: {},
      observer: function(newVal) {
        if (newVal && newVal.id) {
          this.processMaterialData(newVal);
        }
      }
    },

    // 物料分类
    category: {
      type: String,
      value: 'feed' // feed, medicine, equipment, other
    },

    // 显示模式
    mode: {
      type: String,
      value: 'card' // card, list, grid, compact
    },

    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      value: true
    },

    // 是否显示供应商信息
    showSupplier: {
      type: Boolean,
      value: false
    },

    // 是否显示库存预警
    showAlert: {
      type: Boolean,
      value: true
    },

    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    }
  },

  data: {
    // 处理后的物料数据
    processedMaterial: {},

    // 物料分类配置
    categoryConfig: {
      feed: {
        name: '饲料',
        icon: '食物',
        color: '#4CAF50',
        bgColor: '#E8F5E8',
        unit: '袋'
      },
      medicine: {
        name: '药品',
        icon: '药品',
        color: '#2196F3',
        bgColor: '#E3F2FD',
        unit: '瓶'
      },
      equipment: {
        name: '设备',
        icon: '设备',
        color: '#FF9800',
        bgColor: '#FFF3E0',
        unit: '个'
      },
      other: {
        name: '其他',
        icon: '其他',
        color: '#9C27B0',
        bgColor: '#F3E5F5',
        unit: '件'
      }
    },

    // 库存状态配置
    statusConfig: {
      sufficient: {
        text: '充足',
        color: '#4CAF50',
        bgColor: '#E8F5E8',
        icon: '成功'
      },
      normal: {
        text: '正常',
        color: '#4CAF50',
        bgColor: '#E8F5E8',
        icon: '成功'
      },
      warning: {
        text: '偏低',
        color: '#FF9800',
        bgColor: '#FFF3E0',
        icon: '警告'
      },
      danger: {
        text: '不足',
        color: '#F44336',
        bgColor: '#FFEBEE',
        icon: '错误'
      },
      expired: {
        text: '过期',
        color: '#9E9E9E',
        bgColor: '#F5F5F5',
        icon: '禁用'
      },
      outofstock: {
        text: '缺货',
        color: '#F44336',
        bgColor: '#FFEBEE',
        icon: '错误'
      }
    },

    // 操作按钮配置
    actionButtons: [
      { id: 'in', name: '入库', icon: '入库', color: '#4CAF50' },
      { id: 'out', name: '出库', icon: '出库', color: '#FF9800' },
      { id: 'check', name: '盘点', icon: '盘点', color: '#2196F3' },
      { id: 'edit', name: '编辑', icon: '编辑', color: '#9C27B0' }
    ]
  },

  methods: {
    // 处理物料数据
    processMaterialData: function(material) {
      const categoryInfo = this.data.categoryConfig[this.data.category] || this.data.categoryConfig.other;
      const statusInfo = this.data.statusConfig[material.status] || this.data.statusConfig.normal;

      // 计算库存预警级别
      const alertLevel = this.calculateAlertLevel(material);
      
      // 格式化过期时间
      const formattedExpiry = this.formatExpiryDate(material.expiryDate);
      
      // 计算库存天数
      const stockDays = this.calculateStockDays(material);

      this.setData({
        processedMaterial: {
          ...material,
          categoryInfo,
          statusInfo,
          alertLevel,
          formattedExpiry,
          stockDays,
          displayUnit: material.unit || categoryInfo.unit
        }
      });
    },

    // 计算预警级别
    calculateAlertLevel: function(material) {
      const { stock, minStock, maxStock } = material;
      
      if (!stock || stock === 0) return 'outofstock';
      if (minStock && stock <= minStock) return 'danger';
      if (minStock && stock <= minStock * 1.5) return 'warning';
      if (maxStock && stock >= maxStock) return 'sufficient';
      
      return 'normal';
    },

    // 格式化过期日期
    formatExpiryDate: function(dateStr) {
      if (!dateStr) return '';
      
      const date = new Date(dateStr);
      const now = new Date();
      const diffDays = Math.ceil((date - now) / (1000 * 60 * 60 * 24));
      
      if (diffDays < 0) return '已过期';
      if (diffDays === 0) return '今天到期';
      if (diffDays <= 7) return `${diffDays}天后到期`;
      if (diffDays <= 30) return `${diffDays}天后到期`;
      
      return date.toLocaleDateString();
    },

    // 计算库存可用天数
    calculateStockDays: function(material) {
      const { stock, dailyConsumption } = material;
      
      if (!dailyConsumption || dailyConsumption === 0) return null;
      
      const days = Math.floor(stock / dailyConsumption);
      return days;
    },

    // 获取库存状态颜色
    getStockStatusColor: function(level) {
      const colors = {
        sufficient: '#4CAF50',
        normal: '#4CAF50',
        warning: '#FF9800',
        danger: '#F44336',
        outofstock: '#F44336'
      };
      return colors[level] || colors.normal;
    },

    // 卡片点击事件
    onCardTap: function() {
      const { material } = this.data;
      this.triggerEvent('tap', {
        material,
        category: this.data.category
      });
    },

    // 查看详情
    onViewDetail: function() {
      const { material } = this.data;
      this.triggerEvent('detail', {
        material,
        category: this.data.category
      });
    },

    // 快捷操作
    onQuickAction: function(e) {
      const { action } = e.currentTarget.dataset;
      const { material } = this.data;
      
      this.triggerEvent('action', {
        action,
        material,
        category: this.data.category
      });

      // 触觉反馈
      wx.vibrateShort();
    },

    // 入库操作
    onStockIn: function() {
      const { material } = this.data;
      this.triggerEvent('stockIn', {
        material,
        category: this.data.category
      });
    },

    // 出库操作
    onStockOut: function() {
      const { material } = this.data;
      this.triggerEvent('stockOut', {
        material,
        category: this.data.category
      });
    },

    // 盘点操作
    onInventoryCheck: function() {
      const { material } = this.data;
      this.triggerEvent('inventoryCheck', {
        material,
        category: this.data.category
      });
    },

    // 编辑物料
    onEditMaterial: function() {
      const { material } = this.data;
      this.triggerEvent('edit', {
        material,
        category: this.data.category
      });
    },

    // 删除物料
    onDeleteMaterial: function() {
      const { material } = this.data;
      wx.showModal({
        title: '确认删除',
        content: `确定要删除物料"${material.name}"吗？`,
        success: (res) => {
          if (res.confirm) {
            this.triggerEvent('delete', {
              material,
              category: this.data.category
            });
          }
        }
      });
    },

    // 联系供应商
    onContactSupplier: function() {
      const { material } = this.data;
      if (material.supplier && material.supplier.phone) {
        wx.makePhoneCall({
          phoneNumber: material.supplier.phone
        });
      } else {
        wx.showToast({
          title: '无供应商联系方式',
          icon: 'none'
        });
      }
    },

    // 查看供应商详情
    onViewSupplier: function() {
      const { material } = this.data;
      this.triggerEvent('viewSupplier', {
        supplier: material.supplier,
        material
      });
    }
  },

  lifetimes: {
    attached: function() {
      // 组件初始化
      if (this.data.material && this.data.material.id) {
        this.processMaterialData(this.data.material);
      }
    }
  },

  observers: {
    // 监听物料数据变化
    'material': function(material) {
      if (material && material.id) {
        this.processMaterialData(material);
      }
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);