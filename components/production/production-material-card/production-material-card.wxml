<!-- components/production/production-material-card/production-material-card.wxml -->
<view class="production-material-card {{customClass}} material-card--{{mode}} material-card--{{category}} {{stockStatus}}"
      bind:tap="onCardTap">

  <!-- 卡片模式 -->
  <view wx:if="{{mode === 'card'}}" class="card-content">
    <!-- 头部信息 -->
    <view class="card-header">
      <view class="material-info">
        <view class="material-icon" style="background-color: {{categoryConfig.color}}20; border-color: {{categoryConfig.color}}">
          <global-icon
            name="{{categoryConfig.icon || '物料'}}"
            size="20"
            color="{{categoryConfig.color || 'var(--global-primary-color)'}}"
            custom-class="icon">
          </global-icon>
        </view>
        <view class="material-details">
          <text class="material-name">{{material.name}}</text>
          <text class="material-spec">{{material.specification || '规格未指定'}}</text>
          <text wx:if="{{material.brand}}" class="material-brand">{{material.brand}}</text>
        </view>
      </view>
      
      <!-- 状态标签 -->
      <view class="status-tags">
        <view wx:if="{{isLowStock}}" class="status-tag status-tag--warning">
          <global-icon name="警告" size="10" color="#FF9800"></global-icon>
          <text>库存不足</text>
        </view>
        <view wx:if="{{isNearExpiry}}" class="status-tag status-tag--danger">
          <global-icon name="时间" size="10" color="#F44336"></global-icon>
          <text>即将过期</text>
        </view>
        <view wx:if="{{isExpired}}" class="status-tag status-tag--expired">
          <global-icon name="错误" size="10" color="#D32F2F"></global-icon>
          <text>已过期</text>
        </view>
      </view>
    </view>

    <!-- 库存信息 -->
    <view class="stock-info">
      <view class="stock-display">
        <view class="current-stock">
          <text class="stock-label">当前库存</text>
          <view class="stock-value">
            <text class="stock-number" style="color: {{stockColor}}">{{displayStock}}</text>
            <text class="stock-unit">{{material.unit || ''}}</text>
          </view>
        </view>
        
        <!-- 库存进度条 -->
        <view class="stock-progress">
          <view class="progress-bar">
            <view class="progress-fill" 
                  style="width: {{stockPercentage}}%; background-color: {{stockColor}}">
            </view>
          </view>
          <text class="progress-text">{{stockPercentage}}%</text>
        </view>
      </view>

      <!-- 预警阈值 -->
      <view wx:if="{{material.minStock || material.maxStock}}" class="stock-threshold">
        <text class="threshold-label">库存范围：</text>
        <text class="threshold-value">
          {{material.minStock || 0}} - {{material.maxStock || '无上限'}} {{material.unit || ''}}
        </text>
      </view>
    </view>

    <!-- 有效期信息 -->
    <view wx:if="{{material.expiryDate || material.productionDate}}" class="expiry-info">
      <view class="date-row">
        <view wx:if="{{material.productionDate}}" class="date-item">
          <global-icon name="时间" size="12" color="var(--global-text-color-tertiary)"></global-icon>
          <text class="date-label">生产：</text>
          <text class="date-value">{{formatDate(material.productionDate)}}</text>
        </view>
        <view wx:if="{{material.expiryDate}}" class="date-item {{isNearExpiry ? 'near-expiry' : ''}} {{isExpired ? 'expired' : ''}}">
          <global-icon name="时间" size="12" color="{{isExpired ? '#F44336' : isNearExpiry ? '#FF9800' : 'var(--global-text-color-tertiary)'}}"></global-icon>
          <text class="date-label">到期：</text>
          <text class="date-value">{{formatDate(material.expiryDate)}}</text>
        </view>
      </view>
      <view wx:if="{{daysToExpiry !== null}}" class="expiry-countdown">
        <text class="countdown-text {{isExpired ? 'expired' : isNearExpiry ? 'warning' : ''}}">
          {{isExpired ? '已过期' : daysToExpiry === 0 ? '今天到期' : daysToExpiry + '天后到期'}}
        </text>
      </view>
    </view>

    <!-- 供应商信息 -->
    <view wx:if="{{showSupplier && material.supplier}}" class="supplier-info">
      <view class="supplier-header">
        <global-icon name="供应商" size="14" color="var(--global-text-color-secondary)"></global-icon>
        <text class="supplier-label">供应商</text>
      </view>
      <view class="supplier-details">
        <text class="supplier-name">{{material.supplier.name}}</text>
        <text wx:if="{{material.supplier.phone}}" class="supplier-phone">{{material.supplier.phone}}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view wx:if="{{showActions}}" class="card-actions">
      <view class="action-btn" catch:tap="onStockIn">
        <global-icon name="入库" size="14" color="var(--global-success-color)"></global-icon>
        <text>入库</text>
      </view>
      <view class="action-btn" catch:tap="onStockOut">
        <global-icon name="出库" size="14" color="var(--global-warning-color)"></global-icon>
        <text>出库</text>
      </view>
      <view class="action-btn" catch:tap="onInventory">
        <global-icon name="盘点" size="14" color="var(--global-info-color)"></global-icon>
        <text>盘点</text>
      </view>
      <view wx:if="{{material.supplier}}" class="action-btn" catch:tap="onContact">
        <global-icon name="联系" size="14" color="var(--global-text-color-secondary)"></global-icon>
        <text>联系</text>
      </view>
    </view>
  </view>

  <!-- 列表模式 -->
  <view wx:elif="{{mode === 'list'}}" class="list-content">
    <view class="list-icon" style="background-color: {{categoryConfig.color}}20">
      <global-icon 
        name="{{categoryConfig.icon}}" 
        size="16" 
        color="{{categoryConfig.color}}">
      </global-icon>
    </view>
    
    <view class="list-info">
      <view class="list-header">
        <text class="list-name">{{material.name}}</text>
        <view class="list-tags">
          <view wx:if="{{isLowStock}}" class="mini-tag warning">库存不足</view>
          <view wx:if="{{isNearExpiry}}" class="mini-tag danger">即将过期</view>
        </view>
      </view>
      <text class="list-spec">{{material.specification || '规格未指定'}}</text>
      <view class="list-stock">
        <text class="stock-text" style="color: {{stockColor}}">{{displayStock}} {{material.unit || ''}}</text>
        <text class="stock-status">{{stockStatusText}}</text>
      </view>
    </view>
    
    <view class="list-arrow">
      <global-icon name="前进" size="12" color="var(--global-text-color-tertiary)"></global-icon>
    </view>
  </view>

  <!-- 网格模式 -->
  <view wx:elif="{{mode === 'grid'}}" class="grid-content">
    <view class="grid-header">
      <view class="grid-icon" style="background-color: {{categoryConfig.color}}20">
        <global-icon
          name="{{categoryConfig.icon || '物料'}}"
          size="18"
          color="{{categoryConfig.color || 'var(--global-primary-color)'}}">
        </global-icon>
      </view>
      <view wx:if="{{isLowStock || isNearExpiry}}" class="grid-badge">
        <global-icon
          name="{{(isNearExpiry ? '时间' : '警告') || '警告'}}"
          size="8"
          color="{{isNearExpiry ? '#F44336' : '#FF9800'}}">
        </global-icon>
      </view>
    </view>
    
    <view class="grid-info">
      <text class="grid-name">{{material.name}}</text>
      <text class="grid-spec">{{material.specification || ''}}</text>
    </view>
    
    <view class="grid-stock">
      <text class="grid-number" style="color: {{stockColor}}">{{displayStock}}</text>
      <text class="grid-unit">{{material.unit || ''}}</text>
    </view>
    
    <view class="grid-progress">
      <view class="mini-progress-bar">
        <view class="mini-progress-fill" 
              style="width: {{stockPercentage}}%; background-color: {{stockColor}}">
        </view>
      </view>
    </view>
  </view>

  <!-- 紧凑模式 -->
  <view wx:elif="{{mode === 'compact'}}" class="compact-content">
    <view class="compact-left">
      <view class="compact-icon" style="background-color: {{categoryConfig.color}}20">
        <global-icon
          name="{{categoryConfig.icon || '物料'}}"
          size="14"
          color="{{categoryConfig.color || 'var(--global-primary-color)'}}">
        </global-icon>
      </view>
      <view class="compact-info">
        <text class="compact-name">{{material.name}}</text>
        <text class="compact-stock" style="color: {{stockColor}}">{{displayStock}} {{material.unit || ''}}</text>
      </view>
    </view>
    
    <view class="compact-right">
      <view wx:if="{{isLowStock || isNearExpiry}}" class="compact-alert">
        <global-icon
          name="{{(isNearExpiry ? '时间' : '警告') || '警告'}}"
          size="10"
          color="{{isNearExpiry ? '#F44336' : '#FF9800'}}">
        </global-icon>
      </view>
      <global-icon name="前进" size="10" color="var(--global-text-color-tertiary)"></global-icon>
    </view>
  </view>

  <!-- 自定义插槽 -->
  <slot name="extra"></slot>
</view>