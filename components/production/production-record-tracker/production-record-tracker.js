// components/production/production-record-tracker/production-record-tracker.js
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');

/**
 * Production生产记录追踪组件
 * 功能：生产记录管理、时间轴展示、数据统计、批次管理、导出功能
 * 设计：工业级生产记录界面，支持多种记录类型和显示模式
 */

const componentConfig = {
  options: {
    addGlobalClass: true,
    multipleSlots: true
  },

  properties: {
    // 记录数据
    records: {
      type: Array,
      value: [],
      observer: function(newVal) {
        this.processRecords(newVal);
      }
    },

    // 记录类型
    type: {
      type: String,
      value: 'all' // all, growth, weight, sale, health, feed, medicine
    },

    // 批次ID
    batchId: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },

    // 显示模式
    mode: {
      type: String,
      value: 'timeline' // timeline, list, stats, card
    },

    // 是否显示批次信息
    showBatch: {
      type: Boolean,
      value: true
    },

    // 是否允许编辑
    allowEdit: {
      type: Boolean,
      value: true
    },

    // 是否显示统计
    showStats: {
      type: Boolean,
      value: true
    },

    // 时间范围
    timeRange: {
      type: String,
      value: '7d' // 1d, 7d, 30d, 3m, 6m, 1y, all
    },

    // 分页配置
    pagination: {
      type: Object,
      value: {
        page: 1,
        pageSize: 20,
        total: 0
      }
    },

    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    }
  },

  data: {
    // 处理后的记录数据
    processedRecords: [],

    // 记录类型配置
    typeConfig: {
      growth: {
        name: '生长记录',
        icon: '成长',
        color: '#4CAF50',
        unit: '天',
        fields: ['age', 'weight', 'height', 'health']
      },
      weight: {
        name: '称重记录',
        icon: '称重',
        color: '#2196F3',
        unit: 'kg',
        fields: ['weight', 'count', 'avgWeight']
      },
      sale: {
        name: '出栏记录',
        icon: '出栏',
        color: '#FF9800',
        unit: '只',
        fields: ['count', 'totalWeight', 'price', 'buyer']
      },
      health: {
        name: '健康记录',
        icon: '健康',
        color: '#E91E63',
        unit: '次',
        fields: ['temperature', 'symptoms', 'treatment', 'medicine']
      },
      feed: {
        name: '喂养记录',
        icon: '饲料',
        color: '#795548',
        unit: 'kg',
        fields: ['feedType', 'amount', 'time', 'notes']
      },
      medicine: {
        name: '用药记录',
        icon: '药品',
        color: '#9C27B0',
        unit: 'ml',
        fields: ['medicine', 'dosage', 'reason', 'effect']
      }
    },

    // 统计数据
    statistics: {
      total: 0,
      byType: {},
      byDate: {},
      trends: {}
    },

    // 过滤器
    filters: {
      dateRange: [],
      types: [],
      batchIds: []
    },

    // 加载状态
    loading: false,
    loadingMore: false,

    // 选中的记录
    selectedRecords: []
  },

  methods: {
    // 处理记录数据
    processRecords: function(records) {
      if (!records || records.length === 0) {
        this.setData({
          processedRecords: [],
          statistics: { total: 0, byType: {}, byDate: {}, trends: {} }
        });
        return;
      }

      // 按时间排序
      const sortedRecords = records.sort((a, b) => new Date(b.date) - new Date(a.date));
      
      // 过滤记录
      const filteredRecords = this.filterRecords(sortedRecords);
      
      // 分组处理
      const groupedRecords = this.groupRecords(filteredRecords);
      
      // 计算统计
      const statistics = this.calculateStatistics(filteredRecords);

      this.setData({
        processedRecords: groupedRecords,
        statistics: statistics
      });
    },

    // 过滤记录
    filterRecords: function(records) {
      return records.filter(record => {
        // 类型过滤
        if (this.data.type !== 'all' && record.type !== this.data.type) {
          return false;
        }

        // 批次过滤
        if (this.data.batchId && record.batchId !== this.data.batchId) {
          return false;
        }

        // 时间范围过滤
        if (this.data.timeRange !== 'all') {
          const recordDate = new Date(record.date);
          const now = new Date();
          const timeRanges = {
            '1d': 1,
            '7d': 7,
            '30d': 30,
            '3m': 90,
            '6m': 180,
            '1y': 365
          };
          const days = timeRanges[this.data.timeRange];
          if (days && (now - recordDate) > days * 24 * 60 * 60 * 1000) {
            return false;
          }
        }

        // 自定义过滤器
        const { filters } = this.data;
        if (filters.types.length > 0 && !filters.types.includes(record.type)) {
          return false;
        }
        if (filters.batchIds.length > 0 && !filters.batchIds.includes(record.batchId)) {
          return false;
        }

        return true;
      });
    },

    // 分组记录
    groupRecords: function(records) {
      if (this.data.mode === 'timeline') {
        // 按日期分组
        const grouped = {};
        records.forEach(record => {
          const date = new Date(record.date).toDateString();
          if (!grouped[date]) {
            grouped[date] = [];
          }
          grouped[date].push(record);
        });
        
        return Object.keys(grouped).map(date => ({
          date: date,
          records: grouped[date]
        }));
      } else if (this.data.mode === 'stats') {
        // 按类型分组
        const grouped = {};
        records.forEach(record => {
          if (!grouped[record.type]) {
            grouped[record.type] = [];
          }
          grouped[record.type].push(record);
        });
        return grouped;
      } else {
        // 简单列表
        return records;
      }
    },

    // 计算统计数据
    calculateStatistics: function(records) {
      const stats = {
        total: records.length,
        byType: {},
        byDate: {},
        trends: {}
      };

      // 按类型统计
      records.forEach(record => {
        if (!stats.byType[record.type]) {
          stats.byType[record.type] = 0;
        }
        stats.byType[record.type]++;

        // 按日期统计
        const date = new Date(record.date).toDateString();
        if (!stats.byDate[date]) {
          stats.byDate[date] = 0;
        }
        stats.byDate[date]++;
      });

      // 计算趋势
      const dates = Object.keys(stats.byDate).sort();
      if (dates.length > 1) {
        const recent = stats.byDate[dates[dates.length - 1]] || 0;
        const previous = stats.byDate[dates[dates.length - 2]] || 0;
        stats.trends.daily = recent - previous;
      }

      return stats;
    },

    // 添加记录
    onAddRecord: function() {
      this.triggerEvent('addRecord', {
        type: this.data.type,
        batchId: this.data.batchId
      });
    },

    // 记录项点击
    onRecordTap: function(e) {
      const { record } = e.currentTarget.dataset;
      this.triggerEvent('recordTap', {
        record: record,
        mode: this.data.mode
      });
    },

    // 编辑记录
    onEditRecord: function(e) {
      const { record } = e.currentTarget.dataset;
      if (!this.data.allowEdit) return;
      
      this.triggerEvent('editRecord', {
        record: record
      });
    },

    // 删除记录
    onDeleteRecord: function(e) {
      const { record } = e.currentTarget.dataset;
      if (!this.data.allowEdit) return;

      wx.showModal({
        title: '确认删除',
        content: '确定要删除这条记录吗？此操作不可撤销。',
        success: (res) => {
          if (res.confirm) {
            this.triggerEvent('deleteRecord', {
              record: record
            });
          }
        }
      });
    },

    // 复制记录
    onCopyRecord: function(e) {
      const { record } = e.currentTarget.dataset;
      this.triggerEvent('copyRecord', {
        record: record
      });
    },

    // 分享记录
    onShareRecord: function(e) {
      const { record } = e.currentTarget.dataset;
      this.triggerEvent('shareRecord', {
        record: record
      });
    },

    // 导出记录
    onExportRecords: function() {
      if (this.data.processedRecords.length === 0) {
        wx.showToast({
          title: '暂无记录可导出',
          icon: 'none'
        });
        return;
      }

      this.triggerEvent('exportRecords', {
        records: this.data.processedRecords,
        type: this.data.type,
        timeRange: this.data.timeRange,
        statistics: this.data.statistics
      });
    },

    // 筛选记录
    onFilterRecords: function() {
      this.triggerEvent('filterRecords', {
        currentFilters: this.data.filters
      });
    },

    // 切换显示模式
    onModeChange: function(e) {
      const { mode } = e.currentTarget.dataset;
      this.setData({ mode: mode });
      this.processRecords(this.data.records);
      
      this.triggerEvent('modeChange', {
        mode: mode
      });
    },

    // 切换时间范围
    onTimeRangeChange: function(e) {
      const { range } = e.currentTarget.dataset;
      this.setData({ timeRange: range });
      this.processRecords(this.data.records);
      
      this.triggerEvent('timeRangeChange', {
        timeRange: range
      });
    },

    // 刷新数据
    onRefresh: function() {
      this.setData({ loading: true });
      this.triggerEvent('refresh', {
        type: this.data.type,
        batchId: this.data.batchId,
        timeRange: this.data.timeRange
      });
      
      setTimeout(() => {
        this.setData({ loading: false });
      }, 1000);
    },

    // 加载更多
    onLoadMore: function() {
      if (this.data.loadingMore) return;
      
      const { pagination } = this.data;
      if (pagination.page * pagination.pageSize >= pagination.total) {
        return;
      }

      this.setData({ loadingMore: true });
      this.triggerEvent('loadMore', {
        page: pagination.page + 1,
        pageSize: pagination.pageSize
      });
    },

    // 选择记录
    onSelectRecord: function(e) {
      const { record } = e.currentTarget.dataset;
      const { selectedRecords } = this.data;
      
      const index = selectedRecords.findIndex(r => r.id === record.id);
      if (index > -1) {
        selectedRecords.splice(index, 1);
      } else {
        selectedRecords.push(record);
      }
      
      this.setData({ selectedRecords: selectedRecords });
      this.triggerEvent('recordSelect', {
        selectedRecords: selectedRecords,
        record: record
      });
    },

    // 格式化日期
    formatDate: function(dateStr) {
      const date = new Date(dateStr);
      const now = new Date();
      const diff = Math.floor((now - date) / 1000);

      if (diff < 60) return '刚刚';
      if (diff < 3600) return `${Math.floor(diff / 60)}分钟前`;
      if (diff < 86400) return `${Math.floor(diff / 3600)}小时前`;
      
      const days = Math.floor(diff / 86400);
      if (days === 1) return '昨天';
      if (days < 7) return `${days}天前`;
      
      return date.toLocaleDateString();
    },

    // 格式化数值
    formatValue: function(value, type) {
      const config = this.data.typeConfig[type];
      if (!config) return value;
      
      if (typeof value === 'number') {
        return value.toFixed(1) + (config.unit || '');
      }
      return value;
    },

    // 获取记录图标
    getRecordIcon: function(type) {
      return this.data.typeConfig[type]?.icon || '记录';
    },

    // 获取记录颜色
    getRecordColor: function(type) {
      return this.data.typeConfig[type]?.color || '#666';
    }
  },

  lifetimes: {
    attached: function() {
      // 初始化处理记录
      if (this.data.records.length > 0) {
        this.processRecords(this.data.records);
      }
    }
  },

  observers: {
    'type, timeRange, batchId': function() {
      // 过滤条件变化时重新处理记录
      this.processRecords(this.data.records);
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);