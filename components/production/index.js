// components/production/index.js

/**
 * Production生产管理专用组件库
 * 统一导出所有Production模块组件
 * 提供组件注册、配置管理、版本控制等功能
 */

// 组件版本信息
const PRODUCTION_COMPONENT_VERSION = '1.0.0';
const PRODUCTION_COMPONENT_BUILD = '20241230';

// 组件列表
const ProductionComponents = {
  // 环境监控组件
  'production-environment-card': {
    path: '/components/production/production-environment-card/production-environment-card',
    name: '环境监控卡片',
    description: '实时环境数据展示、状态监控、趋势分析组件',
    version: '1.0.0',
    category: 'monitoring',
    features: [
      '多环境支持 (温度/湿度/PM2.5/光照/CO₂/NH₃)',
      '智能状态检测 (正常/警告/危险/离线)',
      '三种显示模式 (card/compact/dashboard)',
      '趋势分析和可视化',
      '智能预警系统',
      '历史数据对比'
    ],
    dependencies: ['global-icon'],
    props: {
      type: 'String',
      value: 'Number',
      unit: 'String',
      status: 'String',
      threshold: 'Object',
      trend: 'Array',
      showTrend: 'Boolean',
      showAlert: 'Boolean',
      mode: 'String'
    },
    events: [
      'statusChange',
      'viewTrend', 
      'refresh',
      'setAlert'
    ]
  },

  // 物料管理组件
  'production-material-card': {
    path: '/components/production/production-material-card/production-material-card',
    name: '物料管理卡片',
    description: '物料库存管理、分类展示、预警提醒组件',
    version: '1.0.0',
    category: 'management',
    features: [
      '分类管理 (饲料/药品/设备/其他)',
      '库存状态监控 (正常/不足/危险/过期)',
      '三种展示模式 (card/list/grid)',
      '智能预警机制',
      '供应商信息管理',
      '快捷操作支持'
    ],
    dependencies: ['global-icon'],
    props: {
      material: 'Object',
      category: 'String',
      mode: 'String',
      showActions: 'Boolean',
      showSupplier: 'Boolean'
    },
    events: [
      'materialTap',
      'stockWarning',
      'supplierContact',
      'inStock',
      'outStock'
    ]
  },

  // 生产记录追踪组件
  'production-record-tracker': {
    path: '/components/production/production-record-tracker/production-record-tracker',
    name: '生产记录追踪器',
    description: '生产记录管理、时间轴展示、数据统计组件',
    version: '1.0.0',
    category: 'tracking',
    features: [
      '多记录类型 (生长/称重/出栏/健康/喂养/用药)',
      '三种显示模式 (timeline/list/stats)',
      '智能数据统计和趋势分析',
      '灵活的筛选和排序',
      '批量操作支持',
      '记录导出功能'
    ],
    dependencies: ['global-icon'],
    props: {
      records: 'Array',
      type: 'String',
      batchId: 'String',
      mode: 'String',
      showBatch: 'Boolean',
      allowEdit: 'Boolean',
      timeRange: 'String'
    },
    events: [
      'addRecord',
      'recordTap',
      'editRecord',
      'deleteRecord',
      'exportRecords',
      'filterRecords'
    ]
  },

  // AI智能盘点组件
  'production-ai-inventory': {
    path: '/components/production/production-ai-inventory/production-ai-inventory',
    name: 'AI智能盘点面板',
    description: 'AI图像识别盘点、流程管理、结果校正组件',
    version: '1.0.0',
    category: 'ai',
    features: [
      'AI图像识别技术',
      '四阶段盘点流程 (拍照/识别/结果/历史)',
      '多盘点类型支持 (鹅群/鹅蛋/饲料)',
      '人工校正功能',
      '置信度评估',
      '历史记录对比'
    ],
    dependencies: ['global-icon'],
    props: {
      mode: 'String',
      batchId: 'String',
      aiConfig: 'Object',
      showHistory: 'Boolean',
      allowManualEdit: 'Boolean',
      historyRecords: 'Array'
    },
    events: [
      'modeChange',
      'recognitionComplete',
      'manualCorrection',
      'saveResult',
      'shareResult',
      'historyTap'
    ]
  },

  // 数据看板组件
  'production-data-dashboard': {
    path: '/components/production/production-data-dashboard/production-data-dashboard',
    name: '数据可视化看板',
    description: '综合数据展示、多图表类型、实时更新组件',
    version: '1.0.0',
    category: 'visualization',
    features: [
      '五种看板类型 (综合/环境/生产/财务/健康)',
      'KPI指标卡片展示',
      '多种图表类型支持',
      '实时数据刷新',
      '交互式筛选功能',
      '多格式数据导出'
    ],
    dependencies: ['global-icon'],
    props: {
      dashboardType: 'String',
      timeRange: 'String',
      chartConfig: 'Object',
      dataSource: 'Array',
      showControls: 'Boolean',
      allowExport: 'Boolean'
    },
    events: [
      'dataLoad',
      'typeChange',
      'timeRangeChange',
      'kpiTap',
      'chartTap',
      'dataExport'
    ]
  }
};

// 组件分类
const ComponentCategories = {
  monitoring: {
    name: '监控类组件',
    description: '环境监控、状态检测相关组件',
    icon: '监控',
    color: '#4CAF50'
  },
  management: {
    name: '管理类组件',
    description: '物料管理、库存管理相关组件',
    icon: '管理',
    color: '#2196F3'
  },
  tracking: {
    name: '追踪类组件',
    description: '记录追踪、数据统计相关组件',
    icon: '追踪',
    color: '#FF9800'
  },
  ai: {
    name: 'AI智能组件',
    description: 'AI识别、智能分析相关组件',
    icon: '智能',
    color: '#9C27B0'
  },
  visualization: {
    name: '可视化组件',
    description: '数据展示、图表可视化相关组件',
    icon: '图表',
    color: '#E91E63'
  }
};

// 组件配置管理
const ComponentConfig = {
  // 默认主题配置
  defaultTheme: {
    primaryColor: '#4CAF50',
    secondaryColor: '#2196F3',
    warningColor: '#FF9800',
    errorColor: '#F44336',
    successColor: '#4CAF50'
  },

  // 默认尺寸配置
  defaultSizes: {
    sm: {
      padding: '8px',
      fontSize: '12px',
      iconSize: '14px'
    },
    md: {
      padding: '12px',
      fontSize: '14px',
      iconSize: '16px'
    },
    lg: {
      padding: '16px',
      fontSize: '16px',
      iconSize: '18px'
    }
  },

  // 默认动画配置
  defaultAnimations: {
    duration: '300ms',
    easing: 'ease-in-out',
    delay: '0ms'
  }
};

// 组件注册函数
function registerProductionComponents(page) {
  const usingComponents = {};
  
  Object.keys(ProductionComponents).forEach(componentName => {
    const component = ProductionComponents[componentName];
    usingComponents[componentName] = component.path;
  });
  
  return usingComponents;
}

// 获取组件信息
function getComponentInfo(componentName) {
  return ProductionComponents[componentName] || null;
}

// 获取组件列表
function getComponentList(category = null) {
  if (!category) {
    return Object.keys(ProductionComponents).map(name => ({
      name,
      ...ProductionComponents[name]
    }));
  }
  
  return Object.keys(ProductionComponents)
    .filter(name => ProductionComponents[name].category === category)
    .map(name => ({
      name,
      ...ProductionComponents[name]
    }));
}

// 获取分类信息
function getCategoryInfo(category) {
  return ComponentCategories[category] || null;
}

// 获取所有分类
function getAllCategories() {
  return Object.keys(ComponentCategories).map(category => ({
    category,
    ...ComponentCategories[category]
  }));
}

// 检查组件依赖
function checkDependencies(componentName) {
  const component = ProductionComponents[componentName];
  if (!component) return { valid: false, message: '组件不存在' };
  
  const missing = [];
  if (component.dependencies) {
    component.dependencies.forEach(dep => {
      // 这里应该检查依赖组件是否存在
      // 简化处理，假设依赖都存在
    });
  }
  
  return {
    valid: missing.length === 0,
    missing: missing,
    message: missing.length > 0 ? `缺少依赖: ${missing.join(', ')}` : '依赖检查通过'
  };
}

// 获取版本信息
function getVersionInfo() {
  return {
    version: PRODUCTION_COMPONENT_VERSION,
    build: PRODUCTION_COMPONENT_BUILD,
    components: Object.keys(ProductionComponents).length,
    categories: Object.keys(ComponentCategories).length,
    buildDate: new Date().toISOString()
  };
}

// 组件使用统计
function getUsageStats() {
  return {
    totalComponents: Object.keys(ProductionComponents).length,
    categoriesDistribution: Object.keys(ComponentCategories).reduce((acc, category) => {
      acc[category] = getComponentList(category).length;
      return acc;
    }, {}),
    averageFeatures: Math.round(
      Object.values(ProductionComponents).reduce((sum, comp) => sum + comp.features.length, 0) /
      Object.keys(ProductionComponents).length
    )
  };
}

// 导出所有功能
module.exports = {
  // 组件数据
  ProductionComponents,
  ComponentCategories,
  ComponentConfig,
  
  // 核心函数
  registerProductionComponents,
  getComponentInfo,
  getComponentList,
  getCategoryInfo,
  getAllCategories,
  checkDependencies,
  getVersionInfo,
  getUsageStats,
  
  // 版本信息
  version: PRODUCTION_COMPONENT_VERSION,
  build: PRODUCTION_COMPONENT_BUILD
};

// 使用示例:
/*
// 在页面中注册所有Production组件
const ProductionLibrary = require('/components/production/index.js');

Page({
  // 自动注册所有组件
  usingComponents: ProductionLibrary.registerProductionComponents(),
  
  onLoad: function() {
    // 获取组件库信息
    );
    
    // 获取监控类组件列表
    const monitoringComponents = ProductionLibrary.getComponentList('monitoring');
    
    // 检查组件依赖
    const depCheck = ProductionLibrary.checkDependencies('production-environment-card');
  }
});
*/