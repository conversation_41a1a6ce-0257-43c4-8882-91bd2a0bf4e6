// components/production/production-ai-inventory/production-ai-inventory.js
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');

/**
 * Production AI智能盘点组件
 * 功能：AI图像识别、盘点流程管理、准确度显示、人工校正、历史对比
 * 设计：智能化盘点界面，支持多阶段盘点流程和AI识别技术
 */

const componentConfig = {
  options: {
    addGlobalClass: true,
    multipleSlots: true
  },

  properties: {
    // 盘点模式
    mode: {
      type: String,
      value: 'camera', // camera, processing, result, history
      observer: function(newVal, oldVal) {
        this.onModeChange(newVal, oldVal);
      }
    },

    // 盘点批次
    batchId: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },

    // AI识别配置
    aiConfig: {
      type: Object,
      value: {
        confidence: 0.8,        // 最低置信度
        maxCount: 1000,         // 最大识别数量
        enableMultiAngle: true, // 多角度识别
        autoCorrection: true    // 自动校正
      }
    },

    // 是否显示历史记录
    showHistory: {
      type: Boolean,
      value: true
    },

    // 是否允许手动编辑
    allowManualEdit: {
      type: Boolean,
      value: true
    },

    // 历史盘点记录
    historyRecords: {
      type: Array,
      value: []
    },

    // 当前盘点数据
    currentInventory: {
      type: Object,
      value: null
    },

    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    }
  },

  data: {
    // 拍照相关
    cameraAuth: false,
    cameraContext: null,
    capturedImages: [],
    
    // AI识别状态
    aiProcessing: false,
    recognitionProgress: 0,
    recognitionSteps: [
      { step: 1, name: '图像预处理', status: 'pending' },
      { step: 2, name: 'AI模型分析', status: 'pending' },
      { step: 3, name: '目标识别', status: 'pending' },
      { step: 4, name: '数量统计', status: 'pending' },
      { step: 5, name: '结果校验', status: 'pending' }
    ],

    // 识别结果
    recognitionResult: {
      totalCount: 0,
      confidence: 0,
      detectedObjects: [],
      analysisTime: 0,
      suggestions: []
    },

    // 人工校正数据
    manualCorrection: {
      enabled: false,
      originalCount: 0,
      correctedCount: 0,
      corrections: []
    },

    // 盘点比较
    comparison: {
      lastInventory: null,
      difference: 0,
      changeRate: 0
    },

    // UI状态
    showCorrectionModal: false,
    showHistoryModal: false,
    showResultDetail: false,

    // 盘点类型配置
    inventoryTypes: {
      goose: {
        name: '鹅群盘点',
        icon: '动物',
        color: '#4CAF50',
        aiModel: 'goose-detection-v2',
        avgAccuracy: 0.92,
        accuracyPercent: 92
      },
      egg: {
        name: '鹅蛋盘点',
        icon: '蛋',
        color: '#FF9800',
        aiModel: 'egg-detection-v1',
        avgAccuracy: 0.88,
        accuracyPercent: 88
      },
      feed: {
        name: '饲料盘点',
        icon: '饲料',
        color: '#795548',
        aiModel: 'feed-detection-v1',
        avgAccuracy: 0.85,
        accuracyPercent: 85
      }
    },

    // 当前盘点类型
    currentType: 'goose',
    
    // 计算属性
    computedConfidencePercent: 80,
    recognitionProgressPercent: 0,
    analysisProgressPercent: 0
  },

  methods: {
    // 更新计算值
    updateComputedValues: function() {
      const { aiConfig } = this.data;
      this.setData({
        computedConfidencePercent: Math.round(aiConfig.confidence * 100)
      });
    },
    
    // 模式变化处理
    onModeChange: function(newMode, oldMode) {
      
      if (newMode === 'camera') {
        this.initCamera();
      } else if (newMode === 'processing') {
        this.startAIRecognition();
      } else if (newMode === 'result') {
        this.processResults();
      }
    },

    // 初始化相机
    initCamera: function() {
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.camera']) {
            this.setData({ cameraAuth: true });
            this.createCameraContext();
          } else {
            this.requestCameraAuth();
          }
        }
      });
    },

    // 请求相机权限
    requestCameraAuth: function() {
      wx.authorize({
        scope: 'scope.camera',
        success: () => {
          this.setData({ cameraAuth: true });
          this.createCameraContext();
        },
        fail: () => {
          wx.showModal({
            title: '相机权限',
            content: '需要相机权限才能进行AI盘点，请在设置中开启',
            showCancel: false
          });
        }
      });
    },

    // 创建相机上下文
    createCameraContext: function() {
      const cameraContext = wx.createCameraContext();
      this.setData({ cameraContext: cameraContext });
    },

    // 拍照
    onTakePhoto: function() {
      if (!this.data.cameraContext) {
        wx.showToast({
          title: '相机未就绪',
          icon: 'none'
        });
        return;
      }

      this.data.cameraContext.takePhoto({
        quality: 'high',
        success: (res) => {
          const images = [...this.data.capturedImages, {
            id: Date.now(),
            path: res.tempImagePath,
            timestamp: new Date().toISOString(),
            processed: false
          }];
          
          this.setData({ capturedImages: images });
          wx.vibrateShort();
          
          // 提示用户
          wx.showToast({
            title: '拍照成功',
            icon: 'success'
          });

          // 如果启用自动处理，直接进入处理模式
          if (this.data.aiConfig.autoProcess) {
            this.onStartProcessing();
          }
        },
        fail: (err) => {
          console.error('拍照失败:', err);
          wx.showToast({
            title: '拍照失败',
            icon: 'none'
          });
        }
      });
    },

    // 重新拍照
    onRetakePhoto: function() {
      this.setData({ 
        capturedImages: [],
        recognitionResult: {
          totalCount: 0,
          confidence: 0,
          detectedObjects: [],
          analysisTime: 0,
          suggestions: []
        }
      });
    },

    // 开始AI处理
    onStartProcessing: function() {
      if (this.data.capturedImages.length === 0) {
        wx.showToast({
          title: '请先拍照',
          icon: 'none'
        });
        return;
      }

      this.setData({ mode: 'processing' });
      this.triggerEvent('modeChange', { mode: 'processing' });
    },

    // 开始AI识别
    startAIRecognition: function() {
      this.setData({ 
        aiProcessing: true,
        recognitionProgress: 0
      });

      // 重置识别步骤状态
      const steps = this.data.recognitionSteps.map(step => ({
        ...step,
        status: 'pending'
      }));
      this.setData({ recognitionSteps: steps });

      // 模拟AI识别过程
      this.simulateAIProcess();
    },

    // 模拟AI识别过程
    simulateAIProcess: function() {
      let currentStep = 0;
      const steps = [...this.data.recognitionSteps];
      
      const processStep = () => {
        if (currentStep < steps.length) {
          // 更新当前步骤状态
          steps[currentStep].status = 'processing';
          this.setData({ 
            recognitionSteps: steps,
            recognitionProgress: ((currentStep + 1) / steps.length) * 100
          });

          // 模拟处理时间
          setTimeout(() => {
            steps[currentStep].status = 'completed';
            this.setData({ recognitionSteps: steps });
            currentStep++;
            processStep();
          }, 800 + Math.random() * 1200);
        } else {
          // 所有步骤完成，生成识别结果
          this.generateRecognitionResult();
        }
      };

      processStep();
    },

    // 生成识别结果
    generateRecognitionResult: function() {
      const config = this.data.inventoryTypes[this.data.currentType];
      const baseCount = 80 + Math.floor(Math.random() * 40); // 80-120
      const confidence = 0.75 + Math.random() * 0.2; // 0.75-0.95
      
      const result = {
        totalCount: baseCount,
        confidence: confidence,
        confidencePercent: Math.round(confidence * 100),
        detectedObjects: this.generateDetectedObjects(baseCount),
        analysisTime: Date.now() - this.startTime,
        suggestions: this.generateSuggestions(confidence)
      };

      this.setData({
        recognitionResult: result,
        aiProcessing: false,
        mode: 'result'
      });

      // 计算与历史记录的比较
      this.calculateComparison(result.totalCount);

      this.triggerEvent('recognitionComplete', {
        result: result,
        batchId: this.data.batchId
      });
    },

    // 生成检测对象
    generateDetectedObjects: function(totalCount) {
      const objects = [];
      for (let i = 0; i < totalCount; i++) {
        const confidence = 0.7 + Math.random() * 0.25;
        objects.push({
          id: i + 1,
          confidence: confidence,
          confidencePercent: Math.round(confidence * 100),
          bbox: {
            x: Math.random() * 800,
            y: Math.random() * 600,
            width: 20 + Math.random() * 40,
            height: 20 + Math.random() * 40
          },
          status: Math.random() > 0.1 ? 'healthy' : 'attention'
        });
      }
      return objects;
    },

    // 生成建议
    generateSuggestions: function(confidence) {
      const suggestions = [];
      
      if (confidence < 0.8) {
        suggestions.push({
          type: 'warning',
          message: '识别置信度较低，建议手动校验结果',
          action: 'manual_check'
        });
      }

      if (confidence > 0.9) {
        suggestions.push({
          type: 'success',
          message: '识别结果准确度很高，可直接保存',
          action: 'auto_save'
        });
      }

      suggestions.push({
        type: 'info',
        message: '建议多角度拍摄以提高识别准确度',
        action: 'multi_angle'
      });

      return suggestions;
    },

    // 计算与历史记录比较
    calculateComparison: function(currentCount) {
      const { historyRecords } = this.data;
      if (historyRecords.length === 0) return;

      const lastRecord = historyRecords[historyRecords.length - 1];
      const difference = currentCount - lastRecord.count;
      const changeRate = ((difference / lastRecord.count) * 100).toFixed(1);

      this.setData({
        comparison: {
          lastInventory: lastRecord,
          difference: difference,
          changeRate: parseFloat(changeRate)
        }
      });
    },

    // 手动校正
    onManualCorrection: function() {
      this.setData({
        showCorrectionModal: true,
        'manualCorrection.enabled': true,
        'manualCorrection.originalCount': this.data.recognitionResult.totalCount
      });
    },

    // 确认校正
    onConfirmCorrection: function(e) {
      const correctedCount = parseInt(e.detail.value) || this.data.recognitionResult.totalCount;
      
      this.setData({
        'manualCorrection.correctedCount': correctedCount,
        'recognitionResult.totalCount': correctedCount,
        showCorrectionModal: false
      });

      this.triggerEvent('manualCorrection', {
        original: this.data.manualCorrection.originalCount,
        corrected: correctedCount,
        difference: correctedCount - this.data.manualCorrection.originalCount
      });
    },

    // 取消校正
    onCancelCorrection: function() {
      this.setData({
        showCorrectionModal: false,
        'manualCorrection.enabled': false
      });
    },

    // 保存盘点结果
    onSaveResult: function() {
      const result = {
        id: Date.now(),
        batchId: this.data.batchId,
        type: this.data.currentType,
        count: this.data.recognitionResult.totalCount,
        confidence: this.data.recognitionResult.confidence,
        timestamp: new Date().toISOString(),
        images: this.data.capturedImages,
        manualCorrected: this.data.manualCorrection.enabled,
        aiModel: this.data.inventoryTypes[this.data.currentType].aiModel
      };

      this.triggerEvent('saveResult', {
        result: result
      });

      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });
    },

    // 重新盘点
    onRestartInventory: function() {
      this.setData({
        mode: 'camera',
        capturedImages: [],
        recognitionResult: {
          totalCount: 0,
          confidence: 0,
          detectedObjects: [],
          analysisTime: 0,
          suggestions: []
        },
        manualCorrection: {
          enabled: false,
          originalCount: 0,
          correctedCount: 0,
          corrections: []
        }
      });

      this.triggerEvent('restart', {});
    },

    // 查看历史记录
    onViewHistory: function() {
      this.setData({ showHistoryModal: true });
    },

    // 关闭历史记录
    onCloseHistory: function() {
      this.setData({ showHistoryModal: false });
    },

    // 历史记录项点击
    onHistoryItemTap: function(e) {
      const { record } = e.currentTarget.dataset;
      this.triggerEvent('historyTap', { record });
    },

    // 切换盘点类型
    onTypeChange: function(e) {
      const { type } = e.currentTarget.dataset;
      this.setData({ currentType: type });
      
      this.triggerEvent('typeChange', {
        type: type,
        config: this.data.inventoryTypes[type]
      });
    },

    // 查看结果详情
    onViewResultDetail: function() {
      this.setData({ showResultDetail: true });
    },

    // 关闭结果详情
    onCloseResultDetail: function() {
      this.setData({ showResultDetail: false });
    },

    // 分享结果
    onShareResult: function() {
      this.triggerEvent('shareResult', {
        result: this.data.recognitionResult,
        batchId: this.data.batchId
      });
    },

    // 导出结果
    onExportResult: function() {
      this.triggerEvent('exportResult', {
        result: this.data.recognitionResult,
        images: this.data.capturedImages,
        batchId: this.data.batchId
      });
    }
  },

  lifetimes: {
    attached: function() {
      this.startTime = Date.now();
      
      // 初始化相机（如果是拍照模式）
      if (this.data.mode === 'camera') {
        this.initCamera();
      }
    },

    detached: function() {
      // 清理资源
      if (this.data.cameraContext) {
        this.data.cameraContext = null;
      }
    }
  },

  observers: {
    'capturedImages': function(images) {
      // 图片变化时的处理
      if (images.length > 0) {
        this.triggerEvent('imagesCapture', { images });
      }
    },
    
    'aiConfig.confidence': function(confidence) {
      this.setData({
        computedConfidencePercent: Math.round(confidence * 100)
      });
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);