// components/production/production-data-dashboard/production-data-dashboard.js
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');

/**
 * Production数据可视化看板组件
 * 功能：综合数据展示、多图表类型、实时更新、交互筛选、导出分享
 * 设计：工业级数据看板界面，支持多种图表和数据源
 */

const componentConfig = {
  options: {
    addGlobalClass: true,
    multipleSlots: true
  },

  properties: {
    // 看板类型
    dashboardType: {
      type: String,
      value: 'overview' // overview, environment, production, finance, health
    },

    // 时间范围
    timeRange: {
      type: String,
      value: '7d', // 1d, 7d, 30d, 3m, 6m, 1y
      observer: function(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.loadDashboardData();
        }
      }
    },

    // 图表配置
    chartConfig: {
      type: Object,
      value: {
        showLegend: true,
        showGrid: true,
        showTooltip: true,
        animationDuration: 800,
        colors: ['#4CAF50', '#2196F3', '#FF9800', '#F44336', '#9C27B0']
      }
    },

    // 数据源
    dataSource: {
      type: Array,
      value: [],
      observer: function(newVal) {
        this.processDataSource(newVal);
      }
    },

    // 是否显示控制器
    showControls: {
      type: Boolean,
      value: true
    },

    // 是否允许导出
    allowExport: {
      type: Boolean,
      value: true
    },

    // 刷新间隔（秒）
    refreshInterval: {
      type: Number,
      value: 30
    },

    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    }
  },

  data: {
    // 处理后的数据
    processedData: {
      overview: {},
      charts: [],
      kpis: [],
      trends: {}
    },

    // 看板类型配置
    dashboardTypes: {
      overview: {
        name: '综合概览',
        icon: '概览',
        color: '#2196F3',
        charts: ['kpi', 'line', 'bar', 'pie']
      },
      environment: {
        name: '环境监控',
        icon: '环境',
        color: '#4CAF50',
        charts: ['gauge', 'line', 'area']
      },
      production: {
        name: '生产数据',
        icon: '生产',
        color: '#FF9800',
        charts: ['bar', 'line', 'scatter']
      },
      finance: {
        name: '财务分析',
        icon: '财务',
        color: '#9C27B0',
        charts: ['line', 'bar', 'pie']
      },
      health: {
        name: '健康监测',
        icon: '健康',
        color: '#E91E63',
        charts: ['line', 'radar', 'bar']
      }
    },

    // 图表实例
    chartInstances: {},

    // KPI指标
    kpiMetrics: [
      {
        id: 'total_geese',
        name: '鹅群总数',
        value: 0,
        unit: '只',
        trend: 0,
        trendDisplay: '0.0',
        icon: '养殖',
        color: '#4CAF50'
      },
      {
        id: 'daily_growth',
        name: '日增重',
        value: 0,
        unit: 'kg',
        trend: 0,
        trendDisplay: '0.0',
        icon: '增长',
        color: '#2196F3'
      },
      {
        id: 'feed_consumption',
        name: '饲料消耗',
        value: 0,
        unit: 'kg',
        trend: 0,
        trendDisplay: '0.0',
        icon: '饲料',
        color: '#FF9800'
      },
      {
        id: 'health_rate',
        name: '健康率',
        value: 0,
        unit: '%',
        trend: 0,
        trendDisplay: '0.0',
        icon: '健康',
        color: '#E91E63'
      }
    ],

    // 图表数据
    chartData: {
      environmentTrend: [],
      productionStats: [],
      healthMonitor: [],
      financeOverview: []
    },

    // UI状态
    loading: false,
    refreshing: false,
    showExportModal: false,
    showFilterModal: false,

    // 筛选器
    filters: {
      dateRange: [],
      categories: [],
      metrics: []
    },

    // 刷新定时器
    refreshTimer: null,

    // 图表容器尺寸
    containerSize: {
      width: 0,
      height: 0
    }
  },

  methods: {
    // 处理数据源
    processDataSource: function(dataSource) {
      if (!dataSource || dataSource.length === 0) {
        this.setData({
          processedData: {
            overview: {},
            charts: [],
            kpis: [],
            trends: {}
          }
        });
        return;
      }

      // 处理KPI数据
      this.processKPIData(dataSource);
      
      // 处理图表数据
      this.processChartData(dataSource);
      
      // 计算趋势
      this.calculateTrends(dataSource);
    },

    // 处理KPI数据
    processKPIData: function(dataSource) {
      const kpiMetrics = [...this.data.kpiMetrics];
      
      // 模拟KPI计算
      kpiMetrics.forEach(kpi => {
        // 确保icon和color属性不为null
        if (!kpi.icon || kpi.icon === null || kpi.icon === undefined) {
          kpi.icon = '数据';
        }
        if (!kpi.color || kpi.color === null || kpi.color === undefined) {
          kpi.color = '#666666';
        }
        
        switch (kpi.id) {
          case 'total_geese':
            kpi.value = 850 + Math.floor(Math.random() * 100);
            kpi.trend = Math.random() * 10 - 5;
            kpi.icon = kpi.icon || '养殖';
            kpi.color = kpi.color || '#4CAF50';
            break;
          case 'daily_growth':
            kpi.value = 2.3 + Math.random() * 0.8;
            kpi.trend = Math.random() * 2 - 1;
            kpi.icon = kpi.icon || '增长';
            kpi.color = kpi.color || '#2196F3';
            break;
          case 'feed_consumption':
            kpi.value = 1200 + Math.random() * 200;
            kpi.trend = Math.random() * 20 - 10;
            kpi.icon = kpi.icon || '饲料';
            kpi.color = kpi.color || '#FF9800';
            break;
          case 'health_rate':
            kpi.value = 95 + Math.random() * 4;
            kpi.trend = Math.random() * 2 - 1;
            kpi.icon = kpi.icon || '健康';
            kpi.color = kpi.color || '#E91E63';
            break;
        }
        
        // 格式化趋势显示
        kpi.trendDisplay = kpi.trend > 0 ? `+${kpi.trend.toFixed(1)}` : kpi.trend.toFixed(1);
      });

      this.setData({ kpiMetrics });
    },

    // 处理图表数据
    processChartData: function(dataSource) {
      const chartData = {
        environmentTrend: this.generateEnvironmentData(),
        productionStats: this.generateProductionData(),
        healthMonitor: this.generateHealthData(),
        financeOverview: this.generateFinanceData()
      };

      this.setData({ chartData });
      
      // 更新图表
      this.updateCharts();
    },

    // 生成环境数据
    generateEnvironmentData: function() {
      const data = [];
      const now = new Date();
      
      for (let i = 29; i >= 0; i--) {
        const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
        data.push({
          date: date.toISOString().split('T')[0],
          temperature: 22 + Math.random() * 8,
          humidity: 60 + Math.random() * 20,
          pm25: Math.random() * 50
        });
      }
      
      return data;
    },

    // 生成生产数据
    generateProductionData: function() {
      const categories = ['生长记录', '称重记录', '出栏记录', '健康记录'];
      const data = categories.map(category => ({
        category,
        value: Math.floor(Math.random() * 100) + 20,
        growth: Math.random() * 20 - 10
      }));
      
      return data;
    },

    // 生成健康数据
    generateHealthData: function() {
      const data = [];
      const now = new Date();
      
      for (let i = 6; i >= 0; i--) {
        const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
        data.push({
          date: date.toISOString().split('T')[0],
          healthScore: 85 + Math.random() * 10,
          incidents: Math.floor(Math.random() * 5)
        });
      }
      
      return data;
    },

    // 生成财务数据
    generateFinanceData: function() {
      const months = ['1月', '2月', '3月', '4月', '5月', '6月'];
      const data = months.map(month => ({
        month,
        income: Math.floor(Math.random() * 50000) + 20000,
        expense: Math.floor(Math.random() * 30000) + 15000
      }));
      
      return data;
    },

    // 计算趋势
    calculateTrends: function(dataSource) {
      const trends = {
        overall: Math.random() > 0.5 ? 'up' : 'down',
        production: Math.random() > 0.5 ? 'stable' : 'up',
        environment: Math.random() > 0.6 ? 'up' : 'stable',
        health: Math.random() > 0.7 ? 'up' : 'stable'
      };

      this.setData({
        'processedData.trends': trends
      });
    },

    // 更新图表
    updateCharts: function() {
      // 这里应该调用图表库的更新方法
      // 由于是演示，我们模拟图表更新
      
      // 触发图表更新事件
      this.triggerEvent('chartUpdate', {
        chartData: this.data.chartData,
        dashboardType: this.data.dashboardType
      });
    },

    // 加载看板数据
    loadDashboardData: function() {
      this.setData({ loading: true });

      // 模拟数据加载
      setTimeout(() => {
        this.processDataSource(this.data.dataSource);
        this.setData({ loading: false });
      }, 1000);

      this.triggerEvent('dataLoad', {
        dashboardType: this.data.dashboardType,
        timeRange: this.data.timeRange
      });
    },

    // 刷新数据
    onRefreshData: function() {
      this.setData({ refreshing: true });
      
      setTimeout(() => {
        this.loadDashboardData();
        this.setData({ refreshing: false });
        
        wx.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      }, 1500);
    },

    // 切换看板类型
    onDashboardTypeChange: function(e) {
      const { type } = e.currentTarget.dataset;
      this.setData({ dashboardType: type });
      this.loadDashboardData();
      
      this.triggerEvent('typeChange', {
        dashboardType: type
      });
    },

    // 切换时间范围
    onTimeRangeChange: function(e) {
      const { range } = e.currentTarget.dataset;
      this.setData({ timeRange: range });
      
      this.triggerEvent('timeRangeChange', {
        timeRange: range
      });
    },

    // KPI卡片点击
    onKPITap: function(e) {
      const { kpi } = e.currentTarget.dataset;
      this.triggerEvent('kpiTap', {
        kpi: kpi,
        dashboardType: this.data.dashboardType
      });
    },

    // 图表点击
    onChartTap: function(e) {
      const { chart } = e.currentTarget.dataset;
      this.triggerEvent('chartTap', {
        chart: chart,
        data: this.data.chartData[chart]
      });
    },

    // 显示筛选器
    onShowFilter: function() {
      this.setData({ showFilterModal: true });
    },

    // 隐藏筛选器
    onHideFilter: function() {
      this.setData({ showFilterModal: false });
    },

    // 应用筛选
    onApplyFilter: function(e) {
      const { filters } = e.detail;
      this.setData({ 
        filters: filters,
        showFilterModal: false
      });
      
      this.loadDashboardData();
      
      this.triggerEvent('filterApply', {
        filters: filters
      });
    },

    // 显示导出选项
    onShowExport: function() {
      this.setData({ showExportModal: true });
    },

    // 隐藏导出选项
    onHideExport: function() {
      this.setData({ showExportModal: false });
    },

    // 导出数据
    onExportData: function(e) {
      const { format } = e.currentTarget.dataset;
      
      this.setData({ showExportModal: false });
      
      wx.showLoading({
        title: '导出中...'
      });
      
      setTimeout(() => {
        wx.hideLoading();
        wx.showToast({
          title: '导出成功',
          icon: 'success'
        });
      }, 2000);

      this.triggerEvent('dataExport', {
        format: format,
        data: this.data.processedData,
        timeRange: this.data.timeRange
      });
    },

    // 分享看板
    onShareDashboard: function() {
      this.triggerEvent('dashboardShare', {
        dashboardType: this.data.dashboardType,
        data: this.data.processedData
      });
    },

    // 全屏显示
    onFullscreen: function() {
      this.triggerEvent('fullscreen', {
        dashboardType: this.data.dashboardType
      });
    },

    // 格式化数值
    formatValue: function(value, unit) {
      if (typeof value !== 'number') return value;
      
      if (unit === '%') {
        return value.toFixed(1) + unit;
      } else if (value >= 1000) {
        return (value / 1000).toFixed(1) + 'K' + unit;
      } else {
        return value.toFixed(value < 10 ? 1 : 0) + unit;
      }
    },

    // 获取趋势图标
    getTrendIcon: function(trend) {
      if (trend > 0) return '上升';
      if (trend < 0) return '下降';
      return '稳定';
    },

    // 获取趋势颜色
    getTrendColor: function(trend) {
      if (trend > 0) return '#4CAF50';
      if (trend < 0) return '#F44336';
      return '#9E9E9E';
    },

    // 启动自动刷新
    startAutoRefresh: function() {
      if (this.data.refreshInterval <= 0) return;
      
      this.stopAutoRefresh();
      
      const timer = setInterval(() => {
        this.onRefreshData();
      }, this.data.refreshInterval * 1000);
      
      this.setData({ refreshTimer: timer });
    },

    // 停止自动刷新
    stopAutoRefresh: function() {
      if (this.data.refreshTimer) {
        clearInterval(this.data.refreshTimer);
        this.setData({ refreshTimer: null });
      }
    },

    // 获取容器尺寸
    getContainerSize: function() {
      const query = this.createSelectorQuery();
      query.select('.dashboard-container').boundingClientRect();
      query.exec((res) => {
        if (res && res[0]) {
          this.setData({
            containerSize: {
              width: res[0].width,
              height: res[0].height
            }
          });
          
          // 重新渲染图表
          this.updateCharts();
        }
      });
    }
  },

  lifetimes: {
    attached: function() {
      // 初始化数据
      this.loadDashboardData();
      
      // 获取容器尺寸
      setTimeout(() => {
        this.getContainerSize();
      }, 500);
      
      // 启动自动刷新
      if (this.data.refreshInterval > 0) {
        this.startAutoRefresh();
      }
    },

    detached: function() {
      // 清理定时器
      this.stopAutoRefresh();
    }
  },

  observers: {
    'dashboardType': function(type) {
      // 看板类型变化时重新加载数据
      this.loadDashboardData();
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);