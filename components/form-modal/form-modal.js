// components/form-modal/form-modal.js
const { mixinComponentUtils, createPropertyObserver } = require('../../utils/component-utils.js');
const componentConfig = {
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    title: {
      type: String,
      value: '表单',
      observer: createPropertyObserver('string', '表单')
    },
    fields: {
      type: Array,
      value: []
    },
    formData: {
      type: Object,
      value: {}
    },
    loading: {
      type: Boolean,
      value: false
    }
  },

  data: {
    
  },

  methods: {
    // 输入框变化
    onInputChange(e) {
      const field = e.currentTarget.dataset.field;
      const value = e.detail.value;
      
      this.triggerEvent('input-change', {
        field,
        value
      });
    },

    // 选择器变化
    onPickerChange(e) {
      const field = e.currentTarget.dataset.field;
      const value = parseInt(e.detail.value);
      
      this.triggerEvent('picker-change', {
        field,
        value
      });
    },

    // 日期选择器变化
    onDateChange(e) {
      const field = e.currentTarget.dataset.field;
      const value = e.detail.value;
      
      this.triggerEvent('date-change', {
        field,
        value
      });
    },

    // 时间选择器变化
    onTimeChange(e) {
      const field = e.currentTarget.dataset.field;
      const value = e.detail.value;
      
      this.triggerEvent('time-change', {
        field,
        value
      });
    },

    // 开关变化
    onSwitchChange(e) {
      const field = e.currentTarget.dataset.field;
      const value = e.detail.value;
      
      this.triggerEvent('switch-change', {
        field,
        value
      });
    },

    // 点击遮罩
    onMaskTap() {
      // 可以选择是否允许点击遮罩关闭
      // this.onClose();
    },

    // 关闭弹窗
    onClose() {
      this.triggerEvent('close');
    },

    // 取消
    onCancel() {
      this.triggerEvent('cancel');
    },

    // 确认
    onConfirm() {
      this.triggerEvent('confirm', {
        formData: this.properties.formData
      });
    },

    // 图片上传
    onImageUpload(e) {
      const field = e.currentTarget.dataset.field;
      const that = this;

      wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: function(res) {
          const tempFilePath = res.tempFilePaths[0];

          // 这里可以上传到服务器，暂时使用本地路径
          that.triggerEvent('image-upload', {
            field,
            value: tempFilePath
          });
        },
        fail: function(err) {
          wx.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      });
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);