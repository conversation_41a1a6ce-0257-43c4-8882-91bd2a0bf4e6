// components/form-builder/form-field-input/form-field-input.js
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');
const componentConfig = {
  properties: {
    field: {
      type: Object,
      value: {}
    }
  },

  methods: {
    onInput(e) {
      this.triggerEvent('change', {
        field: this.properties.field,
        value: e.detail.value
      });
    },

    onBlur(e) {
      this.triggerEvent('blur', {
        field: this.properties.field,
        value: e.detail.value
      });
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);