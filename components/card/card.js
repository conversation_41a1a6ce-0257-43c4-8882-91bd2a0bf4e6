// components/card/card.js
const { UI } = require('../../constants/index.js');
const { mixinComponentUtils, createPropertyObserver } = require('../../utils/component-utils.js');

const componentConfig = {
  properties: {
    // 卡片标题
    title: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 是否显示边框
    bordered: {
      type: Boolean,
      value: true
    },
    // 是否显示阴影
    shadow: {
      type: Boolean,
      value: true
    },
    // 内边距大小 xs, sm, md, lg, xl
    padding: {
      type: String,
      value: 'md',
      observer: createPropertyObserver('string', 'md')
    },
    // 圆角大小 none, sm, md, lg, full
    radius: {
      type: String,
      value: 'md',
      observer: createPropertyObserver('string', 'md')
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 标题右侧内容插槽
    extra: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 是否可点击
    clickable: {
      type: <PERSON>olean,
      value: false
    }
  },

  data: {
    ui: UI,
    cardStyle: '',
    paddingMap: {
      xs: '16rpx',
      sm: '24rpx', 
      md: '32rpx',
      lg: '40rpx',
      xl: '48rpx'
    },
    radiusMap: {
      none: '0',
      sm: '8rpx',
      md: '16rpx', 
      lg: '24rpx',
      full: '50%'
    }
  },

  methods: {
    onCardTap() {
      if (this.data.clickable) {
        this.triggerEvent('tap');
      }
    },

    getCardStyle() {
      const padding = this.data.paddingMap[this.data.padding] || this.data.paddingMap.md;
      const radius = this.data.radiusMap[this.data.radius] || this.data.radiusMap.md;
      
      return `padding: ${padding}; border-radius: ${radius};`;
    }
  },

  ready() {
    // 组件生命周期 - 在组件实例进入页面节点树时执行
    this.setData({
      cardStyle: this.getCardStyle()
    });
  },

  observers: {
    'padding,radius': function(padding, radius) {
      this.setData({
        cardStyle: this.getCardStyle()
      });
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);