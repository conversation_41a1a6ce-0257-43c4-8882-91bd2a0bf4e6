// components/oa/page-header/page-header.js
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');

/**
 * OA页面头部组件
 * 提供统一的页面头部样式和功能
 */

const componentConfig = {
  /**
   * 组件的属性列表
   */
  properties: {
    // 页面标题
    title: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 副标题
    subtitle: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 描述信息
    description: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      value: false
    },
    
    // 右侧操作按钮
    rightActions: {
      type: Array,
      value: []
    },
    
    // 进度百分比 (0-100)
    progress: {
      type: Number,
      value: null
    },
    
    // 进度条颜色
    progressColor: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 背景颜色
    backgroundColor: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 标题颜色
    titleColor: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 是否固定在顶部
    fixed: {
      type: Boolean,
      value: false
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 自定义内联样式
    customStyle: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 返回按钮点击事件
     */
    onBackTap() {
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      // 触发自定义事件
      this.triggerEvent('back');
      
      // 默认行为：返回上一页
      wx.navigateBack({
        delta: 1,
        fail: () => {
          // 如果无法返回，跳转到首页
          wx.switchTab({
            url: '/pages/home/<USER>',
            fail: () => {
              wx.reLaunch({
                url: '/pages/home/<USER>'
              });
            }
          });
        }
      });
    },
    
    /**
     * 右侧操作按钮点击事件
     */
    onActionTap(e) {
      const action = e.currentTarget.dataset.action;
      
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      // 触发自定义事件
      this.triggerEvent('action', {
        action: action
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 如果是固定定位，需要考虑安全区域
      if (this.data.fixed) {
        this.handleSafeArea();
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    
    /**
     * 处理安全区域
     */
    handleSafeArea() {
      const systemInfo = wx.getSystemInfoSync();
      const statusBarHeight = systemInfo.statusBarHeight || 0;
      
      this.setData({
        statusBarHeight: statusBarHeight
      });
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);