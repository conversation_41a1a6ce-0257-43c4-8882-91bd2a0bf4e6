// components/oa/data-card/data-card.js
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');

/**
 * OA数据卡片组件
 * 用于展示统计数据、指标等信息
 */

const componentConfig = {
  /**
   * 组件的属性列表
   */
  properties: {
    // 卡片标题
    title: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 数值
    value: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 图标文字
    icon: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 图标颜色
    iconColor: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 图标背景色
    iconBgColor: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 数值颜色
    valueColor: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 描述信息
    description: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 趋势信息 {type: 'up'|'down'|'flat', text: '描述'}
    trend: {
      type: Object,
      value: null
    },
    
    // 徽章文字
    badge: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 卡片尺寸
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 自定义内联样式
    customStyle: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 卡片点击事件
     */
    onCardTap() {
      if (this.data.disabled) {
        return;
      }
      
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      // 触发自定义事件
      this.triggerEvent('tap', {
        title: this.data.title,
        value: this.data.value,
        icon: this.data.icon
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件初始化
    },
    
    detached() {
      // 组件销毁
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 页面显示
    },
    
    hide() {
      // 页面隐藏
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);