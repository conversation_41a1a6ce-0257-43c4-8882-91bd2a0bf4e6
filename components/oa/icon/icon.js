// components/oa/icon/icon.js
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');

/**
 * OA图标组件
 * 统一的图标系统，支持多种图标类型
 */

const componentConfig = {
  /**
   * 组件的属性列表
   */
  properties: {
    // 图标名称
    name: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 图标类型：svg, text, image
    type: {
      type: String,
      value: 'text',
      observer: createPropertyObserver('string', 'text')
    },
    
    // 图标大小 (rpx)
    size: {
      type: Number,
      value: 40
    },
    
    // 图标颜色
    color: {
      type: String,
      value: 'var(--oa-text-color-primary)',
      observer: createPropertyObserver('string', 'var(--oa-text-color-primary)')
    },
    
    // 图片图标的源地址
    src: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 是否可点击
    clickable: {
      type: Boolean,
      value: false
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 自定义内联样式
    customStyle: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 图标映射表 - 将图标名称映射为对应的文字或符号
    iconMapping: {
      // 业务图标
      'finance': '财务',
      'purchase': '采购',
      'reimbursement': '报销',
      'approval': '审批',
      'workflow': '流程',
      'notification': '通知',
      'users': '用户',
      'roles': '角色',
      'permission': '权限',
      'statistics': '统计',
      'settings': '设置',
      
      // 操作图标
      'search': '搜索',
      'filter': '筛选',
      'add': '新增',
      'edit': '编辑',
      'delete': '删除',
      'view': '查看',
      'download': '下载',
      'upload': '上传',
      
      // 导航图标
      'back': '返回',
      'forward': '前进',
      'arrow-up': '上',
      'arrow-down': '下',
      'close': '关闭',
      'more': '更多',
      
      // 状态图标
      'success': '✓',
      'error': '✗',
      'warning': '⚠',
      'info': 'ℹ',
      'pending': '⏳',
      'processing': '⚙',
      
      // 功能图标
      'time': '时间',
      'calendar': '日历',
      'location': '位置',
      'mail': '邮件',
      'phone': '电话',
      'document': '文档',
      'folder': '文件夹',
      
      // 状态指示
      'active': '启用',
      'inactive': '停用',
      'completed': '完成',
      'draft': '草稿',
      'submitted': '已提交',
      'approved': '已批准',
      'rejected': '已拒绝',
      
      // 优先级
      'high': '高',
      'medium': '中',
      'low': '低',
      'urgent': '紧急',
      
      // 趋势
      'trend-up': '↗',
      'trend-down': '↘',
      'trend-flat': '→',
      
      // 数量相关
      'total': '总数',
      'count': '计数',
      'amount': '金额',
      'percent': '百分比'
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 图标点击事件
     */
    onIconTap() {
      if (!this.data.clickable) {
        return;
      }
      
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      // 触发自定义事件
      this.triggerEvent('tap', {
        name: this.data.name,
        type: this.data.type
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件初始化
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);