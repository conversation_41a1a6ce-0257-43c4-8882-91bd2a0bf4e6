// components/oa/workflow-designer/workflow-designer.js
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');

/**
 * OA流程设计器组件
 * 用于可视化设计审批流程
 */

const componentConfig = {
  /**
   * 组件的属性列表
   */
  properties: {
    // 流程配置数据
    workflowConfig: {
      type: Object,
      value: null
    },
    // 是否为只读模式
    readonly: {
      type: Boolean,
      value: false
    },
    // 组件高度
    height: {
      type: String,
      value: '600rpx',
      observer: createPropertyObserver('string', '600rpx')
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 流程节点
    nodes: [],
    // 连接线
    connections: [],
    // 当前选中的节点
    selectedNode: null,
    // 节点类型配置
    nodeTypes: {
      start: {
        label: '开始',
        icon: '🚀',
        color: '#00A86B',
        allowDelete: false
      },
      approval: {
        label: '审批',
        icon: '✅',
        color: '#007AFF',
        allowDelete: true
      },
      condition: {
        label: '条件',
        icon: '🔀',
        color: '#FF9500',
        allowDelete: true
      },
      end: {
        label: '结束',
        icon: '🏁',
        color: '#8E8E93',
        allowDelete: false
      }
    },
    // 显示节点配置面板
    showNodeConfig: false,
    // 当前配置的节点
    configNode: null,
    // 节点配置表单
    nodeForm: {
      name: '',
      type: 'approval',
      approvers: [],
      conditions: [],
      description: ''
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化流程
     */
    initWorkflow() {
      const { workflowConfig } = this.properties;
      
      if (workflowConfig && workflowConfig.nodes) {
        this.setData({
          nodes: workflowConfig.nodes,
          connections: workflowConfig.connections || []
        });
      } else {
        // 创建默认的开始和结束节点
        this.setData({
          nodes: [
            {
              id: 'start',
              type: 'start',
              name: '开始',
              x: 100,
              y: 100
            },
            {
              id: 'end',
              type: 'end',
              name: '结束',
              x: 100,
              y: 400
            }
          ],
          connections: []
        });
      }
    },

    /**
     * 添加节点
     */
    addNode(type) {
      if (this.properties.readonly) return;
      
      const nodeConfig = this.data.nodeTypes[type];
      const newNode = {
        id: `node_${Date.now()}`,
        type: type,
        name: nodeConfig.label,
        x: 200,
        y: 250,
        config: {}
      };
      
      const nodes = [...this.data.nodes];
      nodes.splice(-1, 0, newNode); // 插入到结束节点前
      
      this.setData({ nodes });
      this.triggerChange();
    },

    /**
     * 选择节点
     */
    onNodeTap(e) {
      const { nodeId } = e.currentTarget.dataset;
      const node = this.data.nodes.find(n => n.id === nodeId);
      
      this.setData({ selectedNode: node });
      
      if (!this.properties.readonly) {
        this.showNodeConfigPanel(node);
      }
    },

    /**
     * 显示节点配置面板
     */
    showNodeConfigPanel(node) {
      this.setData({
        showNodeConfig: true,
        configNode: node,
        nodeForm: {
          name: node.name,
          type: node.type,
          approvers: node.config?.approvers || [],
          conditions: node.config?.conditions || [],
          description: node.config?.description || ''
        }
      });
    },

    /**
     * 隐藏节点配置面板
     */
    hideNodeConfig() {
      this.setData({
        showNodeConfig: false,
        configNode: null,
        nodeForm: {
          name: '',
          type: 'approval',
          approvers: [],
          conditions: [],
          description: ''
        }
      });
    },

    /**
     * 删除节点
     */
    deleteNode(nodeId) {
      if (this.properties.readonly) return;
      
      const node = this.data.nodes.find(n => n.id === nodeId);
      if (!this.data.nodeTypes[node.type].allowDelete) {
        wx.showToast({
          title: '该节点不能删除',
          icon: 'none'
        });
        return;
      }
      
      const nodes = this.data.nodes.filter(n => n.id !== nodeId);
      const connections = this.data.connections.filter(c => 
        c.source !== nodeId && c.target !== nodeId
      );
      
      this.setData({ nodes, connections });
      this.triggerChange();
    },

    /**
     * 保存节点配置
     */
    saveNodeConfig() {
      const { configNode, nodeForm } = this.data;
      
      const nodes = this.data.nodes.map(node => {
        if (node.id === configNode.id) {
          return {
            ...node,
            name: nodeForm.name,
            config: {
              approvers: nodeForm.approvers,
              conditions: nodeForm.conditions,
              description: nodeForm.description
            }
          };
        }
        return node;
      });
      
      this.setData({ nodes });
      this.hideNodeConfig();
      this.triggerChange();
    },

    /**
     * 触发变更事件
     */
    triggerChange() {
      this.triggerEvent('change', {
        nodes: this.data.nodes,
        connections: this.data.connections
      });
    },

    /**
     * 获取节点样式
     */
    getNodeStyle(node) {
      const nodeType = this.data.nodeTypes[node.type];
      return `left: ${node.x}rpx; top: ${node.y}rpx; border-color: ${nodeType.color};`;
    },

    /**
     * 获取节点类名
     */
    getNodeClass(node) {
      let classes = ['workflow-node', `node-${node.type}`];
      
      if (this.data.selectedNode && this.data.selectedNode.id === node.id) {
        classes.push('selected');
      }
      
      return classes.join(' ');
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.initWorkflow();
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'workflowConfig': function(workflowConfig) {
      this.initWorkflow();
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);