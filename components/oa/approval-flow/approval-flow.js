// components/oa/approval-flow/approval-flow.js
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');

/**
 * OA审批流程可视化组件
 * 用于展示审批流程的各个步骤和状态
 */

const componentConfig = {
  /**
   * 组件的属性列表
   */
  properties: {
    // 审批流程数据
    approvals: {
      type: Array,
      value: []
    },
    // 当前状态
    currentStatus: {
      type: String,
      value: 'pending',
      observer: createPropertyObserver('string', 'pending')
    },
    // 是否显示详细信息
    showDetails: {
      type: Boolean,
      value: true
    },
    // 是否可操作（审批）
    actionable: {
      type: Boolean,
      value: false
    },
    // 当前用户ID
    currentUserId: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 组件大小
    size: {
      type: String,
      value: 'medium' // small/medium/large
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 状态配置
    statusConfig: {
      pending: {
        label: '待处理',
        color: '#FF9500',
        icon: '⏳'
      },
      approved: {
        label: '已通过',
        color: '#00A86B',
        icon: '✅'
      },
      rejected: {
        label: '已拒绝',
        color: '#FF3B30',
        icon: '❌'
      },
      skipped: {
        label: '已跳过',
        color: '#8E8E93',
        icon: '⏭'
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 获取步骤状态配置
     */
    getStepConfig(step) {
      return this.data.statusConfig[step.status] || this.data.statusConfig.pending;
    },

    /**
     * 检查是否为当前用户的待处理步骤
     */
    isCurrentUserStep(step) {
      return step.status === 'pending' && 
             step.approver_id === this.properties.currentUserId &&
             this.properties.actionable;
    },

    /**
     * 获取步骤样式类
     */
    getStepClass(step, index) {
      let classes = ['approval-step'];
      
      // 添加状态类
      classes.push(`status-${step.status}`);
      
      // 添加尺寸类
      classes.push(`size-${this.properties.size}`);
      
      // 当前用户可操作的步骤
      if (this.isCurrentUserStep(step)) {
        classes.push('current-user');
      }
      
      // 最后一个步骤
      if (index === this.properties.approvals.length - 1) {
        classes.push('last-step');
      }
      
      return classes.join(' ');
    },

    /**
     * 获取连接线样式
     */
    getConnectorClass(step, index) {
      let classes = ['step-connector'];
      
      // 根据当前步骤状态设置连接线样式
      if (step.status === 'approved') {
        classes.push('completed');
      } else if (step.status === 'rejected') {
        classes.push('rejected');
      } else {
        classes.push('pending');
      }
      
      return classes.join(' ');
    },

    /**
     * 格式化时间
     */
    formatTime(timeStr) {
      if (!timeStr) return '';
      
      const time = new Date(timeStr);
      const now = new Date();
      const diff = now - time;
      
      // 小于1小时显示分钟
      if (diff < 3600000) {
        const minutes = Math.floor(diff / 60000);
        return `${minutes}分钟前`;
      }
      
      // 小于24小时显示小时
      if (diff < 86400000) {
        const hours = Math.floor(diff / 3600000);
        return `${hours}小时前`;
      }
      
      // 超过24小时显示日期
      return time.toLocaleDateString();
    },

    /**
     * 审批操作
     */
    onApprovalAction(e) {
      const { action, stepId } = e.currentTarget.dataset;
      
      this.triggerEvent('approval', {
        action,
        stepId,
        step: this.properties.approvals.find(step => step.id === stepId)
      });
    },

    /**
     * 查看步骤详情
     */
    onStepTap(e) {
      const { index } = e.currentTarget.dataset;
      const step = this.properties.approvals[index];
      
      this.triggerEvent('stepTap', {
        step,
        index
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例进入页面节点树时执行
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);