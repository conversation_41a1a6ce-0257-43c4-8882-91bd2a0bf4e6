// components/section-header/section-header.js
const { IMAGES } = require('../../constants/index.js');
const { mixinComponentUtils, createPropertyObserver } = require('../../utils/component-utils.js');

const componentConfig = {
  properties: {
    // 标题文本
    title: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 标题图标
    icon: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 更多按钮文本
    moreText: {
      type: String,
      value: '更多',
      observer: createPropertyObserver('string', '更多')
    },
    // 是否显示更多按钮
    showMore: {
      type: Boolean,
      value: true
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    }
  },

  methods: {
    // 点击更多按钮
    onMoreTap() {
      this.triggerEvent('moretap');
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);