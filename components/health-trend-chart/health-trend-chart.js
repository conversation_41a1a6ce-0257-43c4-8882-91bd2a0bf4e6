// components/health-trend-chart/health-trend-chart.js
const { mixinComponentUtils, createPropertyObserver } = require('../../utils/component-utils.js');
const componentConfig = {
  properties: {
    chartData: {
      type: Array,
      value: [],
      observer: 'onDataChange'
    },
    width: {
      type: Number,
      value: 0
    },
    height: {
      type: Number,
      value: 400
    }
  },

  data: {
    showTooltip: false,
    tooltipX: 0,
    tooltipY: 0,
    tooltipData: {}
  },

  lifetimes: {
    attached() {
      this.initChart();
    },
    
    ready() {
      // 获取canvas上下文
      this.ctx = wx.createCanvasContext('healthTrendChart', this);
      
      // 延迟绘制，确保组件完全加载
      setTimeout(() => {
        this.drawChart();
      }, 100);
    }
  },

  methods: {
    initChart() {
      // 获取组件尺寸
      const query = this.createSelectorQuery();
      query.select('.chart-canvas').boundingClientRect((rect) => {
        if (rect) {
          this.canvasWidth = rect.width;
          this.canvasHeight = rect.height;
          this.drawChart();
        }
      }).exec();
    },

    onDataChange() {
      // 数据变化时重新绘制
      if (this.ctx) {
        this.drawChart();
      }
    },

    drawChart() {
      if (!this.ctx || !this.data.chartData || this.data.chartData.length === 0) {
        return;
      }

      const { chartData } = this.data;
      const ctx = this.ctx;
      
      // 设置画布尺寸
      const canvasWidth = this.canvasWidth || 300;
      const canvasHeight = this.canvasHeight || 400;
      
      // 设置边距
      const padding = {
        top: 40,
        right: 40,
        bottom: 60,
        left: 60
      };
      
      const chartWidth = canvasWidth - padding.left - padding.right;
      const chartHeight = canvasHeight - padding.top - padding.bottom;

      // 清空画布
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);

      // 计算数据范围
      const maxValue = Math.max(...chartData.map(item => 
        Math.max(item.healthy, item.sick, item.death)
      ));
      const minValue = 0;

      // 绘制背景网格
      this.drawGrid(ctx, padding, chartWidth, chartHeight, maxValue);

      // 绘制坐标轴
      this.drawAxes(ctx, padding, chartWidth, chartHeight, chartData, maxValue);

      // 绘制数据线
      this.drawLines(ctx, padding, chartWidth, chartHeight, chartData, maxValue, minValue);

      // 提交绘制
      ctx.draw();
    },

    drawGrid(ctx, padding, width, height, maxValue) {
      ctx.setStrokeStyle('#f0f0f0');
      ctx.setLineWidth(1);

      // 绘制水平网格线
      const ySteps = 5;
      for (let i = 0; i <= ySteps; i++) {
        const y = padding.top + (height / ySteps) * i;
        ctx.beginPath();
        ctx.moveTo(padding.left, y);
        ctx.lineTo(padding.left + width, y);
        ctx.stroke();
      }

      // 绘制垂直网格线
      const xSteps = this.data.chartData.length - 1;
      for (let i = 0; i <= xSteps; i++) {
        const x = padding.left + (width / xSteps) * i;
        ctx.beginPath();
        ctx.moveTo(x, padding.top);
        ctx.lineTo(x, padding.top + height);
        ctx.stroke();
      }
    },

    drawAxes(ctx, padding, width, height, data, maxValue) {
      ctx.setFillStyle('#666');
      ctx.setFontSize(12);

      // Y轴标签
      const ySteps = 5;
      for (let i = 0; i <= ySteps; i++) {
        const value = Math.round((maxValue / ySteps) * (ySteps - i));
        const y = padding.top + (height / ySteps) * i;
        ctx.fillText(value.toString(), padding.left - 30, y + 4);
      }

      // X轴标签
      data.forEach((item, index) => {
        const x = padding.left + (width / (data.length - 1)) * index;
        ctx.save();
        ctx.translate(x, padding.top + height + 20);
        ctx.rotate(-Math.PI / 6); // 旋转-30度
        ctx.fillText(item.date, 0, 0);
        ctx.restore();
      });
    },

    drawLines(ctx, padding, width, height, data, maxValue, minValue) {
      const colors = {
        healthy: '#52C41A',
        sick: '#FAAD14',
        death: '#FF4D4F'
      };

      ['healthy', 'sick', 'death'].forEach(type => {
        ctx.setStrokeStyle(colors[type]);
        ctx.setLineWidth(3);
        ctx.beginPath();

        data.forEach((item, index) => {
          const x = padding.left + (width / (data.length - 1)) * index;
          const y = padding.top + height - ((item[type] - minValue) / (maxValue - minValue)) * height;

          if (index === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        });

        ctx.stroke();

        // 绘制数据点
        ctx.setFillStyle(colors[type]);
        data.forEach((item, index) => {
          const x = padding.left + (width / (data.length - 1)) * index;
          const y = padding.top + height - ((item[type] - minValue) / (maxValue - minValue)) * height;
          
          // 绘制方形数据点（遵循微信小程序Canvas规范）
          const pointSize = 8; // 4*2，保持视觉大小一致
          ctx.fillRect(x - pointSize/2, y - pointSize/2, pointSize, pointSize);
        });
      });
    },

    // 触摸事件处理
    onTouchStart(e) {
      this.handleTouch(e);
    },

    onTouchMove(e) {
      this.handleTouch(e);
    },

    onTouchEnd() {
      this.setData({
        showTooltip: false
      });
    },

    handleTouch(e) {
      if (!this.data.chartData || this.data.chartData.length === 0) return;

      const touch = e.touches[0];
      const { chartData } = this.data;
      
      // 计算触摸点对应的数据索引
      const canvasWidth = this.canvasWidth || 300;
      const padding = { left: 60, right: 40 };
      const chartWidth = canvasWidth - padding.left - padding.right;
      
      const touchX = touch.x - padding.left;
      const dataIndex = Math.round((touchX / chartWidth) * (chartData.length - 1));
      
      if (dataIndex >= 0 && dataIndex < chartData.length) {
        const data = chartData[dataIndex];
        this.setData({
          showTooltip: true,
          tooltipX: touch.x,
          tooltipY: touch.y - 100,
          tooltipData: data
        });
      }
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);