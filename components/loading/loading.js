// components/loading/loading.js
const { mixinComponentUtils, createPropertyObserver } = require('../../utils/component-utils.js');
const componentConfig = {
  properties: {
    // 是否显示加载状态
    loading: {
      type: Boolean,
      value: false
    },
    // 加载文本
    text: {
      type: String,
      value: '加载中...',
      observer: createPropertyObserver('string', '加载中...')
    },
    // 加载类型 spinner, dots, skeleton
    type: {
      type: String,
      value: 'spinner',
      observer: createPropertyObserver('string', 'spinner')
    },
    // 尺寸 small, medium, large
    size: {
      type: String,
      value: 'medium',
      observer: createPropertyObserver('string', 'medium')
    },
    // 自定义样式
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 是否全屏遮罩
    overlay: {
      type: Boolean,
      value: false
    }
  },

  data: {
    sizeMap: {
      small: '32rpx',
      medium: '48rpx',
      large: '64rpx'
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);