// components/global/index.js

/**
 * 全局组件库统一导出文件
 * 基于OA模块的成功经验，创建企业级通用组件库
 */

// 导出实际存在的全局组件
module.exports = {
  // 核心组件（实际存在）
  'global-icon': '/components/global/icon/icon',
  'global-page-header': '/components/global/page-header/page-header'
};

/**
 * 组件库使用指南:
 * 
 * 1. 在页面的.json文件中引用:
 * {
 *   "usingComponents": {
 *     "global-page-header": "/components/global/page-header/page-header",
 *     "global-data-card": "/components/global/data-card/data-card"
 *   }
 * }
 * 
 * 2. 在wxml中使用:
 * <global-page-header title="页面标题" subtitle="页面描述"></global-page-header>
 * 
 * 3. 应用全局设计令牌:
 * @import '/styles/global-design-tokens.wxss';
 */