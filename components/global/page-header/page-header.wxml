<!-- components/global/page-header/page-header.wxml -->

<view 
  class="global-page-header {{customClass}} size-{{size}} theme-{{theme}} {{fixed ? 'fixed' : ''}} {{showDivider ? 'with-divider' : ''}}"
  style="{{customStyle}}"
>
  <!-- 安全区域占位 -->
  <view wx:if="{{fixed}}" class="safe-area-placeholder" style="height: {{statusBarHeight}}px;"></view>
  
  <!-- 头部内容 -->
  <view class="header-content">
    <!-- 左侧区域 -->
    <view class="header-left">
      <!-- 返回按钮 -->
      <view wx:if="{{showBack}}" 
            class="{{backButtonClass}}" 
            data-button-type="back"
            bind:tap="onBackTap"
            bind:touchstart="onButtonTouchStart"
            bind:touchend="onButtonTouchEnd"
            bind:touchcancel="onButtonTouchEnd">
        <global-icon name="返回" size="18" color="var(--global-text-color-inverse)"></global-icon>
      </view>
    </view>
    
    <!-- 中间内容区域 -->
    <view class="header-center">
      <!-- 主标题 -->
      <view wx:if="{{title}}" class="title-section">
        <text class="main-title" style="color: {{titleColor}}">{{title}}</text>
        
        <!-- 副标题 -->
        <text wx:if="{{subtitle}}" class="subtitle">{{subtitle}}</text>
        
        <!-- 描述信息 -->
        <text wx:if="{{description}}" class="description">{{description}}</text>
      </view>
    </view>
    
    <!-- 右侧操作区域 -->
    <view class="header-right">
      <!-- 右侧操作按钮 -->
      <view wx:for="{{rightActions}}" wx:key="id" 
            class="{{actionButtonsClass[index] || 'action-button'}}"
            data-button-type="action"
            bind:tap="onActionTap"
            bind:touchstart="onButtonTouchStart"
            bind:touchend="onButtonTouchEnd"
            bind:touchcancel="onButtonTouchEnd"
            data-action="{{item}}"
            data-index="{{index}}">
        <global-icon
          wx:if="{{item.icon}}"
          name="{{item.icon || '操作'}}"
          size="18"
          color="var(--global-text-color-inverse)">
        </global-icon>
        <text wx:if="{{item.text}}" class="action-text">{{item.text}}</text>
      </view>
      
      <!-- 额外内容插槽 -->
      <slot name="extra"></slot>
    </view>
  </view>
  
  <!-- 分割线 -->
  <view wx:if="{{showDivider}}" class="header-divider"></view>
  
  <!-- 进度条 -->
  <view wx:if="{{progress !== null}}" class="progress-container">
    <view class="progress-bar">
      <view 
        class="progress-fill" 
        style="width: {{progress}}%; background-color: {{progressColor || 'var(--global-text-color-inverse)'}};">
      </view>
    </view>
  </view>
</view>