/* components/global/page-header/page-header.wxss */
/* 使用新的核心样式系统 */

/* 主容器 */
.global-page-header {
  position: relative;
  width: 100%;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: var(--text-inverse);
  overflow: hidden;
}

/* 固定定位 */
.global-page-header.fixed {
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--global-z-index-sticky);
}

/* 底部分割线 - 使用独立元素替代伪元素 */
.global-page-header.with-divider .header-divider {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background-color: rgba(255, 255, 255, 0.2);
}

/* 安全区域占位 */
.safe-area-placeholder {
  width: 100%;
  background: inherit;
}

/* 头部内容 */
.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

/* 尺寸变化 */
.global-page-header.size-small .header-content {
  padding: var(--global-spacer-4) var(--global-spacer-8);
  min-height: 88rpx;
}

.global-page-header.size-medium .header-content {
  padding: var(--global-spacer-6) var(--global-spacer-8);
  min-height: 120rpx;
}

.global-page-header.size-large .header-content {
  padding: var(--global-spacer-10) var(--global-spacer-8);
  min-height: 160rpx;
}

/* 左侧区域 */
.header-left {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  border-radius: var(--global-radius-round);
  background: rgba(255, 255, 255, 0.1);
  transition: var(--global-transition-fast);
}

/* 交互状态通过 JS 控制 class 实现 */
.back-button.hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

/* 中间内容区域 */
.header-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding: 0 var(--global-spacer-4);
  text-align: center;
}

.title-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.main-title {
  font-size: var(--global-font-size-xl);
  font-weight: var(--global-font-weight-semibold);
  line-height: var(--global-line-height-compact);
  color: var(--global-text-color-inverse);
  margin-bottom: var(--global-spacer-1);
}

.subtitle {
  font-size: var(--global-font-size-s);
  font-weight: var(--global-font-weight-normal);
  color: rgba(255, 255, 255, 0.8);
  line-height: var(--global-line-height-normal);
  margin-bottom: var(--global-spacer-1);
}

.description {
  font-size: var(--global-font-size-xs);
  font-weight: var(--global-font-weight-normal);
  color: rgba(255, 255, 255, 0.7);
  line-height: var(--global-line-height-normal);
}

/* 大尺寸标题样式调整 */
.global-page-header.size-large .main-title {
  font-size: var(--global-font-size-xxl);
}

.global-page-header.size-large .subtitle {
  font-size: var(--global-font-size-m);
}

/* 右侧操作区域 */
.header-right {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  gap: var(--global-spacer-2);
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 64rpx;
  height: 64rpx;
  padding: 0 var(--global-spacer-3);
  border-radius: var(--global-radius-m);
  background: rgba(255, 255, 255, 0.1);
  transition: var(--global-transition-fast);
}

.action-button.hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.action-text {
  font-size: var(--global-font-size-s);
  color: var(--global-text-color-inverse);
  margin-left: var(--global-spacer-1);
}

/* 进度条 */
.progress-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: rgba(255, 255, 255, 0.2);
}

.progress-bar {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--global-text-color-inverse);
  transition: width var(--global-transition-normal);
}

/* ==================== 主题样式 ==================== */

/* 默认主题 (品牌蓝) */
.global-page-header.theme-default,
.global-page-header.theme-brand {
  background: linear-gradient(135deg, var(--global-brand-color) 0%, var(--global-brand-color-dark) 100%);
}

/* OA主题 */
.global-page-header.theme-oa {
  background: linear-gradient(135deg, var(--oa-brand-color) 0%, var(--oa-brand-color-dark) 100%);
}

/* 健康主题 */
.global-page-header.theme-health {
  background: linear-gradient(135deg, var(--health-primary-color) 0%, #C0392B 100%);
}

/* 生产主题 */
.global-page-header.theme-production {
  background: linear-gradient(135deg, var(--production-primary-color) 0%, #239B56 100%);
}

/* 商城主题 */
.global-page-header.theme-shop {
  background: linear-gradient(135deg, var(--shop-primary-color) 0%, #D35400 100%);
}

/* ==================== 响应式适配 ==================== */

/* 小屏幕适配 */
@media (max-width: 320px) {
  .main-title {
    font-size: var(--global-font-size-l);
  }
  
  .header-content {
    padding-left: var(--global-spacer-4);
    padding-right: var(--global-spacer-4);
  }
}

/* ==================== 动画效果 ==================== */

.global-page-header {
  animation: headerSlideIn 0.3s ease-out;
}

@keyframes headerSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 交互状态 */
.back-button.active,
.action-button.active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}