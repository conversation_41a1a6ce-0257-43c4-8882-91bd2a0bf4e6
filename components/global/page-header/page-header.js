// components/global/page-header/page-header.js
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');

/**
 * 全局页面头部组件
 * 基于OA模块page-header的成功经验，扩展为全应用通用组件
 * 支持所有模块的统一页面头部需求
 */

const componentConfig = {
  /**
   * 组件的属性列表
   */
  properties: {
    // 页面标题
    title: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 副标题
    subtitle: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 描述信息
    description: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      value: false
    },
    
    // 右侧操作按钮
    rightActions: {
      type: Array,
      value: []
    },
    
    // 进度百分比 (0-100)
    progress: {
      type: Number,
      value: null
    },
    
    // 进度条颜色
    progressColor: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 背景颜色主题
    theme: {
      type: String,
      value: 'default' // default, brand, health, production, shop, oa
    },
    
    // 背景颜色 (自定义)
    backgroundColor: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 标题颜色
    titleColor: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 是否固定在顶部
    fixed: {
      type: Boolean,
      value: false
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 自定义内联样式
    customStyle: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 头部尺寸
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    
    // 是否显示底部分割线
    showDivider: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    statusBarHeight: 0,
    
    // 按钮交互状态
    backButtonClass: 'back-button',
    actionButtonsClass: {},
    
    // 主题色彩映射
    themeColors: {
      'default': 'var(--global-brand-color)',
      'brand': 'var(--global-brand-color)',
      'health': 'var(--health-primary-color)',
      'production': 'var(--production-primary-color)',
      'shop': 'var(--shop-primary-color)',
      'oa': 'var(--oa-brand-color)'
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 返回按钮点击事件
     */
    onBackTap() {
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      // 触发自定义事件
      this.triggerEvent('back');
      
      // 默认行为：返回上一页
      wx.navigateBack({
        delta: 1,
        fail: () => {
          // 如果无法返回，尝试跳转到首页
          wx.switchTab({
            url: '/pages/home/<USER>',
            fail: () => {
              wx.reLaunch({
                url: '/pages/home/<USER>'
              });
            }
          });
        }
      });
    },
    
    /**
     * 右侧操作按钮点击事件
     */
    onActionTap(e) {
      const action = e.currentTarget.dataset.action;
      const index = e.currentTarget.dataset.index;
      
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      // 触发自定义事件
      this.triggerEvent('action', {
        action: action,
        index: index
      });
    },

    /**
     * 按钮触摸开始事件 (模拟hover效果)
     */
    onButtonTouchStart(e) {
      const buttonType = e.currentTarget.dataset.buttonType || 'action';
      const index = e.currentTarget.dataset.index;
      
      if (buttonType === 'back') {
        this.setData({
          backButtonClass: 'back-button hover'
        });
      } else {
        // 为操作按钮添加hover状态
        const actionButtonsClass = { ...this.data.actionButtonsClass };
        actionButtonsClass[index] = 'action-button hover';
        this.setData({
          actionButtonsClass
        });
      }
    },

    /**
     * 按钮触摸结束事件 (移除hover效果)
     */
    onButtonTouchEnd(e) {
      const buttonType = e.currentTarget.dataset.buttonType || 'action';
      const index = e.currentTarget.dataset.index;
      
      if (buttonType === 'back') {
        this.setData({
          backButtonClass: 'back-button'
        });
      } else {
        // 移除操作按钮的hover状态
        const actionButtonsClass = { ...this.data.actionButtonsClass };
        actionButtonsClass[index] = 'action-button';
        this.setData({
          actionButtonsClass
        });
      }
    },
    
    /**
     * 获取主题背景色
     */
    getThemeBackgroundColor() {
      const { theme, backgroundColor } = this.properties;
      
      if (backgroundColor) {
        return backgroundColor;
      }
      
      return this.data.themeColors[theme] || this.data.themeColors.default;
    },
    
    /**
     * 处理安全区域
     */
    handleSafeArea() {
      const systemInfo = wx.getSystemInfoSync();
      const statusBarHeight = systemInfo.statusBarHeight || 0;
      
      this.setData({
        statusBarHeight: statusBarHeight
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 如果是固定定位，需要考虑安全区域
      if (this.properties.fixed) {
        this.handleSafeArea();
      }
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 页面显示时更新安全区域
      if (this.properties.fixed) {
        this.handleSafeArea();
      }
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);