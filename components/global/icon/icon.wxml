<!-- components/global/icon/icon.wxml -->

<view 
  class="{{iconClass}} {{customClass}} type-{{type}}"
  style="{{customStyle}}"
  bind:tap="onIconTap"
  bind:touchstart="onIconTouchStart"
  bind:touchend="onIconTouchEnd"
  bind:touchcancel="onIconTouchEnd"
>
  <!-- 文字图标 -->
  <text 
    wx:if="{{type === 'text'}}"
    class="global-icon-text"
    style="font-size: {{computedSize}}rpx; color: {{color || 'var(--global-text-color-primary)'}}; background-color: {{backgroundColor}};"
  >{{iconMapping[name || ''] || (name || '')}}</text>
  
  <!-- 图片图标 -->
  <image 
    wx:elif="{{type === 'image'}}"
    class="global-icon-image"
    src="{{src || '/images/icons/' + (name || 'default') + '.png'}}"
    style="width: {{computedSize}}rpx; height: {{computedSize}}rpx; background-color: {{backgroundColor}};"
    mode="aspectFit"
  ></image>
  
  <!-- Unicode图标 -->
  <text 
    wx:elif="{{type === 'unicode'}}"
    class="global-icon-unicode"
    style="font-size: {{computedSize}}rpx; color: {{color || 'var(--global-text-color-primary)'}}; background-color: {{backgroundColor}};"
  >{{name || ''}}</text>
  
  <!-- SVG图标 (未来扩展) -->
  <view 
    wx:elif="{{type === 'svg'}}"
    class="global-icon-svg"
    style="width: {{computedSize}}rpx; height: {{computedSize}}rpx; color: {{color || 'var(--global-text-color-primary)'}}; background-color: {{backgroundColor}};"
  >
    <!-- SVG内容将通过动态加载实现 -->
    <text class="icon-placeholder">{{name || ''}}</text>
  </view>
  
  <!-- 默认文字图标 -->
  <text 
    wx:else
    class="global-icon-text"
    style="font-size: {{computedSize}}rpx; color: {{color || 'var(--global-text-color-primary)'}}; background-color: {{backgroundColor}};"
  >{{iconMapping[name || ''] || (name || '')}}</text>
</view>