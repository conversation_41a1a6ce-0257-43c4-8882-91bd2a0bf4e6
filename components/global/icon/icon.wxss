/* components/global/icon/icon.wxss */
/* 使用新的核心样式系统 */

/* 主容器 */
.global-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  vertical-align: middle;
  line-height: 1;
}

/* 可点击状态 */
.global-icon.clickable {
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
}

/* 交互状态通过 JS 控制 class 实现，移除伪类选择器 */
.global-icon.clickable.hover {
  transform: scale(1.1);
  opacity: 0.8;
}

.global-icon.clickable.active {
  transform: scale(0.95);
}

/* ==================== 图标类型样式 ==================== */

/* 文字图标 */
.global-icon-text {
  display: inline-block;
  text-align: center;
  line-height: 1;
  font-style: normal;
  font-variant: normal;
  text-transform: none;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  border-radius: var(--global-radius-s);
  padding: 4rpx;
  box-sizing: border-box;
  transition: var(--global-transition-fast);
}

/* 图片图标 */
.global-icon-image {
  display: block;
  border-radius: var(--global-radius-s);
  transition: var(--global-transition-fast);
}

/* Unicode图标 */
.global-icon-unicode {
  display: inline-block;
  text-align: center;
  line-height: 1;
  font-family: 'Arial Unicode MS', 'Segoe UI Emoji', 'Segoe UI Symbol', sans-serif;
  border-radius: var(--global-radius-s);
  padding: 4rpx;
  box-sizing: border-box;
  transition: var(--global-transition-fast);
}

/* SVG图标 */
.global-icon-svg {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--global-radius-s);
  transition: var(--global-transition-fast);
}

.global-icon-placeholder {
  font-size: 24rpx;
  color: var(--global-text-color-tertiary);
}

/* ==================== 主题样式 ==================== */

/* 默认主题 */
.global-icon.theme-default .global-icon-text,
.global-icon.theme-default .global-icon-unicode {
  color: var(--global-text-color-primary);
}

/* 品牌主题 */
.global-icon.theme-brand .global-icon-text,
.global-icon.theme-brand .global-icon-unicode {
  color: var(--global-brand-color);
}

/* 健康主题 */
.global-icon.theme-health .global-icon-text,
.global-icon.theme-health .global-icon-unicode {
  color: var(--health-primary-color);
}

/* 生产主题 */
.global-icon.theme-production .global-icon-text,
.global-icon.theme-production .global-icon-unicode {
  color: var(--production-primary-color);
}

/* 商城主题 */
.global-icon.theme-shop .global-icon-text,
.global-icon.theme-shop .global-icon-unicode {
  color: var(--shop-primary-color);
}

/* OA主题 */
.global-icon.theme-oa .global-icon-text,
.global-icon.theme-oa .global-icon-unicode {
  color: var(--oa-brand-color);
}

/* ==================== 尺寸变化 ==================== */

/* 小尺寸 */
.global-icon.size-small .global-icon-text,
.global-icon.size-small .global-icon-unicode {
  padding: 2rpx;
}

/* 大尺寸 */
.global-icon.size-large .global-icon-text,
.global-icon.size-large .global-icon-unicode {
  padding: 6rpx;
}

/* ==================== 特殊效果 ==================== */

/* 旋转动画 (用于加载图标) - 使用类选择器实现 */
.global-icon.rotating {
  transform: rotate(180deg);
  transition: transform 0.5s linear;
}

.global-icon.rotating-full {
  transform: rotate(360deg);
  transition: transform 1s linear;
}

.global-icon.rotating-reset {
  transform: rotate(0deg);
  transition: transform 0.25s linear;
}

/* 呼吸动画 (用于提醒图标) - 使用类选择器实现 */
.global-icon.breathing {
  opacity: 1;
  transition: opacity 1s ease-in-out;
}

.global-icon.breathing-dim {
  opacity: 0.5;
  transition: opacity 1s ease-in-out;
}

.global-icon.breathing-bright {
  opacity: 1;
  transition: opacity 1s ease-in-out;
}

/* 弹跳动画 (用于新消息图标) - 使用类选择器实现 */
.global-icon.bouncing {
  transform: translateY(0);
  transition: transform 0.2s ease-in-out;
}

.global-icon.bouncing-up {
  transform: translateY(-10rpx);
  transition: transform 0.2s ease-in-out;
}

.global-icon.bouncing-mid {
  transform: translateY(-5rpx);
  transition: transform 0.2s ease-in-out;
}

.global-icon.bouncing-down {
  transform: translateY(0);
  transition: transform 0.2s ease-in-out;
}

/* ==================== 状态变化 ==================== */

/* 禁用状态 */
.global-icon.disabled {
  opacity: 0.4;
  pointer-events: none;
}

/* 选中状态 */
.global-icon.selected .global-icon-text,
.global-icon.selected .global-icon-unicode {
  color: var(--global-brand-color);
  background-color: var(--global-brand-color-focus);
}

/* 错误状态 */
.global-icon.error .global-icon-text,
.global-icon.error .global-icon-unicode {
  color: var(--global-error-color);
}

/* 成功状态 */
.global-icon.success .global-icon-text,
.global-icon.success .global-icon-unicode {
  color: var(--global-success-color);
}

/* 警告状态 */
.global-icon.warning .global-icon-text,
.global-icon.warning .global-icon-unicode {
  color: var(--global-warning-color);
}