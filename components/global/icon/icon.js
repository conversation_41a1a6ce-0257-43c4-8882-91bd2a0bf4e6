// components/global/icon/icon.js
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');

/**
 * 全局图标组件
 * 基于OA模块icon组件的成功经验，扩展为全应用统一图标系统
 * 支持多种图标类型和丰富的图标库
 */

// 定义组件配置
const componentConfig = {
  /**
   * 组件的属性列表
   */
  properties: {
    // 图标名称
    name: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 图标类型：text, svg, image, unicode
    type: {
      type: String,
      value: 'text'
    },
    
    // 图标大小 (rpx) - 支持数字或预设字符串 (small, medium, large)
    size: {
      type: null, // 接受任何类型
      value: 40
    },
    
    // 图标颜色
    color: {
      type: String,
      value: 'var(--global-text-color-primary)',
      observer: createPropertyObserver('string', 'var(--global-text-color-primary)')
    },
    
    // 图标背景色
    backgroundColor: {
      type: String,
      value: 'transparent'
    },
    
    // 图片图标的源地址
    src: {
      type: String,
      value: ''
    },
    
    // 是否可点击
    clickable: {
      type: Boolean,
      value: false
    },
    
    // 图标主题
    theme: {
      type: String,
      value: 'default' // default, brand, health, production, shop, oa
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    
    // 自定义内联样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 计算后的size值
    computedSize: 40,
    
    // 交互状态
    iconClass: 'global-icon',
    
    // 全局图标映射表 - 扩展版
    iconMapping: {
      // ==================== 基础操作图标 ====================
      '返回': '←',
      '前进': '→',
      '上': '↑',
      '下': '↓',
      '关闭': '×',
      '更多': '⋯',
      '搜索': '🔍',
      '筛选': '⚡',
      '设置': '⚙',
      '刷新': '↻',
      '编辑': '✎',
      '删除': '🗑',
      '添加': '+',
      '减少': '-',
      '新增': '+',
      '复制': '📋',
      '分享': '📤',
      '下载': '⬇',
      '上传': '⬆',
      '保存': '💾',
      '打印': '🖨',
      '导出': '📤',
      '导入': '📥',
      
      // ==================== 状态指示图标 ====================
      '成功': '✓',
      '失败': '✗',
      '错误': '⚠',
      '警告': '⚠',
      '信息': 'ℹ',
      '帮助': '?',
      '通过': '✓',
      '拒绝': '✗',
      '待审': '⏳',
      '处理中': '⚙',
      '已完成': '✅',
      '进行中': '🔄',
      '暂停': '⏸',
      '启用': '●',
      '停用': '○',
      '在线': '🟢',
      '离线': '⚪',
      '正常': '✅',
      '异常': '❌',
      
      // ==================== 业务功能图标 ====================
      // OA办公
      '审批': '📋',
      '任务': '📝',
      '流程': '🔄',
      '模板': '📄',
      '工作流': '⚡',
      '通知': '🔔',
      '消息': '💬',
      '邮件': '✉',
      '日历': '📅',
      '时间': '⏰',
      '报表': '📊',
      '统计': '📈',
      '分析': '📉',
      
      // 财务管理
      '财务': '💰',
      '金额': '¥',
      '收入': '📈',
      '支出': '📉',
      '利润': '💹',
      '预算': '💳',
      '账单': '🧾',
      '发票': '📄',
      '报销': '💸',
      '付款': '💳',
      '收款': '💰',
      '转账': '🔄',
      '余额': '💵',
      
      // 采购管理
      '采购': '🛒',
      '订单': '📦',
      '商品': '📦',
      '库存': '📊',
      '供应商': '🏭',
      '合同': '📄',
      '物流': '🚚',
      '仓库': '🏢',
      '出库': '📤',
      '入库': '📥',
      
      // 人员管理
      '用户': '👤',
      '用户组': '👥',
      '角色': '🎭',
      '权限': '🔐',
      '部门': '🏢',
      '团队': '👥',
      '员工': '👨‍💼',
      '管理员': '👨‍💻',
      '访客': '👤',
      
      // ==================== 生产管理图标 ====================
      '生产': '🏭',
      '环境': '🌡',
      '温度': '🌡',
      '湿度': '💧',
      '空气': '💨',
      '光照': '☀',
      '监控': '📹',
      '设备': '⚙',
      '传感器': '📡',
      '报警': '🚨',
      '养殖': '🐣',
      '饲料': '🌾',
      '防疫': '💉',
      '检测': '🔬',
      '记录': '📝',
      '巡检': '👀',
      
      // ==================== 健康管理图标 ====================
      '健康': '❤',
      '医疗': '🏥',
      '诊断': '🔬',
      '治疗': '💊',
      '疫苗': '💉',
      '体检': '🩺',
      '病历': '📋',
      '处方': '📝',
      '药品': '💊',
      '症状': '🤒',
      '康复': '💪',
      '预防': '🛡',
      
      // ==================== 商城交易图标 ====================
      '商城': '🛒',
      '购物车': '🛒',
      '商品': '📦',
      '分类': '📂',
      '品牌': '🏷',
      '价格': '💰',
      '折扣': '🏷',
      '优惠': '🎁',
      '积分': '⭐',
      '会员': '👑',
      '支付': '💳',
      '配送': '🚚',
      '评价': '⭐',
      '收藏': '❤',
      
      // ==================== 数据展示图标 ====================
      '总数': '📊',
      '计数': '#',
      '百分比': '%',
      '增长': '📈',
      '下降': '📉',
      '趋势': '📊',
      '对比': '⚖',
      '排行': '🏆',
      '评分': '⭐',
      '等级': '🎖',
      
      // ==================== 文件操作图标 ====================
      '文件': '📄',
      '文档': '📝',
      '图片': '🖼',
      '视频': '🎬',
      '音频': '🎵',
      '压缩包': '📦',
      '表格': '📊',
      '演示': '📽',
      '文件夹': '📁',
      '附件': '📎',
      '链接': '🔗',
      '二维码': '⬛',
      
      // ==================== 通信联系图标 ====================
      '电话': '📞',
      '手机': '📱',
      '微信': '💬',
      'QQ': '🐧',
      '邮箱': '📧',
      '地址': '📍',
      '位置': '📍',
      '导航': '🧭',
      '地图': '🗺',
      
      // ==================== 时间日期图标 ====================
      '今日': '📅',
      '昨日': '📅',
      '本周': '📅',
      '本月': '📅',
      '本年': '📅',
      '历史': '📚',
      '未来': '🔮',
      '截止': '⏰',
      '倒计时': '⏱',
      
      // ==================== 系统功能图标 ====================
      '首页': '🏠',
      '菜单': '☰',
      '标签': '🏷',
      '分组': '📂',
      '分类': '🗂',
      '排序': '🔃',
      '过滤': '🔍',
      '全选': '☑',
      '取消': '☒',
      '确认': '✅',
      '重置': '🔄',
      '清空': '🗑',
      '备份': '💾',
      '恢复': '↩',
      '同步': '🔄',
      '登录': '🔑',
      '登出': '🚪',
      '注册': '📝',
      '密码': '🔒',
      '安全': '🛡',
      
      // ==================== 优先级和状态 ====================
      '高': '🔴',
      '中': '🟡',
      '低': '🟢',
      '紧急': '🚨',
      '重要': '⭐',
      '普通': '⚪',
      '草稿': '📝',
      '提交': '📤',
      '审核': '👁',
      '发布': '📢',
      '归档': '📚',
      
      // ==================== 特殊标识 ====================
      '新': 'NEW',
      '热': 'HOT',
      '推荐': '★',
      '精选': '💎',
      '限时': '⏰',
      '免费': 'FREE',
      '会员': 'VIP',
      '专属': '👑',
      
      // ==================== 无数据状态 ====================
      '无数据': '📭',
      '空': '📪',
      '加载中': '⏳',
      '重试': '🔄',
      '网络错误': '📡',
      '服务错误': '⚠'
    },
    
    // 主题色彩映射
    themeColors: {
      'default': 'var(--global-text-color-primary)',
      'brand': 'var(--global-brand-color)',
      'health': 'var(--health-primary-color)',
      'production': 'var(--production-primary-color)',
      'shop': 'var(--shop-primary-color)',
      'oa': 'var(--oa-brand-color)'
    }
  },

  /**
   * 数据监听器
   */
  observers: {
    'size': function(size) {
      this.setData({
        computedSize: this.getComputedSize()
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 图标点击事件
     */
    onIconTap() {
      if (!this.properties.clickable) {
        return;
      }
      
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      // 触发自定义事件
      this.triggerEvent('tap', {
        name: this.properties.name,
        type: this.properties.type
      });
    },

    /**
     * 图标触摸开始事件 (模拟hover效果)
     */
    onIconTouchStart() {
      if (!this.properties.clickable) {
        return;
      }
      
      this.setData({
        iconClass: 'global-icon clickable hover'
      });
    },

    /**
     * 图标触摸结束事件 (移除hover效果)
     */
    onIconTouchEnd() {
      if (!this.properties.clickable) {
        return;
      }
      
      this.setData({
        iconClass: 'global-icon clickable'
      });
    },
    
    /**
     * 获取图标内容
     */
    getIconContent() {
      const { name, type } = this.properties;
      
      if (type === 'text') {
        return this.data.iconMapping[name] || name;
      }
      
      return name;
    },
    
    /**
     * 获取主题颜色
     */
    getThemeColor() {
      const { theme, color } = this.properties;
      
      if (color && color !== 'var(--global-text-color-primary)') {
        return color;
      }
      
      return this.data.themeColors[theme] || this.data.themeColors.default;
    },
    
    /**
     * 获取计算后的图标大小
     */
    getComputedSize() {
      const { size } = this.properties;
      
      // 如果是字符串预设值，转换为对应的数字
      if (typeof size === 'string') {
        const sizeMap = {
          'mini': 16,
          'small': 24,
          'medium': 32,
          'large': 48
        };
        return sizeMap[size] || 40; // 默认值
      }
      
      // 如果是数字，直接返回
      return size || 40;
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件初始化 - 设置计算后的size值和样式类
      const baseClass = 'global-icon';
      const clickableClass = this.properties.clickable ? ' clickable' : '';
      const themeClass = this.properties.theme ? ` theme-${this.properties.theme}` : '';
      const sizeClass = typeof this.properties.size === 'string' ? ` size-${this.properties.size}` : '';
      
      this.setData({
        computedSize: this.getComputedSize(),
        iconClass: `${baseClass}${clickableClass}${themeClass}${sizeClass}`
      });
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);