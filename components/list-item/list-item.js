// components/list-item/list-item.js
const { IMAGES, UI } = require('../../constants/index.js');
const { mixinComponentUtils, createPropertyObserver } = require('../../utils/component-utils.js');

// 定义组件配置
const componentConfig = {
  properties: {
    // 主标题
    title: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 副标题/描述
    subtitle: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 左侧图标
    icon: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 右侧文本
    extra: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 是否显示右箭头
    arrow: {
      type: Boolean,
      value: false
    },
    // 是否显示徽章
    badge: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 徽章类型 default, primary, success, warning, error
    badgeType: {
      type: String,
      value: 'default',
      observer: createPropertyObserver('string', 'default')
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      value: true
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 是否显示分割线
    border: {
      type: Boolean,
      value: true
    }
  },

  data: {
    badgeColors: {
      default: '#f5f5f5',
      primary: '#1890ff',
      success: '#52c41a',
      warning: '#faad14',
      error: '#ff4d4f'
    }
  },

  computed: {
    badgeStyle() {
      const color = this.data.badgeColors[this.data.badgeType] || this.data.badgeColors.default;
      const textColor = this.data.badgeType === 'default' ? '#666' : '#fff';
      return `background-color: ${color}; color: ${textColor};`;
    }
  },

  methods: {
    onItemTap() {
      if (this.data.clickable) {
        this.triggerEvent('tap');
      }
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);