/**
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');
 * 加载状态组件
 * 提供统一的加载状态展示
 * 支持骨架屏、进度条、自定义加载动画
 */
const componentConfig = {
  properties: {
    // 加载状态
    loading: {
      type: Boolean,
      value: false
    },
    // 加载类型: spinner, skeleton, progress, dots, pulse
    type: {
      type: String,
      value: 'spinner',
      observer: createPropertyObserver('string', 'spinner')
    },
    // 加载文本
    text: {
      type: String,
      value: '加载中...',
      observer: createPropertyObserver('string', '加载中...')
    },
    // 是否显示文本
    showText: {
      type: Boolean,
      value: true
    },
    // 进度值 (0-100)
    progress: {
      type: Number,
      value: 0
    },
    // 骨架屏行数
    skeletonRows: {
      type: Number,
      value: 3
    },
    // 大小: small, medium, large
    size: {
      type: String,
      value: 'medium',
      observer: createPropertyObserver('string', 'medium')
    },
    // 颜色主题
    theme: {
      type: String,
      value: 'primary',
      observer: createPropertyObserver('string', 'primary')
    },
    // 是否全屏遮罩
    overlay: {
      type: Boolean,
      value: false
    },
    // 最小显示时间(ms)，避免闪烁
    minDuration: {
      type: Number,
      value: 300
    }
  },

  data: {
    visible: false,
    showTime: 0
  },

  observers: {
    'loading': function(loading) {
      if (loading) {
        this.showLoading();
      } else {
        this.hideLoading();
      }
    }
  },

  methods: {
    /**
     * 显示加载状态
     */
    showLoading() {
      this.setData({
        visible: true,
        showTime: Date.now()
      });
    },

    /**
     * 隐藏加载状态
     */
    hideLoading() {
      const elapsed = Date.now() - this.data.showTime;
      const remaining = this.properties.minDuration - elapsed;

      if (remaining > 0) {
        setTimeout(() => {
          this.setData({ visible: false });
        }, remaining);
      } else {
        this.setData({ visible: false });
      }
    },

    /**
     * 生成骨架屏行
     */
    getSkeletonRows() {
      const rows = [];
      for (let i = 0; i < this.properties.skeletonRows; i++) {
        rows.push({
          width: Math.floor(Math.random() * 40) + 60 + '%', // 60-100%随机宽度
          isLast: i === this.properties.skeletonRows - 1
        });
      }
      return rows;
    }
  },

  lifetimes: {
    attached() {
      if (this.properties.type === 'skeleton') {
        this.setData({
          skeletonRowsData: this.getSkeletonRows()
        });
      }
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);