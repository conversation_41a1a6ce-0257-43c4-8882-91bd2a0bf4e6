/**
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');
 * 错误边界组件
 * 统一处理小程序中的错误，提供友好的错误页面
 * 支持错误恢复、错误上报、重试机制
 */
const componentConfig = {
  properties: {
    // 是否显示错误详情（开发环境）
    showDetails: {
      type: Boolean,
      value: false
    },
    // 错误类型：network, system, business, unknown
    errorType: {
      type: String,
      value: 'unknown',
      observer: createPropertyObserver('string', 'unknown')
    },
    // 自定义错误信息
    errorMessage: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 是否显示重试按钮
    showRetry: {
      type: Boolean,
      value: true
    },
    // 是否显示返回按钮
    showGoBack: {
      type: Boolean,
      value: true
    },
    // 错误图标
    errorIcon: {
      type: String,
      value: '/images/icons/error.png',
      observer: createPropertyObserver('string', '/images/icons/error.png')
    }
  },

  data: {
    hasError: false,
    error: null,
    errorInfo: null,
    retryCount: 0,
    maxRetries: 3
  },

  methods: {
    /**
     * 捕获错误
     */
    catchError(error, errorInfo = {}) {
      console.error('ErrorBoundary caught an error:', error);
      
      this.setData({
        hasError: true,
        error: {
          message: error.message || this.getErrorMessage(error),
          stack: error.stack,
          name: error.name,
          type: this.determineErrorType(error)
        },
        errorInfo: {
          componentStack: errorInfo.componentStack,
          timestamp: new Date().toISOString(),
          userAgent: wx.getSystemInfoSync(),
          ...errorInfo
        }
      });

      // 上报错误
      this.reportError(error, errorInfo);
    },

    /**
     * 确定错误类型
     */
    determineErrorType(error) {
      if (this.properties.errorType !== 'unknown') {
        return this.properties.errorType;
      }

      const message = error.message || '';
      
      if (message.includes('network') || message.includes('timeout')) {
        return 'network';
      } else if (message.includes('permission') || message.includes('auth')) {
        return 'permission';
      } else if (message.includes('not found') || message.includes('404')) {
        return 'notfound';
      } else if (message.includes('server') || message.includes('500')) {
        return 'server';
      } else {
        return 'system';
      }
    },

    /**
     * 获取错误信息
     */
    getErrorMessage(error) {
      if (this.properties.errorMessage) {
        return this.properties.errorMessage;
      }

      const errorType = this.determineErrorType(error);
      const messages = {
        network: '网络连接异常，请检查网络设置',
        permission: '权限不足，请联系管理员',
        notfound: '请求的内容不存在',
        server: '服务器异常，请稍后重试',
        system: '系统异常，请重启应用'
      };

      return messages[errorType] || '发生未知错误，请稍后重试';
    },

    /**
     * 获取错误图标
     */
    getErrorIcon() {
      const errorType = this.data.error?.type || 'unknown';
      const icons = {
        network: '/images/icons/network-error.png',
        permission: '/images/icons/permission-error.png',
        notfound: '/images/icons/not-found.png',
        server: '/images/icons/server-error.png',
        system: '/images/icons/system-error.png'
      };

      return icons[errorType] || this.properties.errorIcon;
    },

    /**
     * 重试操作
     */
    retry() {
      if (this.data.retryCount >= this.data.maxRetries) {
        wx.showToast({
          title: '重试次数过多，请稍后再试',
          icon: 'none'
        });
        return;
      }

      this.setData({
        retryCount: this.data.retryCount + 1
      });

      // 触发重试事件
      this.triggerEvent('retry', {
        retryCount: this.data.retryCount,
        error: this.data.error
      });

      // 清除错误状态
      this.clearError();
    },

    /**
     * 返回上一页
     */
    goBack() {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        wx.navigateBack();
      } else {
        wx.switchTab({
          url: '/pages/home/<USER>'
        });
      }
    },

    /**
     * 清除错误状态
     */
    clearError() {
      this.setData({
        hasError: false,
        error: null,
        errorInfo: null
      });
    },

    /**
     * 上报错误
     */
    reportError(error, errorInfo) {
      // 构建错误报告
      const errorReport = {
        message: error.message,
        stack: error.stack,
        name: error.name,
        type: this.determineErrorType(error),
        timestamp: Date.now(),
        url: getCurrentPages().pop()?.route || '',
        userInfo: wx.getStorageSync('userInfo') || {},
        systemInfo: wx.getSystemInfoSync(),
        errorInfo,
        retryCount: this.data.retryCount
      };

      // 上报到服务器
      wx.request({
        url: '/api/v1/error-report',
        method: 'POST',
        data: errorReport,
        fail: (err) => {
          console.error('错误上报失败:', err);
        }
      });

      // 本地存储错误日志
      try {
        const errorLogs = wx.getStorageSync('errorLogs') || [];
        errorLogs.push(errorReport);
        
        // 只保留最近50条错误日志
        if (errorLogs.length > 50) {
          errorLogs.splice(0, errorLogs.length - 50);
        }
        
        wx.setStorageSync('errorLogs', errorLogs);
      } catch (e) {
        console.error('保存错误日志失败:', e);
      }
    },

    /**
     * 复制错误信息
     */
    copyErrorInfo() {
      const errorText = `
错误类型: ${this.data.error?.type}
错误信息: ${this.data.error?.message}
发生时间: ${this.data.errorInfo?.timestamp}
页面路径: ${getCurrentPages().pop()?.route}
${this.properties.showDetails ? '错误堆栈: ' + this.data.error?.stack : ''}
      `.trim();

      wx.setClipboardData({
        data: errorText,
        success: () => {
          wx.showToast({
            title: '错误信息已复制',
            icon: 'success'
          });
        }
      });
    },

    /**
     * 获取帮助
     */
    getHelp() {
      wx.navigateTo({
        url: '/pages/profile/help/help'
      });
    }
  },

  lifetimes: {
    attached() {
      // 监听全局错误
      const originalOnError = App.onError;
      App.onError = (error) => {
        this.catchError(new Error(error));
        originalOnError && originalOnError(error);
      };

      // 监听未处理的Promise拒绝
      const originalOnUnhandledRejection = App.onUnhandledRejection;
      App.onUnhandledRejection = (res) => {
        this.catchError(new Error(res.reason), { type: 'unhandledRejection' });
        originalOnUnhandledRejection && originalOnUnhandledRejection(res);
      };
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);