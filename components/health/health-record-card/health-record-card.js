// components/health/health-record-card/health-record-card.js
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');

/**
 * Health健康记录卡片组件
 * 功能：展示单条健康记录信息，支持多种记录类型
 * 设计：现代化卡片设计，支持状态指示和快捷操作
 */

const componentConfig = {
  options: {
    addGlobalClass: true,
    multipleSlots: true
  },

  properties: {
    // 记录数据
    record: {
      type: Object,
      value: {},
      observer: function(newVal, oldVal) {
        if (newVal && newVal.id) {
          this.processRecordData(newVal);
        }
      }
    },

    // 记录类型
    type: {
      type: String,
      value: 'general', // general, checkup, diagnosis, treatment, vaccination
    },

    // 显示模式
    mode: {
      type: String,
      value: 'card', // card, list, compact
    },

    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      value: true
    },

    // 是否显示状态
    showStatus: {
      type: Boolean,
      value: true
    },

    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    }
  },

  data: {
    // 处理后的记录数据
    processedRecord: {
      typeInfo: {
        name: '常规记录',
        icon: '记录',
        color: 'var(--global-primary-color)'
      },
      statusInfo: {
        text: '正常',
        color: 'var(--global-success-color)',
        bgColor: 'var(--global-success-color-light)'
      },
      formattedDate: '',
      formattedTime: ''
    },
    
    // 记录类型配置
    typeConfig: {
      general: {
        name: '常规记录',
        icon: '记录',
        color: 'var(--global-primary-color)'
      },
      checkup: {
        name: '体检记录',
        icon: '检查',
        color: 'var(--global-info-color)'
      },
      diagnosis: {
        name: '诊断记录',
        icon: '诊断',
        color: 'var(--global-warning-color)'
      },
      treatment: {
        name: '治疗记录',
        icon: '治疗',
        color: 'var(--global-success-color)'
      },
      vaccination: {
        name: '疫苗记录',
        icon: '疫苗',
        color: 'var(--global-secondary-color)'
      }
    },

    // 状态配置
    statusConfig: {
      normal: {
        text: '正常',
        color: 'var(--global-success-color)',
        bgColor: 'var(--global-success-color-light)'
      },
      attention: {
        text: '需关注',
        color: 'var(--global-warning-color)',
        bgColor: 'var(--global-warning-color-light)'
      },
      abnormal: {
        text: '异常',
        color: 'var(--global-error-color)',
        bgColor: 'var(--global-error-color-light)'
      },
      pending: {
        text: '待处理',
        color: 'var(--global-info-color)',
        bgColor: 'var(--global-info-color-light)'
      }
    }
  },

  methods: {
    // 处理记录数据
    processRecordData: function(record) {
      // 确保record存在且有必要的属性
      if (!record) {
        console.warn('[health-record-card] processRecordData: record is null or undefined');
        return;
      }

      const typeInfo = this.data.typeConfig[this.data.type] || this.data.typeConfig.general;
      const statusInfo = this.data.statusConfig[record.status] || this.data.statusConfig.normal;

      // 确保typeInfo和statusInfo有必要的属性
      const safeTypeInfo = {
        name: typeInfo.name || '常规记录',
        icon: typeInfo.icon || '记录',
        color: typeInfo.color || 'var(--global-primary-color)'
      };

      const safeStatusInfo = {
        text: statusInfo.text || '正常',
        color: statusInfo.color || 'var(--global-success-color)',
        bgColor: statusInfo.bgColor || 'var(--global-success-color-light)'
      };

      this.setData({
        processedRecord: {
          ...record,
          typeInfo: safeTypeInfo,
          statusInfo: safeStatusInfo,
          formattedDate: this.formatDate(record.date),
          formattedTime: this.formatTime(record.date)
        }
      });
    },

    // 格式化日期
    formatDate: function(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const now = new Date();
      const diffDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));
      
      if (diffDays === 0) return '今天';
      if (diffDays === 1) return '昨天';
      if (diffDays < 7) return `${diffDays}天前`;
      
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    },

    // 格式化时间
    formatTime: function(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    },

    // 卡片点击事件
    onCardTap: function() {
      const { record } = this.data;
      this.triggerEvent('tap', {
        record,
        type: this.data.type
      });
    },

    // 查看详情
    onViewDetail: function() {
      const { record } = this.data;
      this.triggerEvent('detail', {
        record,
        type: this.data.type
      });
    },

    // 编辑记录
    onEdit: function() {
      const { record } = this.data;
      this.triggerEvent('edit', {
        record,
        type: this.data.type
      });
    },

    // 删除记录
    onDelete: function() {
      const { record } = this.data;
      wx.showModal({
        title: '确认删除',
        content: '确定要删除这条健康记录吗？',
        success: (res) => {
          if (res.confirm) {
            this.triggerEvent('delete', {
              record,
              type: this.data.type
            });
          }
        }
      });
    },

    // 分享记录
    onShare: function() {
      const { record } = this.data;
      this.triggerEvent('share', {
        record,
        type: this.data.type
      });
    }
  },

  lifetimes: {
    attached: function() {
      // 组件实例被放到页面节点树后执行
      if (this.data.record && this.data.record.id) {
        this.processRecordData(this.data.record);
      }
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);