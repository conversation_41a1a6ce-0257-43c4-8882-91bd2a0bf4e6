<!-- components/health/health-record-card/health-record-card.wxml -->
<view class="health-record-card {{customClass}} health-record-card--{{mode}} health-record-card--{{type}}"
      bind:tap="onCardTap">
  
  <!-- 卡片模式 -->
  <view wx:if="{{mode === 'card'}}" class="card-content">
    <!-- 头部信息 -->
    <view class="record-header">
      <view class="type-info">
        <global-icon
          name="{{processedRecord.typeInfo.icon || '记录'}}"
          size="18"
          color="{{processedRecord.typeInfo.color || 'var(--global-primary-color)'}}"
          custom-class="type-icon">
        </global-icon>
        <text class="type-name">{{processedRecord.typeInfo.name}}</text>
      </view>
      
      <view wx:if="{{showStatus}}" class="status-badge" 
            style="color: {{processedRecord.statusInfo.color}}; background-color: {{processedRecord.statusInfo.bgColor}}">
        {{processedRecord.statusInfo.text}}
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="record-content">
      <view class="title">{{record.title || '健康记录'}}</view>
      <view wx:if="{{record.description}}" class="description">{{record.description}}</view>
      
      <!-- 关键指标 -->
      <view wx:if="{{record.metrics && record.metrics.length > 0}}" class="metrics">
        <view wx:for="{{record.metrics}}" wx:key="key" 
              class="metric-item metric-item--{{item.status}}">
          <text class="metric-label">{{item.label}}:</text>
          <text class="metric-value">{{item.value}}{{item.unit || ''}}</text>
        </view>
      </view>
    </view>

    <!-- 底部信息 -->
    <view class="record-footer">
      <view class="time-info">
        <text class="date">{{processedRecord.formattedDate}}</text>
        <text class="time">{{processedRecord.formattedTime}}</text>
      </view>
      
      <!-- 操作按钮 -->
      <view wx:if="{{showActions}}" class="actions">
        <view class="action-btn" catch:tap="onViewDetail">
          <global-icon name="查看" size="14" color="var(--global-text-color-secondary)"></global-icon>
        </view>
        <view class="action-btn" catch:tap="onEdit">
          <global-icon name="编辑" size="14" color="var(--global-text-color-secondary)"></global-icon>
        </view>
        <view class="action-btn" catch:tap="onShare">
          <global-icon name="分享" size="14" color="var(--global-text-color-secondary)"></global-icon>
        </view>
      </view>
    </view>
  </view>

  <!-- 列表模式 -->
  <view wx:elif="{{mode === 'list'}}" class="list-content">
    <view class="list-main">
      <global-icon
        name="{{processedRecord.typeInfo.icon || '记录'}}"
        size="16"
        color="{{processedRecord.typeInfo.color || 'var(--global-primary-color)'}}"
        custom-class="list-icon">
      </global-icon>
      
      <view class="list-info">
        <view class="list-title">{{record.title || processedRecord.typeInfo.name}}</view>
        <view class="list-desc">{{record.description || processedRecord.formattedDate}}</view>
      </view>
      
      <view wx:if="{{showStatus}}" class="list-status" 
            style="color: {{processedRecord.statusInfo.color}}">
        {{processedRecord.statusInfo.text}}
      </view>
    </view>
  </view>

  <!-- 紧凑模式 -->
  <view wx:elif="{{mode === 'compact'}}" class="compact-content">
    <global-icon
      name="{{processedRecord.typeInfo.icon || '记录'}}"
      size="12"
      color="{{processedRecord.typeInfo.color || 'var(--global-primary-color)'}}"
      custom-class="compact-icon">
    </global-icon>
    <text class="compact-text">{{record.title || processedRecord.typeInfo.name}}</text>
    <text class="compact-time">{{processedRecord.formattedDate}}</text>
  </view>

  <!-- 自定义插槽 -->
  <slot name="extra"></slot>
</view>