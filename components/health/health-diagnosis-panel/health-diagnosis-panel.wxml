<!-- components/health/health-diagnosis-panel/health-diagnosis-panel.wxml -->
<view class="health-diagnosis-panel {{customClass}}">
  
  <!-- 症状输入模式 -->
  <view wx:if="{{mode === 'input'}}" class="input-mode">
    <!-- AI助手头部 -->
    <view class="ai-header">
      <view class="ai-avatar">
        <global-icon name="智能" size="24" color="var(--global-primary-color)"></global-icon>
      </view>
      <view class="ai-info">
        <view class="ai-name">AI健康助手</view>
        <view class="ai-desc">智能分析鹅群健康状况</view>
      </view>
      <view class="ai-status">
        <view class="status-dot"></view>
        <text class="status-text">在线</text>
      </view>
    </view>

    <!-- 症状输入区域 -->
    <view class="input-section">
      <view class="section-title">
        <global-icon name="编辑" size="16" color="var(--global-text-color-secondary)"></global-icon>
        <text>详细描述症状</text>
      </view>
      
      <textarea 
        class="symptoms-input"
        placeholder="请详细描述观察到的症状，如：鹅群出现发热、咳嗽、精神萎靡等..."
        value="{{symptoms}}"
        maxlength="500"
        bind:input="onSymptomsInput">
      </textarea>
      
      <view class="input-footer">
        <text class="char-count">{{symptoms.length}}/500</text>
      </view>
    </view>

    <!-- 常见症状快选 -->
    <view class="quick-symptoms">
      <view class="section-title">
        <global-icon name="标签" size="16" color="var(--global-text-color-secondary)"></global-icon>
        <text>常见症状快选</text>
      </view>
      
      <view class="symptoms-grid">
        <view wx:for="{{commonSymptoms}}" wx:key="id"
              class="symptom-item {{item.selected ? 'selected' : ''}}"
              data-symptom="{{item}}"
              bind:tap="onSymptomTap">
          <global-icon
            name="{{item.icon || '症状'}}"
            size="16"
            color="{{item.selected ? 'var(--global-primary-color)' : 'var(--global-text-color-secondary)'}}">
          </global-icon>
          <text class="symptom-name">{{item.name}}</text>
          <view wx:if="{{item.severity === 'high'}}" class="severity-badge high">
            <global-icon name="警告" size="10" color="var(--global-error-color)"></global-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 开始诊断按钮 -->
    <view class="action-section">
      <button class="diagnosis-btn primary" bind:tap="onStartDiagnosis">
        <global-icon name="智能" size="18" color="var(--global-text-color-inverse)"></global-icon>
        <text>开始AI诊断</text>
      </button>
    </view>

    <!-- 历史记录预览 -->
    <view wx:if="{{showHistory && historyRecords.length > 0}}" class="history-preview">
      <view class="section-title" bind:tap="onViewHistory">
        <global-icon name="历史" size="16" color="var(--global-text-color-secondary)"></global-icon>
        <text>最近诊断</text>
        <global-icon name="前进" size="12" color="var(--global-text-color-tertiary)"></global-icon>
      </view>
      
      <view class="history-list">
        <view wx:for="{{recentHistoryRecords}}" wx:key="id"
              class="history-item"
              data-record="{{item}}"
              bind:tap="onHistoryTap">
          <view class="history-date">{{item.date}}</view>
          <view class="history-symptoms">
            <text wx:for="{{item.symptoms}}" wx:key="*this" wx:for-item="symptom" class="symptom-tag">{{symptom}}</text>
          </view>
          <view class="history-result">{{item.result}}</view>
          <view class="confidence-badge">{{item.confidence}}%</view>
        </view>
      </view>
    </view>
  </view>

  <!-- AI分析模式 -->
  <view wx:elif="{{mode === 'analyzing'}}" class="analyzing-mode">
    <!-- 分析头部 -->
    <view class="analysis-header">
      <view class="ai-brain">
        <global-icon name="智能" size="32" color="var(--global-primary-color)"></global-icon>
        <view class="brain-pulse"></view>
      </view>
      <view class="analysis-title">AI正在分析中...</view>
      <view class="analysis-subtitle">请稍候，这通常需要10-15秒</view>
    </view>

    <!-- 进度条 -->
    <view class="progress-section">
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{analysisProgress}}%"></view>
      </view>
      <view class="progress-text">{{analysisProgressPercent}}%</view>
    </view>

    <!-- 分析步骤 -->
    <view class="analysis-steps">
      <view wx:for="{{analysisSteps}}" wx:key="id"
            class="step-item step-item--{{item.status}}">
        <view class="step-icon">
          <global-icon wx:if="{{item.status === 'pending'}}" 
                      name="时间" size="16" color="var(--global-text-color-tertiary)"></global-icon>
          <global-icon wx:elif="{{item.status === 'processing'}}" 
                      name="加载" size="16" color="var(--global-primary-color)"></global-icon>
          <global-icon wx:elif="{{item.status === 'completed'}}" 
                      name="成功" size="16" color="var(--global-success-color)"></global-icon>
        </view>
        <text class="step-name">{{item.name}}</text>
        <view wx:if="{{item.status === 'processing'}}" class="step-loading"></view>
      </view>
    </view>
  </view>

  <!-- 诊断结果模式 -->
  <view wx:elif="{{mode === 'result'}}" class="result-mode">
    <!-- 结果头部 -->
    <view class="result-header">
      <global-icon name="诊断" size="24" color="var(--global-success-color)"></global-icon>
      <view class="result-title">诊断完成</view>
      <view class="confidence-score">
        <text>可信度：</text>
        <text class="score">{{diagnosisResult.confidence}}%</text>
      </view>
    </view>

    <!-- 诊断结果 -->
    <view class="diagnosis-result">
      <view class="result-item main-result">
        <view class="result-label">诊断结果</view>
        <view class="result-value primary">{{diagnosisResult.diagnosis}}</view>
      </view>
      
      <view class="result-description">
        {{diagnosisResult.description}}
      </view>

      <!-- 紧急程度 -->
      <view class="urgency-section">
        <view class="urgency-badge" 
              style="color: {{getUrgencyConfig(diagnosisResult.urgency).color}}; background-color: {{getUrgencyConfig(diagnosisResult.urgency).bgColor}}">
          <global-icon name="警告" size="14" color="{{getUrgencyConfig(diagnosisResult.urgency).color || 'var(--global-info-color)'}}"></global-icon>
          <text>{{getUrgencyConfig(diagnosisResult.urgency).text}}</text>
        </view>
        <view class="follow-up">复查时间：{{diagnosisResult.followUp}}</view>
      </view>
    </view>

    <!-- 处理建议 -->
    <view class="recommendations">
      <view class="section-title">
        <global-icon name="建议" size="16" color="var(--global-text-color-secondary)"></global-icon>
        <text>处理建议</text>
      </view>
      
      <view class="recommendation-list">
        <view wx:for="{{diagnosisResult.recommendations}}" wx:key="*this" 
              class="recommendation-item">
          <view class="recommendation-number">{{index + 1}}</view>
          <text class="recommendation-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="result-actions">
      <button class="action-btn secondary" bind:tap="onRediagnosis">
        <global-icon name="重置" size="16" color="var(--global-text-color-primary)"></global-icon>
        <text>重新诊断</text>
      </button>
      <button class="action-btn primary" bind:tap="onSaveResult">
        <global-icon name="保存" size="16" color="var(--global-text-color-inverse)"></global-icon>
        <text>保存结果</text>
      </button>
      <button class="action-btn secondary" bind:tap="onShareResult">
        <global-icon name="分享" size="16" color="var(--global-text-color-primary)"></global-icon>
        <text>分享</text>
      </button>
    </view>
  </view>

  <!-- 自定义插槽 -->
  <slot name="extra"></slot>
</view>