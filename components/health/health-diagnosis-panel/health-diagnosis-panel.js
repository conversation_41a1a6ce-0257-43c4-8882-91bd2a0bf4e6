// components/health/health-diagnosis-panel/health-diagnosis-panel.js
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');

/**
 * Health AI诊断面板组件
 * 功能：智能诊断界面，支持症状输入、AI分析、诊断结果展示
 * 设计：现代化面板设计，突出AI能力和专业性
 */

const componentConfig = {
  options: {
    addGlobalClass: true,
    multipleSlots: true
  },

  properties: {
    // 诊断模式
    mode: {
      type: String,
      value: 'input', // input, analyzing, result
    },

    // 是否显示历史记录
    showHistory: {
      type: Boolean,
      value: true
    },

    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    }
  },

  data: {
    // 症状输入
    symptoms: '',
    selectedSymptoms: [],

    // 常见症状选项
    commonSymptoms: [
      { id: 'fever', name: '发热', icon: '发热', severity: 'high', selected: false },
      { id: 'cough', name: '咳嗽', icon: '咳嗽', severity: 'medium', selected: false },
      { id: 'diarrhea', name: '腹泻', icon: '肠胃', severity: 'medium', selected: false },
      { id: 'loss_appetite', name: '食欲不振', icon: '食物', severity: 'low', selected: false },
      { id: 'lethargy', name: '精神萎靡', icon: '疲劳', severity: 'medium', selected: false },
      { id: 'breathing', name: '呼吸困难', icon: '呼吸', severity: 'high', selected: false },
      { id: 'vomiting', name: '呕吐', icon: '呕吐', severity: 'high', selected: false },
      { id: 'skin_rash', name: '皮疹', icon: '皮肤', severity: 'low', selected: false }
    ],

    // 分析进度
    analysisProgress: 0,
    analysisProgressPercent: 0,
    analysisSteps: [
      { id: 1, name: '症状分析', status: 'pending' },
      { id: 2, name: '数据比对', status: 'pending' },
      { id: 3, name: '诊断推理', status: 'pending' },
      { id: 4, name: '生成报告', status: 'pending' }
    ],

    // 诊断结果
    diagnosisResult: null,

    // 历史诊断记录
    historyRecords: [
      {
        id: 1,
        date: '2024-12-29',
        symptoms: ['发热', '咳嗽'],
        result: '疑似禽流感',
        confidence: 85
      },
      {
        id: 2,
        date: '2024-12-28',
        symptoms: ['腹泻', '食欲不振'],
        result: '消化系统问题',
        confidence: 78
      }
    ]
  },

  methods: {
    // 症状输入事件
    onSymptomsInput: function(e) {
      this.setData({
        symptoms: e.detail.value
      });
    },

    // 选择常见症状
    onSymptomTap: function(e) {
      const { symptom } = e.currentTarget.dataset;
      const { selectedSymptoms, commonSymptoms } = this.data;
      
      const selectedIndex = selectedSymptoms.findIndex(s => s.id === symptom.id);
      const commonIndex = commonSymptoms.findIndex(s => s.id === symptom.id);
      
      if (selectedIndex > -1) {
        // 取消选择
        selectedSymptoms.splice(selectedIndex, 1);
        if (commonIndex > -1) {
          commonSymptoms[commonIndex].selected = false;
        }
      } else {
        // 添加选择
        selectedSymptoms.push(symptom);
        if (commonIndex > -1) {
          commonSymptoms[commonIndex].selected = true;
        }
      }
      
      this.setData({
        selectedSymptoms: [...selectedSymptoms],
        commonSymptoms: [...commonSymptoms]
      });
    },

    // 开始AI诊断
    onStartDiagnosis: function() {
      const { symptoms, selectedSymptoms } = this.data;
      
      if (!symptoms.trim() && selectedSymptoms.length === 0) {
        wx.showToast({
          title: '请输入症状描述',
          icon: 'none'
        });
        return;
      }

      // 切换到分析模式
      this.setData({
        mode: 'analyzing',
        analysisProgress: 0
      });

      // 开始分析动画
      this.startAnalysis();

      // 触发开始诊断事件
      this.triggerEvent('startDiagnosis', {
        symptoms: symptoms.trim(),
        selectedSymptoms
      });
    },

    // 开始分析动画
    startAnalysis: function() {
      const { analysisSteps } = this.data;
      let currentStep = 0;

      const updateStep = () => {
        if (currentStep >= analysisSteps.length) {
          // 分析完成
          this.completeAnalysis();
          return;
        }

        // 更新当前步骤状态
        analysisSteps[currentStep].status = 'processing';
        this.setData({
          analysisSteps: [...analysisSteps],
          analysisProgress: ((currentStep + 1) / analysisSteps.length) * 100
        });

        // 模拟处理时间
        setTimeout(() => {
          analysisSteps[currentStep].status = 'completed';
          this.setData({
            analysisSteps: [...analysisSteps]
          });
          
          currentStep++;
          setTimeout(updateStep, 500);
        }, 1000 + Math.random() * 1000);
      };

      updateStep();
    },

    // 完成分析
    completeAnalysis: function() {
      // 模拟AI诊断结果
      const mockResult = {
        diagnosis: '疑似禽流感',
        confidence: 85,
        severity: 'high',
        description: '根据症状分析，鹅群可能感染了禽流感病毒。建议立即隔离病鹅，进行专业治疗。',
        recommendations: [
          '立即隔离感染个体',
          '联系兽医进行确诊',
          '加强消毒措施',
          '监测其他个体状况',
          '暂停新个体引入'
        ],
        urgency: 'immediate',
        followUp: '24小时内复查'
      };

      this.setData({
        mode: 'result',
        diagnosisResult: mockResult
      });

      // 触发诊断完成事件
      this.triggerEvent('diagnosisComplete', {
        result: mockResult,
        symptoms: this.data.symptoms,
        selectedSymptoms: this.data.selectedSymptoms
      });
    },

    // 重新诊断
    onRediagnosis: function() {
      this.setData({
        mode: 'input',
        symptoms: '',
        selectedSymptoms: [],
        analysisProgress: 0,
        diagnosisResult: null,
        analysisSteps: this.data.analysisSteps.map(step => ({
          ...step,
          status: 'pending'
        }))
      });
    },

    // 保存诊断结果
    onSaveResult: function() {
      const { diagnosisResult } = this.data;
      this.triggerEvent('saveResult', {
        result: diagnosisResult
      });
    },

    // 分享诊断结果
    onShareResult: function() {
      const { diagnosisResult } = this.data;
      this.triggerEvent('shareResult', {
        result: diagnosisResult
      });
    },

    // 查看历史记录
    onViewHistory: function() {
      this.triggerEvent('viewHistory');
    },

    // 点击历史记录
    onHistoryTap: function(e) {
      const { record } = e.currentTarget.dataset;
      this.triggerEvent('historyTap', {
        record
      });
    },

    // 获取严重程度颜色
    getSeverityColor: function(severity) {
      const colors = {
        low: 'var(--global-success-color)',
        medium: 'var(--global-warning-color)',
        high: 'var(--global-error-color)'
      };
      return colors[severity] || colors.medium;
    },

    // 获取紧急程度配置
    getUrgencyConfig: function(urgency) {
      const configs = {
        immediate: {
          text: '立即处理',
          color: 'var(--global-error-color)',
          bgColor: 'var(--global-error-color-light)'
        },
        urgent: {
          text: '尽快处理',
          color: 'var(--global-warning-color)',
          bgColor: 'var(--global-warning-color-light)'
        },
        normal: {
          text: '常规处理',
          color: 'var(--global-info-color)',
          bgColor: 'var(--global-info-color-light)'
        }
      };
      return configs[urgency] || configs.normal;
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);