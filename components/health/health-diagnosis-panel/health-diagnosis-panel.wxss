/* components/health/health-diagnosis-panel/health-diagnosis-panel.wxss */
@import "/styles/global-design-tokens.wxss";

/* 基础面板样式 */
.health-diagnosis-panel {
  background: var(--global-bg-color-primary);
  border-radius: var(--global-border-radius-lg);
  overflow: hidden;
}

/* AI助手头部 */
.ai-header {
  display: flex;
  align-items: center;
  gap: var(--global-spacer-3);
  padding: var(--global-spacer-4);
  background: linear-gradient(135deg, var(--global-primary-color-light) 0%, var(--global-primary-color) 100%);
  color: var(--global-text-color-inverse);
}

.ai-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--global-border-radius-full);
  backdrop-filter: blur(10px);
}

.ai-info {
  flex: 1;
}

.ai-name {
  font-size: var(--global-font-size-lg);
  font-weight: var(--global-font-weight-bold);
  line-height: var(--global-line-height-tight);
  margin-bottom: var(--global-spacer-1);
}

.ai-desc {
  font-size: var(--global-font-size-sm);
  opacity: 0.9;
  line-height: var(--global-line-height-tight);
}

.ai-status {
  display: flex;
  align-items: center;
  gap: var(--global-spacer-1);
}

.status-dot {
  width: 6px;
  height: 6px;
  background: var(--global-success-color);
  border-radius: var(--global-border-radius-full);
}

/* 使用类选择器实现脉冲动画效果 */
.status-dot-pulse {
  animation: status-dot-pulse 2s infinite;
}

.status-dot-pulse-keyframe {
  opacity: 0.5;
}

.status-text {
  font-size: var(--global-font-size-xs);
  opacity: 0.9;
}

/* 输入区域 */
.input-section {
  padding: var(--global-spacer-4);
  border-bottom: 1px solid var(--global-border-color-light);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--global-spacer-2);
  margin-bottom: var(--global-spacer-3);
  font-size: var(--global-font-size-sm);
  font-weight: var(--global-font-weight-medium);
  color: var(--global-text-color-primary);
}

.symptoms-input {
  width: 100%;
  min-height: 120px;
  padding: var(--global-spacer-3);
  border: 1px solid var(--global-border-color-light);
  border-radius: var(--global-border-radius-md);
  font-size: var(--global-font-size-sm);
  line-height: var(--global-line-height-relaxed);
  color: var(--global-text-color-primary);
  background: var(--global-bg-color-secondary);
  transition: var(--global-transition-all);
  box-sizing: border-box;
}

.symptoms-input:focus {
  border-color: var(--global-primary-color);
  box-shadow: 0 0 0 2px var(--global-primary-color-light);
}

.input-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--global-spacer-2);
}

.char-count {
  font-size: var(--global-font-size-xs);
  color: var(--global-text-color-tertiary);
}

/* 常见症状快选 */
.quick-symptoms {
  padding: var(--global-spacer-4);
  border-bottom: 1px solid var(--global-border-color-light);
}

.symptoms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--global-spacer-2);
}

.symptom-item {
  display: flex;
  align-items: center;
  gap: var(--global-spacer-2);
  padding: var(--global-spacer-2) var(--global-spacer-3);
  border: 1px solid var(--global-border-color-light);
  border-radius: var(--global-border-radius-md);
  background: var(--global-bg-color-secondary);
  transition: var(--global-transition-all);
  cursor: pointer;
  position: relative;
}

.symptom-item:active {
  transform: scale(0.98);
}

.symptom-item.selected {
  border-color: var(--global-primary-color);
  background: var(--global-primary-color-light);
  color: var(--global-primary-color);
}

.symptom-name {
  flex: 1;
  font-size: var(--global-font-size-xs);
  font-weight: var(--global-font-weight-medium);
}

.severity-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  width: 16px;
  height: 16px;
  border-radius: var(--global-border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
}

.severity-badge.high {
  background: var(--global-error-color-light);
}

/* 操作区域 */
.action-section {
  padding: var(--global-spacer-4);
}

.diagnosis-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--global-spacer-2);
  padding: var(--global-spacer-3) var(--global-spacer-4);
  border: none;
  border-radius: var(--global-border-radius-md);
  font-size: var(--global-font-size-sm);
  font-weight: var(--global-font-weight-semibold);
  transition: var(--global-transition-all);
}

.diagnosis-btn.primary {
  background: linear-gradient(135deg, var(--global-primary-color) 0%, var(--global-primary-color-dark) 100%);
  color: var(--global-text-color-inverse);
  box-shadow: var(--global-shadow-md);
}

.diagnosis-btn.primary:active {
  transform: translateY(1px);
  box-shadow: var(--global-shadow-sm);
}

/* 历史记录预览 */
.history-preview {
  padding: var(--global-spacer-4);
  background: var(--global-bg-color-secondary);
}

.history-list {
  margin-top: var(--global-spacer-3);
}

.history-item {
  display: flex;
  align-items: center;
  gap: var(--global-spacer-3);
  padding: var(--global-spacer-3);
  margin-bottom: var(--global-spacer-2);
  background: var(--global-bg-color-primary);
  border-radius: var(--global-border-radius-md);
  transition: var(--global-transition-all);
}

.history-item:active {
  transform: scale(0.98);
}

.history-date {
  font-size: var(--global-font-size-xs);
  color: var(--global-text-color-tertiary);
  white-space: nowrap;
}

.history-symptoms {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: var(--global-spacer-1);
}

.symptom-tag {
  padding: var(--global-spacer-1) var(--global-spacer-2);
  background: var(--global-info-color-light);
  color: var(--global-info-color);
  border-radius: var(--global-border-radius-sm);
  font-size: var(--global-font-size-xs);
  line-height: 1;
}

.history-result {
  font-size: var(--global-font-size-xs);
  font-weight: var(--global-font-weight-medium);
  color: var(--global-text-color-primary);
}

.confidence-badge {
  padding: var(--global-spacer-1) var(--global-spacer-2);
  background: var(--global-success-color-light);
  color: var(--global-success-color);
  border-radius: var(--global-border-radius-sm);
  font-size: var(--global-font-size-xs);
  font-weight: var(--global-font-weight-bold);
  line-height: 1;
}

/* 分析模式 */
.analyzing-mode {
  padding: var(--global-spacer-6) var(--global-spacer-4);
  text-align: center;
}

.analysis-header {
  margin-bottom: var(--global-spacer-6);
}

.ai-brain {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  margin-bottom: var(--global-spacer-4);
  background: linear-gradient(135deg, var(--global-primary-color-light) 0%, var(--global-primary-color) 100%);
  border-radius: var(--global-border-radius-full);
}

.brain-pulse {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 2px solid var(--global-primary-color);
  border-radius: var(--global-border-radius-full);
  opacity: 0.8;
  transform: scale(1.2);
}

/* 使用类选择器实现脉冲动画效果 */
.brain-pulse-active {
  opacity: 0.3;
  transform: scale(1.4);
  transition: all 1s ease-in-out;
}

.analysis-title {
  font-size: var(--global-font-size-lg);
  font-weight: var(--global-font-weight-bold);
  color: var(--global-text-color-primary);
  margin-bottom: var(--global-spacer-2);
}

.analysis-subtitle {
  font-size: var(--global-font-size-sm);
  color: var(--global-text-color-secondary);
}

/* 进度条 */
.progress-section {
  margin-bottom: var(--global-spacer-6);
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: var(--global-bg-color-tertiary);
  border-radius: var(--global-border-radius-full);
  overflow: hidden;
  margin-bottom: var(--global-spacer-2);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--global-primary-color) 0%, var(--global-primary-color-light) 100%);
  border-radius: var(--global-border-radius-full);
  transition: width 0.5s ease;
}

.progress-text {
  font-size: var(--global-font-size-sm);
  font-weight: var(--global-font-weight-semibold);
  color: var(--global-primary-color);
}

/* 分析步骤 */
.analysis-steps {
  text-align: left;
}

.step-item {
  display: flex;
  align-items: center;
  gap: var(--global-spacer-3);
  padding: var(--global-spacer-3);
  margin-bottom: var(--global-spacer-2);
  border-radius: var(--global-border-radius-md);
  transition: var(--global-transition-all);
}

.step-item--pending {
  background: var(--global-bg-color-secondary);
  color: var(--global-text-color-tertiary);
}

.step-item--processing {
  background: var(--global-primary-color-light);
  color: var(--global-primary-color);
}

.step-item--completed {
  background: var(--global-success-color-light);
  color: var(--global-success-color);
}

.step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.step-name {
  flex: 1;
  font-size: var(--global-font-size-sm);
  font-weight: var(--global-font-weight-medium);
}

.step-loading {
  width: 16px;
  height: 16px;
  border: 2px solid var(--global-primary-color-light);
  border-top: 2px solid var(--global-primary-color);
  border-radius: var(--global-border-radius-full);
}

/* 使用类选择器实现旋转动画效果 */
.step-loading-active {
  transform: rotate(180deg);
  transition: transform 0.5s linear;
}

/* 结果模式 */
.result-mode {
  padding: var(--global-spacer-4);
}

.result-header {
  display: flex;
  align-items: center;
  gap: var(--global-spacer-3);
  padding: var(--global-spacer-4);
  margin-bottom: var(--global-spacer-4);
  background: var(--global-success-color-light);
  border-radius: var(--global-border-radius-md);
  color: var(--global-success-color);
}

.result-title {
  flex: 1;
  font-size: var(--global-font-size-lg);
  font-weight: var(--global-font-weight-bold);
}

.confidence-score {
  display: flex;
  align-items: center;
  gap: var(--global-spacer-1);
  font-size: var(--global-font-size-sm);
}

.score {
  font-weight: var(--global-font-weight-bold);
  font-size: var(--global-font-size-base);
}

/* 诊断结果 */
.diagnosis-result {
  margin-bottom: var(--global-spacer-6);
}

.result-item {
  margin-bottom: var(--global-spacer-3);
}

.result-label {
  font-size: var(--global-font-size-sm);
  color: var(--global-text-color-secondary);
  margin-bottom: var(--global-spacer-1);
}

.result-value {
  font-size: var(--global-font-size-lg);
  font-weight: var(--global-font-weight-bold);
  color: var(--global-text-color-primary);
}

.result-value.primary {
  color: var(--global-primary-color);
}

.result-description {
  padding: var(--global-spacer-3);
  background: var(--global-bg-color-secondary);
  border-radius: var(--global-border-radius-md);
  font-size: var(--global-font-size-sm);
  line-height: var(--global-line-height-relaxed);
  color: var(--global-text-color-primary);
  margin: var(--global-spacer-3) 0;
}

/* 紧急程度 */
.urgency-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--global-spacer-3);
  background: var(--global-bg-color-secondary);
  border-radius: var(--global-border-radius-md);
  margin: var(--global-spacer-3) 0;
}

.urgency-badge {
  display: flex;
  align-items: center;
  gap: var(--global-spacer-1);
  padding: var(--global-spacer-1) var(--global-spacer-2);
  border-radius: var(--global-border-radius-sm);
  font-size: var(--global-font-size-xs);
  font-weight: var(--global-font-weight-semibold);
}

.follow-up {
  font-size: var(--global-font-size-xs);
  color: var(--global-text-color-secondary);
}

/* 处理建议 */
.recommendations {
  margin-bottom: var(--global-spacer-6);
}

.recommendation-list {
  margin-top: var(--global-spacer-3);
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  gap: var(--global-spacer-3);
  padding: var(--global-spacer-3);
  margin-bottom: var(--global-spacer-2);
  background: var(--global-bg-color-secondary);
  border-radius: var(--global-border-radius-md);
}

.recommendation-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: var(--global-primary-color);
  color: var(--global-text-color-inverse);
  border-radius: var(--global-border-radius-full);
  font-size: var(--global-font-size-xs);
  font-weight: var(--global-font-weight-bold);
  flex-shrink: 0;
}

.recommendation-text {
  flex: 1;
  font-size: var(--global-font-size-sm);
  line-height: var(--global-line-height-relaxed);
  color: var(--global-text-color-primary);
}

/* 结果操作按钮 */
.result-actions {
  display: flex;
  gap: var(--global-spacer-3);
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--global-spacer-2);
  padding: var(--global-spacer-3);
  border: none;
  border-radius: var(--global-border-radius-md);
  font-size: var(--global-font-size-sm);
  font-weight: var(--global-font-weight-medium);
  transition: var(--global-transition-all);
  min-width: 80px;
}

.action-btn.primary {
  background: var(--global-primary-color);
  color: var(--global-text-color-inverse);
}

.action-btn.secondary {
  background: var(--global-bg-color-secondary);
  color: var(--global-text-color-primary);
  border: 1px solid var(--global-border-color-light);
}

.action-btn:active {
  transform: scale(0.98);
}