// components/tab-bar/tab-bar.js
const { UI } = require('../../constants/index.js');
const { mixinComponentUtils, createPropertyObserver } = require('../../utils/component-utils.js');

const componentConfig = {
  properties: {
    // Tab数据
    tabs: {
      type: Array,
      value: []
    },
    // 当前激活的Tab索引
    current: {
      type: Number,
      value: 0
    },
    // Tab容器样式
    containerClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // Tab项样式
    tabClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 激活Tab项样式
    activeTabClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 是否显示底部指示器
    showIndicator: {
      type: Boolean,
      value: true
    }
  },

  data: {
    // UI常量
    colors: UI.COLORS
  },

  methods: {
    // 切换Tab
    onTabTap(e) {
      const index = e.currentTarget.dataset.index;
      if (index !== this.data.current) {
        this.triggerEvent('change', {
          index: index,
          tab: this.data.tabs[index]
        });
      }
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);