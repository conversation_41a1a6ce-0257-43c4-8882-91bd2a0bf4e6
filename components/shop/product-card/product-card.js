// components/shop/product-card/product-card.js
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');

/**
 * Shop商品卡片组件
 * 专为商城模块设计的商品展示组件
 * 基于全局设计系统，提供现代化的商品展示体验
 */

const componentConfig = {
  /**
   * 组件的属性列表
   */
  properties: {
    // 商品信息
    product: {
      type: Object,
      value: {}
    },
    
    // 显示模式
    mode: {
      type: String,
      value: 'grid' // grid, list
    },
    
    // 卡片尺寸
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    
    // 是否显示收藏按钮
    showFavorite: {
      type: Boolean,
      value: true
    },
    
    // 是否显示购物车按钮
    showCart: {
      type: Boolean,
      value: true
    },
    
    // 是否显示库存信息
    showStock: {
      type: Boolean,
      value: true
    },
    
    // 是否显示评分
    showRating: {
      type: Boolean,
      value: true
    },
    
    // 是否显示标签
    showTags: {
      type: Boolean,
      value: true
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 库存状态配置
    stockStatus: {
      'in_stock': { label: '有库存', color: 'var(--global-success-color)' },
      'low_stock': { label: '库存不足', color: 'var(--global-warning-color)' },
      'out_of_stock': { label: '缺货', color: 'var(--global-error-color)' }
    },
    
    // 商品标签配置
    tagConfig: {
      'hot': { text: '热销', color: 'var(--shop-accent-color)' },
      'new': { text: '新品', color: 'var(--shop-primary-color)' },
      'discount': { text: '特价', color: 'var(--global-error-color)' },
      'recommended': { text: '推荐', color: 'var(--shop-secondary-color)' }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 商品卡片点击事件
     */
    onProductTap() {
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      // 触发商品查看事件
      this.triggerEvent('productTap', {
        product: this.properties.product
      });
    },
    
    /**
     * 收藏按钮点击事件
     */
    onFavoriteTap(e) {
      // 阻止事件冒泡
      e.stopPropagation();
      
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      // 触发收藏事件
      this.triggerEvent('favoriteTap', {
        product: this.properties.product,
        isFavorited: !this.properties.product.isFavorited
      });
    },
    
    /**
     * 购物车按钮点击事件
     */
    onCartTap(e) {
      // 阻止事件冒泡
      e.stopPropagation();
      
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      // 检查库存
      if (this.getStockStatus() === 'out_of_stock') {
        wx.showToast({
          title: '商品缺货',
          icon: 'none'
        });
        return;
      }
      
      // 触发加入购物车事件
      this.triggerEvent('cartTap', {
        product: this.properties.product
      });
    },
    
    /**
     * 获取库存状态
     */
    getStockStatus() {
      const { product } = this.properties;
      const stock = product.stock || 0;
      
      if (stock <= 0) {
        return 'out_of_stock';
      } else if (stock <= 10) {
        return 'low_stock';
      } else {
        return 'in_stock';
      }
    },
    
    /**
     * 格式化价格
     */
    formatPrice(price) {
      return Number(price).toFixed(2);
    },
    
    /**
     * 格式化原价
     */
    formatOriginalPrice(originalPrice) {
      return originalPrice ? Number(originalPrice).toFixed(2) : '';
    },
    
    /**
     * 计算折扣
     */
    getDiscount() {
      const { product } = this.properties;
      if (product.originalPrice && product.price) {
        const discount = ((product.originalPrice - product.price) / product.originalPrice * 100).toFixed(0);
        return discount > 0 ? discount : 0;
      }
      return 0;
    },
    
    /**
     * 渲染评分星星
     */
    renderStars() {
      const { product } = this.properties;
      const rating = product.rating || 0;
      const stars = [];
      
      for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
          stars.push('★');
        } else if (i - 0.5 <= rating) {
          stars.push('☆');
        } else {
          stars.push('☆');
        }
      }
      
      return stars.join('');
    },
    
    /**
     * 获取商品标签
     */
    getProductTags() {
      const { product } = this.properties;
      const tags = [];
      
      // 新品标签
      if (product.isNew) {
        tags.push({ type: 'new', ...this.data.tagConfig.new });
      }
      
      // 热销标签
      if (product.isHot) {
        tags.push({ type: 'hot', ...this.data.tagConfig.hot });
      }
      
      // 特价标签
      if (this.getDiscount() > 0) {
        tags.push({ type: 'discount', ...this.data.tagConfig.discount });
      }
      
      // 推荐标签
      if (product.isRecommended) {
        tags.push({ type: 'recommended', ...this.data.tagConfig.recommended });
      }
      
      return tags;
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件初始化
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 页面显示
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);