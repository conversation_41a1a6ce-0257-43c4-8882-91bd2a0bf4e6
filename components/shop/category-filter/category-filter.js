// components/shop/category-filter/category-filter.js
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');

/**
 * Shop分类筛选组件
 * 专为商城模块设计的多维度筛选组件
 * 支持分类、价格区间、排序等多种筛选方式
 */

const componentConfig = {
  /**
   * 组件的属性列表
   */
  properties: {
    // 分类列表
    categories: {
      type: Array,
      value: []
    },
    
    // 当前选中的分类
    selectedCategory: {
      type: String,
      value: 'all',
      observer: createPropertyObserver('string', 'all')
    },
    
    // 价格区间选项
    priceRanges: {
      type: Array,
      value: [
        { id: 'all', label: '全部价格', min: 0, max: 999999 },
        { id: 'low', label: '0-100元', min: 0, max: 100 },
        { id: 'medium', label: '100-500元', min: 100, max: 500 },
        { id: 'high', label: '500元以上', min: 500, max: 999999 }
      ]
    },
    
    // 当前选中的价格区间
    selectedPriceRange: {
      type: String,
      value: 'all',
      observer: createPropertyObserver('string', 'all')
    },
    
    // 排序选项
    sortOptions: {
      type: Array,
      value: [
        { id: 'default', label: '默认排序' },
        { id: 'price_asc', label: '价格从低到高' },
        { id: 'price_desc', label: '价格从高到低' },
        { id: 'sales', label: '销量优先' },
        { id: 'rating', label: '好评优先' },
        { id: 'newest', label: '最新上架' }
      ]
    },
    
    // 当前排序方式
    selectedSort: {
      type: String,
      value: 'default',
      observer: createPropertyObserver('string', 'default')
    },
    
    // 筛选结果数量
    resultCount: {
      type: Number,
      value: 0,
      observer: function(newVal) {
        // 确保值是数字类型
        if (typeof newVal !== 'number' || newVal === null || newVal === undefined || isNaN(newVal)) {
          this.setData({
            resultCount: 0
          });
        }
      }
    },
    
    // 是否显示在顶部
    sticky: {
      type: Boolean,
      value: true
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 筛选面板展开状态
    showFilterPanel: false,
    
    // 临时筛选条件(用于筛选面板)
    tempFilters: {
      category: 'all',
      priceRange: 'all',
      sort: 'default'
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 分类选择事件
     */
    onCategoryTap(e) {
      const categoryId = e.currentTarget.dataset.id;
      
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      // 触发分类变更事件
      this.triggerEvent('categoryChange', {
        categoryId: categoryId,
        category: this.getCategoryById(categoryId)
      });
    },
    
    /**
     * 筛选按钮点击
     */
    onFilterTap() {
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      // 初始化临时筛选条件
      this.setData({
        'tempFilters.category': this.properties.selectedCategory,
        'tempFilters.priceRange': this.properties.selectedPriceRange,
        'tempFilters.sort': this.properties.selectedSort,
        showFilterPanel: true
      });
    },
    
    /**
     * 关闭筛选面板
     */
    onCloseFilter() {
      this.setData({
        showFilterPanel: false
      });
    },
    
    /**
     * 临时分类选择
     */
    onTempCategoryChange(e) {
      const categoryId = e.currentTarget.dataset.id;
      this.setData({
        'tempFilters.category': categoryId
      });
    },
    
    /**
     * 临时价格区间选择
     */
    onTempPriceRangeChange(e) {
      const rangeId = e.currentTarget.dataset.id;
      this.setData({
        'tempFilters.priceRange': rangeId
      });
    },
    
    /**
     * 临时排序方式选择
     */
    onTempSortChange(e) {
      const sortId = e.currentTarget.dataset.id;
      this.setData({
        'tempFilters.sort': sortId
      });
    },
    
    /**
     * 重置筛选条件
     */
    onResetFilter() {
      this.setData({
        'tempFilters.category': 'all',
        'tempFilters.priceRange': 'all',
        'tempFilters.sort': 'default'
      });
    },
    
    /**
     * 确认筛选
     */
    onConfirmFilter() {
      const { tempFilters } = this.data;
      
      // 触觉反馈
      wx.vibrateShort({
        type: 'medium'
      });
      
      // 触发筛选应用事件
      this.triggerEvent('filterApply', {
        filters: {
          category: tempFilters.category,
          priceRange: tempFilters.priceRange,
          sort: tempFilters.sort
        }
      });
      
      // 关闭面板
      this.setData({
        showFilterPanel: false
      });
    },
    
    /**
     * 清除所有筛选
     */
    onClearAll() {
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      // 触发清除筛选事件
      this.triggerEvent('filterClear');
    },
    
    /**
     * 根据ID获取分类信息
     */
    getCategoryById(categoryId) {
      const { categories } = this.properties;
      return categories.find(cat => cat.id === categoryId) || { id: 'all', name: '全部' };
    },
    
    /**
     * 根据ID获取价格区间信息
     */
    getPriceRangeById(rangeId) {
      const { priceRanges } = this.properties;
      return priceRanges.find(range => range.id === rangeId) || priceRanges[0];
    },
    
    /**
     * 根据ID获取排序选项信息
     */
    getSortOptionById(sortId) {
      const { sortOptions } = this.properties;
      return sortOptions.find(opt => opt.id === sortId) || sortOptions[0];
    },
    
    /**
     * 检查是否有活跃筛选
     */
    hasActiveFilters() {
      const { selectedCategory, selectedPriceRange, selectedSort } = this.properties;
      return selectedCategory !== 'all' || selectedPriceRange !== 'all' || selectedSort !== 'default';
    },
    
    /**
     * 获取当前筛选数量
     */
    getActiveFilterCount() {
      let count = 0;
      const { selectedCategory, selectedPriceRange, selectedSort } = this.properties;
      
      if (selectedCategory !== 'all') count++;
      if (selectedPriceRange !== 'all') count++;
      if (selectedSort !== 'default') count++;
      
      return count;
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件初始化
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);