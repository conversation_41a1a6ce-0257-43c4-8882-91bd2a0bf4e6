// components/shop/cart-item/cart-item.js
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');

/**
 * Shop购物车商品项组件
 * 专为购物车页面设计的商品展示和操作组件
 * 支持选择、数量调整、删除等完整购物车功能
 */

const componentConfig = {
  /**
   * 组件的属性列表
   */
  properties: {
    // 购物车商品信息
    cartItem: {
      type: Object,
      value: {}
    },
    
    // 是否选中
    selected: {
      type: Boolean,
      value: false
    },
    
    // 是否显示选择框
    showCheckbox: {
      type: Boolean,
      value: true
    },
    
    // 是否可编辑数量
    editable: {
      type: Boolean,
      value: true
    },
    
    // 最大购买数量
    maxQuantity: {
      type: Number,
      value: 999
    },
    
    // 最小购买数量
    minQuantity: {
      type: Number,
      value: 1
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 数量调整防抖定时器
    quantityTimer: null,
    
    // 库存状态配置
    stockStatus: {
      'available': { label: '有库存', color: 'var(--global-success-color)' },
      'limited': { label: '库存有限', color: 'var(--global-warning-color)' },
      'unavailable': { label: '暂时缺货', color: 'var(--global-error-color)' }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 选择框点击事件
     */
    onCheckboxTap() {
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      // 触发选择事件
      this.triggerEvent('select', {
        cartItem: this.properties.cartItem,
        selected: !this.properties.selected
      });
    },
    
    /**
     * 商品区域点击事件
     */
    onItemTap() {
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      // 触发商品查看事件
      this.triggerEvent('itemTap', {
        cartItem: this.properties.cartItem
      });
    },
    
    /**
     * 减少数量
     */
    onQuantityDecrease() {
      const { cartItem, minQuantity } = this.properties;
      const currentQuantity = cartItem.quantity || 1;
      
      if (currentQuantity <= minQuantity) {
        wx.showToast({
          title: `最少购买${minQuantity}件`,
          icon: 'none'
        });
        return;
      }
      
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      this.updateQuantity(currentQuantity - 1);
    },
    
    /**
     * 增加数量
     */
    onQuantityIncrease() {
      const { cartItem, maxQuantity } = this.properties;
      const currentQuantity = cartItem.quantity || 1;
      const availableStock = cartItem.product?.stock || 0;
      
      // 检查库存限制
      if (currentQuantity >= availableStock) {
        wx.showToast({
          title: '库存不足',
          icon: 'none'
        });
        return;
      }
      
      // 检查最大数量限制
      if (currentQuantity >= maxQuantity) {
        wx.showToast({
          title: `最多购买${maxQuantity}件`,
          icon: 'none'
        });
        return;
      }
      
      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      });
      
      this.updateQuantity(currentQuantity + 1);
    },
    
    /**
     * 数量输入事件
     */
    onQuantityInput(e) {
      const value = parseInt(e.detail.value) || 1;
      this.updateQuantity(value);
    },
    
    /**
     * 更新数量
     */
    updateQuantity(newQuantity) {
      const { cartItem, minQuantity, maxQuantity } = this.properties;
      const availableStock = cartItem.product?.stock || 0;
      
      // 数量边界检查
      let quantity = Math.max(minQuantity, Math.min(newQuantity, maxQuantity, availableStock));
      
      // 防抖处理
      if (this.data.quantityTimer) {
        clearTimeout(this.data.quantityTimer);
      }
      
      this.setData({
        quantityTimer: setTimeout(() => {
          // 触发数量变更事件
          this.triggerEvent('quantityChange', {
            cartItem: this.properties.cartItem,
            oldQuantity: cartItem.quantity,
            newQuantity: quantity
          });
        }, 300)
      });
    },
    
    /**
     * 删除商品
     */
    onDeleteTap() {
      // 触觉反馈
      wx.vibrateShort({
        type: 'medium'
      });
      
      wx.showModal({
        title: '确认删除',
        content: '确定要从购物车中删除这件商品吗？',
        confirmText: '删除',
        confirmColor: '#FF3B30',
        success: (res) => {
          if (res.confirm) {
            // 触发删除事件
            this.triggerEvent('delete', {
              cartItem: this.properties.cartItem
            });
          }
        }
      });
    },
    
    /**
     * 获取库存状态
     */
    getStockStatus() {
      const { cartItem } = this.properties;
      const stock = cartItem.product?.stock || 0;
      const quantity = cartItem.quantity || 1;
      
      if (stock <= 0 || quantity > stock) {
        return 'unavailable';
      } else if (stock <= 10) {
        return 'limited';
      } else {
        return 'available';
      }
    },
    
    /**
     * 格式化价格
     */
    formatPrice(price) {
      return Number(price || 0).toFixed(2);
    },
    
    /**
     * 计算小计
     */
    calculateSubtotal() {
      const { cartItem } = this.properties;
      const price = cartItem.product?.price || 0;
      const quantity = cartItem.quantity || 1;
      return this.formatPrice(price * quantity);
    },
    
    /**
     * 获取商品规格文本
     */
    getSpecText() {
      const { cartItem } = this.properties;
      const specs = cartItem.specs || [];
      return specs.map(spec => `${spec.name}: ${spec.value}`).join(' | ');
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    detached() {
      // 清理定时器
      if (this.data.quantityTimer) {
        clearTimeout(this.data.quantityTimer);
      }
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);