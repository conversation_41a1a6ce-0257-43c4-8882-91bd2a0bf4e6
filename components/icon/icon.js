// 智慧养鹅小程序 - 统一图标组件
// 提供SVG图标、图标字体、网络图片等多种图标类型支持

const { ICONS } = require('../../constants/images.constants');
const { mixinComponentUtils, createPropertyObserver } = require('../../utils/component-utils.js');

const componentConfig = {
  options: {
    addGlobalClass: true, // 允许使用全局样式
    multipleSlots: false,
    styleIsolation: 'apply-shared'
  },

  properties: {
    // 基础属性
    name: {
      type: String,
      value: '',
      observer: 'updateIconPath'
    },
    
    type: {
      type: String,
      value: 'svg', // svg | font | image | unicode
      observer: 'updateIconClass'
    },
    
    src: {
      type: String,
      value: '', // 自定义图片路径（当type为image时使用）
    },
    
    // 尺寸属性
    size: {
      type: String,
      value: 'md', // xs | sm | md | lg | xl | 2xl | 3xl
      observer: 'updateSizeClass'
    },
    
    width: {
      type: String,
      value: '',
    },
    
    height: {
      type: String,
      value: '',
    },
    
    // 颜色属性
    color: {
      type: String,
      value: '', // primary | secondary | accent | success | warning | error | info | white | black
      observer: 'updateColorClass'
    },
    
    customColor: {
      type: String,
      value: '', // 自定义颜色值
    },
    
    // 状态属性
    disabled: {
      type: Boolean,
      value: false,
      observer: 'updateStateClass'
    },
    
    loading: {
      type: Boolean,
      value: false,
      observer: 'updateStateClass'
    },
    
    interactive: {
      type: Boolean,
      value: false,
      observer: 'updateStateClass'
    },
    
    // 背景装饰
    background: {
      type: String,
      value: '', // circle | square | primary | bordered | shadow
      observer: 'updateBgClass'
    },
    
    // 动画效果
    animation: {
      type: String,
      value: '', // spin | breathe | wobble | bounce | rotate
      observer: 'updateAnimationClass'
    },
    
    // 标签相关
    label: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    vertical: {
      type: Boolean,
      value: false
    },
    
    // 无障碍属性
    role: {
      type: String,
      value: 'img',
      observer: createPropertyObserver('string', 'img')
    },
    
    ariaLabel: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    ariaHidden: {
      type: String,
      value: 'false',
      observer: createPropertyObserver('string', 'false')
    },
    
    // 性能属性
    lazyLoad: {
      type: Boolean,
      value: false
    }
  },

  data: {
    iconPath: '',
    iconClass: '',
    sizeClass: 'icon-md',
    colorClass: '',
    stateClass: '',
    bgClass: '',
    animationClass: '',
    customStyle: ''
  },

  lifetimes: {
    attached() {
      this.initIcon();
    }
  },

  methods: {
    /**
     * 初始化图标
     */
    initIcon() {
      this.updateIconPath();
      this.updateIconClass();
      this.updateSizeClass();
      this.updateColorClass();
      this.updateStateClass();
      this.updateBgClass();
      this.updateAnimationClass();
      this.updateCustomStyle();
    },

    /**
     * 更新图标路径
     */
    updateIconPath() {
      const { name, type } = this.properties;
      
      if (type === 'svg' && name) {
        // 从常量配置中获取图标路径
        const iconPath = ICONS[name.toUpperCase()] || `/images/icons/${name}.svg`;
        this.setData({ iconPath });
      }
    },

    /**
     * 更新图标类名
     */
    updateIconClass() {
      const { name, type } = this.properties;
      
      if (type === 'font' && name) {
        const iconClass = `icon-${name}`;
        this.setData({ iconClass });
      }
    },

    /**
     * 更新尺寸类名
     */
    updateSizeClass() {
      const { size } = this.properties;
      const sizeClass = `icon-${size}`;
      this.setData({ sizeClass });
    },

    /**
     * 更新颜色类名
     */
    updateColorClass() {
      const { color } = this.properties;
      let colorClass = '';
      
      if (color) {
        if (['primary', 'secondary', 'accent', 'success', 'warning', 'error', 'info', 'white', 'black'].includes(color)) {
          colorClass = `icon-${color}`;
        } else if (color.startsWith('text-')) {
          colorClass = `icon-${color}`;
        }
      }
      
      this.setData({ colorClass });
    },

    /**
     * 更新状态类名
     */
    updateStateClass() {
      const { disabled, loading, interactive } = this.properties;
      let stateClass = '';
      
      if (disabled) {
        stateClass += ' icon-disabled';
      }
      
      if (loading) {
        stateClass += ' icon-loading';
      }
      
      if (interactive) {
        stateClass += ' icon-interactive';
      }
      
      this.setData({ stateClass: stateClass.trim() });
    },

    /**
     * 更新背景类名
     */
    updateBgClass() {
      const { background } = this.properties;
      let bgClass = '';
      
      if (background) {
        switch (background) {
          case 'circle':
            bgClass = 'icon-bg-circle';
            break;
          case 'square':
            bgClass = 'icon-bg-square';
            break;
          case 'primary':
            bgClass = 'icon-bg-primary';
            break;
          case 'bordered':
            bgClass = 'icon-bordered';
            break;
          case 'shadow':
            bgClass = 'icon-shadow';
            break;
        }
      }
      
      this.setData({ bgClass });
    },

    /**
     * 更新动画类名
     */
    updateAnimationClass() {
      const { animation } = this.properties;
      let animationClass = '';
      
      if (animation) {
        animationClass = `icon-${animation}`;
      }
      
      this.setData({ animationClass });
    },

    /**
     * 更新自定义样式
     */
    updateCustomStyle() {
      const { width, height, customColor } = this.properties;
      let customStyle = '';
      
      if (width) {
        customStyle += `width: ${width}rpx;`;
      }
      
      if (height) {
        customStyle += `height: ${height}rpx;`;
      }
      
      if (customColor) {
        customStyle += `color: ${customColor};`;
      }
      
      this.setData({ customStyle });
    },

    /**
     * 图标点击事件
     */
    onIconTap(e) {
      const { disabled } = this.properties;
      
      if (disabled) {
        return;
      }
      
      // 触发动画效果
      this.triggerAnimation();
      
      // 触发父组件事件
      this.triggerEvent('tap', {
        name: this.properties.name,
        type: this.properties.type
      });
    },

    /**
     * 触发动画效果
     */
    triggerAnimation() {
      const { interactive } = this.properties;
      
      if (!interactive) return;
      
      // 添加点击动画
      this.setData({
        stateClass: this.data.stateClass + ' icon-bounce'
      });
      
      // 移除动画类
      setTimeout(() => {
        this.setData({
          stateClass: this.data.stateClass.replace(' icon-bounce', '')
        });
      }, 600);
    },

    /**
     * 更新图标（外部调用）
     */
    updateIcon(options = {}) {
      Object.keys(options).forEach(key => {
        if (this.properties.hasOwnProperty(key)) {
          this.setData({ [key]: options[key] });
        }
      });
      
      this.initIcon();
    },

    /**
     * 播放动画（外部调用）
     */
    playAnimation(animationType = 'bounce') {
      const currentAnimation = this.data.animationClass;
      
      this.setData({
        animationClass: `icon-${animationType}`
      });
      
      setTimeout(() => {
        this.setData({
          animationClass: currentAnimation
        });
      }, 1000);
    },

    /**
     * 设置加载状态
     */
    setLoading(loading = true) {
      this.setData({ loading });
      this.updateStateClass();
    },

    /**
     * 设置禁用状态
     */
    setDisabled(disabled = true) {
      this.setData({ disabled });
      this.updateStateClass();
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);