/**
 * 通用按钮组件
 * 支持多种类型、尺寸、状态和无障碍访问
 */
const { UI, IMAGES } = require('../../../constants/index.js');
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');

const componentConfig = {
  options: {
    virtualHost: true
  },

  properties: {
    // 按钮类型
    type: {
      type: String,
      value: 'default', // default, primary, success, warning, error, text, link
    },
    
    // 按钮尺寸
    size: {
      type: String,
      value: 'medium', // small, medium, large
    },
    
    // 按钮文本
    text: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 图标（可选）
    icon: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 图标位置
    iconPosition: {
      type: String,
      value: 'left' // left, right
    },
    
    // 禁用状态
    disabled: {
      type: Boolean,
      value: false
    },
    
    // 加载状态
    loading: {
      type: Boolean,
      value: false
    },
    
    // 块级按钮（占满宽度）
    block: {
      type: Boolean,
      value: false
    },
    
    // 圆角按钮
    round: {
      type: Boolean,
      value: false
    },
    
    // 朴素按钮（只有边框）
    plain: {
      type: Boolean,
      value: false
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 自定义样式
    customStyle: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 无障碍标签
    ariaLabel: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 表单类型（当在form中使用时）
    formType: {
      type: String,
      value: '' // submit, reset
    },
    
    // 开放能力
    openType: {
      type: String,
      value: '' // contact, share, getUserInfo, getUserProfile, etc.
    }
  },

  data: {
    // UI常量
    colors: UI.COLORS,
    sizes: UI.SIZES,
    borderRadius: UI.BORDER_RADIUS,
    
    // 加载图标
    loadingIcon: IMAGES.ICONS.LOADING
  },

  computed: {
    // 按钮样式类
    buttonClass() {
      const classes = ['c-button'];
      
      // 类型样式
      classes.push(`c-button--${this.data.type}`);
      
      // 尺寸样式
      classes.push(`c-button--${this.data.size}`);
      
      // 状态样式
      if (this.data.disabled) classes.push('c-button--disabled');
      if (this.data.loading) classes.push('c-button--loading');
      if (this.data.block) classes.push('c-button--block');
      if (this.data.round) classes.push('c-button--round');
      if (this.data.plain) classes.push('c-button--plain');
      
      // 自定义类
      if (this.data.customClass) classes.push(this.data.customClass);
      
      return classes.join(' ');
    },
    
    // 按钮样式
    buttonStyle() {
      const styles = [];
      
      // 自定义样式
      if (this.data.customStyle) {
        styles.push(this.data.customStyle);
      }
      
      return styles.join('; ');
    },
    
    // 无障碍属性
    ariaAttributes() {
      return {
        role: 'button',
        'aria-label': this.data.ariaLabel || this.data.text,
        'aria-disabled': this.data.disabled,
        'aria-busy': this.data.loading
      };
    }
  },

  methods: {
    /**
     * 按钮点击事件
     */
    onTap(e) {
      // 禁用或加载状态下不触发点击
      if (this.data.disabled || this.data.loading) {
        return;
      }
      
      // 触发点击事件
      this.triggerEvent('tap', {
        type: this.data.type,
        text: this.data.text
      }, {
        bubbles: true,
        composed: true
      });
    },
    
    /**
     * 长按事件
     */
    onLongPress(e) {
      if (this.data.disabled || this.data.loading) {
        return;
      }
      
      this.triggerEvent('longpress', {
        type: this.data.type,
        text: this.data.text
      });
    },
    
    /**
     * 触摸开始
     */
    onTouchStart(e) {
      if (this.data.disabled || this.data.loading) {
        return;
      }
      
      this.triggerEvent('touchstart', e.detail);
    },
    
    /**
     * 触摸结束
     */
    onTouchEnd(e) {
      if (this.data.disabled || this.data.loading) {
        return;
      }
      
      this.triggerEvent('touchend', e.detail);
    },
    
    /**
     * 获取用户信息回调
     */
    onGetUserInfo(e) {
      this.triggerEvent('getuserinfo', e.detail);
    },
    
    /**
     * 获取用户手机号回调
     */
    onGetPhoneNumber(e) {
      this.triggerEvent('getphonenumber', e.detail);
    },
    
    /**
     * 获取用户实名信息回调
     */
    onGetRealNameAuthInfo(e) {
      this.triggerEvent('getrealnameauthinfo', e.detail);
    },
    
    /**
     * 客服会话回调
     */
    onContact(e) {
      this.triggerEvent('contact', e.detail);
    },
    
    /**
     * 获取更多信息回调
     */
    onChooseAvatar(e) {
      this.triggerEvent('chooseavatar', e.detail);
    },
    
    /**
     * 错误回调
     */
    onError(e) {
      this.triggerEvent('error', e.detail);
    },
    
    /**
     * 分享回调
     */
    onOpenSetting(e) {
      this.triggerEvent('opensetting', e.detail);
    }
  },

  lifetimes: {
    attached() {
      // 组件初始化时的逻辑
    },
    
    detached() {
      // 组件销毁时的清理逻辑
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);