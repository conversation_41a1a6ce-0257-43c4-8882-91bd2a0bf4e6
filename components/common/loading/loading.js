/**
 * 通用加载组件
 * 支持多种加载样式和骨架屏效果
 */
const { UI, IMAGES } = require('../../../constants/index.js');
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');

const componentConfig = {
  options: {
    virtualHost: true
  },

  properties: {
    // 加载类型
    type: {
      type: String,
      value: 'spinner' // spinner, dots, bars, skeleton, custom
    },
    
    // 加载尺寸
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    
    // 加载颜色
    color: {
      type: String,
      value: '' // 默认使用主题色
    },
    
    // 是否显示
    visible: {
      type: Boolean,
      value: true
    },
    
    // 加载文本
    text: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 文本位置
    textPosition: {
      type: String,
      value: 'bottom' // top, bottom, right, left
    },
    
    // 是否垂直居中
    center: {
      type: Boolean,
      value: false
    },
    
    // 是否全屏遮罩
    overlay: {
      type: Boolean,
      value: false
    },
    
    // 遮罩背景色
    overlayColor: {
      type: String,
      value: 'rgba(255, 255, 255, 0.8)',
      observer: createPropertyObserver('string', 'rgba(255, 255, 255, 0.8)')
    },
    
    // 动画速度（毫秒）
    duration: {
      type: Number,
      value: 1000
    },
    
    // 骨架屏行数（当type为skeleton时）
    skeletonRows: {
      type: Number,
      value: 3
    },
    
    // 骨架屏是否显示头像
    skeletonAvatar: {
      type: Boolean,
      value: false
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 自定义样式
    customStyle: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    }
  },

  data: {
    // UI常量
    colors: UI.COLORS,
    
    // 加载图标
    loadingIcon: IMAGES.ICONS.LOADING,
    
    // 骨架屏随机宽度数组
    skeletonWidths: []
  },

  observers: {
    'skeletonRows'(newVal) {
      this.generateSkeletonWidths(newVal);
    }
  },

  lifetimes: {
    attached() {
      this.generateSkeletonWidths(this.data.skeletonRows);
    }
  },

  computed: {
    // 加载容器样式类
    loadingClass() {
      const classes = ['c-loading'];
      
      // 加载类型
      classes.push(`c-loading--${this.data.type}`);
      
      // 加载尺寸
      classes.push(`c-loading--${this.data.size}`);
      
      // 文本位置
      if (this.data.text) {
        classes.push(`c-loading--text-${this.data.textPosition}`);
      }
      
      // 居中
      if (this.data.center) {
        classes.push('c-loading--center');
      }
      
      // 遮罩
      if (this.data.overlay) {
        classes.push('c-loading--overlay');
      }
      
      // 自定义类
      if (this.data.customClass) {
        classes.push(this.data.customClass);
      }
      
      return classes.join(' ');
    },
    
    // 加载器样式
    loaderStyle() {
      const styles = [];
      
      // 颜色
      const color = this.data.color || this.data.colors.PRIMARY.DEFAULT;
      styles.push(`color: ${color}`);
      
      // 动画时长
      styles.push(`animation-duration: ${this.data.duration}ms`);
      
      return styles.join('; ');
    },
    
    // 遮罩样式
    overlayStyle() {
      const styles = [];
      
      if (this.data.overlay) {
        styles.push(`background-color: ${this.data.overlayColor}`);
      }
      
      if (this.data.customStyle) {
        styles.push(this.data.customStyle);
      }
      
      return styles.join('; ');
    }
  },

  methods: {
    /**
     * 生成骨架屏随机宽度
     */
    generateSkeletonWidths(rows) {
      const widths = [];
      for (let i = 0; i < rows; i++) {
        // 生成60-100%的随机宽度
        const width = Math.floor(Math.random() * 40 + 60);
        widths.push(`${width}%`);
      }
      
      this.setData({
        skeletonWidths: widths
      });
    },
    
    /**
     * 遮罩点击事件
     */
    onOverlayTap() {
      this.triggerEvent('overlaytap');
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);