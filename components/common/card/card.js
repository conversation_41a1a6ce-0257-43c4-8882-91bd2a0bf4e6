/**
 * 通用卡片组件
 * 提供统一的卡片容器样式和交互
 */
const { UI } = require('../../../constants/index.js');
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');

const componentConfig = {
  options: {
    virtualHost: true,
    multipleSlots: true
  },

  properties: {
    // 卡片标题
    title: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 卡片副标题
    subtitle: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 内边距大小
    padding: {
      type: String,
      value: 'medium' // none, small, medium, large, custom
    },
    
    // 外边距大小
    margin: {
      type: String,
      value: 'none' // none, small, medium, large, custom
    },
    
    // 是否显示阴影
    shadow: {
      type: Boolean,
      value: true
    },
    
    // 阴影大小
    shadowSize: {
      type: String,
      value: 'medium' // small, medium, large
    },
    
    // 边框圆角
    radius: {
      type: String,
      value: 'medium' // none, small, medium, large, round
    },
    
    // 是否显示边框
    border: {
      type: Boolean,
      value: false
    },
    
    // 是否可点击
    clickable: {
      type: Boolean,
      value: false
    },
    
    // 是否显示箭头（当可点击时）
    arrow: {
      type: Boolean,
      value: false
    },
    
    // 禁用状态
    disabled: {
      type: Boolean,
      value: false
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 自定义样式
    customStyle: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    
    // 背景色
    backgroundColor: {
      type: String,
      value: '' // 默认使用主题色
    },
    
    // 无障碍标签
    ariaLabel: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    }
  },

  data: {
    // UI常量
    colors: UI.COLORS,
    spacing: UI.SPACING,
    borderRadius: UI.BORDER_RADIUS,
    shadows: UI.SHADOWS
  },

  computed: {
    // 卡片样式类
    cardClass() {
      const classes = ['c-card'];
      
      // 内边距
      classes.push(`c-card--padding-${this.data.padding}`);
      
      // 外边距
      if (this.data.margin !== 'none') {
        classes.push(`c-card--margin-${this.data.margin}`);
      }
      
      // 阴影
      if (this.data.shadow) {
        classes.push(`c-card--shadow-${this.data.shadowSize}`);
      }
      
      // 圆角
      classes.push(`c-card--radius-${this.data.radius}`);
      
      // 边框
      if (this.data.border) {
        classes.push('c-card--border');
      }
      
      // 交互状态
      if (this.data.clickable && !this.data.disabled) {
        classes.push('c-card--clickable');
      }
      
      if (this.data.disabled) {
        classes.push('c-card--disabled');
      }
      
      // 自定义类
      if (this.data.customClass) {
        classes.push(this.data.customClass);
      }
      
      return classes.join(' ');
    },
    
    // 卡片样式
    cardStyle() {
      const styles = [];
      
      // 背景色
      if (this.data.backgroundColor) {
        styles.push(`background-color: ${this.data.backgroundColor}`);
      }
      
      // 自定义样式
      if (this.data.customStyle) {
        styles.push(this.data.customStyle);
      }
      
      return styles.join('; ');
    }
  },

  methods: {
    /**
     * 卡片点击事件
     */
    onTap(e) {
      if (!this.data.clickable || this.data.disabled) {
        return;
      }
      
      this.triggerEvent('tap', {
        title: this.data.title,
        subtitle: this.data.subtitle
      }, {
        bubbles: true,
        composed: true
      });
    },
    
    /**
     * 长按事件
     */
    onLongPress(e) {
      if (this.data.disabled) {
        return;
      }
      
      this.triggerEvent('longpress', {
        title: this.data.title,
        subtitle: this.data.subtitle
      });
    },
    
    /**
     * 头部点击事件
     */
    onHeaderTap(e) {
      e.stopPropagation(); // 阻止冒泡到卡片点击
      
      this.triggerEvent('headertap', {
        title: this.data.title,
        subtitle: this.data.subtitle
      });
    },
    
    /**
     * 底部点击事件
     */
    onFooterTap(e) {
      e.stopPropagation(); // 阻止冒泡到卡片点击
      
      this.triggerEvent('footertap', {
        title: this.data.title,
        subtitle: this.data.subtitle
      });
    }
  },

  lifetimes: {
    attached() {
      // 组件初始化
    },
    
    detached() {
      // 组件销毁
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);