// components/weather-compact/weather-compact.js
const { mixinComponentUtils, createPropertyObserver } = require('../../utils/component-utils.js');
const componentConfig = {
  properties: {
    autoLocation: {
      type: Boolean,
      value: true
    }
  },

  data: {
    currentTemp: '25',
    weatherDesc: '晴',
    weatherIcon: '/assets/icons/weather/sunny.png',
    loading: false
  },

  lifetimes: {
    attached() {
      this.getWeatherData();
    }
  },

  methods: {
    // 获取天气数据
    getWeatherData() {
      // 立即设置默认数据，避免空白
      const defaultData = this.getMockWeatherData();
      this.setData({
        ...defaultData,
        loading: false
      });

      // 模拟API调用获取真实数据
      setTimeout(() => {
        const weatherData = this.getMockWeatherData();
        this.setData({
          ...weatherData,
          loading: false
        });
      }, 1000);
    },

    // 获取模拟天气数据
    getMockWeatherData() {
      const weatherTypes = [
        { desc: '晴', icon: '/assets/icons/weather/sunny.png', temp: 28 },
        { desc: '多云', icon: '/assets/icons/weather/cloudy.png', temp: 25 },
        { desc: '阴', icon: '/assets/icons/weather/overcast.png', temp: 22 },
        { desc: '小雨', icon: '/assets/icons/weather/rainy.png', temp: 20 }
      ];

      const currentWeather = weatherTypes[Math.floor(Math.random() * weatherTypes.length)];
      
      return {
        currentTemp: currentWeather.temp,
        weatherDesc: currentWeather.desc,
        weatherIcon: currentWeather.icon
      };
    },

    // 点击天气组件跳转到详细页面
    onWeatherTap() {
      wx.navigateTo({
        url: '/pages/weather/weather-detail/weather-detail'
      });
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);