// components/lazy-image/lazy-image.js
const { mixinComponentUtils, createPropertyObserver } = require('../../utils/component-utils.js');
/**
 * 图片懒加载组件
 * 基于微信小程序最佳实践，实现高性能图片懒加载
 */

const componentConfig = {
  properties: {
    // 图片源地址
    src: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 占位图地址
    placeholder: {
      type: String,
      value: '/images/placeholder.png',
      observer: createPropertyObserver('string', '/images/placeholder.png')
    },
    // 错误图地址
    errorImage: {
      type: String,
      value: '/images/error.png',
      observer: createPropertyObserver('string', '/images/error.png')
    },
    // 图片模式
    mode: {
      type: String,
      value: 'aspectFit',
      observer: createPropertyObserver('string', 'aspectFit')
    },
    // 懒加载阈值（提前多少像素开始加载）
    threshold: {
      type: Number,
      value: 100
    },
    // 是否启用懒加载
    lazy: {
      type: Boolean,
      value: true
    },
    // 图片宽度
    width: {
      type: String,
      value: '100%',
      observer: createPropertyObserver('string', '100%')
    },
    // 图片高度
    height: {
      type: String,
      value: 'auto',
      observer: createPropertyObserver('string', 'auto')
    },
    // 圆角
    borderRadius: {
      type: String,
      value: '0',
      observer: createPropertyObserver('string', '0')
    },
    // 是否显示加载动画
    showLoading: {
      type: Boolean,
      value: true
    }
  },

  data: {
    // 当前显示的图片地址
    currentSrc: '',
    // 加载状态：loading, loaded, error
    loadStatus: 'loading',
    // 是否在视口中
    inViewport: false,
    // 组件是否已初始化
    initialized: false
  },

  lifetimes: {
    attached() {
      this.initLazyLoad();
    },

    detached() {
      this.destroyObserver();
    }
  },

  observers: {
    'src': function(newSrc) {
      if (newSrc && this.data.inViewport) {
        this.loadImage(newSrc);
      }
    }
  },

  methods: {
    /**
     * 初始化懒加载
     */
    initLazyLoad() {
      if (!this.data.lazy) {
        // 不启用懒加载，直接加载图片
        this.loadImage(this.data.src);
        return;
      }

      // 设置占位图
      this.setData({
        currentSrc: this.data.placeholder,
        initialized: true
      });

      // 创建交叉观察器
      this.createIntersectionObserver();
    },

    /**
     * 创建交叉观察器
     */
    createIntersectionObserver() {
      if (this.intersectionObserver) {
        this.intersectionObserver.disconnect();
      }

      this.intersectionObserver = this.createIntersectionObserver({
        rootMargin: `${this.data.threshold}px`
      });

      this.intersectionObserver.relativeToViewport().observe('.lazy-image-container', (res) => {
        if (res.intersectionRatio > 0 && !this.data.inViewport) {
          this.setData({ inViewport: true });
          this.loadImage(this.data.src);
          // 图片开始加载后可以停止观察
          this.intersectionObserver.disconnect();
        }
      });
    },

    /**
     * 加载图片
     */
    loadImage(src) {
      if (!src) return;

      this.setData({
        loadStatus: 'loading'
      });

      // 预加载图片
      const img = wx.createImage ? wx.createImage() : new Image();
      
      img.onload = () => {
        this.setData({
          currentSrc: src,
          loadStatus: 'loaded'
        });
        this.triggerEvent('load', { src });
      };

      img.onerror = () => {
        this.setData({
          currentSrc: this.data.errorImage,
          loadStatus: 'error'
        });
        this.triggerEvent('error', { src });
      };

      img.src = src;
    },

    /**
     * 图片加载完成
     */
    onImageLoad(e) {
      this.setData({
        loadStatus: 'loaded'
      });
      this.triggerEvent('load', e.detail);
    },

    /**
     * 图片加载失败
     */
    onImageError(e) {
      this.setData({
        currentSrc: this.data.errorImage,
        loadStatus: 'error'
      });
      this.triggerEvent('error', e.detail);
    },

    /**
     * 图片点击事件
     */
    onImageTap(e) {
      this.triggerEvent('tap', e.detail);
    },

    /**
     * 重新加载图片
     */
    reload() {
      if (this.data.src) {
        this.loadImage(this.data.src);
      }
    },

    /**
     * 销毁观察器
     */
    destroyObserver() {
      if (this.intersectionObserver) {
        this.intersectionObserver.disconnect();
        this.intersectionObserver = null;
      }
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);