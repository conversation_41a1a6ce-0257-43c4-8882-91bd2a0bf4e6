/* components/lazy-image/lazy-image.wxss */

.lazy-image-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
  background-color: var(--bg-tertiary);
}

.lazy-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity var(--duration-base) var(--ease-out);
}

.lazy-image.loading {
  opacity: 0;
}

.lazy-image.loaded {
  opacity: 1;
}

.lazy-image.error {
  opacity: 0.3;
}

/* 加载状态 */
.lazy-image-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.loading-spinner {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid var(--border-light);
  border-top-color: var(--primary);
  border-radius: 50%;
  animation: spin var(--duration-slow) linear infinite;
  margin-bottom: var(--space-sm);
}

.loading-text {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

/* 错误状态 */
.lazy-image-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-lg);
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: var(--radius-md);
  color: var(--text-inverse);
  cursor: pointer;
  z-index: 3;
}

.error-icon {
  font-size: var(--text-xl);
  margin-bottom: var(--space-sm);
}

.error-text {
  font-size: var(--text-xs);
  text-align: center;
}

/* 动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 响应式 */
@media (max-width: 480rpx) {
  .loading-text,
  .error-text {
    font-size: var(--text-xs);
  }
  
  .loading-spinner {
    width: 16rpx;
    height: 16rpx;
  }
}
