<!-- components/lazy-image/lazy-image.wxml -->
<view class="lazy-image-container" style="width: {{width}}; height: {{height}}; border-radius: {{borderRadius}};">
  <!-- 加载状态 -->
  <view wx:if="{{showLoading && loadStatus === 'loading'}}" class="lazy-image-loading">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 图片 -->
  <image 
    class="lazy-image {{loadStatus}}"
    src="{{currentSrc}}"
    mode="{{mode}}"
    style="width: {{width}}; height: {{height}}; border-radius: {{borderRadius}};"
    bindload="onImageLoad"
    binderror="onImageError"
    bindtap="onImageTap"
    lazy-load="{{false}}"
  />

  <!-- 错误状态重试按钮 -->
  <view wx:if="{{loadStatus === 'error'}}" class="lazy-image-error" bindtap="reload">
    <view class="error-icon">⚠️</view>
    <text class="error-text">加载失败，点击重试</text>
  </view>
</view>
