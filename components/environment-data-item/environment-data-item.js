// components/environment-data-item/environment-data-item.js
const { IMAGES } = require('../../constants/index.js');
const { mixinComponentUtils, createPropertyObserver } = require('../../utils/component-utils.js');

/**
 * 环境数据项组件
 * 用于统一展示环境监控数据（温度、湿度、PM2.5、光照等）
 */
const componentConfig = {
  /**
   * 组件的属性列表
   */
  properties: {
    // 数据类型
    type: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 显示值
    value: {
      type: String,
      value: '0',
      observer: createPropertyObserver('string', '0')
    },
    // 单位
    unit: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 标签文字
    label: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 状态
    status: {
      type: String,
      value: 'normal',
      observer: createPropertyObserver('string', 'normal')
    },
    // 状态文字
    statusText: {
      type: String,
      value: '正常',
      observer: createPropertyObserver('string', '正常')
    },
    // 是否显示箭头
    showArrow: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    iconMap: {
      temperature: '/images/icon_temperature.png',
      humidity: '/images/icon_humidity.png',
      pm25: '/images/icon_pm25.png',
      light: '/images/icon_light.png'
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点击事件处理
     */
    onTap: function(e) {
      // 阻止事件冒泡，避免重复触发
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      
      this.triggerEvent('tap', {
        type: this.properties.type,
        value: this.properties.value
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached: function() {
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);