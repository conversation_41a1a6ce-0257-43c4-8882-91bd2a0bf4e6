// components/weather/weather.js
const { mixinComponentUtils, createPropertyObserver } = require('../../utils/component-utils.js');
const componentConfig = {
  properties: {
    // 是否自动获取位置
    autoLocation: {
      type: Boolean,
      value: true
    }
  },

  data: {
    location: '获取位置中...',
    currentTemp: '--',
    weatherDesc: '--',
    weatherIcon: '/assets/icons/weather/sunny.png',
    humidity: '--',
    windLevel: '--',
    airQuality: '--',
    updateTime: '',
    forecast: [],
    loading: true
  },

  lifetimes: {
    attached() {
      this.getWeatherData();
    }
  },

  methods: {
    // 获取天气数据
    getWeatherData() {
      if (this.properties.autoLocation) {
        this.getLocation();
      } else {
        this.loadWeatherData('北京市');
      }
    },

    // 获取地理位置
    getLocation() {
      wx.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.reverseGeocode(res.latitude, res.longitude);
        },
        fail: () => {
          // 获取位置失败，使用默认位置
          this.loadWeatherData('北京市');
        }
      });
    },

    // 逆地理编码获取城市名称
    reverseGeocode(latitude, longitude) {
      // 这里应该调用真实的地理编码API
      // 为了演示，我们使用模拟数据
      this.loadWeatherData('当前位置');
    },

    // 加载天气数据
    loadWeatherData(location) {
      this.setData({
        location: location,
        loading: true
      });

      // 模拟API调用
      setTimeout(() => {
        const weatherData = this.getMockWeatherData();
        this.setData({
          ...weatherData,
          loading: false,
          updateTime: this.formatTime(new Date())
        });
      }, 1000);
    },

    // 获取模拟天气数据
    getMockWeatherData() {
      const weatherTypes = [
        { desc: '晴', icon: '/assets/icons/weather/sunny.png', temp: 28 },
        { desc: '多云', icon: '/assets/icons/weather/cloudy.png', temp: 25 },
        { desc: '阴', icon: '/assets/icons/weather/overcast.png', temp: 22 },
        { desc: '小雨', icon: '/assets/icons/weather/rainy.png', temp: 20 }
      ];

      const currentWeather = weatherTypes[Math.floor(Math.random() * weatherTypes.length)];
      
      return {
        currentTemp: currentWeather.temp,
        weatherDesc: currentWeather.desc,
        weatherIcon: currentWeather.icon,
        humidity: Math.floor(Math.random() * 40) + 40, // 40-80%
        windLevel: Math.floor(Math.random() * 3) + 1, // 1-3级
        airQuality: ['优', '良', '轻度污染'][Math.floor(Math.random() * 3)],
        forecast: this.generateForecast()
      };
    },

    // 生成未来3天预报
    generateForecast() {
      const forecast = [];
      const today = new Date();
      
      for (let i = 1; i <= 3; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);
        
        const weatherTypes = [
          { icon: '/assets/icons/weather/sunny.png' },
          { icon: '/assets/icons/weather/cloudy.png' },
          { icon: '/assets/icons/weather/overcast.png' },
          { icon: '/assets/icons/weather/rainy.png' }
        ];
        
        const weather = weatherTypes[Math.floor(Math.random() * weatherTypes.length)];
        const minTemp = Math.floor(Math.random() * 10) + 15; // 15-25°
        const maxTemp = minTemp + Math.floor(Math.random() * 10) + 5; // +5-15°
        
        forecast.push({
          date: this.formatDate(date),
          icon: weather.icon,
          minTemp: minTemp,
          maxTemp: maxTemp
        });
      }
      
      return forecast;
    },

    // 格式化时间
    formatTime(date) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    },

    // 格式化日期
    formatDate(date) {
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
      const weekday = weekdays[date.getDay()];
      return `${month}/${day} 周${weekday}`;
    },

    // 刷新天气数据
    refresh() {
      this.getWeatherData();
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);