// components/empty-state/empty-state.js
const { IMAGES, UI } = require('../../constants/index.js');
const { mixinComponentUtils, createPropertyObserver } = require('../../utils/component-utils.js');

const componentConfig = {
  properties: {
    // 空状态类型
    type: {
      type: String,
      value: 'default' // default, nodata, error, offline
    },
    // 自定义图标
    icon: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 主要文本
    title: {
      type: String,
      value: '暂无数据',
      observer: createPropertyObserver('string', '暂无数据')
    },
    // 描述文本
    description: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 按钮文本
    buttonText: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 是否显示按钮
    showButton: {
      type: Boolean,
      value: false
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    }
  },

  data: {
    // 预定义的空状态配置
    presets: {
      default: {
        icon: IMAGES.ICONS.EMPTY || '/assets/icons/empty.png',
        title: '暂无数据',
        description: ''
      },
      nodata: {
        icon: IMAGES.ICONS.NO_DATA || '/assets/icons/no-data.png',
        title: '暂无数据',
        description: '还没有相关数据'
      },
      error: {
        icon: IMAGES.ICONS.ERROR || '/assets/icons/error.png',
        title: '出错了',
        description: '数据加载失败，请稍后重试'
      },
      offline: {
        icon: IMAGES.ICONS.OFFLINE || '/assets/icons/offline.png',
        title: '网络不给力',
        description: '请检查网络连接后重试'
      }
    }
  },

  computed: {
    // 计算最终显示的配置
    finalConfig() {
      const preset = this.data.presets[this.data.type] || this.data.presets.default;
      return {
        icon: this.data.icon || preset.icon,
        title: this.data.title || preset.title,
        description: this.data.description || preset.description
      };
    }
  },

  methods: {
    // 按钮点击事件
    onButtonTap() {
      this.triggerEvent('buttontap');
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);