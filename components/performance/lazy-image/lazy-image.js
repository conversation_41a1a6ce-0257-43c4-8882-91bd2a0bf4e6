/**
const { mixinComponentUtils, createPropertyObserver } = require('../../../utils/component-utils.js');
 * 图片懒加载组件
 * 基于微信小程序IntersectionObserver API实现
 * 支持占位图、加载状态、错误处理
 */
const componentConfig = {
  properties: {
    // 图片源地址
    src: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 占位图地址
    placeholder: {
      type: String,
      value: '/images/placeholder.png',
      observer: createPropertyObserver('string', '/images/placeholder.png')
    },
    // 错误时显示的图片
    errorImage: {
      type: String,
      value: '/images/error.png',
      observer: createPropertyObserver('string', '/images/error.png')
    },
    // 图片模式
    mode: {
      type: String,
      value: 'aspectFit',
      observer: createPropertyObserver('string', 'aspectFit')
    },
    // 懒加载根边距（提前多少像素开始加载）
    rootMargin: {
      type: String,
      value: '100px',
      observer: createPropertyObserver('string', '100px')
    },
    // 图片样式类
    imageClass: {
      type: String,
      value: '',
      observer: createPropertyObserver('string', '')
    },
    // 是否启用懒加载
    lazy: {
      type: Boolean,
      value: true
    },
    // 加载动画
    showLoading: {
      type: Boolean,
      value: true
    }
  },

  data: {
    currentSrc: '',
    isLoading: false,
    isError: false,
    isVisible: false
  },

  observers: {
    'src,lazy': function(src, lazy) {
      if (!lazy) {
        // 不启用懒加载时直接显示图片
        this.setData({
          currentSrc: src,
          isVisible: true
        });
      } else {
        // 初始显示占位图
        this.setData({
          currentSrc: this.properties.placeholder,
          isVisible: false
        });
      }
    }
  },

  lifetimes: {
    attached() {
      if (this.properties.lazy) {
        this.createIntersectionObserver();
      }
    },

    detached() {
      if (this.intersectionObserver) {
        this.intersectionObserver.disconnect();
      }
    }
  },

  methods: {
    /**
     * 创建交叉观察器
     */
    createIntersectionObserver() {
      const observer = this.createIntersectionObserver({
        rootMargin: this.properties.rootMargin
      });

      observer.relativeToViewport().observe('.lazy-image', (res) => {
        if (res.intersectionRatio > 0 && !this.data.isVisible) {
          this.loadImage();
        }
      });

      this.intersectionObserver = observer;
    },

    /**
     * 加载图片
     */
    loadImage() {
      const { src } = this.properties;
      
      if (!src || this.data.isVisible) return;

      this.setData({
        isLoading: this.properties.showLoading,
        isVisible: true
      });

      // 预加载图片
      const image = wx.createImage ? wx.createImage() : new Image();
      
      image.onload = () => {
        this.setData({
          currentSrc: src,
          isLoading: false,
          isError: false
        });
        this.triggerEvent('load', { src });
      };

      image.onerror = () => {
        this.setData({
          currentSrc: this.properties.errorImage,
          isLoading: false,
          isError: true
        });
        this.triggerEvent('error', { src });
      };

      image.src = src;
    },

    /**
     * 图片加载成功
     */
    onImageLoad(e) {
      if (!this.properties.lazy) {
        this.setData({
          isLoading: false,
          isError: false
        });
        this.triggerEvent('load', e.detail);
      }
    },

    /**
     * 图片加载失败
     */
    onImageError(e) {
      this.setData({
        currentSrc: this.properties.errorImage,
        isLoading: false,
        isError: true
      });
      this.triggerEvent('error', e.detail);
    },

    /**
     * 重试加载
     */
    retry() {
      this.setData({
        isError: false,
        isVisible: false
      });
      this.loadImage();
    }
  }
};

// 应用组件工具混入
mixinComponentUtils(componentConfig);

// 注册组件
Component(componentConfig);