const { test, expect } = require('@playwright/test');

const BASE_URL = 'http://localhost:3002';

test.describe('SaaS平台管理后台快速验证', () => {
  
  // 通用登录函数
  async function login(page) {
    await page.goto(`${BASE_URL}/saas-admin/login`);
    await page.fill('input[name="username"]', 'platform_admin');
    await page.fill('input[name="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/saas-admin/dashboard');
  }

  test('快速验证所有功能模块可访问', async ({ page }) => {
    
    await login(page);
    
    const modules = [
      { name: '仪表盘', path: '/saas-admin/dashboard', pageTitle: '仪表板' },
      { name: '租户管理', path: '/saas-admin/tenants', pageTitle: '租户管理' },
      { name: '用户管理', path: '/saas-admin/users', pageTitle: '用户管理' },
      { name: '鹅群管理', path: '/saas-admin/flocks', pageTitle: '鹅群管理' },
      { name: '健康记录', path: '/saas-admin/health', pageTitle: '健康记录' },
      { name: '生产记录', path: '/saas-admin/production', pageTitle: '生产记录' },
      { name: '库存管理', path: '/saas-admin/inventory', pageTitle: '库存管理' },
      { name: '商城管理', path: '/saas-admin/shop', pageTitle: '商城管理' },
      { name: '知识库', path: '/saas-admin/knowledge', pageTitle: '知识库' },
      { name: 'AI诊断', path: '/saas-admin/ai', pageTitle: 'AI诊断' },
      { name: '任务管理', path: '/saas-admin/tasks', pageTitle: '任务管理' },
      { name: '公告管理', path: '/saas-admin/announcements', pageTitle: '公告管理' },
      { name: '价格管理', path: '/saas-admin/pricing', pageTitle: '价格管理' },
      { name: '订阅计划', path: '/saas-admin/plans', pageTitle: '订阅计划管理' },
      { name: '系统监控', path: '/saas-admin/monitoring', pageTitle: '系统监控' }
    ];

    let passedModules = [];
    let failedModules = [];

    for (const module of modules) {
      try {
        
        await page.goto(`${BASE_URL}${module.path}`);
        await page.waitForLoadState('networkidle', { timeout: 10000 });
        
        // 验证页面基本结构
        await expect(page.locator('#page-title')).toContainText(module.pageTitle, { timeout: 5000 });
        await expect(page.locator('.sidebar')).toBeVisible({ timeout: 5000 });
        await expect(page.locator('main')).toBeVisible({ timeout: 5000 });
        
        passedModules.push(module.name);
        
      } catch (error) {
        failedModules.push(`${module.name}: ${error.message}`);
      }
    }

    // 输出测试结果
    :`);
    passedModules.forEach(name => );
    
    if (failedModules.length > 0) {
      :`);
      failedModules.forEach(error => );
    }
    
    * 100)}%`);
    
    // 要求至少80%的模块通过测试
    expect(passedModules.length / modules.length).toBeGreaterThan(0.8);
  });

  test('验证核心功能完整性', async ({ page }) => {
    
    await login(page);
    
    // 1. 仪表盘 - 验证统计卡片和图表
    await page.goto(`${BASE_URL}/saas-admin/dashboard`);
    await expect(page.locator('.stat-card')).toHaveCount(4);
    await expect(page.locator('#apiChart')).toBeVisible();
    
    // 2. 健康记录 - 验证统计卡片和表格
    await page.goto(`${BASE_URL}/saas-admin/health`);
    await expect(page.locator('.health-card')).toHaveCount.greaterThan(3);
    await expect(page.locator('.table')).toBeVisible();
    
    // 3. 生产记录 - 验证统计和记录
    await page.goto(`${BASE_URL}/saas-admin/production`);
    await expect(page.locator('.production-card')).toHaveCount.greaterThan(3);
    await expect(page.locator('.table')).toBeVisible();
    
    // 4. AI诊断 - 验证聊天界面和统计
    await page.goto(`${BASE_URL}/saas-admin/ai`);
    await expect(page.locator('.ai-card')).toHaveCount.greaterThan(3);
    await expect(page.locator('.chat-container')).toBeVisible();
    
    // 5. 任务管理 - 验证看板和表格视图
    await page.goto(`${BASE_URL}/saas-admin/tasks`);
    await expect(page.locator('.task-card')).toHaveCount.greaterThan(3);
    await expect(page.locator('#tableView')).toBeVisible();
    
  });

});

test.afterAll(async () => {
});