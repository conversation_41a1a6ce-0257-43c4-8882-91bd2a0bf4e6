const { test, expect } = require('@playwright/test');

test.describe('SaaS平台管理后台基础测试', () => {
  
  test('登录页面可访问', async ({ page }) => {
    await page.goto('http://localhost:3002/saas-admin/login');
    await expect(page.locator('title')).toContainText('SaaS平台管理登录');
  });

  test('管理员登录流程', async ({ page }) => {
    // 访问登录页面
    await page.goto('http://localhost:3002/saas-admin/login');
    
    // 填写登录信息
    await page.fill('input[name="username"]', 'platform_admin');
    await page.fill('input[name="password"]', 'admin123');
    
    // 点击登录按钮
    await page.click('button[type="submit"]');
    
    // 验证重定向到仪表盘
    await page.waitForURL('**/saas-admin/dashboard');
    await expect(page.locator('h5')).toContainText('仪表板');
    
  });

  test('仪表盘基本元素', async ({ page }) => {
    // 先登录
    await page.goto('http://localhost:3002/saas-admin/login');
    await page.fill('input[name="username"]', 'platform_admin');
    await page.fill('input[name="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/saas-admin/dashboard');
    
    // 验证基本元素
    await expect(page.locator('.sidebar')).toBeVisible();
    await expect(page.locator('.navbar')).toBeVisible();
    await expect(page.locator('.stat-card')).toHaveCount(4);
    
  });

  test('租户管理页面', async ({ page }) => {
    // 先登录
    await page.goto('http://localhost:3002/saas-admin/login');
    await page.fill('input[name="username"]', 'platform_admin');
    await page.fill('input[name="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/saas-admin/dashboard');
    
    // 访问租户管理页面
    await page.click('a[href="/saas-admin/tenants"]');
    await page.waitForURL('**/saas-admin/tenants');
    
    // 验证页面内容
    await expect(page.locator('h5')).toContainText('租户管理');
    await expect(page.locator('.table')).toBeVisible();
    
  });

});