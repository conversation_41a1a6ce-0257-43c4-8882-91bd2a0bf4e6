/**
 * SAAS平台集成测试
 * Integration Tests for SAAS Platform
 */

const request = require('supertest');
const app = require('../../backend/app.js');
const tenantDatabaseManager = require('../../backend/models/tenant-database.model');

describe('SAAS平台集成测试', () => {
  let tenantToken;
  let demoTenantId;
  let testUser;

  before(async () => {
    // 初始化测试环境
    await setupTestEnvironment();
  });

  after(async () => {
    // 清理测试环境
    await cleanupTestEnvironment();
  });

  describe('1. 租户识别和初始化', () => {
    it('应该能识别租户并初始化租户数据库连接', async () => {
      const response = await request(app)
        .get('/api/v1/tenant/config/features')
        .set('X-Tenant-Code', 'demo')
        .expect(200);

      expect(response.body.success).to.be.true;
    });

    it('应该拒绝无效的租户代码', async () => {
      await request(app)
        .get('/api/v1/tenant/config/features')
        .set('X-Tenant-Code', 'invalid_tenant')
        .expect(404);
    });
  });

  describe('2. 微信小程序登录流程', () => {
    it('应该能成功进行微信登录', async () => {
      // 模拟微信登录
      const response = await request(app)
        .post('/api/v1/tenant/auth/wechat-login')
        .set('X-Tenant-Code', 'demo')
        .send({
          code: 'mock_wechat_code',
          userInfo: {
            nickName: '测试用户',
            avatarUrl: 'https://example.com/avatar.png'
          }
        })
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.accessToken).to.exist;
      expect(response.body.data.refreshToken).to.exist;

      tenantToken = response.body.data.accessToken;
      testUser = response.body.data.userInfo;
    });

    it('应该能获取用户信息', async () => {
      const response = await request(app)
        .get('/api/v1/tenant/auth/profile')
        .set('X-Tenant-Code', 'demo')
        .set('Authorization', `Bearer ${tenantToken}`)
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.id).to.equal(testUser.id);
    });
  });

  describe('3. 权限控制测试', () => {
    let flockId;

    it('应该能创建鹅群（有权限）', async () => {
      const response = await request(app)
        .post('/api/v1/tenant/flocks')
        .set('X-Tenant-Code', 'demo')
        .set('Authorization', `Bearer ${tenantToken}`)
        .send({
          batchNumber: 'TEST001',
          breedType: '狮头鹅',
          quantity: 100,
          dateOfBirth: '2024-01-01',
          status: 'active'
        })
        .expect(201);

      expect(response.body.success).to.be.true;
      expect(response.body.data.id).to.exist;
      flockId = response.body.data.id;
    });

    it('应该能获取自己创建的鹅群', async () => {
      const response = await request(app)
        .get(`/api/v1/tenant/flocks/${flockId}`)
        .set('X-Tenant-Code', 'demo')
        .set('Authorization', `Bearer ${tenantToken}`)
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.batchNumber).to.equal('TEST001');
    });

    it('应该拒绝访问其他用户的资源', async () => {
      // 创建另一个用户的token
      const otherUserResponse = await request(app)
        .post('/api/v1/tenant/auth/wechat-login')
        .set('X-Tenant-Code', 'demo')
        .send({
          code: 'mock_wechat_code_2',
          userInfo: {
            nickName: '其他用户',
            avatarUrl: 'https://example.com/avatar2.png'
          }
        })
        .expect(200);

      const otherUserToken = otherUserResponse.body.data.accessToken;

      // 尝试访问第一个用户创建的鹅群
      await request(app)
        .get(`/api/v1/tenant/flocks/${flockId}`)
        .set('X-Tenant-Code', 'demo')
        .set('Authorization', `Bearer ${otherUserToken}`)
        .expect(403);
    });
  });

  describe('4. 数据隔离测试', () => {
    it('应该确保租户数据完全隔离', async () => {
      // 为demo租户创建数据
      const demoResponse = await request(app)
        .post('/api/v1/tenant/flocks')
        .set('X-Tenant-Code', 'demo')
        .set('Authorization', `Bearer ${tenantToken}`)
        .send({
          batchNumber: 'DEMO001',
          breedType: '白鹅',
          quantity: 50
        })
        .expect(201);

      // 获取demo租户的鹅群列表
      const demoFlocks = await request(app)
        .get('/api/v1/tenant/flocks')
        .set('X-Tenant-Code', 'demo')
        .set('Authorization', `Bearer ${tenantToken}`)
        .expect(200);

      // 为test租户创建token（如果存在）
      // 这里假设有test租户用于测试
      const testResponse = await request(app)
        .post('/api/v1/tenant/auth/wechat-login')
        .set('X-Tenant-Code', 'test')
        .send({
          code: 'mock_wechat_code_test',
          userInfo: {
            nickName: 'Test租户用户',
            avatarUrl: 'https://example.com/test-avatar.png'
          }
        });

      if (testResponse.status === 200) {
        const testToken = testResponse.body.data.accessToken;

        // 获取test租户的鹅群列表
        const testFlocks = await request(app)
          .get('/api/v1/tenant/flocks')
          .set('X-Tenant-Code', 'test')
          .set('Authorization', `Bearer ${testToken}`)
          .expect(200);

        // 确保两个租户的数据完全不同
        expect(demoFlocks.body.data).to.not.deep.equal(testFlocks.body.data);
      }
    });
  });

  describe('5. 功能权限测试', () => {
    it('应该根据订阅计划限制功能访问', async () => {
      // 测试AI诊断功能（需要高级版）
      const response = await request(app)
        .post('/api/v1/tenant/health/ai-diagnosis')
        .set('X-Tenant-Code', 'demo')
        .set('Authorization', `Bearer ${tenantToken}`)
        .send({
          symptoms: ['食欲不振', '精神萎靡'],
          flockId: 1
        });

      // 根据demo租户的计划决定期望结果
      if (response.status === 200) {
        expect(response.body.success).to.be.true;
      } else if (response.status === 403) {
        expect(response.body.code).to.equal('FEATURE_NOT_AVAILABLE');
      }
    });

    it('应该限制数据导出功能', async () => {
      const response = await request(app)
        .get('/api/v1/tenant/export/flocks')
        .set('X-Tenant-Code', 'demo')
        .set('Authorization', `Bearer ${tenantToken}`);

      // 根据用户角色和计划权限决定结果
      if (response.status === 200) {
        expect(response.body.success).to.be.true;
        expect(response.body.data.exportUrl).to.exist;
      } else if (response.status === 403) {
        expect(response.body.code).to.be.oneOf(['INSUFFICIENT_PERMISSIONS', 'FEATURE_NOT_AVAILABLE']);
      }
    });
  });

  describe('6. 审计日志测试', () => {
    it('应该记录重要操作的审计日志', async () => {
      // 执行一个需要记录的操作
      await request(app)
        .post('/api/v1/tenant/flocks')
        .set('X-Tenant-Code', 'demo')
        .set('Authorization', `Bearer ${tenantToken}`)
        .send({
          batchNumber: 'AUDIT001',
          breedType: '审计测试鹅',
          quantity: 25
        })
        .expect(201);

      // 等待一段时间确保审计日志写入
      await new Promise(resolve => setTimeout(resolve, 100));

      // 验证审计日志是否记录（这需要访问SAAS管理后台或数据库）
      // 这里只是示例，实际实现需要查询operation_logs表
      const saasDb = tenantDatabaseManager.getSaasConnection();
      const [logs] = await saasDb.query(
        'SELECT * FROM operation_logs WHERE action = ? AND userId = ? ORDER BY createdAt DESC LIMIT 1',
        { replacements: ['create_flock', testUser.id] }
      );

      expect(logs.length).to.be.greaterThan(0);
      expect(logs[0].success).to.be.true;
    });
  });

  describe('7. 错误处理测试', () => {
    it('应该正确处理无效的认证token', async () => {
      await request(app)
        .get('/api/v1/tenant/flocks')
        .set('X-Tenant-Code', 'demo')
        .set('Authorization', 'Bearer invalid_token')
        .expect(401);
    });

    it('应该正确处理缺失的租户信息', async () => {
      await request(app)
        .get('/api/v1/tenant/flocks')
        .set('Authorization', `Bearer ${tenantToken}`)
        .expect(400); // 或者根据实际实现返回适当的状态码
    });

    it('应该正确处理数据库连接错误', async () => {
      // 这个测试需要模拟数据库连接失败的情况
      // 实际实现取决于如何模拟数据库错误
    });
  });
});

/**
 * 设置测试环境
 */
async function setupTestEnvironment() {
  try {
    // 初始化SAAS数据库连接
    await tenantDatabaseManager.initSaasConnection();

    // 创建测试租户（如果不存在）
    const saasDb = tenantDatabaseManager.getSaasConnection();
    
    // 检查demo租户是否存在
    const [existingTenants] = await saasDb.query(
      'SELECT id FROM tenants WHERE code = ?',
      { replacements: ['demo'] }
    );

    if (existingTenants.length === 0) {
      // 创建demo测试租户
      const [result] = await saasDb.query(
        `INSERT INTO tenants (name, code, status, plan, maxUsers, maxFlocks, contactEmail, createdAt, updatedAt)
         VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        { 
          replacements: [
            'Demo农场', 'demo', 'active', 'premium', 
            100, 50, '<EMAIL>'
          ] 
        }
      );
      demoTenantId = result.insertId;
    } else {
      demoTenantId = existingTenants[0].id;
    }

    // 初始化租户数据库
    await tenantDatabaseManager.initTenantDatabase('demo');
    
  } catch (error) {
    console.error('设置测试环境失败:', error);
    throw error;
  }
}

/**
 * 清理测试环境
 */
async function cleanupTestEnvironment() {
  try {
    // 清理测试数据
    const tenantDb = tenantDatabaseManager.getTenantConnection('demo');
    if (tenantDb) {
      await tenantDb.query('DELETE FROM flocks WHERE batchNumber LIKE "TEST%" OR batchNumber LIKE "DEMO%" OR batchNumber LIKE "AUDIT%"');
      await tenantDb.query('DELETE FROM users WHERE nickname LIKE "%测试%" OR nickname LIKE "%Test%"');
    }

    // 关闭数据库连接
    await tenantDatabaseManager.closeAllConnections();
    
  } catch (error) {
    console.error('清理测试环境失败:', error);
  }
}

module.exports = {
  setupTestEnvironment,
  cleanupTestEnvironment
};