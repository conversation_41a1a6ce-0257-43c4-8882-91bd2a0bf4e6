/**
 * 首页迁移示例脚本
 * 这个脚本展示如何将 pages/home/<USER>
 */

const fs = require('fs');
const path = require('path');

// 读取当前首页文件
const homePagePath = 'pages/home/<USER>';
const backupPath = 'pages/home/<USER>';

console.log('🏠 开始迁移首页到新架构...');

// 1. 创建备份
if (fs.existsSync(homePagePath)) {
    fs.copyFileSync(homePagePath, backupPath);
    console.log('✅ 已创建备份文件:', backupPath);
}

// 2. 生成迁移后的代码示例
const migratedCode = `
// pages/home/<USER>
const { createPage } = require('../../utils/page-mixin');
const { get } = require('../../utils/request-unified');
const { IMAGES, UI, BUSINESS, API } = require('../../constants/index.js');
const { performanceMixin, dataCacheManager, debounce } = require('../../utils/performance.js');

/**
 * 主页页面 - 智慧养鹅小程序核心页面
 * 使用新的统一架构，自动处理错误、loading和生命周期
 */

createPage({
  data: {
    userInfo: {
      inventoryCount: 0,
      healthRate: '0%',
      environmentStatus: '优',
      avatar: IMAGES.DEFAULTS.AVATAR,
      name: '养殖户',
      farmName: '未设置养殖场'
    },
    healthData: {
      healthyCount: 1120,
      sickCount: 65,
      deathCount: 15,
      statusText: '整体健康',
      statusDesc: '鹅群状态良好，无异常'
    },
    tasks: [],
    announcements: [],
    knowledgeList: [],
    unreadCount: 0,
    refreshing: false,
    lastRefreshTime: 0
  },

  /**
   * 页面初始化 - 替代onLoad
   * 自动获得错误处理和loading管理
   */
  async initPage(options) {
    // 页面加载开始
    this.pageStartTime = Date.now();
    
    // 并发加载所有数据
    await this.loadAllData();
    
    // 设置自动刷新
    this.setupAutoRefresh();
  },

  /**
   * 加载所有首页数据
   */
  async loadAllData() {
    try {
      // 使用Promise.allSettled并发请求，自动处理loading
      const [userResult, healthResult, tasksResult, announcementsResult, knowledgeResult] = 
        await Promise.allSettled([
          get('/api/user/info'),
          get('/api/health/summary'),
          get('/api/tasks/pending'),
          get('/api/announcements/latest'),
          get('/api/knowledge/featured')
        ]);

      // 处理用户信息
      if (userResult.status === 'fulfilled') {
        this.setData({
          userInfo: {
            ...this.data.userInfo,
            ...userResult.value.data
          }
        });
      }

      // 处理健康数据
      if (healthResult.status === 'fulfilled') {
        this.setData({
          healthData: healthResult.value.data
        });
      }

      // 处理任务数据
      if (tasksResult.status === 'fulfilled') {
        this.setData({
          tasks: tasksResult.value.data || []
        });
      }

      // 处理公告数据
      if (announcementsResult.status === 'fulfilled') {
        this.setData({
          announcements: announcementsResult.value.data || []
        });
      }

      // 处理知识库数据
      if (knowledgeResult.status === 'fulfilled') {
        this.setData({
          knowledgeList: knowledgeResult.value.data || []
        });
      }

    } catch (error) {
      // 错误已由统一系统处理
      // 这里可以设置默认数据
      this.useDefaultData();
    }
  },

  /**
   * 使用默认数据
   */
  useDefaultData() {
    this.setData({
      tasks: [
        {
          id: 'default-1',
          title: '日常巡检',
          description: '检查鹅群健康状况',
          priority: 'high',
          deadline: '今天 18:00'
        }
      ],
      announcements: [
        {
          id: 'default-1',
          title: '系统维护通知',
          content: '系统将于今晚进行例行维护',
          publishTime: '2024-12-03'
        }
      ]
    });
  },

  /**
   * 设置自动刷新
   */
  setupAutoRefresh() {
    // 每5分钟刷新一次数据
    this.addInterval(() => {
      if (!this.data.refreshing) {
        this.loadAllData();
      }
    }, 5 * 60 * 1000);
  },

  /**
   * 页面显示时触发 - 替代onShow
   */
  onPageShow() {
    // 检查是否需要刷新数据
    const now = Date.now();
    if (now - this.data.lastRefreshTime > 3 * 60 * 1000) { // 3分钟
      this.loadAllData();
      this.setData({ lastRefreshTime: now });
    }
  },

  /**
   * 下拉刷新
   */
  async refreshPage() {
    if (this.data.refreshing) return;
    
    this.setData({ refreshing: true });
    
    try {
      await this.loadAllData();
      this.showSuccess('刷新成功');
    } catch (error) {
      // 错误已统一处理
    } finally {
      this.setData({ 
        refreshing: false,
        lastRefreshTime: Date.now()
      });
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 任务完成处理
   */
  async completeTask(event) {
    const { taskId } = event.currentTarget.dataset;
    
    try {
      await post('/api/tasks/complete', { taskId });
      
      // 更新本地数据
      const tasks = this.data.tasks.filter(task => task.id !== taskId);
      this.setData({ tasks });
      
      this.showSuccess('任务已完成');
    } catch (error) {
      // 错误已统一处理
    }
  },

  /**
   * 跳转到任务详情
   */
  goToTaskDetail(event) {
    const { taskId } = event.currentTarget.dataset;
    this.navigateTo('/pages/task/detail/detail', { id: taskId });
  },

  /**
   * 跳转到公告详情
   */
  goToAnnouncementDetail(event) {
    const { announcementId } = event.currentTarget.dataset;
    this.navigateTo('/pages/announcement/announcement-detail/announcement-detail', { 
      id: announcementId 
    });
  },

  /**
   * 页面卸载清理
   */
  onPageUnload() {
    // 清理资源（由page-mixin自动处理）
  }
});
`;

console.log('📝 生成了迁移后的代码示例');
console.log('💡 提示:');
console.log('1. 查看生成的代码示例了解迁移模式');
console.log('2. 手动应用到实际页面文件中');
console.log('3. 测试功能是否正常');
console.log('4. 逐步迁移其他页面');

// 保存示例代码到文件
fs.writeFileSync('pages/home/<USER>', migratedCode);
console.log('✅ 迁移示例已保存到: pages/home/<USER>');

console.log('\n🎯 下一步:');
console.log('1. 比较原文件和示例文件的差异');
console.log('2. 手动应用迁移（保持现有业务逻辑）');
console.log('3. 测试页面功能');
console.log('4. 继续迁移其他重要页面');