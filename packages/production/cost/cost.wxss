/* packages/production/cost/cost.wxss */
@import '/styles/unified-design-tokens.wxss';

.cost-container {
  background-color: var(--bg-color-page);
  min-height: 100vh;
  padding-bottom: var(--spacer-16);
}

/* 页面头部 */
.cost-header {
  background: linear-gradient(135deg, var(--production-theme-color) 0%, var(--warning-color) 100%);
  border-radius: var(--radius-xl);
  margin: var(--page-padding);
  margin-bottom: var(--section-margin);
  box-shadow: var(--shadow-m);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacer-3);
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-round);
  transition: all var(--transition-normal);
}

.action-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.9);
}

/* 成本概览 */
.cost-overview {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-5);
  padding-bottom: var(--spacer-4);
  border-bottom: 1rpx solid var(--border-color);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
}

.period-picker {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  padding: var(--spacer-2) var(--spacer-4);
  background: var(--bg-color-hover);
  border-radius: var(--radius-m);
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.cost-summary {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-4);
}

.summary-card.main {
  background: linear-gradient(135deg, var(--production-theme-color) 0%, var(--warning-color) 100%);
  color: white;
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  text-align: center;
}

.summary-card.main .card-label {
  font-size: var(--font-size-s);
  opacity: 0.9;
  margin-bottom: var(--spacer-2);
}

.summary-card.main .card-value {
  font-size: var(--font-size-huge);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacer-3);
}

.card-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacer-1);
  font-size: var(--font-size-s);
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacer-4);
}

.summary-card {
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  text-align: center;
  border: 1rpx solid var(--border-color);
}

.card-label {
  display: block;
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  margin-bottom: var(--spacer-2);
}

.card-value {
  display: block;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--production-theme-color);
  margin-bottom: var(--spacer-1);
}

.card-percentage {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

/* 成本构成 */
.cost-breakdown {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-5);
  padding-bottom: var(--spacer-4);
  border-bottom: 1rpx solid var(--border-color);
}

.view-toggle {
  display: flex;
  background: var(--bg-color-page);
  border-radius: var(--radius-m);
  padding: var(--spacer-1);
}

.toggle-btn {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-s);
  transition: all var(--transition-normal);
}

.toggle-btn.active {
  background: var(--bg-color-container);
  box-shadow: var(--shadow-s);
}

/* 饼图 */
.pie-chart {
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  border: 1rpx solid var(--border-color);
}

.chart-container {
  display: flex;
  align-items: center;
  gap: var(--spacer-8);
}

.pie-circle {
  width: 300rpx;
  height: 300rpx;
  position: relative;
  border-radius: var(--radius-round);
  overflow: hidden;
}

.pie-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  background: var(--bg-color-container);
  border-radius: var(--radius-round);
  width: 120rpx;
  height: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-s);
}

.center-value {
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-bold);
  color: var(--production-theme-color);
  margin-bottom: var(--spacer-1);
}

.center-label {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

.pie-legend {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacer-3);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-3);
  padding: var(--spacer-3);
  background: var(--bg-color-container);
  border-radius: var(--radius-m);
  border: 1rpx solid var(--border-color);
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: var(--radius-s);
  flex-shrink: 0;
}

.legend-label {
  flex: 1;
  font-size: var(--font-size-s);
  color: var(--text-color-primary);
}

.legend-value {
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-bold);
  color: var(--production-theme-color);
  margin-right: var(--spacer-2);
}

.legend-percent {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

/* 柱图 */
.bar-chart {
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  border: 1rpx solid var(--border-color);
}

.chart-y-axis {
  width: 80rpx;
  display: flex;
  flex-direction: column-reverse;
  justify-content: space-between;
  padding-right: var(--spacer-3);
  border-right: 2rpx solid var(--border-color);
}

.y-label {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
  text-align: right;
}

.chart-area {
  flex: 1;
  display: flex;
  align-items: end;
  justify-content: space-around;
  padding: 0 var(--spacer-3);
}

.bar-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80rpx;
}

.bar {
  width: 100%;
  border-radius: var(--radius-s) var(--radius-s) 0 0;
  margin-bottom: var(--spacer-2);
  transition: height var(--transition-slow);
}

.bar-value {
  font-size: var(--font-size-xs);
  color: var(--production-theme-color);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacer-1);
}

.bar-label {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
  text-align: center;
}

/* 成本明细 */
.cost-details {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.detail-filters {
  display: flex;
  gap: var(--spacer-3);
}

.filter-picker {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  padding: var(--spacer-2) var(--spacer-3);
  background: var(--bg-color-hover);
  border-radius: var(--radius-m);
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.details-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-3);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-4);
  padding: var(--spacer-4);
  background: var(--bg-color-page);
  border-radius: var(--radius-m);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
}

.detail-item:active {
  background: var(--bg-color-hover);
}

.detail-icon {
  width: 64rpx;
  height: 64rpx;
  background: var(--production-theme-bg);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacer-1);
}

.detail-title {
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
}

.detail-category {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.detail-date {
  font-size: var(--font-size-s);
  color: var(--text-color-tertiary);
}

.detail-amount {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacer-1);
}

.amount-value {
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-bold);
  color: var(--production-theme-color);
}

.recurring-badge {
  display: flex;
  align-items: center;
  gap: var(--spacer-1);
  padding: var(--spacer-1) var(--spacer-2);
  background: var(--info-color-focus);
  border-radius: var(--radius-s);
  font-size: var(--font-size-xs);
  color: var(--info-color);
}

.load-more {
  text-align: center;
  padding: var(--spacer-4);
  color: var(--production-theme-color);
  font-size: var(--font-size-m);
  border-top: 1rpx solid var(--border-color);
  margin-top: var(--spacer-4);
}

/* 预算对比 */
.budget-comparison {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.budget-status {
  padding: var(--spacer-1) var(--spacer-3);
  border-radius: var(--radius-s);
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-medium);
}

.budget-status.normal {
  background: var(--success-color-focus);
  color: var(--success-color);
}

.budget-status.warning {
  background: var(--warning-color-focus);
  color: var(--warning-color);
}

.budget-status.exceeded {
  background: var(--error-color-focus);
  color: var(--error-color);
}

.budget-progress {
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  border: 1rpx solid var(--border-color);
  margin-bottom: var(--spacer-5);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-3);
}

.progress-label {
  font-size: var(--font-size-m);
  color: var(--text-color-primary);
  font-weight: var(--font-weight-medium);
}

.progress-value {
  font-size: var(--font-size-m);
  color: var(--production-theme-color);
  font-weight: var(--font-weight-bold);
}

.progress-bar {
  height: 16rpx;
  background: var(--border-color);
  border-radius: var(--radius-s);
  overflow: hidden;
  margin-bottom: var(--spacer-3);
}

.progress-fill {
  height: 100%;
  border-radius: var(--radius-s);
  transition: width var(--transition-slow);
}

.progress-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.remaining-budget {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.budget-percentage {
  font-size: var(--font-size-s);
  color: var(--production-theme-color);
  font-weight: var(--font-weight-bold);
}

.budget-categories {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-4);
}

.budget-category {
  background: var(--bg-color-page);
  border-radius: var(--radius-m);
  padding: var(--spacer-4);
  border: 1rpx solid var(--border-color);
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-2);
}

.category-name {
  font-size: var(--font-size-s);
  color: var(--text-color-primary);
  font-weight: var(--font-weight-medium);
}

.category-ratio {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.category-progress {
  display: flex;
  align-items: center;
  gap: var(--spacer-3);
}

.category-progress-bar {
  flex: 1;
  height: 8rpx;
  background: var(--border-color);
  border-radius: var(--radius-s);
  overflow: hidden;
}

.category-progress-fill {
  height: 100%;
  border-radius: var(--radius-s);
  transition: width var(--transition-slow);
}

.category-percentage {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
  min-width: 60rpx;
  text-align: right;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacer-16) var(--spacer-6);
  text-align: center;
}

.empty-icon {
  margin-bottom: var(--spacer-6);
  opacity: 0.6;
}

.empty-title {
  font-size: var(--font-size-xl);
  color: var(--text-color-primary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacer-2);
}

.empty-subtitle {
  font-size: var(--font-size-m);
  color: var(--text-color-secondary);
  margin-bottom: var(--spacer-6);
  line-height: var(--line-height-normal);
}

.empty-action {
  background: var(--production-theme-color);
  color: white;
  border: none;
  border-radius: var(--radius-l);
  padding: var(--spacer-4) var(--spacer-8);
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
}

/* 响应式优化 */
@media (max-width: 350px) {
  .summary-cards {
    grid-template-columns: 1fr;
  }
  
  .chart-container {
    flex-direction: column;
    gap: var(--spacer-6);
  }
  
  .pie-circle {
    width: 250rpx;
    height: 250rpx;
  }
}