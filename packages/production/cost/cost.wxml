<!--packages/production/cost/cost.wxml-->
<view class="cost-container theme-production">
  
  <!-- 页面头部 -->
  <global-page-header 
    title="成本分析" 
    subtitle="精准核算，降本增效"
    theme="production"
    show-back="{{true}}"
    custom-class="cost-header">
    <view slot="right" class="header-actions">
      <view class="action-btn" bindtap="exportReport">
        <global-icon name="导出" size="20" color="white"></global-icon>
      </view>
      <view class="action-btn" bindtap="addCost">
        <global-icon name="加号" size="20" color="white"></global-icon>
      </view>
    </view>
  </global-page-header>

  <!-- 成本概览 -->
  <view class="cost-overview">
    <view class="overview-header">
      <text class="section-title">成本概览</text>
      <picker bindchange="onPeriodChange" value="{{periodIndex}}" range="{{periods}}">
        <view class="period-picker">
          <text>{{periods[periodIndex]}}</text>
          <global-icon name="箭头下" size="14" color="var(--text-color-tertiary)"></global-icon>
        </view>
      </picker>
    </view>
    
    <view class="cost-summary">
      <view class="summary-card main">
        <text class="card-label">总成本</text>
        <text class="card-value">¥{{totalCost}}</text>
        <view class="card-trend {{totalTrend}}">
          <global-icon name="{{totalTrendIcon}}" size="12" color="{{totalTrendColor}}"></global-icon>
          <text>{{totalTrendText}}</text>
        </view>
      </view>
      
      <view class="summary-cards">
        <view wx:for="{{costSummary}}" wx:key="type" class="summary-card">
          <text class="card-label">{{item.label}}</text>
          <text class="card-value">¥{{item.value}}</text>
          <text class="card-percentage">{{item.percentage}}%</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 成本构成 -->
  <view class="cost-breakdown">
    <view class="section-header">
      <text class="section-title">成本构成</text>
      <view class="view-toggle">
        <view class="toggle-btn {{chartType === 'pie' ? 'active' : ''}}" 
              bindtap="switchChart" 
              data-type="pie">
          <global-icon name="饼图" size="16" color="{{chartType === 'pie' ? 'var(--production-theme-color)' : 'var(--text-color-tertiary)'}}"></global-icon>
        </view>
        <view class="toggle-btn {{chartType === 'bar' ? 'active' : ''}}" 
              bindtap="switchChart" 
              data-type="bar">
          <global-icon name="柱图" size="16" color="{{chartType === 'bar' ? 'var(--production-theme-color)' : 'var(--text-color-tertiary)'}}"></global-icon>
        </view>
      </view>
    </view>
    
    <!-- 饼图视图 -->
    <view wx:if="{{chartType === 'pie'}}" class="pie-chart">
      <view class="chart-container">
        <view class="pie-circle">
          <view wx:for="{{pieData}}" wx:key="category" 
                class="pie-segment" 
                style="--start-angle: {{item.startAngle}}deg; --end-angle: {{item.endAngle}}deg; --color: {{item.color}};">
          </view>
          <view class="pie-center">
            <text class="center-value">¥{{totalCost}}</text>
            <text class="center-label">总成本</text>
          </view>
        </view>
        <view class="pie-legend">
          <view wx:for="{{pieData}}" wx:key="category" class="legend-item">
            <view class="legend-color" style="background: {{item.color}};"></view>
            <text class="legend-label">{{item.label}}</text>
            <text class="legend-value">¥{{item.value}}</text>
            <text class="legend-percent">{{item.percentage}}%</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 柱图视图 -->
    <view wx:if="{{chartType === 'bar'}}" class="bar-chart">
      <view class="chart-container">
        <view class="chart-y-axis">
          <view wx:for="{{yAxisLabels}}" wx:key="*this" class="y-label">
            <text>{{item}}</text>
          </view>
        </view>
        <view class="chart-area">
          <view wx:for="{{barData}}" wx:key="category" class="bar-group">
            <view class="bar" style="height: {{item.height}}%; background: {{item.color}};"></view>
            <text class="bar-value">¥{{item.value}}</text>
            <text class="bar-label">{{item.label}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 成本明细 -->
  <view class="cost-details">
    <view class="section-header">
      <text class="section-title">成本明细</text>
      <view class="detail-filters">
        <picker bindchange="onCategoryFilterChange" value="{{categoryFilterIndex}}" range="{{categoryFilters}}" range-key="label">
          <view class="filter-picker">
            <text>{{categoryFilters[categoryFilterIndex].label}}</text>
            <global-icon name="箭头下" size="12" color="var(--text-color-tertiary)"></global-icon>
          </view>
        </picker>
      </view>
    </view>
    
    <view class="details-list">
      <view wx:for="{{costDetails}}" wx:key="id" 
            class="detail-item"
            bindtap="viewCostDetail"
            data-cost="{{item}}">
        <view class="detail-icon">
          <global-icon name="{{item.categoryIcon}}" size="20" color="{{item.categoryColor}}"></global-icon>
        </view>
        <view class="detail-content">
          <text class="detail-title">{{item.title}}</text>
          <text class="detail-category">{{item.category}}</text>
          <text class="detail-date">{{item.date}}</text>
        </view>
        <view class="detail-amount">
          <text class="amount-value">¥{{item.amount}}</text>
          <view wx:if="{{item.isRecurring}}" class="recurring-badge">
            <global-icon name="循环" size="12" color="var(--info-color)"></global-icon>
            <text>定期</text>
          </view>
        </view>
      </view>
    </view>
    
    <view class="load-more" bindtap="loadMoreDetails">
      <text>加载更多明细</text>
    </view>
  </view>

  <!-- 成本趋势 -->
  <view class="cost-trends">
    <view class="section-header">
      <text class="section-title">成本趋势</text>
      <picker bindchange="onTrendTypeChange" value="{{trendTypeIndex}}" range="{{trendTypes}}" range-key="label">
        <view class="trend-picker">
          <text>{{trendTypes[trendTypeIndex].label}}</text>
          <global-icon name="箭头下" size="12" color="var(--text-color-tertiary)"></global-icon>
        </view>
      </picker>
    </view>
    
    <view class="trend-chart">
      <view class="chart-container">
        <view class="chart-y-axis">
          <view wx:for="{{trendYLabels}}" wx:key="*this" class="y-label">
            <text>{{item}}</text>
          </view>
        </view>
        <view class="chart-area">
          <view class="trend-line">
            <view wx:for="{{trendData}}" wx:key="period" 
                  class="trend-point" 
                  style="left: {{item.x}}%; bottom: {{item.y}}%;"
                  bindtap="showTrendDetail"
                  data-period="{{item.period}}"
                  data-value="{{item.value}}">
              <view class="point-dot"></view>
              <view class="point-tooltip">
                <text>{{item.period}}</text>
                <text>¥{{item.value}}</text>
              </view>
            </view>
          </view>
          <view class="trend-x-axis">
            <view wx:for="{{trendXLabels}}" wx:key="*this" class="x-label">
              <text>{{item}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 预算对比 -->
  <view class="budget-comparison">
    <view class="section-header">
      <text class="section-title">预算对比</text>
      <text class="budget-status {{budgetStatus}}">{{budgetStatusText}}</text>
    </view>
    
    <view class="budget-progress">
      <view class="progress-header">
        <text class="progress-label">本期预算执行</text>
        <text class="progress-value">{{budgetUsed}}/{{budgetTotal}}</text>
      </view>
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{budgetPercentage}}%; background: {{budgetColor}};"></view>
      </view>
      <view class="progress-footer">
        <text class="remaining-budget">剩余预算: ¥{{remainingBudget}}</text>
        <text class="budget-percentage">{{budgetPercentage}}%</text>
      </view>
    </view>
    
    <view class="budget-categories">
      <view wx:for="{{budgetCategories}}" wx:key="category" class="budget-category">
        <view class="category-header">
          <text class="category-name">{{item.name}}</text>
          <text class="category-ratio">{{item.used}}/{{item.budget}}</text>
        </view>
        <view class="category-progress">
          <view class="category-progress-bar">
            <view class="category-progress-fill" 
                  style="width: {{item.percentage}}%; background: {{item.color}};"></view>
          </view>
          <text class="category-percentage">{{item.percentage}}%</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{showEmptyState}}" class="empty-state">
    <view class="empty-icon">
      <global-icon name="成本空" size="80" color="var(--text-color-tertiary)"></global-icon>
    </view>
    <text class="empty-title">暂无成本数据</text>
    <text class="empty-subtitle">开始记录您的第一笔成本</text>
    <button class="empty-action" bindtap="addCost">
      <text>添加成本</text>
    </button>
  </view>

</view>