/* packages/production/planning/planning.wxss */
@import '/styles/unified-design-tokens.wxss';

.planning-container {
  background-color: var(--bg-color-page);
  min-height: 100vh;
  padding-bottom: 140rpx; /* 为浮动按钮留空间 */
}

/* 页面头部 */
.planning-header {
  background: linear-gradient(135deg, var(--production-theme-color) 0%, var(--warning-color-light) 100%);
  border-radius: var(--radius-xl);
  margin: var(--page-padding);
  margin-bottom: var(--section-margin);
  box-shadow: var(--shadow-m);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacer-3);
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-round);
  transition: all var(--transition-normal);
}

.action-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.9);
}

/* 计划统计概览 */
.planning-stats {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-5);
  padding-bottom: var(--spacer-4);
  border-bottom: 1rpx solid var(--border-color);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
}

.time-picker {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  padding: var(--spacer-2) var(--spacer-4);
  background: var(--bg-color-hover);
  border-radius: var(--radius-m);
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacer-4);
}

.stat-card {
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.stat-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-m);
}

.stat-card.pending {
  border-left: 6rpx solid var(--text-color-tertiary);
}

.stat-card.in-progress {
  border-left: 6rpx solid var(--production-theme-color);
}

.stat-card.completed {
  border-left: 6rpx solid var(--success-color);
}

.stat-card.overdue {
  border-left: 6rpx solid var(--error-color);
}

.stat-icon {
  width: 72rpx;
  height: 72rpx;
  background: var(--production-theme-bg);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacer-3);
}

.stat-content {
  margin-bottom: var(--spacer-3);
}

.stat-number {
  display: block;
  font-size: var(--font-size-xxxl);
  font-weight: var(--font-weight-bold);
  color: var(--production-theme-color);
  line-height: 1;
  margin-bottom: var(--spacer-1);
}

.stat-label {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  font-weight: var(--font-weight-medium);
}

.stat-progress {
  position: absolute;
  top: var(--spacer-3);
  right: var(--spacer-3);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-ring {
  width: 100%;
  height: 100%;
  border-radius: var(--radius-round);
  position: absolute;
}

.progress-text {
  font-size: var(--font-size-xs);
  color: var(--text-color-secondary);
  font-weight: var(--font-weight-bold);
  z-index: 2;
}

/* 视图切换器 */
.view-switcher {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-4) var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switcher-tabs {
  display: flex;
  background: var(--bg-color-page);
  border-radius: var(--radius-m);
  padding: var(--spacer-1);
}

.tab-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  padding: var(--spacer-2) var(--spacer-4);
  border-radius: var(--radius-s);
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  transition: all var(--transition-normal);
}

.tab-item.active {
  background: var(--bg-color-container);
  color: var(--production-theme-color);
  font-weight: var(--font-weight-medium);
  box-shadow: var(--shadow-s);
}

.date-navigation {
  display: flex;
  align-items: center;
  gap: var(--spacer-3);
}

.nav-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-color-hover);
  border-radius: var(--radius-round);
  transition: all var(--transition-normal);
}

.nav-btn:active {
  background: var(--border-color);
  transform: scale(0.9);
}

.current-period {
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  min-width: 200rpx;
  text-align: center;
}

/* 甘特图视图 */
.gantt-view {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
  overflow: hidden;
}

.gantt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacer-6);
  border-bottom: 1rpx solid var(--border-color);
}

.gantt-controls {
  display: flex;
  gap: var(--spacer-2);
}

.control-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-color-hover);
  border-radius: var(--radius-round);
  transition: all var(--transition-normal);
}

.control-btn:active {
  background: var(--border-color);
}

.gantt-scroll {
  width: 100%;
  height: 600rpx;
}

.gantt-chart {
  min-width: 1000rpx;
  background: var(--bg-color-page);
}

.time-axis {
  display: flex;
  height: 80rpx;
  background: var(--bg-color-container);
  border-bottom: 2rpx solid var(--border-color);
}

.time-label-placeholder {
  width: 200rpx;
  flex-shrink: 0;
}

.time-label {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1rpx solid var(--border-color);
  font-size: var(--font-size-xs);
  color: var(--text-color-secondary);
  font-weight: var(--font-weight-medium);
}

.gantt-row {
  display: flex;
  min-height: 100rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.task-label {
  width: 200rpx;
  flex-shrink: 0;
  padding: var(--spacer-4);
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: var(--bg-color-container);
  border-right: 2rpx solid var(--border-color);
}

.task-name {
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-1);
  line-height: var(--line-height-normal);
}

.task-duration {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

.task-timeline {
  flex: 1;
  position: relative;
  padding: var(--spacer-4) 0;
}

.task-bar {
  position: absolute;
  height: 32rpx;
  border-radius: var(--radius-s);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
  cursor: pointer;
}

.task-bar.pending {
  background: var(--text-color-tertiary);
}

.task-bar.in-progress {
  background: var(--production-theme-color);
}

.task-bar.completed {
  background: var(--success-color);
}

.task-bar.overdue {
  background: var(--error-color);
}

.task-bar:active {
  transform: scale(1.05);
  box-shadow: var(--shadow-s);
}

.task-progress {
  font-size: var(--font-size-xs);
  color: white;
  font-weight: var(--font-weight-bold);
}

/* 列表视图 */
.list-view {
  margin: 0 var(--page-padding) var(--section-margin);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--bg-color-container);
  border-radius: var(--radius-l) var(--radius-l) 0 0;
  padding: var(--spacer-6);
  border: 1rpx solid var(--border-color);
  border-bottom: none;
}

.sort-picker {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  padding: var(--spacer-2) var(--spacer-4);
  background: var(--bg-color-hover);
  border-radius: var(--radius-m);
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.plans-list {
  background: var(--bg-color-container);
  border-radius: 0 0 var(--radius-l) var(--radius-l);
  border: 1rpx solid var(--border-color);
  border-top: none;
  overflow: hidden;
}

.plan-item {
  padding: var(--spacer-6);
  border-bottom: 1rpx solid var(--border-color);
  position: relative;
  transition: all var(--transition-normal);
}

.plan-item:last-child {
  border-bottom: none;
}

.plan-item:active {
  background: var(--bg-color-hover);
}

.plan-item.pending {
  border-left: 6rpx solid var(--text-color-tertiary);
}

.plan-item.in-progress {
  border-left: 6rpx solid var(--production-theme-color);
}

.plan-item.completed {
  border-left: 6rpx solid var(--success-color);
}

.plan-item.overdue {
  border-left: 6rpx solid var(--error-color);
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-3);
}

.plan-priority {
  width: 48rpx;
  height: 48rpx;
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
}

.plan-priority.high {
  background: var(--error-color-focus);
}

.plan-priority.medium {
  background: var(--warning-color-focus);
}

.plan-priority.low {
  background: var(--info-color-focus);
}

.plan-status {
  padding: var(--spacer-1) var(--spacer-3);
  border-radius: var(--radius-s);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.plan-status.pending {
  background: var(--text-color-quaternary);
  color: var(--text-color-secondary);
}

.plan-status.in-progress {
  background: var(--production-theme-bg);
  color: var(--production-theme-color);
}

.plan-status.completed {
  background: var(--success-color-focus);
  color: var(--success-color);
}

.plan-status.overdue {
  background: var(--error-color-focus);
  color: var(--error-color);
}

.plan-menu {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-color-hover);
  border-radius: var(--radius-round);
}

.plan-title {
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-2);
  line-height: var(--line-height-normal);
}

.plan-description {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacer-4);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.plan-timeline {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-4);
  padding: var(--spacer-3);
  background: var(--bg-color-page);
  border-radius: var(--radius-m);
}

.timeline-info {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.timeline-duration {
  font-size: var(--font-size-s);
  color: var(--production-theme-color);
  font-weight: var(--font-weight-medium);
}

.plan-progress {
  margin-bottom: var(--spacer-4);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-2);
}

.progress-label {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.progress-value {
  font-size: var(--font-size-s);
  color: var(--production-theme-color);
  font-weight: var(--font-weight-bold);
}

.progress-bar {
  height: 8rpx;
  background: var(--border-color);
  border-radius: var(--radius-s);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: var(--radius-s);
  transition: width var(--transition-slow);
}

.plan-meta {
  display: flex;
  gap: var(--spacer-4);
  margin-bottom: var(--spacer-4);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-1);
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

.plan-alerts {
  margin-bottom: var(--spacer-4);
}

.alert-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  padding: var(--spacer-2) var(--spacer-3);
  border-radius: var(--radius-s);
  font-size: var(--font-size-xs);
  margin-bottom: var(--spacer-2);
}

.alert-item:last-child {
  margin-bottom: 0;
}

.alert-item.warning {
  background: var(--warning-color-focus);
  color: var(--warning-color);
}

.alert-item.error {
  background: var(--error-color-focus);
  color: var(--error-color);
}

.plan-actions {
  display: flex;
  gap: var(--spacer-3);
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  min-width: 160rpx;
  height: 60rpx;
  border-radius: var(--radius-m);
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-medium);
  border: none;
  transition: all var(--transition-normal);
}

.action-btn.start {
  background: var(--production-theme-color);
  color: white;
}

.action-btn.pause {
  background: var(--warning-color);
  color: white;
}

.action-btn.complete {
  background: var(--success-color);
  color: white;
}

.action-btn.detail {
  background: var(--bg-color-hover);
  color: var(--text-color-secondary);
  border: 1rpx solid var(--border-color);
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 日历视图 */
.calendar-view {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
  overflow: hidden;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacer-6);
  border-bottom: 1rpx solid var(--border-color);
}

.calendar-controls .control-btn {
  padding: var(--spacer-2) var(--spacer-4);
  background: var(--production-theme-bg);
  color: var(--production-theme-color);
  border-radius: var(--radius-m);
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-medium);
}

.calendar-grid {
  padding: var(--spacer-4);
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--spacer-2);
  margin-bottom: var(--spacer-3);
}

.weekday {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60rpx;
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  font-weight: var(--font-weight-medium);
}

.calendar-dates {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--spacer-2);
}

.calendar-date {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--bg-color-page);
  border-radius: var(--radius-m);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
  cursor: pointer;
}

.calendar-date:active {
  transform: scale(0.95);
}

.calendar-date.today {
  background: var(--production-theme-bg);
  border-color: var(--production-theme-color);
}

.calendar-date.selected {
  background: var(--production-theme-color);
  border-color: var(--production-theme-color);
}

.calendar-date.selected .date-number {
  color: white;
}

.calendar-date.other-month {
  opacity: 0.3;
}

.date-number {
  font-size: var(--font-size-s);
  color: var(--text-color-primary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacer-1);
}

.date-plans {
  display: flex;
  gap: var(--spacer-1);
  flex-wrap: wrap;
  justify-content: center;
}

.plan-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: var(--radius-round);
}

.plan-dot.pending {
  background: var(--text-color-tertiary);
}

.plan-dot.in-progress {
  background: var(--production-theme-color);
}

.plan-dot.completed {
  background: var(--success-color);
}

.plan-dot.overdue {
  background: var(--error-color);
}

/* 选中日期计划 */
.selected-date-plans {
  padding: var(--spacer-6);
  border-top: 1rpx solid var(--border-color);
  background: var(--bg-color-page);
}

.selected-date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-4);
}

.selected-date {
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
}

.plans-count {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.selected-plans-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-3);
}

.selected-plan-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-3);
  padding: var(--spacer-4);
  background: var(--bg-color-container);
  border-radius: var(--radius-m);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
}

.selected-plan-item:active {
  background: var(--bg-color-hover);
}

.plan-time {
  font-size: var(--font-size-xs);
  color: var(--production-theme-color);
  font-weight: var(--font-weight-medium);
  min-width: 120rpx;
}

.plan-info {
  flex: 1;
}

.plan-name {
  font-size: var(--font-size-s);
  color: var(--text-color-primary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacer-1);
}

.plan-location {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

.plan-status-dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: var(--radius-round);
}

.plan-status-dot.pending {
  background: var(--text-color-tertiary);
}

.plan-status-dot.in-progress {
  background: var(--production-theme-color);
}

.plan-status-dot.completed {
  background: var(--success-color);
}

.plan-status-dot.overdue {
  background: var(--error-color);
}

/* 浮动添加按钮 */
.fab-add {
  position: fixed;
  bottom: var(--spacer-8);
  right: var(--spacer-8);
  width: 112rpx;
  height: 112rpx;
  background: var(--production-theme-color);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-l);
  z-index: var(--z-index-fab);
  transition: all var(--transition-normal);
}

.fab-add:active {
  transform: scale(0.9);
}

/* 筛选弹窗 */
.filter-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: flex-end;
  z-index: var(--z-index-modal);
}

.filter-modal {
  width: 100%;
  background: var(--bg-color-container);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  padding: var(--spacer-6);
  max-height: 80vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-6);
  padding-bottom: var(--spacer-4);
  border-bottom: 1rpx solid var(--border-color);
}

.modal-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-color-hover);
  border-radius: var(--radius-round);
}

.filter-content {
  margin-bottom: var(--spacer-6);
}

.filter-group {
  margin-bottom: var(--spacer-6);
}

.filter-label {
  display: block;
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-3);
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacer-3);
}

.filter-option {
  padding: var(--spacer-2) var(--spacer-4);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-m);
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  background: var(--bg-color-page);
  transition: all var(--transition-normal);
}

.filter-option.selected {
  border-color: var(--production-theme-color);
  background: var(--production-theme-color);
  color: white;
}

.filter-actions {
  display: flex;
  gap: var(--spacer-4);
  padding-top: var(--spacer-4);
  border-top: 1rpx solid var(--border-color);
}

.filter-btn {
  flex: 1;
  height: 80rpx;
  border-radius: var(--radius-l);
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  border: none;
  transition: all var(--transition-normal);
}

.filter-btn.reset {
  background: var(--bg-color-hover);
  color: var(--text-color-secondary);
  border: 1rpx solid var(--border-color);
}

.filter-btn.apply {
  background: var(--production-theme-color);
  color: white;
}

.filter-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: var(--spacer-6);
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  margin: var(--spacer-4) var(--page-padding);
  border: 1rpx solid var(--border-color);
  font-size: var(--font-size-m);
  color: var(--production-theme-color);
  transition: all var(--transition-normal);
}

.load-more:active {
  background: var(--production-theme-bg);
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacer-2);
}

.loading-spinner-small {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid var(--border-color);
  border-top: 3rpx solid var(--production-theme-color);
  border-radius: var(--radius-round);
  animation: spin 1s linear infinite;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
}

.loading-content {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-8);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacer-4);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-color);
  border-top: 4rpx solid var(--production-theme-color);
  border-radius: var(--radius-round);
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: var(--font-size-m);
  color: var(--text-color-tertiary);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacer-16) var(--spacer-6);
  text-align: center;
}

.empty-icon {
  margin-bottom: var(--spacer-6);
  opacity: 0.6;
}

.empty-title {
  font-size: var(--font-size-xl);
  color: var(--text-color-primary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacer-2);
}

.empty-subtitle {
  font-size: var(--font-size-m);
  color: var(--text-color-secondary);
  margin-bottom: var(--spacer-6);
  line-height: var(--line-height-normal);
}

.empty-action {
  background: var(--production-theme-color);
  color: white;
  border: none;
  border-radius: var(--radius-l);
  padding: var(--spacer-4) var(--spacer-8);
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
}

/* 响应式优化 */
@media (max-width: 350px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }
  
  .plan-actions {
    flex-direction: column;
  }
  
  .action-btn {
    min-width: 100%;
  }
  
  .view-switcher {
    flex-direction: column;
    gap: var(--spacer-4);
  }
  
  .calendar-dates {
    gap: var(--spacer-1);
  }
}

/* 安全区域适配 */
.fab-add {
  bottom: calc(var(--spacer-8) + env(safe-area-inset-bottom));
}