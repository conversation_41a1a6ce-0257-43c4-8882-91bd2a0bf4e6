<!--packages/production/planning/planning.wxml-->
<view class="planning-container theme-production">
  
  <!-- 页面头部 -->
  <global-page-header 
    title="生产计划" 
    subtitle="科学规划，高效执行"
    theme="production"
    show-back="{{true}}"
    custom-class="planning-header">
    <view slot="right" class="header-actions">
      <view class="action-btn" bindtap="showFilter">
        <global-icon name="筛选" size="20" color="white"></global-icon>
      </view>
      <view class="action-btn" bindtap="addNewPlan">
        <global-icon name="加号" size="20" color="white"></global-icon>
      </view>
    </view>
  </global-page-header>

  <!-- 计划统计概览 -->
  <view class="planning-stats">
    <view class="stats-header">
      <text class="section-title">计划概览</text>
      <picker bindchange="onTimeRangeChange" value="{{timeRangeIndex}}" range="{{timeRangeOptions}}">
        <view class="time-picker">
          <text>{{timeRangeOptions[timeRangeIndex]}}</text>
          <global-icon name="箭头下" size="14" color="var(--text-color-tertiary)"></global-icon>
        </view>
      </picker>
    </view>
    
    <view class="stats-cards">
      <view wx:for="{{planningStats}}" wx:key="type" 
            class="stat-card {{item.status}}"
            bindtap="filterByStatus"
            data-status="{{item.type}}">
        <view class="stat-icon">
          <global-icon name="{{item.icon || '计划'}}" size="20" color="{{item.iconColor || 'var(--production-theme-color)'}}"></global-icon>
        </view>
        <view class="stat-content">
          <text class="stat-number">{{item.count}}</text>
          <text class="stat-label">{{item.label}}</text>
        </view>
        <view class="stat-progress">
          <view class="progress-ring" style="background: conic-gradient({{item.color}} {{item.percentage * 3.6}}deg, var(--border-color) 0deg);"></view>
          <text class="progress-text">{{item.percentage}}%</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 时间视图切换 -->
  <view class="view-switcher">
    <view class="switcher-tabs">
      <view wx:for="{{viewTypes}}" wx:key="type"
            class="tab-item {{currentView === item.type ? 'active' : ''}}"
            bindtap="switchView"
            data-view="{{item.type}}">
        <global-icon name="{{item.icon}}" size="16" color="{{currentView === item.type ? 'var(--production-theme-color)' : 'var(--text-color-tertiary)'}}"></global-icon>
        <text>{{item.label}}</text>
      </view>
    </view>
    
    <view class="date-navigation">
      <view class="nav-btn" bindtap="previousPeriod">
        <global-icon name="箭头左" size="16" color="var(--text-color-secondary)"></global-icon>
      </view>
      <text class="current-period">{{currentPeriodLabel}}</text>
      <view class="nav-btn" bindtap="nextPeriod">
        <global-icon name="箭头右" size="16" color="var(--text-color-secondary)"></global-icon>
      </view>
    </view>
  </view>

  <!-- 甘特图视图 -->
  <view wx:if="{{currentView === 'gantt'}}" class="gantt-view">
    <view class="gantt-header">
      <text class="section-title">甘特图</text>
      <view class="gantt-controls">
        <view class="control-btn" bindtap="zoomIn">
          <global-icon name="放大" size="16" color="var(--text-color-secondary)"></global-icon>
        </view>
        <view class="control-btn" bindtap="zoomOut">
          <global-icon name="缩小" size="16" color="var(--text-color-secondary)"></global-icon>
        </view>
      </view>
    </view>
    
    <scroll-view class="gantt-scroll" scroll-x="{{true}}" scroll-y="{{true}}">
      <view class="gantt-chart">
        <!-- 时间轴 -->
        <view class="time-axis">
          <view class="time-label-placeholder"></view>
          <view wx:for="{{timeLabels}}" wx:key="*this" class="time-label">
            <text>{{item}}</text>
          </view>
        </view>
        
        <!-- 任务条 -->
        <view wx:for="{{ganttData}}" wx:key="id" class="gantt-row">
          <view class="task-label">
            <text class="task-name">{{item.name}}</text>
            <text class="task-duration">{{item.duration}}天</text>
          </view>
          <view class="task-timeline">
            <view class="task-bar {{item.status}}" 
                  style="left: {{item.startPosition}}%; width: {{item.width}}%;"
                  bindtap="editTask"
                  data-task="{{item}}">
              <text class="task-progress">{{item.progress}}%</text>
            </view>
            <view wx:if="{{item.dependencies.length > 0}}" class="task-dependencies">
              <!-- 依赖关系线条 -->
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 列表视图 -->
  <view wx:if="{{currentView === 'list'}}" class="list-view">
    <view class="list-header">
      <text class="section-title">计划列表</text>
      <view class="sort-controls">
        <picker bindchange="onSortChange" value="{{sortIndex}}" range="{{sortOptions}}" range-key="label">
          <view class="sort-picker">
            <text>{{sortOptions[sortIndex].label}}</text>
            <global-icon name="箭头下" size="14" color="var(--text-color-tertiary)"></global-icon>
          </view>
        </picker>
      </view>
    </view>
    
    <view class="plans-list">
      <view wx:for="{{plansList}}" wx:key="id" 
            class="plan-item {{item.status}}"
            bindtap="viewPlanDetail"
            data-plan="{{item}}">
        <view class="plan-header">
          <view class="plan-priority {{item.priority}}">
            <global-icon name="{{item.priorityIcon}}" size="12" color="{{item.priorityColor}}"></global-icon>
          </view>
          <view class="plan-status {{item.status}}">
            <text>{{item.statusText}}</text>
          </view>
          <view class="plan-menu" bindtap="showPlanMenu" data-plan-id="{{item.id}}" catchtap="true">
            <global-icon name="更多" size="16" color="var(--text-color-tertiary)"></global-icon>
          </view>
        </view>
        
        <text class="plan-title">{{item.title}}</text>
        <text class="plan-description">{{item.description}}</text>
        
        <view class="plan-timeline">
          <view class="timeline-info">
            <global-icon name="时间" size="12" color="var(--text-color-tertiary)"></global-icon>
            <text>{{item.startDate}} - {{item.endDate}}</text>
          </view>
          <view class="timeline-duration">
            <text>{{item.duration}}天</text>
          </view>
        </view>
        
        <view class="plan-progress">
          <view class="progress-info">
            <text class="progress-label">完成进度</text>
            <text class="progress-value">{{item.progress}}%</text>
          </view>
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.progress}}%; background: {{item.progressColor}};"></view>
          </view>
        </view>
        
        <view class="plan-meta">
          <view class="meta-item">
            <global-icon name="用户" size="12" color="var(--text-color-tertiary)"></global-icon>
            <text>{{item.assignee}}</text>
          </view>
          <view class="meta-item">
            <global-icon name="位置" size="12" color="var(--text-color-tertiary)"></global-icon>
            <text>{{item.location}}</text>
          </view>
          <view class="meta-item">
            <global-icon name="任务" size="12" color="var(--text-color-tertiary)"></global-icon>
            <text>{{item.completedTasks}}/{{item.totalTasks}}</text>
          </view>
        </view>
        
        <view wx:if="{{item.hasAlerts}}" class="plan-alerts">
          <view wx:for="{{item.alerts}}" wx:key="*this" class="alert-item {{item.type}}">
            <global-icon name="{{item.icon}}" size="12" color="{{item.color}}"></global-icon>
            <text>{{item.message}}</text>
          </view>
        </view>
        
        <view class="plan-actions">
          <button wx:if="{{item.canStart}}" 
                  class="action-btn start" 
                  bindtap="startPlan" 
                  data-plan-id="{{item.id}}"
                  catchtap="true">
            <text>开始执行</text>
          </button>
          <button wx:if="{{item.canPause}}" 
                  class="action-btn pause" 
                  bindtap="pausePlan" 
                  data-plan-id="{{item.id}}"
                  catchtap="true">
            <text>暂停</text>
          </button>
          <button wx:if="{{item.canComplete}}" 
                  class="action-btn complete" 
                  bindtap="completePlan" 
                  data-plan-id="{{item.id}}"
                  catchtap="true">
            <text>完成</text>
          </button>
          <button class="action-btn detail" 
                  bindtap="viewPlanDetail" 
                  data-plan="{{item}}"
                  catchtap="true">
            <text>查看详情</text>
          </button>
        </view>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view wx:if="{{hasMore}}" class="load-more" bindtap="loadMorePlans">
      <view wx:if="{{loadingMore}}" class="loading-more">
        <view class="loading-spinner-small"></view>
        <text>加载中...</text>
      </view>
      <text wx:else>加载更多计划</text>
    </view>
  </view>

  <!-- 日历视图 -->
  <view wx:if="{{currentView === 'calendar'}}" class="calendar-view">
    <view class="calendar-header">
      <text class="section-title">日历视图</text>
      <view class="calendar-controls">
        <view class="control-btn" bindtap="goToToday">
          <text>今天</text>
        </view>
      </view>
    </view>
    
    <view class="calendar-grid">
      <view class="calendar-weekdays">
        <view wx:for="{{weekdays}}" wx:key="*this" class="weekday">
          <text>{{item}}</text>
        </view>
      </view>
      
      <view class="calendar-dates">
        <view wx:for="{{calendarDates}}" wx:key="date" 
              class="calendar-date {{item.isToday ? 'today' : ''}} {{item.isSelected ? 'selected' : ''}} {{item.isOtherMonth ? 'other-month' : ''}}"
              bindtap="selectDate"
              data-date="{{item.date}}">
          <text class="date-number">{{item.day}}</text>
          <view wx:if="{{item.plans.length > 0}}" class="date-plans">
            <view wx:for="{{item.plans}}" wx:key="id" wx:for-item="plan"
                  class="plan-dot {{plan.status}}"
                  bindtap="quickViewPlan"
                  data-plan="{{plan}}"
                  catchtap="true">
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 选中日期的计划详情 -->
    <view wx:if="{{selectedDatePlans.length > 0}}" class="selected-date-plans">
      <view class="selected-date-header">
        <text class="selected-date">{{selectedDateLabel}}</text>
        <text class="plans-count">{{selectedDatePlans.length}}个计划</text>
      </view>
      
      <view class="selected-plans-list">
        <view wx:for="{{selectedDatePlans}}" wx:key="id" 
              class="selected-plan-item {{item.status}}"
              bindtap="viewPlanDetail"
              data-plan="{{item}}">
          <view class="plan-time">
            <text>{{item.timeRange}}</text>
          </view>
          <view class="plan-info">
            <text class="plan-name">{{item.title}}</text>
            <text class="plan-location">{{item.location}}</text>
          </view>
          <view class="plan-status-dot {{item.status}}"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 浮动添加按钮 -->
  <view class="fab-add" bindtap="addNewPlan">
    <global-icon name="加号" size="24" color="white"></global-icon>
  </view>

  <!-- 筛选弹窗 -->
  <view wx:if="{{showFilterModal}}" class="filter-modal-mask" bindtap="hideFilter">
    <view class="filter-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">筛选条件</text>
        <view class="close-btn" bindtap="hideFilter">
          <global-icon name="关闭" size="20" color="var(--text-color-tertiary)"></global-icon>
        </view>
      </view>
      
      <view class="filter-content">
        <view class="filter-group">
          <text class="filter-label">状态</text>
          <view class="filter-options">
            <view wx:for="{{statusFilters}}" wx:key="value"
                  class="filter-option {{selectedFilters.status.includes(item.value) ? 'selected' : ''}}"
                  bindtap="toggleStatusFilter"
                  data-status="{{item.value}}">
              <text>{{item.label}}</text>
            </view>
          </view>
        </view>
        
        <view class="filter-group">
          <text class="filter-label">优先级</text>
          <view class="filter-options">
            <view wx:for="{{priorityFilters}}" wx:key="value"
                  class="filter-option {{selectedFilters.priority.includes(item.value) ? 'selected' : ''}}"
                  bindtap="togglePriorityFilter"
                  data-priority="{{item.value}}">
              <text>{{item.label}}</text>
            </view>
          </view>
        </view>
        
        <view class="filter-group">
          <text class="filter-label">负责人</text>
          <view class="filter-options">
            <view wx:for="{{assigneeFilters}}" wx:key="value"
                  class="filter-option {{selectedFilters.assignee.includes(item.value) ? 'selected' : ''}}"
                  bindtap="toggleAssigneeFilter"
                  data-assignee="{{item.value}}">
              <text>{{item.label}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="filter-actions">
        <button class="filter-btn reset" bindtap="resetFilters">重置</button>
        <button class="filter-btn apply" bindtap="applyFilters">应用</button>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{showEmptyState}}" class="empty-state">
    <view class="empty-icon">
      <global-icon name="计划空" size="80" color="var(--text-color-tertiary)"></global-icon>
    </view>
    <text class="empty-title">暂无生产计划</text>
    <text class="empty-subtitle">创建您的第一个生产计划</text>
    <button class="empty-action" bindtap="addNewPlan">
      <text>创建计划</text>
    </button>
  </view>

</view>