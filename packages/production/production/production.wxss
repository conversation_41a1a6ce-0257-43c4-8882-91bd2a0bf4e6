/* packages/production/production/production.wxss */
@import '/styles/unified-design-tokens.wxss';

.production-container {
  background-color: var(--bg-color-page);
  min-height: 100vh;
  padding-bottom: var(--spacer-16);
}

/* 页面头部 */
.production-header {
  background: linear-gradient(135deg, var(--production-theme-color) 0%, var(--warning-color-light) 100%);
  border-radius: var(--radius-xl);
  margin: var(--page-padding);
  margin-bottom: var(--section-margin);
  box-shadow: var(--shadow-m);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacer-3);
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-round);
  transition: all var(--transition-normal);
  position: relative;
}

.action-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.9);
}

.notification-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: var(--error-color);
  color: white;
  font-size: var(--font-size-xs);
  padding: var(--spacer-1) var(--spacer-2);
  border-radius: var(--radius-round);
  min-width: 32rpx;
  text-align: center;
  font-weight: var(--font-weight-bold);
}

/* 生产概览仪表盘 */
.production-overview {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.overview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacer-5);
  padding-bottom: var(--spacer-4);
  border-bottom: 1rpx solid var(--border-color);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
}

.section-subtitle {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  margin-left: var(--spacer-3);
}

.refresh-btn {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--production-theme-bg);
  border-radius: var(--radius-round);
  transition: all var(--transition-normal);
}

.refresh-btn:active {
  background: var(--production-theme-color-focus);
  transform: rotate(180deg);
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacer-4);
}

.overview-card {
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  position: relative;
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
}

.overview-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-m);
}

.overview-card.normal {
  border-left: 6rpx solid var(--production-theme-color);
}

.overview-card.warning {
  border-left: 6rpx solid var(--warning-color);
}

.overview-card.error {
  border-left: 6rpx solid var(--error-color);
}

.overview-card.success {
  border-left: 6rpx solid var(--success-color);
}

.card-icon {
  width: 80rpx;
  height: 80rpx;
  background: var(--production-theme-bg);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacer-4);
}

.card-content {
  margin-bottom: var(--spacer-3);
}

.card-value {
  display: block;
  font-size: var(--font-size-xxxl);
  font-weight: var(--font-weight-bold);
  color: var(--production-theme-color);
  line-height: 1;
  margin-bottom: var(--spacer-1);
}

.card-unit {
  font-size: var(--font-size-s);
  color: var(--text-color-tertiary);
  margin-left: var(--spacer-2);
}

.card-label {
  display: block;
  font-size: var(--font-size-m);
  color: var(--text-color-primary);
  font-weight: var(--font-weight-medium);
  margin-top: var(--spacer-2);
}

.card-trend {
  display: flex;
  align-items: center;
  gap: var(--spacer-1);
  font-size: var(--font-size-xs);
}

.card-trend.up {
  color: var(--success-color);
}

.card-trend.down {
  color: var(--error-color);
}

.card-trend.stable {
  color: var(--text-color-tertiary);
}

.card-alert {
  position: absolute;
  top: var(--spacer-3);
  right: var(--spacer-3);
  width: 48rpx;
  height: 48rpx;
  background: var(--warning-color-focus);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: alertPulse 2s infinite;
}

@keyframes alertPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

/* 核心功能模块 */
.function-modules {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.module-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacer-5);
  padding-bottom: var(--spacer-4);
  border-bottom: 1rpx solid var(--border-color);
}

.modules-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-4);
}

.module-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-4);
  padding: var(--spacer-5);
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
}

.module-item:active {
  background: var(--bg-color-hover);
  transform: scale(0.99);
}

.module-icon {
  width: 96rpx;
  height: 96rpx;
  background: var(--production-theme-bg);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-shrink: 0;
}

.module-icon.active {
  background: var(--production-theme-color);
}

.module-icon.active global-icon {
  color: white !important;
}

.module-icon.warning {
  background: var(--warning-color-focus);
}

.module-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: var(--error-color);
  color: white;
  font-size: var(--font-size-xs);
  padding: var(--spacer-1) var(--spacer-2);
  border-radius: var(--radius-round);
  min-width: 32rpx;
  text-align: center;
  font-weight: var(--font-weight-bold);
}

.module-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacer-2);
}

.module-title {
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
}

.module-desc {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  line-height: var(--line-height-normal);
}

.module-warning {
  display: flex;
  align-items: center;
  gap: var(--spacer-1);
  font-size: var(--font-size-xs);
  color: var(--warning-color);
  background: var(--warning-color-focus);
  padding: var(--spacer-1) var(--spacer-2);
  border-radius: var(--radius-s);
  align-self: flex-start;
}

.module-arrow {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-color-hover);
  border-radius: var(--radius-round);
}

/* 生产进度时间轴 */
.production-timeline {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.timeline-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacer-5);
  padding-bottom: var(--spacer-4);
  border-bottom: 1rpx solid var(--border-color);
}

.timeline-progress {
  display: flex;
  align-items: center;
  gap: var(--spacer-3);
}

.progress-text {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  font-weight: var(--font-weight-medium);
}

.progress-bar {
  width: 160rpx;
  height: 8rpx;
  background: var(--border-color);
  border-radius: var(--radius-s);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--production-theme-color);
  border-radius: var(--radius-s);
  transition: width var(--transition-slow);
}

.timeline-content {
  position: relative;
  padding-left: var(--spacer-8);
}

.timeline-content::before {
  content: '';
  position: absolute;
  left: 24rpx;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: var(--border-color);
  border-radius: var(--radius-s);
}

.timeline-item {
  position: relative;
  margin-bottom: var(--spacer-6);
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -44rpx;
  top: var(--spacer-2);
  width: 48rpx;
  height: 48rpx;
  background: var(--bg-color-container);
  border: 4rpx solid var(--border-color);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.timeline-item.completed .timeline-dot {
  border-color: var(--success-color);
  background: var(--success-color);
}

.timeline-item.in-progress .timeline-dot {
  border-color: var(--production-theme-color);
  background: var(--production-theme-color);
  animation: progressPulse 2s infinite;
}

.timeline-item.pending .timeline-dot {
  border-color: var(--text-color-tertiary);
}

.dot-inner {
  width: 16rpx;
  height: 16rpx;
  background: white;
  border-radius: var(--radius-round);
}

@keyframes progressPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

.timeline-content-item {
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  border: 1rpx solid var(--border-color);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-2);
}

.task-title {
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
}

.task-time {
  font-size: var(--font-size-s);
  color: var(--production-theme-color);
  font-weight: var(--font-weight-medium);
}

.task-description {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacer-3);
}

.task-meta {
  display: flex;
  gap: var(--spacer-4);
  margin-bottom: var(--spacer-4);
}

.task-location, .task-assignee {
  display: flex;
  align-items: center;
  gap: var(--spacer-1);
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

.task-actions {
  display: flex;
  gap: var(--spacer-3);
}

.task-btn {
  flex: 1;
  height: 64rpx;
  border-radius: var(--radius-m);
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-medium);
  border: none;
  transition: all var(--transition-normal);
}

.task-btn.start {
  background: var(--production-theme-color);
  color: white;
}

.task-btn.complete {
  background: var(--success-color);
  color: white;
}

.task-btn.pause {
  background: var(--bg-color-hover);
  color: var(--text-color-secondary);
  border: 1rpx solid var(--border-color);
}

.task-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.timeline-footer {
  margin-top: var(--spacer-6);
  padding-top: var(--spacer-4);
  border-top: 1rpx solid var(--border-color);
  text-align: center;
}

.view-all-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacer-2);
  background: var(--production-theme-bg);
  color: var(--production-theme-color);
  border: none;
  border-radius: var(--radius-l);
  padding: var(--spacer-3) var(--spacer-6);
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
}

.view-all-btn:active {
  background: var(--production-theme-color-focus);
}

/* 快速操作 */
.quick-actions {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.actions-header {
  margin-bottom: var(--spacer-5);
  padding-bottom: var(--spacer-4);
  border-bottom: 1rpx solid var(--border-color);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacer-4);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacer-3);
  padding: var(--spacer-4);
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
}

.action-item:active {
  background: var(--production-theme-bg);
  transform: scale(0.95);
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  background: var(--production-theme-bg);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-title {
  font-size: var(--font-size-xs);
  color: var(--text-color-primary);
  text-align: center;
  font-weight: var(--font-weight-medium);
}

/* 重要通知 */
.important-notices {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.notices-header {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  margin-bottom: var(--spacer-5);
  padding-bottom: var(--spacer-4);
  border-bottom: 1rpx solid var(--border-color);
}

.notices-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-3);
}

.notice-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-3);
  padding: var(--spacer-4);
  background: var(--bg-color-page);
  border-radius: var(--radius-m);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
}

.notice-item:active {
  background: var(--bg-color-hover);
}

.notice-item.high {
  border-left: 6rpx solid var(--error-color);
}

.notice-item.medium {
  border-left: 6rpx solid var(--warning-color);
}

.notice-item.low {
  border-left: 6rpx solid var(--info-color);
}

.notice-icon {
  width: 64rpx;
  height: 64rpx;
  background: var(--warning-color-focus);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.notice-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacer-1);
}

.notice-title {
  font-size: var(--font-size-s);
  color: var(--text-color-primary);
  font-weight: var(--font-weight-medium);
}

.notice-time {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

.notice-arrow {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
}

.loading-content {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-8);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacer-4);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-color);
  border-top: 4rpx solid var(--production-theme-color);
  border-radius: var(--radius-round);
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: var(--font-size-m);
  color: var(--text-color-tertiary);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacer-16) var(--spacer-6);
  text-align: center;
}

.empty-icon {
  margin-bottom: var(--spacer-6);
  opacity: 0.6;
}

.empty-title {
  font-size: var(--font-size-xl);
  color: var(--text-color-primary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacer-2);
}

.empty-subtitle {
  font-size: var(--font-size-m);
  color: var(--text-color-secondary);
  margin-bottom: var(--spacer-6);
  line-height: var(--line-height-normal);
}

.empty-action {
  background: var(--production-theme-color);
  color: white;
  border: none;
  border-radius: var(--radius-l);
  padding: var(--spacer-4) var(--spacer-8);
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
}

/* 响应式优化 */
@media (max-width: 350px) {
  .overview-cards {
    grid-template-columns: 1fr;
  }
  
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .task-actions {
    flex-direction: column;
  }
  
  .timeline-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacer-3);
  }
}

/* 安全区域适配 */
.production-container {
  padding-bottom: calc(var(--spacer-16) + env(safe-area-inset-bottom));
}