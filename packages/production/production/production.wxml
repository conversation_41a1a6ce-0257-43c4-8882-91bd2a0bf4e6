<!--packages/production/production/production.wxml-->
<view class="production-container theme-production">
  
  <!-- 页面头部 -->
  <global-page-header 
    title="生产管理" 
    subtitle="智能化生产，高效管理"
    theme="production"
    show-back="{{false}}"
    custom-class="production-header">
    <view slot="right" class="header-actions">
      <view class="action-btn" bindtap="showSearch">
        <global-icon name="搜索" size="20" color="white"></global-icon>
      </view>
      <view class="action-btn" bindtap="showNotifications">
        <global-icon name="通知" size="20" color="white"></global-icon>
        <view wx:if="{{unreadCount > 0}}" class="notification-badge">{{unreadCount > 99 ? '99+' : unreadCount}}</view>
      </view>
    </view>
  </global-page-header>

  <!-- 生产概览仪表盘 -->
  <view class="production-overview">
    <view class="overview-header">
      <text class="section-title">生产概览</text>
      <text class="section-subtitle">实时监控生产状态</text>
      <view class="refresh-btn" bindtap="refreshData">
        <global-icon name="刷新" size="16" color="var(--production-theme-color)"></global-icon>
      </view>
    </view>
    
    <view class="overview-cards">
      <view wx:for="{{overviewData}}" wx:key="id" 
            class="overview-card {{item.status}}"
            bindtap="goToDetail"
            data-type="{{item.type}}">
        <view class="card-icon">
          <global-icon name="{{item.icon || '统计'}}" size="24" color="{{item.iconColor || 'var(--production-theme-color)'}}"></global-icon>
        </view>
        <view class="card-content">
          <text class="card-value">{{item.value}}</text>
          <text class="card-unit">{{item.unit}}</text>
          <text class="card-label">{{item.label}}</text>
        </view>
        <view class="card-trend {{item.trend > 0 ? 'up' : item.trend < 0 ? 'down' : 'stable'}}">
          <global-icon name="{{item.trend > 0 ? '趋势上升' : item.trend < 0 ? '趋势下降' : '趋势平稳'}}" 
                       size="12" 
                       color="{{item.trend > 0 ? 'var(--success-color)' : item.trend < 0 ? 'var(--error-color)' : 'var(--text-color-tertiary)'}}"></global-icon>
          <text>{{item.trendText}}</text>
        </view>
        <view wx:if="{{item.alert}}" class="card-alert">
          <global-icon name="警告" size="12" color="var(--warning-color)"></global-icon>
        </view>
      </view>
    </view>
  </view>

  <!-- 核心功能模块 -->
  <view class="function-modules">
    <view class="module-header">
      <text class="section-title">核心功能</text>
      <text class="section-subtitle">一站式生产管理</text>
    </view>
    
    <view class="modules-grid">
      <view wx:for="{{functionModules}}" wx:key="id" 
            class="module-item"
            bindtap="navigateToModule"
            data-module="{{item.path}}">
        <view class="module-icon {{item.status === 'active' ? 'active' : item.status === 'warning' ? 'warning' : ''}}">
          <global-icon name="{{item.icon || '生产'}}" size="28" color="{{item.iconColor || 'var(--production-primary-color)'}}"></global-icon>
          <view wx:if="{{item.badge}}" class="module-badge">{{item.badge}}</view>
        </view>
        <view class="module-content">
          <text class="module-title">{{item.title}}</text>
          <text class="module-desc">{{item.description}}</text>
          <view wx:if="{{item.status === 'warning'}}" class="module-warning">
            <global-icon name="警告" size="12" color="var(--warning-color)"></global-icon>
            <text>{{item.warningText}}</text>
          </view>
        </view>
        <view class="module-arrow">
          <global-icon name="箭头右" size="16" color="var(--text-color-tertiary)"></global-icon>
        </view>
      </view>
    </view>
  </view>

  <!-- 生产进度时间轴 -->
  <view class="production-timeline">
    <view class="timeline-header">
      <text class="section-title">今日生产计划</text>
      <text class="section-subtitle">{{currentDate}} 进度追踪</text>
      <view class="timeline-progress">
        <text class="progress-text">{{completedTasks}}/{{totalTasks}}</text>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progressPercentage}}%;"></view>
        </view>
      </view>
    </view>
    
    <view class="timeline-content">
      <view wx:for="{{todayTasks}}" wx:key="id" class="timeline-item {{item.status}}">
        <view class="timeline-dot">
          <view class="dot-inner"></view>
        </view>
        <view class="timeline-content-item">
          <view class="task-header">
            <text class="task-title">{{item.title}}</text>
            <text class="task-time">{{item.time}}</text>
          </view>
          <text class="task-description">{{item.description}}</text>
          <view class="task-meta">
            <view class="task-location">
              <global-icon name="位置" size="12" color="var(--text-color-tertiary)"></global-icon>
              <text>{{item.location}}</text>
            </view>
            <view class="task-assignee">
              <global-icon name="用户" size="12" color="var(--text-color-tertiary)"></global-icon>
              <text>{{item.assignee}}</text>
            </view>
          </view>
          <view wx:if="{{item.status === 'pending'}}" class="task-actions">
            <button class="task-btn start" bindtap="startTask" data-task-id="{{item.id}}">
              <text>开始</text>
            </button>
          </view>
          <view wx:elif="{{item.status === 'in-progress'}}" class="task-actions">
            <button class="task-btn complete" bindtap="completeTask" data-task-id="{{item.id}}">
              <text>完成</text>
            </button>
            <button class="task-btn pause" bindtap="pauseTask" data-task-id="{{item.id}}">
              <text>暂停</text>
            </button>
          </view>
        </view>
      </view>
    </view>
    
    <view class="timeline-footer">
      <button class="view-all-btn" bindtap="goToPlanning">
        <text>查看完整计划</text>
        <global-icon name="箭头右" size="14" color="var(--production-theme-color)"></global-icon>
      </button>
    </view>
  </view>

  <!-- 快速操作 -->
  <view class="quick-actions">
    <view class="actions-header">
      <text class="section-title">快速操作</text>
    </view>
    
    <view class="actions-grid">
      <view wx:for="{{quickActions}}" wx:key="id" 
            class="action-item"
            bindtap="performQuickAction"
            data-action="{{item.action}}">
        <view class="action-icon">
          <global-icon name="{{item.icon}}" size="20" color="var(--production-theme-color)"></global-icon>
        </view>
        <text class="action-title">{{item.title}}</text>
      </view>
    </view>
  </view>

  <!-- 重要通知 -->
  <view wx:if="{{importantNotices.length > 0}}" class="important-notices">
    <view class="notices-header">
      <global-icon name="重要" size="18" color="var(--warning-color)"></global-icon>
      <text class="section-title">重要通知</text>
    </view>
    
    <view class="notices-list">
      <view wx:for="{{importantNotices}}" wx:key="id" 
            class="notice-item {{item.priority}}"
            bindtap="viewNotice"
            data-notice="{{item}}">
        <view class="notice-icon">
          <global-icon name="{{item.icon || '通知'}}" size="16" color="{{item.iconColor || 'var(--production-accent-color)'}}"></global-icon>
        </view>
        <view class="notice-content">
          <text class="notice-title">{{item.title}}</text>
          <text class="notice-time">{{item.timeLabel}}</text>
        </view>
        <view class="notice-arrow">
          <global-icon name="箭头右" size="14" color="var(--text-color-tertiary)"></global-icon>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{showEmptyState}}" class="empty-state">
    <view class="empty-icon">
      <global-icon name="生产空" size="80" color="var(--text-color-tertiary)"></global-icon>
    </view>
    <text class="empty-title">暂无生产数据</text>
    <text class="empty-subtitle">开始您的第一个生产计划吧</text>
    <button class="empty-action" bindtap="createFirstPlan">
      <text>创建生产计划</text>
    </button>
  </view>

</view>