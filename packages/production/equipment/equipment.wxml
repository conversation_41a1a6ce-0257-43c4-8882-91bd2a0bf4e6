<!--packages/production/equipment/equipment.wxml-->
<view class="equipment-container theme-production">
  
  <!-- 页面头部 -->
  <global-page-header 
    title="设备监控" 
    subtitle="智能监控，预防维护"
    theme="production"
    show-back="{{true}}"
    custom-class="equipment-header">
    <view slot="right" class="header-actions">
      <view class="action-btn" bindtap="refreshStatus">
        <global-icon name="刷新" size="20" color="white"></global-icon>
      </view>
      <view class="action-btn" bindtap="showAlerts">
        <global-icon name="警告" size="20" color="white"></global-icon>
        <view wx:if="{{alertCount > 0}}" class="alert-badge">{{alertCount}}</view>
      </view>
    </view>
  </global-page-header>

  <!-- 设备状态概览 -->
  <view class="equipment-overview">
    <view class="overview-header">
      <text class="section-title">设备状态</text>
      <text class="last-update">最后更新: {{lastUpdateTime}}</text>
    </view>
    
    <view class="status-cards">
      <view wx:for="{{statusOverview}}" wx:key="type" 
            class="status-card {{item.type}}"
            bindtap="filterByStatus"
            data-status="{{item.type}}">
        <view class="status-icon">
          <global-icon name="{{item.icon || '设备'}}" size="24" color="{{item.iconColor || 'var(--production-theme-color)'}}"></global-icon>
        </view>
        <view class="status-content">
          <text class="status-number">{{item.count}}</text>
          <text class="status-label">{{item.label}}</text>
        </view>
        <view class="status-chart">
          <view class="chart-ring" style="background: conic-gradient({{item.color}} {{item.percentage * 3.6}}deg, var(--border-color) 0deg);"></view>
          <text class="chart-text">{{item.percentage}}%</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 实时监控 -->
  <view class="realtime-monitor">
    <view class="monitor-header">
      <text class="section-title">实时监控</text>
      <view class="auto-refresh {{autoRefresh ? 'active' : ''}}" bindtap="toggleAutoRefresh">
        <global-icon name="{{autoRefresh ? '暂停' : '播放'}}" size="16" color="{{autoRefresh ? 'var(--success-color)' : 'var(--text-color-tertiary)'}}"></global-icon>
        <text>自动刷新</text>
      </view>
    </view>
    
    <scroll-view class="monitor-scroll" scroll-x="{{true}}" show-scrollbar="{{false}}">
      <view class="monitor-cards">
        <view wx:for="{{realtimeData}}" wx:key="id" 
              class="monitor-card {{item.status}}"
              bindtap="viewEquipmentDetail"
              data-equipment="{{item}}">
          <view class="card-header">
            <view class="equipment-icon">
              <global-icon name="{{item.typeIcon}}" size="20" color="{{item.statusColor}}"></global-icon>
            </view>
            <view class="equipment-status {{item.status}}">
              <view class="status-dot"></view>
              <text>{{item.statusText}}</text>
            </view>
          </view>
          
          <text class="equipment-name">{{item.name}}</text>
          <text class="equipment-location">{{item.location}}</text>
          
          <view class="equipment-metrics">
            <view wx:for="{{item.metrics}}" wx:key="key" wx:for-item="metric" class="metric-item">
              <text class="metric-label">{{metric.label}}</text>
              <text class="metric-value {{metric.status}}">{{metric.value}} {{metric.unit}}</text>
            </view>
          </view>
          
          <view wx:if="{{item.alerts.length > 0}}" class="equipment-alerts">
            <view wx:for="{{item.alerts}}" wx:key="*this" wx:for-item="alert" 
                  class="alert-item {{alert.level}}">
              <global-icon name="{{alert.icon}}" size="12" color="{{alert.color}}"></global-icon>
              <text>{{alert.message}}</text>
            </view>
          </view>
          
          <view class="card-footer">
            <text class="last-check">{{item.lastCheckTime}}</text>
            <view class="signal-strength">
              <global-icon name="信号" size="14" color="{{item.signalColor}}"></global-icon>
              <text>{{item.signalStrength}}%</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 设备分类 -->
  <view class="equipment-categories">
    <view class="categories-header">
      <text class="section-title">设备分类</text>
      <view class="view-mode">
        <view class="mode-btn {{viewMode === 'grid' ? 'active' : ''}}" 
              bindtap="switchViewMode" 
              data-mode="grid">
          <global-icon name="网格" size="16" color="{{viewMode === 'grid' ? 'var(--production-theme-color)' : 'var(--text-color-tertiary)'}}"></global-icon>
        </view>
        <view class="mode-btn {{viewMode === 'list' ? 'active' : ''}}" 
              bindtap="switchViewMode" 
              data-mode="list">
          <global-icon name="列表" size="16" color="{{viewMode === 'list' ? 'var(--production-theme-color)' : 'var(--text-color-tertiary)'}}"></global-icon>
        </view>
      </view>
    </view>
    
    <view class="category-filter">
      <scroll-view class="filter-scroll" scroll-x="{{true}}" show-scrollbar="{{false}}">
        <view class="filter-tags">
          <view wx:for="{{categoryFilters}}" wx:key="value"
                class="filter-tag {{selectedCategory === item.value ? 'active' : ''}}"
                bindtap="selectCategory"
                data-category="{{item.value}}">
            <global-icon name="{{item.icon}}" size="14" color="{{selectedCategory === item.value ? 'white' : 'var(--production-theme-color)'}}"></global-icon>
            <text>{{item.label}}</text>
            <view wx:if="{{item.count > 0}}" class="tag-count">{{item.count}}</view>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 网格视图 -->
    <view wx:if="{{viewMode === 'grid'}}" class="equipment-grid">
      <view wx:for="{{filteredEquipment}}" wx:key="id" 
            class="equipment-grid-item {{item.status}}"
            bindtap="viewEquipmentDetail"
            data-equipment="{{item}}">
        <view class="grid-item-header">
          <view class="equipment-type-icon">
            <global-icon name="{{item.typeIcon}}" size="32" color="{{item.statusColor}}"></global-icon>
          </view>
          <view class="equipment-status-badge {{item.status}}">
            <text>{{item.statusText}}</text>
          </view>
        </view>
        
        <text class="grid-item-name">{{item.name}}</text>
        <text class="grid-item-model">{{item.model}}</text>
        
        <view class="grid-item-metrics">
          <view class="primary-metric">
            <text class="metric-value">{{item.primaryMetric.value}}</text>
            <text class="metric-unit">{{item.primaryMetric.unit}}</text>
          </view>
          <text class="metric-label">{{item.primaryMetric.label}}</text>
        </view>
        
        <view class="grid-item-footer">
          <view class="maintenance-info">
            <global-icon name="工具" size="12" color="var(--text-color-tertiary)"></global-icon>
            <text>{{item.nextMaintenanceLabel}}</text>
          </view>
        </view>
        
        <view wx:if="{{item.hasAlert}}" class="grid-alert-indicator">
          <global-icon name="警告" size="16" color="var(--error-color)"></global-icon>
        </view>
      </view>
    </view>
    
    <!-- 列表视图 -->
    <view wx:if="{{viewMode === 'list'}}" class="equipment-list">
      <view wx:for="{{filteredEquipment}}" wx:key="id" 
            class="equipment-list-item {{item.status}}"
            bindtap="viewEquipmentDetail"
            data-equipment="{{item}}">
        <view class="list-item-main">
          <view class="equipment-info">
            <view class="equipment-type-icon">
              <global-icon name="{{item.typeIcon}}" size="24" color="{{item.statusColor}}"></global-icon>
            </view>
            <view class="equipment-details">
              <text class="equipment-name">{{item.name}}</text>
              <text class="equipment-model">{{item.model}} | {{item.location}}</text>
              <view class="equipment-tags">
                <view class="tag">{{item.category}}</view>
                <view wx:if="{{item.isConnected}}" class="tag online">在线</view>
                <view wx:else class="tag offline">离线</view>
              </view>
            </view>
          </view>
          
          <view class="equipment-status-section">
            <view class="status-indicator {{item.status}}">
              <view class="status-dot"></view>
              <text>{{item.statusText}}</text>
            </view>
            <view class="quick-actions">
              <view class="quick-btn" bindtap="quickControl" data-equipment-id="{{item.id}}" data-action="toggle" catchtap="true">
                <global-icon name="{{item.isRunning ? '暂停' : '播放'}}" size="16" color="var(--text-color-secondary)"></global-icon>
              </view>
              <view class="quick-btn" bindtap="quickControl" data-equipment-id="{{item.id}}" data-action="settings" catchtap="true">
                <global-icon name="设置" size="16" color="var(--text-color-secondary)"></global-icon>
              </view>
            </view>
          </view>
        </view>
        
        <view class="list-item-metrics">
          <view wx:for="{{item.displayMetrics}}" wx:key="key" wx:for-item="metric" 
                class="list-metric {{metric.status}}">
            <text class="metric-label">{{metric.label}}</text>
            <text class="metric-value">{{metric.value}} {{metric.unit}}</text>
          </view>
        </view>
        
        <view wx:if="{{item.alerts.length > 0}}" class="list-item-alerts">
          <view wx:for="{{item.alerts}}" wx:key="*this" wx:for-item="alert" 
                class="list-alert {{alert.level}}">
            <global-icon name="{{alert.icon}}" size="12" color="{{alert.color}}"></global-icon>
            <text>{{alert.message}}</text>
          </view>
        </view>
        
        <view class="list-item-footer">
          <view class="maintenance-schedule">
            <global-icon name="日历" size="12" color="var(--text-color-tertiary)"></global-icon>
            <text>下次维护: {{item.nextMaintenanceDate}}</text>
          </view>
          <view class="last-update">
            <text>{{item.lastUpdateLabel}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 维护提醒 -->
  <view wx:if="{{maintenanceReminders.length > 0}}" class="maintenance-reminders">
    <view class="reminders-header">
      <global-icon name="维护" size="18" color="var(--warning-color)"></global-icon>
      <text class="section-title">维护提醒</text>
      <text class="reminders-count">{{maintenanceReminders.length}}条</text>
    </view>
    
    <view class="reminders-list">
      <view wx:for="{{maintenanceReminders}}" wx:key="id" 
            class="reminder-item {{item.urgency}}"
            bindtap="handleMaintenanceReminder"
            data-reminder="{{item}}">
        <view class="reminder-icon">
          <global-icon name="{{item.icon || '维护'}}" size="16" color="{{item.iconColor || 'var(--warning-color)'}}"></global-icon>
        </view>
        <view class="reminder-content">
          <text class="reminder-title">{{item.title}}</text>
          <text class="reminder-equipment">{{item.equipmentName}}</text>
          <text class="reminder-due">{{item.dueLabel}}</text>
        </view>
        <view class="reminder-actions">
          <button class="reminder-btn postpone" 
                  bindtap="postponeReminder" 
                  data-reminder-id="{{item.id}}"
                  catchtap="true">
            <text>延后</text>
          </button>
          <button class="reminder-btn schedule" 
                  bindtap="scheduleNow" 
                  data-reminder-id="{{item.id}}"
                  catchtap="true">
            <text>立即安排</text>
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view wx:if="{{showSearch}}" class="search-section">
    <view class="search-bar">
      <global-icon name="搜索" size="20" color="var(--text-color-tertiary)"></global-icon>
      <input class="search-input" 
             placeholder="搜索设备名称、型号、位置..."
             value="{{searchKeyword}}"
             bindchange="onSearchChange"
             bindinput="onSearchInput"
             bindconfirm="confirmSearch"
             focus="{{searchFocused}}"
             confirm-type="search"></input>
      <view wx:if="{{searchKeyword}}" class="search-clear" bindtap="clearSearch">
        <global-icon name="关闭" size="16" color="var(--text-color-tertiary)"></global-icon>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载设备数据中...</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{showEmptyState}}" class="empty-state">
    <view class="empty-icon">
      <global-icon name="设备空" size="80" color="var(--text-color-tertiary)"></global-icon>
    </view>
    <text class="empty-title">暂无设备数据</text>
    <text class="empty-subtitle">请检查设备连接状态</text>
    <button class="empty-action" bindtap="refreshStatus">
      <text>刷新状态</text>
    </button>
  </view>

</view>