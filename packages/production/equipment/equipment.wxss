/* packages/production/equipment/equipment.wxss */
@import '/styles/unified-design-tokens.wxss';

.equipment-container {
  background-color: var(--bg-color-page);
  min-height: 100vh;
  padding-bottom: var(--spacer-16);
}

/* 页面头部 */
.equipment-header {
  background: linear-gradient(135deg, var(--production-theme-color) 0%, var(--warning-color-light) 100%);
  border-radius: var(--radius-xl);
  margin: var(--page-padding);
  margin-bottom: var(--section-margin);
  box-shadow: var(--shadow-m);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacer-3);
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-round);
  transition: all var(--transition-normal);
  position: relative;
}

.action-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.9);
}

.alert-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: var(--error-color);
  color: white;
  font-size: var(--font-size-xs);
  padding: var(--spacer-1) var(--spacer-2);
  border-radius: var(--radius-round);
  min-width: 32rpx;
  text-align: center;
  font-weight: var(--font-weight-bold);
}

/* 设备状态概览 */
.equipment-overview {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-5);
  padding-bottom: var(--spacer-4);
  border-bottom: 1rpx solid var(--border-color);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
}

.last-update {
  font-size: var(--font-size-s);
  color: var(--text-color-tertiary);
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacer-4);
}

.status-card {
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.status-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-m);
}

.status-card.normal {
  border-left: 6rpx solid var(--success-color);
}

.status-card.warning {
  border-left: 6rpx solid var(--warning-color);
}

.status-card.error {
  border-left: 6rpx solid var(--error-color);
}

.status-card.offline {
  border-left: 6rpx solid var(--text-color-tertiary);
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  background: var(--production-theme-bg);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacer-4);
}

.status-content {
  margin-bottom: var(--spacer-3);
}

.status-number {
  display: block;
  font-size: var(--font-size-xxxl);
  font-weight: var(--font-weight-bold);
  color: var(--production-theme-color);
  line-height: 1;
  margin-bottom: var(--spacer-1);
}

.status-label {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  font-weight: var(--font-weight-medium);
}

.status-chart {
  position: absolute;
  top: var(--spacer-3);
  right: var(--spacer-3);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-ring {
  width: 100%;
  height: 100%;
  border-radius: var(--radius-round);
  position: absolute;
}

.chart-text {
  font-size: var(--font-size-xs);
  color: var(--text-color-secondary);
  font-weight: var(--font-weight-bold);
  z-index: 2;
}

/* 实时监控 */
.realtime-monitor {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-5);
  padding-bottom: var(--spacer-4);
  border-bottom: 1rpx solid var(--border-color);
}

.auto-refresh {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  padding: var(--spacer-2) var(--spacer-4);
  background: var(--bg-color-hover);
  border-radius: var(--radius-m);
  font-size: var(--font-size-s);
  color: var(--text-color-tertiary);
  transition: all var(--transition-normal);
}

.auto-refresh.active {
  background: var(--success-color-focus);
  color: var(--success-color);
}

.monitor-scroll {
  width: 100%;
}

.monitor-cards {
  display: flex;
  gap: var(--spacer-4);
  padding-bottom: var(--spacer-2);
}

.monitor-card {
  width: 280rpx;
  flex-shrink: 0;
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
}

.monitor-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-m);
}

.monitor-card.normal {
  border-top: 4rpx solid var(--success-color);
}

.monitor-card.warning {
  border-top: 4rpx solid var(--warning-color);
}

.monitor-card.error {
  border-top: 4rpx solid var(--error-color);
}

.monitor-card.offline {
  border-top: 4rpx solid var(--text-color-tertiary);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-3);
}

.equipment-icon {
  width: 64rpx;
  height: 64rpx;
  background: var(--production-theme-bg);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
}

.equipment-status {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: var(--radius-round);
}

.equipment-status.normal .status-dot {
  background: var(--success-color);
}

.equipment-status.warning .status-dot {
  background: var(--warning-color);
}

.equipment-status.error .status-dot {
  background: var(--error-color);
}

.equipment-status.offline .status-dot {
  background: var(--text-color-tertiary);
}

.equipment-name {
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-1);
  line-height: var(--line-height-normal);
}

.equipment-location {
  font-size: var(--font-size-s);
  color: var(--text-color-tertiary);
  margin-bottom: var(--spacer-4);
}

.equipment-metrics {
  margin-bottom: var(--spacer-4);
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-2);
}

.metric-item:last-child {
  margin-bottom: 0;
}

.metric-label {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.metric-value {
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-medium);
}

.metric-value.normal {
  color: var(--success-color);
}

.metric-value.warning {
  color: var(--warning-color);
}

.metric-value.error {
  color: var(--error-color);
}

.equipment-alerts {
  margin-bottom: var(--spacer-4);
}

.alert-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  padding: var(--spacer-2) var(--spacer-3);
  border-radius: var(--radius-s);
  font-size: var(--font-size-xs);
  margin-bottom: var(--spacer-2);
}

.alert-item:last-child {
  margin-bottom: 0;
}

.alert-item.high {
  background: var(--error-color-focus);
  color: var(--error-color);
}

.alert-item.medium {
  background: var(--warning-color-focus);
  color: var(--warning-color);
}

.alert-item.low {
  background: var(--info-color-focus);
  color: var(--info-color);
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

.signal-strength {
  display: flex;
  align-items: center;
  gap: var(--spacer-1);
}

/* 设备分类 */
.equipment-categories {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
  overflow: hidden;
}

.categories-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacer-6);
  border-bottom: 1rpx solid var(--border-color);
}

.view-mode {
  display: flex;
  background: var(--bg-color-page);
  border-radius: var(--radius-m);
  padding: var(--spacer-1);
}

.mode-btn {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-s);
  transition: all var(--transition-normal);
}

.mode-btn.active {
  background: var(--bg-color-container);
  box-shadow: var(--shadow-s);
}

.category-filter {
  padding: var(--spacer-4) var(--spacer-6);
  border-bottom: 1rpx solid var(--border-color);
}

.filter-scroll {
  width: 100%;
}

.filter-tags {
  display: flex;
  gap: var(--spacer-3);
  padding-bottom: var(--spacer-2);
}

.filter-tag {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  padding: var(--spacer-2) var(--spacer-4);
  background: var(--bg-color-page);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-m);
  font-size: var(--font-size-s);
  color: var(--production-theme-color);
  transition: all var(--transition-normal);
  position: relative;
  flex-shrink: 0;
}

.filter-tag.active {
  background: var(--production-theme-color);
  border-color: var(--production-theme-color);
  color: white;
}

.tag-count {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: var(--error-color);
  color: white;
  font-size: var(--font-size-xs);
  padding: var(--spacer-1) var(--spacer-2);
  border-radius: var(--radius-round);
  min-width: 32rpx;
  text-align: center;
  font-weight: var(--font-weight-bold);
}

/* 网格视图 */
.equipment-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacer-4);
  padding: var(--spacer-6);
}

.equipment-grid-item {
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
}

.equipment-grid-item:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-m);
}

.equipment-grid-item.normal {
  border-left: 6rpx solid var(--success-color);
}

.equipment-grid-item.warning {
  border-left: 6rpx solid var(--warning-color);
}

.equipment-grid-item.error {
  border-left: 6rpx solid var(--error-color);
}

.equipment-grid-item.offline {
  border-left: 6rpx solid var(--text-color-tertiary);
}

.grid-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-3);
}

.equipment-type-icon {
  width: 80rpx;
  height: 80rpx;
  background: var(--production-theme-bg);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
}

.equipment-status-badge {
  padding: var(--spacer-1) var(--spacer-3);
  border-radius: var(--radius-s);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.equipment-status-badge.normal {
  background: var(--success-color-focus);
  color: var(--success-color);
}

.equipment-status-badge.warning {
  background: var(--warning-color-focus);
  color: var(--warning-color);
}

.equipment-status-badge.error {
  background: var(--error-color-focus);
  color: var(--error-color);
}

.equipment-status-badge.offline {
  background: var(--text-color-quaternary);
  color: var(--text-color-tertiary);
}

.grid-item-name {
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-1);
  line-height: var(--line-height-normal);
}

.grid-item-model {
  font-size: var(--font-size-s);
  color: var(--text-color-tertiary);
  margin-bottom: var(--spacer-4);
}

.grid-item-metrics {
  text-align: center;
  margin-bottom: var(--spacer-4);
}

.primary-metric {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: var(--spacer-1);
  margin-bottom: var(--spacer-2);
}

.metric-value {
  font-size: var(--font-size-xxxl);
  font-weight: var(--font-weight-bold);
  color: var(--production-theme-color);
  line-height: 1;
}

.metric-unit {
  font-size: var(--font-size-s);
  color: var(--text-color-tertiary);
}

.metric-label {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.grid-item-footer {
  border-top: 1rpx solid var(--border-color);
  padding-top: var(--spacer-3);
}

.maintenance-info {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

.grid-alert-indicator {
  position: absolute;
  top: var(--spacer-3);
  right: var(--spacer-3);
  width: 48rpx;
  height: 48rpx;
  background: var(--error-color-focus);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: alertPulse 2s infinite;
}

@keyframes alertPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

/* 列表视图 */
.equipment-list {
  padding: var(--spacer-6);
}

.equipment-list-item {
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  border: 1rpx solid var(--border-color);
  margin-bottom: var(--spacer-4);
  transition: all var(--transition-normal);
}

.equipment-list-item:last-child {
  margin-bottom: 0;
}

.equipment-list-item:active {
  background: var(--bg-color-hover);
}

.equipment-list-item.normal {
  border-left: 6rpx solid var(--success-color);
}

.equipment-list-item.warning {
  border-left: 6rpx solid var(--warning-color);
}

.equipment-list-item.error {
  border-left: 6rpx solid var(--error-color);
}

.equipment-list-item.offline {
  border-left: 6rpx solid var(--text-color-tertiary);
}

.list-item-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacer-4);
}

.equipment-info {
  display: flex;
  gap: var(--spacer-4);
  flex: 1;
}

.equipment-details {
  flex: 1;
}

.equipment-name {
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-1);
  line-height: var(--line-height-normal);
}

.equipment-model {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  margin-bottom: var(--spacer-2);
}

.equipment-tags {
  display: flex;
  gap: var(--spacer-2);
}

.tag {
  padding: var(--spacer-1) var(--spacer-2);
  background: var(--bg-color-hover);
  border-radius: var(--radius-s);
  font-size: var(--font-size-xs);
  color: var(--text-color-secondary);
}

.tag.online {
  background: var(--success-color-focus);
  color: var(--success-color);
}

.tag.offline {
  background: var(--text-color-quaternary);
  color: var(--text-color-tertiary);
}

.equipment-status-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacer-3);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-medium);
}

.quick-actions {
  display: flex;
  gap: var(--spacer-2);
}

.quick-btn {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-color-hover);
  border-radius: var(--radius-round);
  transition: all var(--transition-normal);
}

.quick-btn:active {
  background: var(--border-color);
  transform: scale(0.9);
}

.list-item-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacer-4);
  margin-bottom: var(--spacer-4);
  padding: var(--spacer-4);
  background: var(--bg-color-container);
  border-radius: var(--radius-m);
}

.list-metric {
  text-align: center;
}

.list-metric .metric-label {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
  margin-bottom: var(--spacer-1);
}

.list-metric .metric-value {
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-bold);
}

.list-metric.normal .metric-value {
  color: var(--success-color);
}

.list-metric.warning .metric-value {
  color: var(--warning-color);
}

.list-metric.error .metric-value {
  color: var(--error-color);
}

.list-item-alerts {
  margin-bottom: var(--spacer-4);
}

.list-alert {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  padding: var(--spacer-2) var(--spacer-3);
  border-radius: var(--radius-s);
  font-size: var(--font-size-xs);
  margin-bottom: var(--spacer-2);
}

.list-alert:last-child {
  margin-bottom: 0;
}

.list-alert.high {
  background: var(--error-color-focus);
  color: var(--error-color);
}

.list-alert.medium {
  background: var(--warning-color-focus);
  color: var(--warning-color);
}

.list-alert.low {
  background: var(--info-color-focus);
  color: var(--info-color);
}

.list-item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
  border-top: 1rpx solid var(--border-color);
  padding-top: var(--spacer-3);
}

.maintenance-schedule {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
}

/* 维护提醒 */
.maintenance-reminders {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.reminders-header {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  margin-bottom: var(--spacer-5);
  padding-bottom: var(--spacer-4);
  border-bottom: 1rpx solid var(--border-color);
}

.reminders-count {
  background: var(--warning-color-focus);
  color: var(--warning-color);
  padding: var(--spacer-1) var(--spacer-2);
  border-radius: var(--radius-s);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  margin-left: auto;
}

.reminders-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-4);
}

.reminder-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-4);
  padding: var(--spacer-5);
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
}

.reminder-item:active {
  background: var(--bg-color-hover);
}

.reminder-item.high {
  border-left: 6rpx solid var(--error-color);
}

.reminder-item.medium {
  border-left: 6rpx solid var(--warning-color);
}

.reminder-item.low {
  border-left: 6rpx solid var(--info-color);
}

.reminder-icon {
  width: 64rpx;
  height: 64rpx;
  background: var(--warning-color-focus);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.reminder-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacer-1);
}

.reminder-title {
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
}

.reminder-equipment {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.reminder-due {
  font-size: var(--font-size-s);
  color: var(--warning-color);
  font-weight: var(--font-weight-medium);
}

.reminder-actions {
  display: flex;
  gap: var(--spacer-2);
}

.reminder-btn {
  padding: var(--spacer-2) var(--spacer-4);
  border-radius: var(--radius-m);
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-medium);
  border: none;
  transition: all var(--transition-normal);
}

.reminder-btn.postpone {
  background: var(--bg-color-hover);
  color: var(--text-color-secondary);
  border: 1rpx solid var(--border-color);
}

.reminder-btn.schedule {
  background: var(--production-theme-color);
  color: white;
}

.reminder-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 搜索栏 */
.search-section {
  padding: 0 var(--page-padding);
  margin-bottom: var(--section-margin);
}

.search-bar {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-4) var(--spacer-5);
  display: flex;
  align-items: center;
  gap: var(--spacer-3);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.search-input {
  flex: 1;
  font-size: var(--font-size-m);
  color: var(--text-color-primary);
  background: transparent;
  border: none;
}

.search-clear {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-color-hover);
  border-radius: var(--radius-round);
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
}

.loading-content {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-8);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacer-4);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-color);
  border-top: 4rpx solid var(--production-theme-color);
  border-radius: var(--radius-round);
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: var(--font-size-m);
  color: var(--text-color-tertiary);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacer-16) var(--spacer-6);
  text-align: center;
}

.empty-icon {
  margin-bottom: var(--spacer-6);
  opacity: 0.6;
}

.empty-title {
  font-size: var(--font-size-xl);
  color: var(--text-color-primary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacer-2);
}

.empty-subtitle {
  font-size: var(--font-size-m);
  color: var(--text-color-secondary);
  margin-bottom: var(--spacer-6);
  line-height: var(--line-height-normal);
}

.empty-action {
  background: var(--production-theme-color);
  color: white;
  border: none;
  border-radius: var(--radius-l);
  padding: var(--spacer-4) var(--spacer-8);
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
}

/* 响应式优化 */
@media (max-width: 350px) {
  .status-cards, .equipment-grid {
    grid-template-columns: 1fr;
  }
  
  .list-item-metrics {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .reminder-actions {
    flex-direction: column;
  }
  
  .reminder-btn {
    width: 100%;
  }
}

/* 安全区域适配 */
.equipment-container {
  padding-bottom: calc(var(--spacer-16) + env(safe-area-inset-bottom));
}