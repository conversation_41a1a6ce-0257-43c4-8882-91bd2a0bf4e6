/* packages/production/quality/quality.wxss */
@import '/styles/unified-design-tokens.wxss';

.quality-container {
  background-color: var(--bg-color-page);
  min-height: 100vh;
  padding-bottom: var(--spacer-16);
}

/* 页面头部 */
.quality-header {
  background: linear-gradient(135deg, var(--production-theme-color) 0%, var(--success-color) 100%);
  border-radius: var(--radius-xl);
  margin: var(--page-padding);
  margin-bottom: var(--section-margin);
  box-shadow: var(--shadow-m);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacer-3);
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-round);
  transition: all var(--transition-normal);
}

.action-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.9);
}

/* 质量概览 */
.quality-overview {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacer-4);
}

.stat-item {
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  border: 1rpx solid var(--border-color);
}

.stat-item.excellent {
  border-left: 6rpx solid var(--success-color);
}

.stat-item.good {
  border-left: 6rpx solid var(--production-theme-color);
}

.stat-item.warning {
  border-left: 6rpx solid var(--warning-color);
}

.stat-item.poor {
  border-left: 6rpx solid var(--error-color);
}

.stat-icon {
  width: 64rpx;
  height: 64rpx;
  background: var(--production-theme-bg);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacer-3);
}

.stat-content {
  margin-bottom: var(--spacer-3);
}

.stat-value {
  display: block;
  font-size: var(--font-size-xxxl);
  font-weight: var(--font-weight-bold);
  color: var(--production-theme-color);
  line-height: 1;
  margin-bottom: var(--spacer-1);
}

.stat-label {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  font-weight: var(--font-weight-medium);
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: var(--spacer-1);
  font-size: var(--font-size-xs);
}

.stat-trend.up {
  color: var(--success-color);
}

.stat-trend.down {
  color: var(--error-color);
}

.stat-trend.stable {
  color: var(--text-color-tertiary);
}

/* 检查任务 */
.inspection-tasks {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-5);
  padding-bottom: var(--spacer-4);
  border-bottom: 1rpx solid var(--border-color);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
}

.task-summary {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  background: var(--production-theme-bg);
  padding: var(--spacer-2) var(--spacer-3);
  border-radius: var(--radius-m);
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-3);
}

.task-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-4);
  padding: var(--spacer-4);
  background: var(--bg-color-page);
  border-radius: var(--radius-m);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
}

.task-item:active {
  background: var(--bg-color-hover);
}

.task-priority {
  width: 48rpx;
  height: 48rpx;
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.task-priority.high {
  background: var(--error-color-focus);
}

.task-priority.medium {
  background: var(--warning-color-focus);
}

.task-priority.low {
  background: var(--info-color-focus);
}

.task-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacer-1);
}

.task-title {
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
}

.task-location {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.task-time {
  font-size: var(--font-size-s);
  color: var(--production-theme-color);
  font-weight: var(--font-weight-medium);
}

.task-status {
  flex-shrink: 0;
}

.status-badge {
  padding: var(--spacer-1) var(--spacer-3);
  border-radius: var(--radius-s);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.status-badge.pending {
  background: var(--text-color-quaternary);
  color: var(--text-color-tertiary);
}

.status-badge.in-progress {
  background: var(--production-theme-bg);
  color: var(--production-theme-color);
}

.status-badge.completed {
  background: var(--success-color-focus);
  color: var(--success-color);
}

/* 质量问题 */
.quality-issues {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.filter-picker {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  padding: var(--spacer-2) var(--spacer-4);
  background: var(--bg-color-hover);
  border-radius: var(--radius-m);
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.issues-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-4);
}

.issue-item {
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
}

.issue-item:active {
  background: var(--bg-color-hover);
}

.issue-item.critical {
  border-left: 6rpx solid var(--error-color);
}

.issue-item.major {
  border-left: 6rpx solid var(--warning-color);
}

.issue-item.minor {
  border-left: 6rpx solid var(--info-color);
}

.issue-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-3);
}

.issue-severity {
  width: 48rpx;
  height: 48rpx;
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
}

.issue-severity.critical {
  background: var(--error-color-focus);
}

.issue-severity.major {
  background: var(--warning-color-focus);
}

.issue-severity.minor {
  background: var(--info-color-focus);
}

.issue-status {
  padding: var(--spacer-1) var(--spacer-3);
  border-radius: var(--radius-s);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.issue-status.open {
  background: var(--error-color-focus);
  color: var(--error-color);
}

.issue-status.in-progress {
  background: var(--warning-color-focus);
  color: var(--warning-color);
}

.issue-status.resolved {
  background: var(--success-color-focus);
  color: var(--success-color);
}

.issue-date {
  font-size: var(--font-size-s);
  color: var(--text-color-tertiary);
}

.issue-title {
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-2);
}

.issue-description {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacer-4);
}

.issue-meta {
  display: flex;
  gap: var(--spacer-4);
  margin-bottom: var(--spacer-4);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-1);
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

.corrective-actions {
  border-top: 1rpx solid var(--border-color);
  padding-top: var(--spacer-4);
}

.actions-title {
  display: block;
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-3);
}

.action-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  margin-bottom: var(--spacer-2);
}

.action-item:last-child {
  margin-bottom: 0;
}

.action-status {
  width: 32rpx;
  height: 32rpx;
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.action-status.pending {
  background: var(--text-color-quaternary);
}

.action-status.in-progress {
  background: var(--warning-color-focus);
}

.action-status.completed {
  background: var(--success-color-focus);
}

.action-text {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  line-height: var(--line-height-normal);
}

/* 质量趋势 */
.quality-trends {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.period-picker {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  padding: var(--spacer-2) var(--spacer-4);
  background: var(--bg-color-hover);
  border-radius: var(--radius-m);
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.trend-chart {
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  border: 1rpx solid var(--border-color);
}

.chart-container {
  display: flex;
  height: 400rpx;
  margin-bottom: var(--spacer-4);
}

.chart-y-axis {
  width: 80rpx;
  display: flex;
  flex-direction: column-reverse;
  justify-content: space-between;
  padding-right: var(--spacer-3);
  border-right: 2rpx solid var(--border-color);
}

.y-label {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
  text-align: right;
}

.chart-area {
  flex: 1;
  display: flex;
  align-items: end;
  justify-content: space-around;
  padding: 0 var(--spacer-3);
}

.chart-bar-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80rpx;
}

.chart-bars {
  width: 100%;
  height: 360rpx;
  display: flex;
  align-items: end;
  gap: var(--spacer-1);
  margin-bottom: var(--spacer-2);
}

.chart-bar {
  flex: 1;
  border-radius: var(--radius-s) var(--radius-s) 0 0;
  transition: height var(--transition-slow);
}

.chart-bar.passed {
  background: var(--success-color);
}

.chart-bar.failed {
  background: var(--error-color);
}

.chart-x-label {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
  text-align: center;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: var(--spacer-4);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: var(--radius-s);
}

.legend-color.passed {
  background: var(--success-color);
}

.legend-color.failed {
  background: var(--error-color);
}

/* 快速操作 */
.quick-actions {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacer-4);
  margin-top: var(--spacer-4);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacer-3);
  padding: var(--spacer-5);
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
}

.action-item:active {
  background: var(--production-theme-bg);
  transform: scale(0.95);
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  background: var(--production-theme-bg);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-title {
  font-size: var(--font-size-s);
  color: var(--text-color-primary);
  text-align: center;
  font-weight: var(--font-weight-medium);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacer-16) var(--spacer-6);
  text-align: center;
}

.empty-icon {
  margin-bottom: var(--spacer-6);
  opacity: 0.6;
}

.empty-title {
  font-size: var(--font-size-xl);
  color: var(--text-color-primary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacer-2);
}

.empty-subtitle {
  font-size: var(--font-size-m);
  color: var(--text-color-secondary);
  margin-bottom: var(--spacer-6);
  line-height: var(--line-height-normal);
}

.empty-action {
  background: var(--production-theme-color);
  color: white;
  border: none;
  border-radius: var(--radius-l);
  padding: var(--spacer-4) var(--spacer-8);
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
}

/* 响应式优化 */
@media (max-width: 350px) {
  .overview-stats {
    grid-template-columns: 1fr;
  }
  
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .chart-container {
    height: 300rpx;
  }
  
  .chart-bars {
    height: 260rpx;
  }
}