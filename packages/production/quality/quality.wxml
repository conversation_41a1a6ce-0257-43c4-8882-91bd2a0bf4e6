<!--packages/production/quality/quality.wxml-->
<view class="quality-container theme-production">
  
  <!-- 页面头部 -->
  <global-page-header 
    title="质量控制" 
    subtitle="严控质量，保证品质"
    theme="production"
    show-back="{{true}}"
    custom-class="quality-header">
    <view slot="right" class="header-actions">
      <view class="action-btn" bindtap="newInspection">
        <global-icon name="检查" size="20" color="white"></global-icon>
      </view>
      <view class="action-btn" bindtap="exportReport">
        <global-icon name="下载" size="20" color="white"></global-icon>
      </view>
    </view>
  </global-page-header>

  <!-- 质量概览 -->
  <view class="quality-overview">
    <view class="overview-stats">
      <view wx:for="{{qualityStats}}" wx:key="type" class="stat-item {{item.type}}">
        <view class="stat-icon">
          <global-icon name="{{item.icon || '质量'}}" size="20" color="{{item.iconColor || 'var(--production-theme-color)'}}"></global-icon>
        </view>
        <view class="stat-content">
          <text class="stat-value">{{item.value}}</text>
          <text class="stat-label">{{item.label}}</text>
        </view>
        <view class="stat-trend {{item.trend}}">
          <global-icon name="{{item.trendIcon}}" size="12" color="{{item.trendColor}}"></global-icon>
          <text>{{item.trendText}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 检查任务 -->
  <view class="inspection-tasks">
    <view class="section-header">
      <text class="section-title">今日检查任务</text>
      <view class="task-summary">
        <text>{{completedTasks}}/{{totalTasks}} 已完成</text>
      </view>
    </view>
    
    <view class="tasks-list">
      <view wx:for="{{inspectionTasks}}" wx:key="id" 
            class="task-item {{item.status}}"
            bindtap="startInspection"
            data-task="{{item}}">
        <view class="task-priority {{item.priority}}">
          <global-icon name="{{item.priorityIcon}}" size="14" color="{{item.priorityColor}}"></global-icon>
        </view>
        <view class="task-content">
          <text class="task-title">{{item.title}}</text>
          <text class="task-location">{{item.location}}</text>
          <text class="task-time">{{item.scheduledTime}}</text>
        </view>
        <view class="task-status">
          <view class="status-badge {{item.status}}">
            <text>{{item.statusText}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 质量问题追踪 -->
  <view class="quality-issues">
    <view class="section-header">
      <text class="section-title">质量问题</text>
      <picker bindchange="onIssueFilterChange" value="{{issueFilterIndex}}" range="{{issueFilters}}" range-key="label">
        <view class="filter-picker">
          <text>{{issueFilters[issueFilterIndex].label}}</text>
          <global-icon name="箭头下" size="14" color="var(--text-color-tertiary)"></global-icon>
        </view>
      </picker>
    </view>
    
    <view class="issues-list">
      <view wx:for="{{qualityIssues}}" wx:key="id" 
            class="issue-item {{item.severity}}"
            bindtap="viewIssueDetail"
            data-issue="{{item}}">
        <view class="issue-header">
          <view class="issue-severity {{item.severity}}">
            <global-icon name="{{item.severityIcon}}" size="16" color="{{item.severityColor}}"></global-icon>
          </view>
          <view class="issue-status {{item.status}}">
            <text>{{item.statusText}}</text>
          </view>
          <text class="issue-date">{{item.reportDate}}</text>
        </view>
        
        <text class="issue-title">{{item.title}}</text>
        <text class="issue-description">{{item.description}}</text>
        
        <view class="issue-meta">
          <view class="meta-item">
            <global-icon name="位置" size="12" color="var(--text-color-tertiary)"></global-icon>
            <text>{{item.location}}</text>
          </view>
          <view class="meta-item">
            <global-icon name="用户" size="12" color="var(--text-color-tertiary)"></global-icon>
            <text>{{item.reporter}}</text>
          </view>
        </view>
        
        <view wx:if="{{item.corrective_actions.length > 0}}" class="corrective-actions">
          <text class="actions-title">改进措施:</text>
          <view wx:for="{{item.corrective_actions}}" wx:key="*this" wx:for-item="action" class="action-item">
            <view class="action-status {{action.status}}">
              <global-icon name="{{action.statusIcon}}" size="12" color="{{action.statusColor}}"></global-icon>
            </view>
            <text class="action-text">{{action.description}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 质量趋势 -->
  <view class="quality-trends">
    <view class="section-header">
      <text class="section-title">质量趋势</text>
      <picker bindchange="onTrendPeriodChange" value="{{trendPeriodIndex}}" range="{{trendPeriods}}">
        <view class="period-picker">
          <text>{{trendPeriods[trendPeriodIndex]}}</text>
          <global-icon name="箭头下" size="14" color="var(--text-color-tertiary)"></global-icon>
        </view>
      </picker>
    </view>
    
    <view class="trend-chart">
      <view class="chart-container">
        <view class="chart-y-axis">
          <view wx:for="{{chartYLabels}}" wx:key="*this" class="y-label">
            <text>{{item}}</text>
          </view>
        </view>
        <view class="chart-area">
          <view wx:for="{{chartData}}" wx:key="period" class="chart-bar-group">
            <view class="chart-bars">
              <view class="chart-bar passed" style="height: {{item.passedHeight}}%;"></view>
              <view class="chart-bar failed" style="height: {{item.failedHeight}}%;"></view>
            </view>
            <text class="chart-x-label">{{item.period}}</text>
          </view>
        </view>
      </view>
      
      <view class="chart-legend">
        <view class="legend-item">
          <view class="legend-color passed"></view>
          <text>合格</text>
        </view>
        <view class="legend-item">
          <view class="legend-color failed"></view>
          <text>不合格</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 快速操作 -->
  <view class="quick-actions">
    <view class="section-title">快速操作</view>
    <view class="actions-grid">
      <view wx:for="{{quickActions}}" wx:key="action" 
            class="action-item"
            bindtap="performQuickAction"
            data-action="{{item.action}}">
        <view class="action-icon">
          <global-icon name="{{item.icon}}" size="24" color="var(--production-theme-color)"></global-icon>
        </view>
        <text class="action-title">{{item.title}}</text>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{showEmptyState}}" class="empty-state">
    <view class="empty-icon">
      <global-icon name="质量空" size="80" color="var(--text-color-tertiary)"></global-icon>
    </view>
    <text class="empty-title">暂无质量数据</text>
    <text class="empty-subtitle">开始您的第一次质量检查</text>
    <button class="empty-action" bindtap="newInspection">
      <text>开始检查</text>
    </button>
  </view>

</view>