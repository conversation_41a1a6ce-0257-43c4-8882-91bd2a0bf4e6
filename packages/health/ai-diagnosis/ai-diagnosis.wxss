/* packages/health/ai-diagnosis/ai-diagnosis.wxss */
@import '/styles/unified-design-tokens.wxss';

.ai-diagnosis-container {
  background-color: var(--bg-color-page);
  min-height: 100vh;
  padding-bottom: 140rpx; /* 为底部操作栏留空间 */
}

/* 页面头部 */
.ai-diagnosis-header {
  background: linear-gradient(135deg, var(--health-theme-color) 0%, var(--success-color) 100%);
  border-radius: var(--radius-xl);
  margin: var(--page-padding);
  margin-bottom: var(--section-margin);
  box-shadow: var(--shadow-m);
}

/* 诊断进度指示器 */
.diagnosis-progress {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacer-4);
  position: relative;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.step-circle {
  width: 64rpx;
  height: 64rpx;
  border-radius: var(--radius-round);
  background: var(--bg-color-hover);
  border: 2rpx solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-tertiary);
  transition: all var(--transition-normal);
  margin-bottom: var(--spacer-2);
  z-index: 2;
}

.step-item.active .step-circle {
  background: var(--health-theme-color);
  border-color: var(--health-theme-color);
  color: white;
  transform: scale(1.1);
}

.step-item.completed .step-circle {
  background: var(--success-color);
  border-color: var(--success-color);
  color: white;
}

.step-title {
  font-size: var(--font-size-xs);
  color: var(--text-color-secondary);
  text-align: center;
  font-weight: var(--font-weight-medium);
}

.step-item.active .step-title {
  color: var(--health-theme-color);
  font-weight: var(--font-weight-bold);
}

.progress-bar {
  height: 8rpx;
  background: var(--border-color);
  border-radius: var(--radius-s);
  position: relative;
  margin-top: var(--spacer-2);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--health-theme-color) 0%, var(--success-color) 100%);
  border-radius: var(--radius-s);
  transition: width var(--transition-slow);
}

/* 步骤内容 */
.step-content {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-6);
  margin: 0 var(--page-padding) var(--section-margin);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
  min-height: 600rpx;
}

.step-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: var(--spacer-6);
  padding-bottom: var(--spacer-4);
  border-bottom: 1rpx solid var(--border-color);
}

.step-header .step-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  margin: var(--spacer-3) 0 var(--spacer-2);
}

.step-subtitle {
  font-size: var(--font-size-m);
  color: var(--text-color-secondary);
  line-height: var(--line-height-normal);
}

/* 表单样式 */
.form-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-5);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-2);
}

.form-group.half {
  flex: 1;
}

.form-row {
  display: flex;
  gap: var(--spacer-4);
}

.form-label {
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
}

.picker-input, .form-input, .form-textarea {
  background: var(--bg-color-page);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-m);
  padding: var(--spacer-4);
  font-size: var(--font-size-m);
  color: var(--text-color-primary);
  transition: all var(--transition-normal);
}

.picker-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 80rpx;
}

.form-input {
  min-height: 80rpx;
}

.form-textarea {
  min-height: 120rpx;
  resize: none;
}

.picker-input:focus, .form-input:focus, .form-textarea:focus {
  border-color: var(--health-theme-color);
  box-shadow: 0 0 0 4rpx var(--health-theme-color-focus);
}

/* 年龄选项 */
.age-options {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacer-3);
}

.age-option {
  flex: 1;
  min-width: 120rpx;
  padding: var(--spacer-3) var(--spacer-4);
  background: var(--bg-color-page);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-m);
  text-align: center;
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  transition: all var(--transition-normal);
}

.age-option.selected {
  background: var(--health-theme-color);
  border-color: var(--health-theme-color);
  color: white;
  font-weight: var(--font-weight-medium);
}

/* 数量输入 */
.quantity-input {
  display: flex;
  align-items: center;
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-m);
  overflow: hidden;
  background: var(--bg-color-page);
}

.quantity-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-color-hover);
  transition: all var(--transition-normal);
}

.quantity-btn:active {
  background: var(--border-color);
}

.quantity-value {
  flex: 1;
  height: 80rpx;
  text-align: center;
  border: none;
  background: transparent;
  font-size: var(--font-size-m);
  color: var(--text-color-primary);
  border-left: 1rpx solid var(--border-color);
  border-right: 1rpx solid var(--border-color);
}

/* 症状选择 */
.symptoms-categories {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-6);
}

.symptom-category {
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  background: var(--bg-color-page);
}

.category-header {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  margin-bottom: var(--spacer-4);
  padding-bottom: var(--spacer-3);
  border-bottom: 1rpx solid var(--border-color);
}

.category-title {
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
}

.symptoms-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacer-3);
}

.symptom-item {
  padding: var(--spacer-4);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-m);
  background: var(--bg-color-container);
  transition: all var(--transition-normal);
  cursor: pointer;
}

.symptom-item:active {
  transform: scale(0.98);
}

.symptom-item.selected {
  background: var(--health-theme-color);
  border-color: var(--health-theme-color);
  color: white;
}

.symptom-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  background: var(--bg-color-hover);
  border-radius: var(--radius-round);
  margin-bottom: var(--spacer-2);
}

.symptom-item.selected .symptom-icon {
  background: rgba(255, 255, 255, 0.2);
}

.symptom-name {
  display: block;
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-1);
}

.symptom-item.selected .symptom-name {
  color: white;
}

.symptom-desc {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
  line-height: var(--line-height-normal);
}

.symptom-item.selected .symptom-desc {
  color: rgba(255, 255, 255, 0.8);
}

/* 自定义症状输入 */
.custom-symptom {
  margin-top: var(--spacer-6);
  padding-top: var(--spacer-6);
  border-top: 1rpx solid var(--border-color);
}

.custom-label {
  display: block;
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-3);
}

.custom-textarea {
  width: 100%;
  min-height: 120rpx;
  margin-bottom: var(--spacer-2);
}

.char-count {
  text-align: right;
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

/* 饲料类型选择 */
.feed-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacer-3);
}

.feed-option {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  padding: var(--spacer-3) var(--spacer-4);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-m);
  background: var(--bg-color-page);
  transition: all var(--transition-normal);
}

.feed-option.selected {
  background: var(--health-theme-color-focus);
  border-color: var(--health-theme-color);
}

/* 照片上传 */
.photo-tips {
  background: var(--health-theme-color-focus);
  border-radius: var(--radius-m);
  padding: var(--spacer-4);
  margin-bottom: var(--spacer-6);
}

.tips-title {
  display: block;
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  color: var(--health-theme-color);
  margin-bottom: var(--spacer-3);
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-2);
}

.tip-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacer-4);
}

.photo-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: var(--radius-m);
  overflow: hidden;
}

.photo-image {
  width: 100%;
  height: 100%;
}

.photo-delete {
  position: absolute;
  top: var(--spacer-2);
  right: var(--spacer-2);
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.photo-add {
  aspect-ratio: 1;
  border: 2rpx dashed var(--border-color);
  border-radius: var(--radius-m);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacer-2);
  background: var(--bg-color-page);
  transition: all var(--transition-normal);
}

.photo-add:active {
  border-color: var(--health-theme-color);
  background: var(--health-theme-color-focus);
}

.add-text {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.add-count {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

/* AI分析结果 */
.diagnosis-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacer-12) var(--spacer-6);
}

.loading-animation {
  position: relative;
  margin-bottom: var(--spacer-6);
}

.ai-brain {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

.loading-dots {
  display: flex;
  gap: var(--spacer-2);
  margin-top: var(--spacer-4);
}

.dot {
  width: 16rpx;
  height: 16rpx;
  background: var(--health-theme-color);
  border-radius: var(--radius-round);
  animation: dotBounce 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes dotBounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

.loading-text {
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-2);
}

.loading-subtitle {
  font-size: var(--font-size-m);
  color: var(--text-color-secondary);
  text-align: center;
}

/* 诊断结果 */
.diagnosis-result {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from { transform: translateY(40rpx); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.result-header {
  display: flex;
  align-items: center;
  gap: var(--spacer-4);
  padding: var(--spacer-6);
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  margin-bottom: var(--spacer-6);
  border: 1rpx solid var(--border-color);
}

.result-status {
  width: 96rpx;
  height: 96rpx;
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.result-status.high {
  background: var(--error-color);
}

.result-status.medium {
  background: var(--warning-color);
}

.result-status.low {
  background: var(--success-color);
}

.result-info {
  flex: 1;
}

.result-title {
  display: block;
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-2);
}

.result-confidence {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

/* 结果详情 */
.result-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-6);
  margin-bottom: var(--spacer-8);
}

.detail-section {
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  border: 1rpx solid var(--border-color);
}

.section-title {
  display: block;
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-4);
  padding-bottom: var(--spacer-3);
  border-bottom: 1rpx solid var(--border-color);
}

/* 病因列表 */
.causes-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-3);
}

.cause-item {
  position: relative;
  padding: var(--spacer-3) var(--spacer-4);
  background: var(--bg-color-container);
  border-radius: var(--radius-m);
  display: flex;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
}

.cause-probability {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  opacity: 0.1;
  border-radius: var(--radius-m);
}

.cause-name {
  font-size: var(--font-size-s);
  color: var(--text-color-primary);
  font-weight: var(--font-weight-medium);
  position: relative;
}

.cause-percent {
  font-size: var(--font-size-s);
  color: var(--health-theme-color);
  font-weight: var(--font-weight-bold);
  position: relative;
}

/* 建议列表 */
.suggestions-list, .prevention-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-3);
}

.suggestion-item, .prevention-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacer-3);
  padding: var(--spacer-3);
  background: var(--bg-color-container);
  border-radius: var(--radius-m);
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  line-height: var(--line-height-normal);
}

/* 结果操作 */
.result-actions {
  display: flex;
  gap: var(--spacer-4);
  padding: var(--spacer-4);
  background: var(--bg-color-page);
  border-radius: var(--radius-l);
  border: 1rpx solid var(--border-color);
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: var(--radius-l);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacer-2);
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  border: none;
  transition: all var(--transition-normal);
}

.action-btn.secondary {
  background: var(--bg-color-hover);
  color: var(--text-color-secondary);
  border: 1rpx solid var(--border-color);
}

.action-btn.primary {
  background: var(--health-theme-color);
  color: white;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.action-btn.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-color-container);
  padding: var(--spacer-4) var(--spacer-6);
  display: flex;
  gap: var(--spacer-4);
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: var(--z-index-fixed);
  border-top: 1rpx solid var(--border-color);
  padding-bottom: calc(var(--spacer-4) + env(safe-area-inset-bottom));
}

.bottom-actions .action-btn {
  flex: 1;
  height: 80rpx;
}

/* 响应式优化 */
@media (max-width: 350px) {
  .symptoms-grid {
    grid-template-columns: 1fr;
  }
  
  .photo-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .feed-options {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .age-options {
    flex-direction: column;
  }
  
  .result-actions {
    flex-direction: column;
  }
}