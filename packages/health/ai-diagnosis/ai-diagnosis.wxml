<!--packages/health/ai-diagnosis/ai-diagnosis.wxml-->
<view class="ai-diagnosis-container theme-health">
  
  <!-- 页面头部 -->
  <global-page-header 
    title="AI智能诊断" 
    subtitle="科学诊断，专业建议"
    theme="health"
    show-back="{{true}}"
    custom-class="ai-diagnosis-header">
  </global-page-header>

  <!-- 诊断进度指示器 -->
  <view class="diagnosis-progress">
    <view class="progress-steps">
      <view wx:for="{{diagnosisSteps}}" wx:key="id" 
            class="step-item {{currentStep >= item.step ? 'completed' : ''}} {{currentStep === item.step ? 'active' : ''}}">
        <view class="step-circle">
          <global-icon wx:if="{{currentStep > item.step}}" 
                       name="对勾" 
                       size="16" 
                       color="white"></global-icon>
          <text wx:else>{{item.step}}</text>
        </view>
        <text class="step-title">{{item.title}}</text>
      </view>
    </view>
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{(currentStep - 1) / (diagnosisSteps.length - 1) * 100}}%;"></view>
    </view>
  </view>

  <!-- 步骤1: 基本信息 -->
  <view wx:if="{{currentStep === 1}}" class="step-content basic-info-step">
    <view class="step-header">
      <global-icon name="信息" size="24" color="var(--health-theme-color)"></global-icon>
      <text class="step-title">基本信息</text>
      <text class="step-subtitle">请填写基本信息以便准确诊断</text>
    </view>
    
    <view class="form-section">
      <view class="form-group">
        <text class="form-label">鹅的品种</text>
        <picker bindchange="onBreedChange" value="{{basicInfo.breedIndex}}" range="{{breedOptions}}">
          <view class="picker-input">
            <text>{{breedOptions[basicInfo.breedIndex] || '请选择品种'}}</text>
            <global-icon name="箭头下" size="16" color="var(--text-color-tertiary)"></global-icon>
          </view>
        </picker>
      </view>
      
      <view class="form-group">
        <text class="form-label">年龄阶段</text>
        <view class="age-options">
          <view wx:for="{{ageOptions}}" wx:key="value"
                class="age-option {{basicInfo.age === item.value ? 'selected' : ''}}"
                bindtap="selectAge"
                data-age="{{item.value}}">
            <text>{{item.label}}</text>
          </view>
        </view>
      </view>
      
      <view class="form-group">
        <text class="form-label">数量</text>
        <view class="quantity-input">
          <view class="quantity-btn" bindtap="changeQuantity" data-action="minus">
            <global-icon name="减号" size="16" color="var(--text-color-secondary)"></global-icon>
          </view>
          <input class="quantity-value" 
                 type="number" 
                 value="{{basicInfo.quantity}}" 
                 bindchange="inputQuantity"
                 placeholder="请输入数量"></input>
          <view class="quantity-btn" bindtap="changeQuantity" data-action="plus">
            <global-icon name="加号" size="16" color="var(--text-color-secondary)"></global-icon>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 步骤2: 症状选择 -->
  <view wx:if="{{currentStep === 2}}" class="step-content symptoms-step">
    <view class="step-header">
      <global-icon name="症状" size="24" color="var(--health-theme-color)"></global-icon>
      <text class="step-title">症状描述</text>
      <text class="step-subtitle">请选择观察到的症状（可多选）</text>
    </view>
    
    <view class="symptoms-categories">
      <view wx:for="{{symptomCategories}}" wx:key="category" class="symptom-category">
        <view class="category-header">
          <global-icon name="{{item.icon}}" size="18" color="var(--health-theme-color)"></global-icon>
          <text class="category-title">{{item.title}}</text>
        </view>
        
        <view class="symptoms-grid">
          <view wx:for="{{item.symptoms}}" wx:key="id" wx:for-item="symptom"
                class="symptom-item {{selectedSymptoms.includes(symptom.id) ? 'selected' : ''}}"
                bindtap="toggleSymptom"
                data-symptom-id="{{symptom.id}}">
            <view class="symptom-icon">
              <global-icon name="{{symptom.icon || '症状'}}" size="16" color="{{selectedSymptoms.includes(symptom.id) ? 'white' : 'var(--text-color-secondary)'}}"></global-icon>
            </view>
            <text class="symptom-name">{{symptom.name}}</text>
            <text class="symptom-desc">{{symptom.description}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 自定义症状输入 -->
    <view class="custom-symptom">
      <text class="custom-label">其他症状描述</text>
      <textarea class="custom-textarea" 
                placeholder="请描述其他观察到的症状..."
                value="{{customSymptoms}}"
                bindchange="onCustomSymptomsChange"
                maxlength="200"
                show-confirm-bar="{{false}}"></textarea>
      <text class="char-count">{{customSymptoms.length}}/200</text>
    </view>
  </view>

  <!-- 步骤3: 环境信息 -->
  <view wx:if="{{currentStep === 3}}" class="step-content environment-step">
    <view class="step-header">
      <global-icon name="环境" size="24" color="var(--health-theme-color)"></global-icon>
      <text class="step-title">环境信息</text>
      <text class="step-subtitle">了解环境因素有助于精准诊断</text>
    </view>
    
    <view class="environment-form">
      <view class="form-row">
        <view class="form-group half">
          <text class="form-label">温度 (°C)</text>
          <input class="form-input" 
                 type="digit" 
                 value="{{environmentInfo.temperature}}"
                 bindchange="onEnvironmentChange"
                 data-field="temperature"
                 placeholder="如：25"></input>
        </view>
        <view class="form-group half">
          <text class="form-label">湿度 (%)</text>
          <input class="form-input" 
                 type="number" 
                 value="{{environmentInfo.humidity}}"
                 bindchange="onEnvironmentChange"
                 data-field="humidity"
                 placeholder="如：60"></input>
        </view>
      </view>
      
      <view class="form-group">
        <text class="form-label">饲养密度</text>
        <picker bindchange="onDensityChange" value="{{environmentInfo.densityIndex}}" range="{{densityOptions}}">
          <view class="picker-input">
            <text>{{densityOptions[environmentInfo.densityIndex] || '请选择密度'}}</text>
            <global-icon name="箭头下" size="16" color="var(--text-color-tertiary)"></global-icon>
          </view>
        </picker>
      </view>
      
      <view class="form-group">
        <text class="form-label">饲料类型</text>
        <view class="feed-options">
          <view wx:for="{{feedTypes}}" wx:key="value"
                class="feed-option {{environmentInfo.feedType === item.value ? 'selected' : ''}}"
                bindtap="selectFeedType"
                data-feed="{{item.value}}">
            <global-icon name="{{item.icon}}" size="16" color="{{environmentInfo.feedType === item.value ? 'var(--health-theme-color)' : 'var(--text-color-secondary)'}}"></global-icon>
            <text>{{item.label}}</text>
          </view>
        </view>
      </view>
      
      <view class="form-group">
        <text class="form-label">最近变化</text>
        <textarea class="form-textarea" 
                  placeholder="最近饲养环境、饲料、管理方式等是否有变化..."
                  value="{{environmentInfo.recentChanges}}"
                  bindchange="onEnvironmentChange"
                  data-field="recentChanges"
                  maxlength="150"></textarea>
      </view>
    </view>
  </view>

  <!-- 步骤4: 图片上传 -->
  <view wx:if="{{currentStep === 4}}" class="step-content photo-step">
    <view class="step-header">
      <global-icon name="照片" size="24" color="var(--health-theme-color)"></global-icon>
      <text class="step-title">上传照片</text>
      <text class="step-subtitle">清晰的照片有助于AI更准确地诊断</text>
    </view>
    
    <view class="photo-tips">
      <text class="tips-title">拍照建议：</text>
      <view class="tips-list">
        <view class="tip-item">
          <global-icon name="对勾" size="12" color="var(--success-color)"></global-icon>
          <text>光线充足，避免逆光</text>
        </view>
        <view class="tip-item">
          <global-icon name="对勾" size="12" color="var(--success-color)"></global-icon>
          <text>拍摄患病部位特写</text>
        </view>
        <view class="tip-item">
          <global-icon name="对勾" size="12" color="var(--success-color)"></global-icon>
          <text>多角度拍摄，最多9张</text>
        </view>
      </view>
    </view>
    
    <view class="photo-upload">
      <view class="photo-grid">
        <view wx:for="{{uploadedPhotos}}" wx:key="*this" class="photo-item">
          <image src="{{item}}" class="photo-image" mode="aspectFill" bindtap="previewPhoto" data-src="{{item}}"></image>
          <view class="photo-delete" bindtap="deletePhoto" data-index="{{index}}">
            <global-icon name="关闭" size="16" color="white"></global-icon>
          </view>
        </view>
        
        <view wx:if="{{uploadedPhotos.length < 9}}" class="photo-add" bindtap="addPhoto">
          <global-icon name="相机" size="32" color="var(--text-color-tertiary)"></global-icon>
          <text class="add-text">添加照片</text>
          <text class="add-count">{{uploadedPhotos.length}}/9</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 步骤5: AI分析结果 -->
  <view wx:if="{{currentStep === 5}}" class="step-content result-step">
    <view wx:if="{{diagnosisLoading}}" class="diagnosis-loading">
      <view class="loading-animation">
        <view class="ai-brain">
          <global-icon name="AI" size="48" color="var(--health-theme-color)"></global-icon>
        </view>
        <view class="loading-dots">
          <view class="dot"></view>
          <view class="dot"></view>
          <view class="dot"></view>
        </view>
      </view>
      <text class="loading-text">AI正在分析中...</text>
      <text class="loading-subtitle">请稍候，智能诊断需要一些时间</text>
    </view>
    
    <view wx:else class="diagnosis-result">
      <view class="result-header">
        <view class="result-status {{diagnosisResult.level}}">
          <global-icon name="{{diagnosisResult.level === 'high' ? '警告' : diagnosisResult.level === 'medium' ? '注意' : '正常'}}" 
                       size="24" 
                       color="white"></global-icon>
        </view>
        <view class="result-info">
          <text class="result-title">{{diagnosisResult.title}}</text>
          <text class="result-confidence">置信度: {{diagnosisResult.confidence}}%</text>
        </view>
      </view>
      
      <view class="result-details">
        <view class="detail-section">
          <text class="section-title">可能病因</text>
          <view class="causes-list">
            <view wx:for="{{diagnosisResult.causes}}" wx:key="name" class="cause-item">
              <view class="cause-probability" style="width: {{item.probability}}%; background: var(--health-theme-color);"></view>
              <text class="cause-name">{{item.name}}</text>
              <text class="cause-percent">{{item.probability}}%</text>
            </view>
          </view>
        </view>
        
        <view class="detail-section">
          <text class="section-title">治疗建议</text>
          <view class="suggestions-list">
            <view wx:for="{{diagnosisResult.suggestions}}" wx:key="*this" class="suggestion-item">
              <global-icon name="建议" size="16" color="var(--health-theme-color)"></global-icon>
              <text>{{item}}</text>
            </view>
          </view>
        </view>
        
        <view class="detail-section">
          <text class="section-title">预防措施</text>
          <view class="prevention-list">
            <view wx:for="{{diagnosisResult.prevention}}" wx:key="*this" class="prevention-item">
              <global-icon name="预防" size="16" color="var(--success-color)"></global-icon>
              <text>{{item}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="result-actions">
        <button class="action-btn secondary" bindtap="saveResult">
          <global-icon name="保存" size="16" color="var(--text-color-secondary)"></global-icon>
          <text>保存结果</text>
        </button>
        <button class="action-btn primary" bindtap="consultExpert">
          <global-icon name="专家" size="16" color="white"></global-icon>
          <text>咨询专家</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button wx:if="{{currentStep > 1}}" 
            class="action-btn secondary" 
            bindtap="prevStep">
      <text>上一步</text>
    </button>
    
    <button wx:if="{{currentStep < 5}}" 
            class="action-btn primary {{!canProceed ? 'disabled' : ''}}" 
            bindtap="nextStep"
            disabled="{{!canProceed}}">
      <text>{{currentStep === 4 ? '开始诊断' : '下一步'}}</text>
    </button>
    
    <button wx:if="{{currentStep === 5 && !diagnosisLoading}}" 
            class="action-btn primary" 
            bindtap="restartDiagnosis">
      <text>重新诊断</text>
    </button>
  </view>

</view>