/* packages/health/health/health.wxss */
@import '/styles/unified-design-tokens.wxss';

.health-container {
  padding: var(--page-padding);
  background-color: var(--bg-color-page);
  min-height: 100vh;
}

/* 页面头部 */
.health-main-header {
  background: linear-gradient(135deg, var(--health-theme-color) 0%, var(--success-color-light) 100%);
  border-radius: var(--radius-xl);
  padding: var(--spacer-8) var(--spacer-6);
  margin-bottom: var(--section-margin);
  box-shadow: var(--shadow-m);
}

/* 健康概览 */
.health-overview {
  margin-bottom: var(--section-margin);
}

.overview-title {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-4);
}

.health-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacer-4);
}

.health-stat-card {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal) var(--ease-out);
  position: relative;
  overflow: hidden;
}

.health-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: var(--health-theme-color);
}

.health-stat-card.success::before {
  background: var(--success-color);
}

.health-stat-card.warning::before {
  background: var(--warning-color);
}

.health-stat-card.info::before {
  background: var(--info-color);
}

.health-stat-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-m);
}

.stat-icon {
  width: 64rpx;
  height: 64rpx;
  background: var(--health-theme-bg);
  border-radius: var(--radius-m);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacer-3);
}

.health-stat-card.success .stat-icon {
  background: var(--success-color-focus);
}

.health-stat-card.warning .stat-icon {
  background: var(--warning-color-focus);
}

.health-stat-card.info .stat-icon {
  background: var(--info-color-focus);
}

.stat-content {
  text-align: left;
}

.stat-value {
  display: block;
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-1);
  line-height: var(--line-height-tight);
}

.stat-label {
  display: block;
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  margin-bottom: var(--spacer-2);
}

.stat-trend {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: var(--spacer-1) var(--spacer-2);
  border-radius: var(--radius-s);
}

.stat-trend.up {
  color: var(--success-color);
  background: var(--success-color-focus);
}

.stat-trend.down {
  color: var(--error-color);
  background: var(--error-color-focus);
}

.stat-trend.stable {
  color: var(--text-color-secondary);
  background: var(--bg-color-hover);
}

/* 趋势图表 */
.trend-section {
  margin-bottom: var(--section-margin);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-4);
}

.section-title {
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
}

.trend-period-selector {
  display: flex;
  background: var(--bg-color-container);
  border-radius: var(--radius-m);
  padding: var(--spacer-1);
  border: 1rpx solid var(--border-color);
}

.period-btn {
  padding: var(--spacer-2) var(--spacer-3);
  border-radius: var(--radius-s);
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  transition: all var(--transition-normal);
}

.period-btn.active {
  background: var(--health-theme-color);
  color: var(--text-color-inverse);
}

.trend-chart-container {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: var(--spacer-6);
  margin-bottom: var(--spacer-5);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: var(--radius-s);
}

.legend-color.healthy {
  background: var(--success-color);
}

.legend-color.warning {
  background: var(--warning-color);
}

.legend-color.sick {
  background: var(--error-color);
}

.trend-chart {
  display: flex;
  align-items: flex-end;
  height: 400rpx;
}

.chart-y-axis {
  display: flex;
  flex-direction: column-reverse;
  justify-content: space-between;
  height: 100%;
  margin-right: var(--spacer-3);
  width: 60rpx;
}

.y-label {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
  text-align: right;
}

.chart-content {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 100%;
  gap: var(--spacer-2);
}

.chart-day {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  height: 100%;
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  width: 100%;
  height: calc(100% - 40rpx);
  gap: 2rpx;
}

.chart-bar {
  flex: 1;
  min-height: 8rpx;
  border-radius: var(--radius-xs) var(--radius-xs) 0 0;
  transition: all var(--transition-normal);
}

.chart-date {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
  margin-top: var(--spacer-2);
  text-align: center;
}

/* 快捷功能 */
.quick-actions-section {
  margin-bottom: var(--section-margin);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-4);
}

.quick-actions-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-3);
}

.quick-action-card {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  display: flex;
  align-items: center;
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal) var(--ease-out);
}

.quick-action-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-m);
  background: var(--bg-color-hover);
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: var(--radius-m);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacer-4);
}

.action-info {
  flex: 1;
}

.action-title {
  display: block;
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-1);
}

.action-description {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  line-height: var(--line-height-normal);
}

.action-arrow {
  opacity: 0.6;
}

/* AI诊断区域 */
.ai-diagnosis-section {
  margin-bottom: var(--section-margin);
}

.ai-card {
  background: linear-gradient(135deg, var(--health-theme-color) 0%, var(--info-color) 100%);
  border-radius: var(--radius-xl);
  padding: var(--spacer-6);
  display: flex;
  align-items: center;
  box-shadow: var(--shadow-m);
  position: relative;
  overflow: hidden;
}

.ai-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-round);
}

.ai-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-m);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacer-4);
  backdrop-filter: blur(10px);
}

.ai-content {
  flex: 1;
}

.ai-title {
  display: block;
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-inverse);
  margin-bottom: var(--spacer-1);
}

.ai-description {
  font-size: var(--font-size-s);
  color: rgba(255, 255, 255, 0.9);
  line-height: var(--line-height-normal);
}

.ai-btn {
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-color-inverse);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-m);
  padding: var(--spacer-3) var(--spacer-4);
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
}

.ai-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

/* 最近记录 */
.recent-records-section {
  margin-bottom: var(--section-margin);
}

.view-all-btn {
  font-size: var(--font-size-s);
  color: var(--health-theme-color);
  font-weight: var(--font-weight-medium);
}

.records-list {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  overflow: hidden;
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.record-item {
  padding: var(--spacer-5);
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
}

.record-item:last-child {
  border-bottom: none;
}

.record-item:active {
  background: var(--bg-color-hover);
}

.record-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: var(--radius-m);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacer-4);
}

.record-icon.checkup {
  background: var(--info-color);
}

.record-icon.treatment {
  background: var(--warning-color);
}

.record-icon.vaccination {
  background: var(--success-color);
}

.record-content {
  flex: 1;
}

.record-title {
  display: block;
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-1);
}

.record-description {
  display: block;
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  margin-bottom: var(--spacer-1);
  line-height: var(--line-height-normal);
}

.record-time {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

.record-status {
  padding: var(--spacer-1) var(--spacer-3);
  border-radius: var(--radius-s);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.record-status.completed {
  background: var(--success-color-focus);
  color: var(--success-color);
}

.record-status.pending {
  background: var(--warning-color-focus);
  color: var(--warning-color);
}

.record-status.scheduled {
  background: var(--info-color-focus);
  color: var(--info-color);
}

/* 空状态 */
.empty-records {
  text-align: center;
  padding: var(--spacer-16) var(--spacer-8);
  color: var(--text-color-tertiary);
}

.empty-text {
  display: block;
  font-size: var(--font-size-m);
  margin: var(--spacer-4) 0 var(--spacer-6);
}

.create-record-btn {
  background: var(--health-theme-color);
  color: var(--text-color-inverse);
  border: none;
  border-radius: var(--radius-m);
  padding: var(--spacer-3) var(--spacer-6);
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-medium);
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacer-16);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacer-4);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-color);
  border-top: 4rpx solid var(--health-theme-color);
  border-radius: var(--radius-round);
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: var(--font-size-m);
  color: var(--text-color-tertiary);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式优化 */
@media (max-width: 350px) {
  .health-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-content {
    gap: var(--spacer-1);
  }
  
  .quick-action-card {
    padding: var(--spacer-4);
  }
  
  .action-icon {
    width: 64rpx;
    height: 64rpx;
  }
}