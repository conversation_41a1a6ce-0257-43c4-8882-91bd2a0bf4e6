<!--packages/health/health/health.wxml-->
<view class="health-container theme-health">
  
  <!-- 页面头部 -->
  <global-page-header 
    title="健康管理" 
    subtitle="智能监测，科学养殖"
    theme="health"
    size="large"
    custom-class="health-main-header">
  </global-page-header>

  <!-- 健康概览统计 -->
  <view class="health-overview">
    <view class="overview-title">
      <global-icon name="健康" size="16" color="var(--health-theme-color)"></global-icon>
      <text>健康概览</text>
    </view>
    
    <view class="health-stats-grid">
      <view class="health-stat-card primary">
        <view class="stat-icon">
          <global-icon name="体温" size="24" color="var(--health-theme-color)"></global-icon>
        </view>
        <view class="stat-content">
          <text class="stat-value">{{healthData.temperature}}°C</text>
          <text class="stat-label">平均体温</text>
          <text class="stat-trend {{healthData.temperatureTrend}}">
            {{healthData.temperatureTrend === 'up' ? '↗' : healthData.temperatureTrend === 'down' ? '↘' : '→'}} 
            {{healthData.temperatureChange}}°C
          </text>
        </view>
      </view>
      
      <view class="health-stat-card success">
        <view class="stat-icon">
          <global-icon name="鹅" size="24" color="var(--success-color)"></global-icon>
        </view>
        <view class="stat-content">
          <text class="stat-value">{{healthData.healthyCount}}</text>
          <text class="stat-label">健康鹅数</text>
          <text class="stat-trend up">↗ +{{healthData.healthyIncrease}}</text>
        </view>
      </view>
      
      <view class="health-stat-card warning">
        <view class="stat-icon">
          <global-icon name="警告" size="24" color="var(--warning-color)"></global-icon>
        </view>
        <view class="stat-content">
          <text class="stat-value">{{healthData.warningCount}}</text>
          <text class="stat-label">预警数量</text>
          <text class="stat-trend {{healthData.warningTrend}}">
            {{healthData.warningTrend === 'up' ? '↗' : '↘'}} 
            {{healthData.warningChange}}
          </text>
        </view>
      </view>
      
      <view class="health-stat-card info">
        <view class="stat-icon">
          <global-icon name="诊断" size="24" color="var(--info-color)"></global-icon>
        </view>
        <view class="stat-content">
          <text class="stat-value">{{healthData.diagnosisCount}}</text>
          <text class="stat-label">今日诊断</text>
          <text class="stat-trend up">↗ +{{healthData.diagnosisIncrease}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 健康趋势图表 -->
  <view class="trend-section">
    <view class="section-header">
      <text class="section-title">健康趋势</text>
      <view class="trend-period-selector">
        <view wx:for="{{trendPeriods}}" wx:key="value"
              class="period-btn {{currentPeriod === item.value ? 'active' : ''}}"
              bindtap="selectPeriod"
              data-period="{{item.value}}">
          <text>{{item.label}}</text>
        </view>
      </view>
    </view>
    
    <view class="trend-chart-container">
      <view class="chart-legend">
        <view class="legend-item">
          <view class="legend-color healthy"></view>
          <text>健康</text>
        </view>
        <view class="legend-item">
          <view class="legend-color warning"></view>
          <text>预警</text>
        </view>
        <view class="legend-item">
          <view class="legend-color sick"></view>
          <text>患病</text>
        </view>
      </view>
      
      <view class="trend-chart">
        <view class="chart-y-axis">
          <text wx:for="{{chartYLabels}}" wx:key="*this" class="y-label">{{item}}</text>
        </view>
        <view class="chart-content">
          <view wx:for="{{healthTrendData}}" wx:key="date" class="chart-day">
            <view class="chart-bars">
              <view class="chart-bar healthy" 
                    style="height: {{item.healthy}}%; background: var(--success-color);"></view>
              <view class="chart-bar warning" 
                    style="height: {{item.warning}}%; background: var(--warning-color);"></view>
              <view class="chart-bar sick" 
                    style="height: {{item.sick}}%; background: var(--error-color);"></view>
            </view>
            <text class="chart-date">{{item.dateLabel}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷功能 -->
  <view class="quick-actions-section">
    <view class="section-title">
      <global-icon name="功能" size="16" color="var(--health-theme-color)"></global-icon>
      <text>快捷功能</text>
    </view>
    
    <view class="quick-actions-grid">
      <view wx:for="{{quickActions}}" wx:key="id"
            class="quick-action-card"
            bindtap="onQuickAction"
            data-action="{{item}}">
        <view class="action-icon" style="background: {{item.bgColor}};">
          <global-icon name="{{item.icon || '健康'}}" size="24" color="{{item.iconColor || 'var(--health-theme-color)'}}"></global-icon>
        </view>
        <view class="action-info">
          <text class="action-title">{{item.title}}</text>
          <text class="action-description">{{item.description}}</text>
        </view>
        <view class="action-arrow">
          <global-icon name="箭头右" size="16" color="var(--text-color-tertiary)"></global-icon>
        </view>
      </view>
    </view>
  </view>

  <!-- AI诊断入口 -->
  <view class="ai-diagnosis-section">
    <view class="ai-card">
      <view class="ai-icon">
        <global-icon name="AI" size="32" color="white"></global-icon>
      </view>
      <view class="ai-content">
        <text class="ai-title">AI智能诊断</text>
        <text class="ai-description">基于症状快速诊断，提供专业建议</text>
      </view>
      <button class="ai-btn" bindtap="startAIDiagnosis">
        <text>开始诊断</text>
        <global-icon name="箭头右" size="16" color="white"></global-icon>
      </button>
    </view>
  </view>

  <!-- 最近记录 -->
  <view class="recent-records-section">
    <view class="section-header">
      <text class="section-title">最近记录</text>
      <text class="view-all-btn" bindtap="viewAllRecords">查看全部</text>
    </view>
    
    <view class="records-list">
      <view wx:for="{{recentRecords}}" wx:key="id" 
            class="record-item"
            bindtap="viewRecordDetail"
            data-record="{{item}}">
        <view class="record-icon {{item.type}}">
          <global-icon name="{{item.iconName}}" size="20" color="white"></global-icon>
        </view>
        <view class="record-content">
          <text class="record-title">{{item.title}}</text>
          <text class="record-description">{{item.description}}</text>
          <text class="record-time">{{item.timeLabel}}</text>
        </view>
        <view class="record-status {{item.status}}">
          <text>{{item.statusText}}</text>
        </view>
      </view>
    </view>
    
    <view wx:if="{{recentRecords.length === 0}}" class="empty-records">
      <global-icon name="记录" size="48" color="var(--text-color-tertiary)"></global-icon>
      <text class="empty-text">暂无健康记录</text>
      <button class="create-record-btn" bindtap="createRecord">
        <text>添加记录</text>
      </button>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载健康数据中...</text>
    </view>
  </view>

</view>