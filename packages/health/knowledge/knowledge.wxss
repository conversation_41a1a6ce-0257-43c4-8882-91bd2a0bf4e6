/* packages/health/knowledge/knowledge.wxss */
@import '/styles/unified-design-tokens.wxss';

.knowledge-container {
  background-color: var(--bg-color-page);
  min-height: 100vh;
  padding-bottom: var(--spacer-16);
}

/* 页面头部 */
.knowledge-header {
  background: linear-gradient(135deg, var(--health-theme-color) 0%, var(--success-color) 100%);
  border-radius: var(--radius-xl);
  margin: var(--page-padding);
  margin-bottom: var(--section-margin);
  box-shadow: var(--shadow-m);
}

/* 搜索区域 */
.search-section {
  padding: 0 var(--page-padding);
  margin-bottom: var(--section-margin);
  position: relative;
}

.search-bar {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-4) var(--spacer-5);
  display: flex;
  align-items: center;
  gap: var(--spacer-3);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
}

.search-bar:focus-within {
  border-color: var(--health-theme-color);
  box-shadow: 0 0 0 4rpx var(--health-theme-color-focus);
}

.search-input {
  flex: 1;
  font-size: var(--font-size-m);
  color: var(--text-color-primary);
  background: transparent;
  border: none;
}

.search-input::placeholder {
  color: var(--text-color-placeholder);
}

.search-clear {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-color-hover);
  border-radius: var(--radius-round);
  transition: all var(--transition-normal);
}

.search-clear:active {
  background: var(--border-color);
  transform: scale(0.9);
}

/* 搜索建议 */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: var(--page-padding);
  right: var(--page-padding);
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  border: 1rpx solid var(--border-color);
  box-shadow: var(--shadow-m);
  z-index: var(--z-index-dropdown);
  max-height: 400rpx;
  overflow-y: auto;
  margin-top: var(--spacer-2);
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-3);
  padding: var(--spacer-4) var(--spacer-5);
  border-bottom: 1rpx solid var(--border-color);
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  transition: background-color var(--transition-normal);
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:active {
  background: var(--bg-color-hover);
}

/* 快速导航 */
.quick-nav {
  padding: 0 var(--page-padding);
  margin-bottom: var(--section-margin);
}

.nav-scroll {
  width: 100%;
}

.nav-list {
  display: flex;
  gap: var(--spacer-3);
  padding-bottom: var(--spacer-2);
}

.nav-item {
  flex-shrink: 0;
  width: 160rpx;
  padding: var(--spacer-4);
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  border: 1rpx solid var(--border-color);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacer-2);
  transition: all var(--transition-normal);
  position: relative;
}

.nav-item:active {
  transform: scale(0.98);
}

.nav-item.active {
  background: var(--health-theme-color);
  border-color: var(--health-theme-color);
  color: white;
  box-shadow: var(--shadow-m);
}

.nav-icon {
  width: 64rpx;
  height: 64rpx;
  background: var(--health-theme-color-focus);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-item.active .nav-icon {
  background: rgba(255, 255, 255, 0.2);
}

.nav-title {
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  text-align: center;
}

.nav-item.active .nav-title {
  color: white;
}

.nav-count {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: var(--error-color);
  color: white;
  font-size: var(--font-size-xs);
  padding: var(--spacer-1) var(--spacer-2);
  border-radius: var(--radius-round);
  min-width: 32rpx;
  text-align: center;
  font-weight: var(--font-weight-bold);
}

/* 内容区域 */
.content-section {
  padding: 0 var(--page-padding);
}

/* 热门推荐 */
.featured-articles {
  margin-bottom: var(--section-margin);
}

.section-header {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  margin-bottom: var(--spacer-4);
  padding: var(--spacer-4) var(--spacer-5);
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  border: 1rpx solid var(--border-color);
}

.section-title {
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  margin-right: var(--spacer-2);
}

.section-subtitle {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  flex: 1;
}

.featured-swiper {
  width: 100%;
  height: 400rpx;
  border-radius: var(--radius-l);
  overflow: hidden;
}

.featured-item {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.featured-image {
  width: 100%;
  height: 100%;
}

.featured-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: var(--spacer-8) var(--spacer-6) var(--spacer-6);
  color: white;
}

.featured-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-2);
}

.featured-title {
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-normal);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.featured-summary {
  font-size: var(--font-size-s);
  color: rgba(255, 255, 255, 0.8);
  line-height: var(--line-height-normal);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.featured-meta {
  display: flex;
  gap: var(--spacer-4);
  margin-top: var(--spacer-2);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-1);
  font-size: var(--font-size-xs);
  color: rgba(255, 255, 255, 0.8);
}

/* 搜索结果头部 */
.search-result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-4);
  padding: var(--spacer-4) var(--spacer-5);
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  border: 1rpx solid var(--border-color);
}

.result-count {
  font-size: var(--font-size-m);
  color: var(--text-color-secondary);
}

.sort-picker {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  padding: var(--spacer-2) var(--spacer-3);
  background: var(--bg-color-hover);
  border-radius: var(--radius-m);
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

/* 文章列表 */
.articles-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-4);
  margin-bottom: var(--spacer-6);
}

.article-item {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-5);
  display: flex;
  gap: var(--spacer-4);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
}

.article-item:active {
  transform: scale(0.99);
  box-shadow: var(--shadow-m);
}

.article-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: var(--radius-m);
  flex-shrink: 0;
}

.article-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 150rpx;
}

.article-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-2);
}

.article-category {
  display: flex;
  align-items: center;
  gap: var(--spacer-1);
  background: var(--health-theme-color-focus);
  padding: var(--spacer-1) var(--spacer-2);
  border-radius: var(--radius-s);
  font-size: var(--font-size-xs);
  color: var(--health-theme-color);
  font-weight: var(--font-weight-medium);
}

.new-badge {
  background: var(--error-color);
  color: white;
  font-size: var(--font-size-xs);
  padding: var(--spacer-1) var(--spacer-2);
  border-radius: var(--radius-s);
  font-weight: var(--font-weight-bold);
}

.article-title {
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacer-2);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.article-summary {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacer-3);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-left {
  display: flex;
  gap: var(--spacer-3);
}

.article-meta .meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-1);
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

.article-difficulty {
  display: flex;
  align-items: center;
  gap: var(--spacer-1);
  padding: var(--spacer-1) var(--spacer-2);
  border-radius: var(--radius-s);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.article-difficulty.easy {
  background: var(--success-color-focus);
  color: var(--success-color);
}

.article-difficulty.medium {
  background: var(--warning-color-focus);
  color: var(--warning-color);
}

.article-difficulty.hard {
  background: var(--error-color-focus);
  color: var(--error-color);
}

.article-bookmark {
  position: absolute;
  top: var(--spacer-4);
  right: var(--spacer-4);
  width: 64rpx;
  height: 64rpx;
  background: var(--bg-color-hover);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
}

.article-bookmark:active {
  transform: scale(0.9);
}

.article-bookmark.active {
  background: var(--warning-color-focus);
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: var(--spacer-6);
  font-size: var(--font-size-m);
  color: var(--health-theme-color);
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
}

.load-more:active {
  background: var(--health-theme-color-focus);
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacer-2);
}

.loading-spinner-small {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid var(--border-color);
  border-top: 3rpx solid var(--health-theme-color);
  border-radius: var(--radius-round);
  animation: spin 1s linear infinite;
}

.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacer-2);
  padding: var(--spacer-4);
  font-size: var(--font-size-s);
  color: var(--text-color-tertiary);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacer-16) var(--spacer-6);
  text-align: center;
}

.empty-icon {
  margin-bottom: var(--spacer-6);
  opacity: 0.6;
}

.empty-title {
  font-size: var(--font-size-xl);
  color: var(--text-color-primary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacer-2);
}

.empty-subtitle {
  font-size: var(--font-size-m);
  color: var(--text-color-secondary);
  margin-bottom: var(--spacer-6);
  line-height: var(--line-height-normal);
}

.empty-action {
  background: var(--health-theme-color);
  color: white;
  border: none;
  border-radius: var(--radius-l);
  padding: var(--spacer-4) var(--spacer-8);
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
}

/* 浮动操作按钮 */
.fab-container {
  position: fixed;
  bottom: var(--spacer-8);
  right: var(--spacer-8);
  z-index: var(--z-index-fab);
}

.fab-btn {
  width: 112rpx;
  height: 112rpx;
  background: var(--health-theme-color);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-l);
  transition: all var(--transition-normal);
  margin-bottom: var(--spacer-3);
}

.fab-btn:active {
  transform: scale(0.9);
}

.fab-btn.main-fab {
  background: var(--health-theme-color);
  margin-bottom: 0;
}

.fab-menu {
  animation: fabMenuShow 0.3s ease-out;
}

@keyframes fabMenuShow {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fab-menu .fab-btn {
  width: 96rpx;
  height: 96rpx;
  background: var(--success-color);
}

.fab-menu .fab-btn:nth-child(2) {
  background: var(--warning-color);
}

.fab-menu .fab-btn:nth-child(3) {
  background: var(--primary-color);
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
}

.loading-content {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-8);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacer-4);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-color);
  border-top: 4rpx solid var(--health-theme-color);
  border-radius: var(--radius-round);
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: var(--font-size-m);
  color: var(--text-color-tertiary);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式优化 */
@media (max-width: 350px) {
  .article-item {
    flex-direction: column;
  }
  
  .article-image {
    width: 100%;
    height: 200rpx;
  }
  
  .nav-item {
    width: 120rpx;
    padding: var(--spacer-3);
  }
  
  .nav-icon {
    width: 48rpx;
    height: 48rpx;
  }
  
  .featured-swiper {
    height: 300rpx;
  }
  
  .search-result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacer-2);
  }
  
  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacer-2);
  }
  
  .fab-container {
    bottom: var(--spacer-6);
    right: var(--spacer-6);
  }
}

/* 安全区域适配 */
.fab-container {
  bottom: calc(var(--spacer-8) + env(safe-area-inset-bottom));
}