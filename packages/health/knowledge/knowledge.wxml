<!--packages/health/knowledge/knowledge.wxml-->
<view class="knowledge-container theme-health">
  
  <!-- 页面头部 -->
  <global-page-header 
    title="健康知识库" 
    subtitle="专业知识，科学养殖"
    theme="health"
    show-back="{{true}}"
    custom-class="knowledge-header">
  </global-page-header>

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <global-icon name="搜索" size="20" color="var(--text-color-tertiary)"></global-icon>
      <input class="search-input" 
             placeholder="搜索疾病、症状、预防知识..."
             value="{{searchKeyword}}"
             bindchange="onSearchChange"
             bindinput="onSearchInput"
             bindconfirm="confirmSearch"
             focus="{{searchFocused}}"
             confirm-type="search"></input>
      <view wx:if="{{searchKeyword}}" class="search-clear" bindtap="clearSearch">
        <global-icon name="关闭" size="16" color="var(--text-color-tertiary)"></global-icon>
      </view>
    </view>
    
    <!-- 搜索建议 -->
    <view wx:if="{{searchSuggestions.length > 0 && searchFocused}}" class="search-suggestions">
      <view wx:for="{{searchSuggestions}}" wx:key="*this" 
            class="suggestion-item"
            bindtap="selectSuggestion"
            data-suggestion="{{item}}">
        <global-icon name="搜索" size="14" color="var(--text-color-tertiary)"></global-icon>
        <text>{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 快速导航 -->
  <view wx:if="{{!searchKeyword}}" class="quick-nav">
    <scroll-view class="nav-scroll" scroll-x="{{true}}" show-scrollbar="{{false}}">
      <view class="nav-list">
        <view wx:for="{{categories}}" wx:key="id" 
              class="nav-item {{selectedCategory === item.id ? 'active' : ''}}"
              bindtap="selectCategory"
              data-category="{{item.id}}">
          <view class="nav-icon">
            <global-icon name="{{item.icon}}" size="20" color="{{selectedCategory === item.id ? 'white' : 'var(--health-theme-color)'}}"></global-icon>
          </view>
          <text class="nav-title">{{item.name}}</text>
          <view wx:if="{{item.count > 0}}" class="nav-count">{{item.count}}</view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 热门推荐/搜索结果 -->
  <view class="content-section">
    <!-- 热门推荐 -->
    <view wx:if="{{!searchKeyword}}" class="featured-articles">
      <view class="section-header">
        <global-icon name="热门" size="18" color="var(--health-theme-color)"></global-icon>
        <text class="section-title">热门推荐</text>
        <text class="section-subtitle">最受关注的健康知识</text>
      </view>
      
      <swiper class="featured-swiper" 
              indicator-dots="{{true}}"
              indicator-color="rgba(255,255,255,0.5)"
              indicator-active-color="white"
              autoplay="{{true}}"
              interval="4000"
              duration="500"
              circular="{{true}}">
        <swiper-item wx:for="{{featuredArticles}}" wx:key="id">
          <view class="featured-item" bindtap="openArticle" data-article="{{item}}">
            <image src="{{item.banner}}" class="featured-image" mode="aspectFill"></image>
            <view class="featured-overlay">
              <view class="featured-content">
                <text class="featured-title">{{item.title}}</text>
                <text class="featured-summary">{{item.summary}}</text>
                <view class="featured-meta">
                  <view class="meta-item">
                    <global-icon name="眼睛" size="12" color="rgba(255,255,255,0.8)"></global-icon>
                    <text>{{item.views}}</text>
                  </view>
                  <view class="meta-item">
                    <global-icon name="时间" size="12" color="rgba(255,255,255,0.8)"></global-icon>
                    <text>{{item.publishTime}}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 文章列表 -->
    <view class="articles-section">
      <view wx:if="{{searchKeyword}}" class="search-result-header">
        <text class="result-count">找到 {{filteredArticles.length}} 篇相关文章</text>
        <view class="sort-options">
          <picker bindchange="onSortChange" value="{{sortIndex}}" range="{{sortOptions}}" range-key="label">
            <view class="sort-picker">
              <text>{{sortOptions[sortIndex].label}}</text>
              <global-icon name="箭头下" size="14" color="var(--text-color-tertiary)"></global-icon>
            </view>
          </picker>
        </view>
      </view>
      
      <view class="articles-list">
        <view wx:for="{{displayArticles}}" wx:key="id" 
              class="article-item"
              bindtap="openArticle"
              data-article="{{item}}">
          <image src="{{item.thumbnail}}" class="article-image" mode="aspectFill" lazy-load="{{true}}"></image>
          
          <view class="article-content">
            <view class="article-header">
              <view class="article-category">
                <global-icon name="{{item.categoryIcon}}" size="12" color="var(--health-theme-color)"></global-icon>
                <text>{{item.categoryName}}</text>
              </view>
              <view wx:if="{{item.isNew}}" class="new-badge">新</view>
            </view>
            
            <text class="article-title">{{item.title}}</text>
            <text class="article-summary">{{item.summary}}</text>
            
            <view class="article-meta">
              <view class="meta-left">
                <view class="meta-item">
                  <global-icon name="眼睛" size="12" color="var(--text-color-tertiary)"></global-icon>
                  <text>{{item.views}}</text>
                </view>
                <view class="meta-item">
                  <global-icon name="点赞" size="12" color="var(--text-color-tertiary)"></global-icon>
                  <text>{{item.likes}}</text>
                </view>
                <view class="meta-item">
                  <global-icon name="时间" size="12" color="var(--text-color-tertiary)"></global-icon>
                  <text>{{item.publishTime}}</text>
                </view>
              </view>
              
              <view class="article-difficulty {{item.difficulty}}">
                <global-icon name="{{item.difficulty === 'easy' ? '简单' : item.difficulty === 'medium' ? '中等' : '困难'}}" 
                             size="12" 
                             color="{{item.difficulty === 'easy' ? 'var(--success-color)' : item.difficulty === 'medium' ? 'var(--warning-color)' : 'var(--error-color)'}}"></global-icon>
                <text>{{item.difficulty === 'easy' ? '入门' : item.difficulty === 'medium' ? '进阶' : '专业'}}</text>
              </view>
            </view>
          </view>
          
          <view class="article-bookmark {{item.isBookmarked ? 'active' : ''}}" 
                bindtap="toggleBookmark" 
                data-article-id="{{item.id}}"
                catchtap="true">
            <global-icon name="{{item.isBookmarked ? '收藏实心' : '收藏空心'}}" 
                         size="16" 
                         color="{{item.isBookmarked ? 'var(--warning-color)' : 'var(--text-color-tertiary)'}}"></global-icon>
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view wx:if="{{hasMore}}" class="load-more" bindtap="loadMoreArticles">
        <view wx:if="{{loadingMore}}" class="loading-more">
          <view class="loading-spinner-small"></view>
          <text>加载中...</text>
        </view>
        <text wx:else>点击加载更多</text>
      </view>
      
      <!-- 无更多内容 -->
      <view wx:if="{{!hasMore && displayArticles.length > 0}}" class="no-more">
        <global-icon name="完成" size="16" color="var(--text-color-tertiary)"></global-icon>
        <text>没有更多内容了</text>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{displayArticles.length === 0 && !loading}}" class="empty-state">
      <view class="empty-icon">
        <global-icon name="{{searchKeyword ? '搜索空' : '文章空'}}" size="80" color="var(--text-color-tertiary)"></global-icon>
      </view>
      <text class="empty-title">{{searchKeyword ? '没有找到相关文章' : '暂无文章'}}</text>
      <text class="empty-subtitle">{{searchKeyword ? '试试其他关键词吧' : '更多精彩内容即将上线'}}</text>
      
      <button wx:if="{{searchKeyword}}" class="empty-action" bindtap="clearSearch">
        <text>浏览全部文章</text>
      </button>
    </view>
  </view>

  <!-- 浮动操作按钮 -->
  <view class="fab-container">
    <view class="fab-btn main-fab" bindtap="toggleFabMenu">
      <global-icon name="{{fabMenuOpen ? '关闭' : '菜单'}}" size="24" color="white"></global-icon>
    </view>
    
    <view wx:if="{{fabMenuOpen}}" class="fab-menu">
      <view class="fab-btn" bindtap="goToBookmarks">
        <global-icon name="收藏" size="20" color="white"></global-icon>
      </view>
      <view class="fab-btn" bindtap="goToHistory">
        <global-icon name="历史" size="20" color="white"></global-icon>
      </view>
      <view class="fab-btn" bindtap="submitFeedback">
        <global-icon name="反馈" size="20" color="white"></global-icon>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

</view>