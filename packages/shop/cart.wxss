/* packages/shop/cart.wxss */
@import '/styles/unified-design-tokens.wxss';

.cart-container {
  background-color: var(--bg-color-page);
  min-height: 100vh;
  padding-bottom: 140rpx; /* 为底部结算栏留空间 */
}

/* 页面头部 */
.cart-header {
  background: linear-gradient(135deg, var(--shop-theme-color) 0%, var(--error-color-light) 100%);
  border-radius: var(--radius-xl);
  margin: var(--page-padding);
  margin-bottom: var(--section-margin);
  box-shadow: var(--shadow-m);
}

/* 购物车内容 */
.cart-content {
  padding: 0 var(--page-padding);
}

/* 全选控制 */
.select-all-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--bg-color-container);
  padding: var(--spacer-4) var(--spacer-6);
  border-radius: var(--radius-l);
  margin-bottom: var(--spacer-3);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.select-all-control {
  display: flex;
  align-items: center;
  gap: var(--spacer-3);
  transition: opacity var(--transition-normal);
}

.select-all-control:active {
  opacity: 0.7;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-s);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
}

.checkbox.checked {
  background: var(--shop-theme-color);
  border-color: var(--shop-theme-color);
}

.select-all-text {
  font-size: var(--font-size-m);
  color: var(--text-color-primary);
  font-weight: var(--font-weight-medium);
}

.manage-btn {
  font-size: var(--font-size-s);
  color: var(--shop-theme-color);
  padding: var(--spacer-2) var(--spacer-4);
  border-radius: var(--radius-m);
  border: 1rpx solid var(--shop-theme-color);
  transition: all var(--transition-normal);
}

.manage-btn.active {
  background: var(--shop-theme-color);
  color: var(--text-color-inverse);
}

/* 购物车商品列表 */
.cart-items-list {
  margin-bottom: var(--spacer-6);
}

.cart-item {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  margin-bottom: var(--spacer-3);
  padding: var(--spacer-5);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
  display: flex;
  align-items: flex-start;
  gap: var(--spacer-4);
  transition: all var(--transition-normal);
}

.cart-item:active {
  background: var(--bg-color-hover);
}

.item-checkbox {
  padding: var(--spacer-2);
  margin-top: var(--spacer-2);
}

.item-checkbox .checkbox {
  width: 36rpx;
  height: 36rpx;
}

.item-content {
  flex: 1;
  display: flex;
  gap: var(--spacer-4);
}

.item-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: var(--radius-m);
  flex-shrink: 0;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 160rpx;
}

.item-title {
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacer-2);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.item-specs {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  margin-bottom: var(--spacer-3);
  background: var(--bg-color-hover);
  padding: var(--spacer-1) var(--spacer-2);
  border-radius: var(--radius-s);
  align-self: flex-start;
}

.item-price-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: var(--spacer-2);
}

.price-info {
  display: flex;
  align-items: baseline;
  gap: var(--spacer-2);
}

.current-price {
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-bold);
  color: var(--shop-theme-color);
}

.original-price {
  font-size: var(--font-size-s);
  color: var(--text-color-tertiary);
  text-decoration: line-through;
}

/* 数量控制 */
.quantity-controls {
  display: flex;
  align-items: center;
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-m);
  overflow: hidden;
  background: var(--bg-color-container);
}

.quantity-btn {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-color-hover);
  transition: all var(--transition-normal);
}

.quantity-btn:active {
  background: var(--border-color);
}

.quantity-btn.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.quantity-input {
  width: 80rpx;
  height: 56rpx;
  text-align: center;
  font-size: var(--font-size-s);
  border: none;
  background: var(--bg-color-container);
  border-left: 1rpx solid var(--border-color);
  border-right: 1rpx solid var(--border-color);
}

.stock-warning {
  display: flex;
  align-items: center;
  gap: var(--spacer-1);
  font-size: var(--font-size-xs);
  color: var(--warning-color);
  padding: var(--spacer-1) var(--spacer-2);
  background: var(--warning-color-focus);
  border-radius: var(--radius-s);
  align-self: flex-start;
}

.delete-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--error-color-focus);
  border-radius: var(--radius-m);
  margin-top: var(--spacer-2);
  transition: all var(--transition-normal);
}

.delete-btn:active {
  background: var(--error-color);
  transform: scale(0.9);
}

/* 推荐商品 */
.recommend-section {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-5) var(--spacer-6);
  margin-bottom: var(--spacer-6);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
}

.recommend-header {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  margin-bottom: var(--spacer-4);
}

.recommend-title {
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
}

.recommend-scroll {
  width: 100%;
}

.recommend-list {
  display: flex;
  gap: var(--spacer-4);
  padding-bottom: var(--spacer-2);
}

.recommend-item {
  width: 180rpx;
  flex-shrink: 0;
  position: relative;
  transition: transform var(--transition-normal);
}

.recommend-item:active {
  transform: scale(0.95);
}

.recommend-image {
  width: 100%;
  height: 180rpx;
  border-radius: var(--radius-m);
  margin-bottom: var(--spacer-2);
}

.recommend-info {
  padding: 0 var(--spacer-1);
}

.recommend-item .recommend-title {
  display: block;
  font-size: var(--font-size-s);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-1);
  line-height: var(--line-height-normal);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.recommend-price {
  font-size: var(--font-size-s);
  color: var(--shop-theme-color);
  font-weight: var(--font-weight-bold);
}

.add-recommend-btn {
  position: absolute;
  bottom: 80rpx;
  right: var(--spacer-2);
  width: 48rpx;
  height: 48rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-s);
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
}

.add-recommend-btn:active {
  transform: scale(0.9);
  background: var(--shop-theme-color);
}

/* 底部结算栏 */
.checkout-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-color-container);
  padding: var(--spacer-4) var(--spacer-6);
  display: flex;
  align-items: center;
  gap: var(--spacer-4);
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: var(--z-index-fixed);
  border-top: 1rpx solid var(--border-color);
  padding-bottom: calc(var(--spacer-4) + env(safe-area-inset-bottom));
}

.checkout-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--spacer-4);
}

.select-all-mini {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.select-all-mini .checkbox {
  width: 32rpx;
  height: 32rpx;
}

.price-info {
  flex: 1;
  text-align: right;
}

.total-section {
  display: flex;
  align-items: baseline;
  justify-content: flex-end;
  gap: var(--spacer-1);
  margin-bottom: var(--spacer-1);
}

.total-label {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.total-price {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--shop-theme-color);
}

.discount-info {
  font-size: var(--font-size-xs);
  color: var(--success-color);
}

.checkout-btn {
  width: 200rpx;
  height: 80rpx;
  background: var(--shop-theme-color);
  color: var(--text-color-inverse);
  border: none;
  border-radius: var(--radius-l);
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
}

.checkout-btn:active {
  background: var(--shop-theme-color);
  opacity: 0.8;
  transform: scale(0.98);
}

.checkout-btn.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 空购物车状态 */
.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacer-16) var(--spacer-8);
  text-align: center;
}

.empty-icon {
  margin-bottom: var(--spacer-6);
  opacity: 0.6;
}

.empty-text {
  font-size: var(--font-size-xl);
  color: var(--text-color-primary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacer-2);
}

.empty-subtitle {
  font-size: var(--font-size-m);
  color: var(--text-color-secondary);
  margin-bottom: var(--spacer-8);
  line-height: var(--line-height-normal);
}

.go-shopping-btn {
  background: var(--shop-theme-color);
  color: var(--text-color-inverse);
  border: none;
  border-radius: var(--radius-l);
  padding: var(--spacer-4) var(--spacer-8);
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacer-8);
}

/* 空状态推荐 */
.empty-recommend {
  width: 100%;
  max-width: 600rpx;
}

.empty-recommend-title {
  font-size: var(--font-size-l);
  color: var(--text-color-primary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacer-4);
  text-align: center;
}

.empty-recommend-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacer-4);
}

.empty-recommend-item {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-4);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
}

.empty-recommend-item:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-m);
}

.empty-recommend-image {
  width: 100%;
  height: 200rpx;
  border-radius: var(--radius-m);
  margin-bottom: var(--spacer-3);
}

.empty-recommend-name {
  display: block;
  font-size: var(--font-size-s);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-2);
  line-height: var(--line-height-normal);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.empty-recommend-price {
  font-size: var(--font-size-s);
  color: var(--shop-theme-color);
  font-weight: var(--font-weight-bold);
}

/* 删除确认弹窗 */
.delete-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
}

.delete-modal {
  background: var(--bg-color-container);
  border-radius: var(--radius-xl);
  padding: var(--spacer-8);
  margin: var(--spacer-6);
  max-width: 500rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  animation: modalShow 0.3s ease-out;
}

@keyframes modalShow {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal-icon {
  margin-bottom: var(--spacer-4);
}

.modal-title {
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-3);
}

.modal-message {
  font-size: var(--font-size-m);
  color: var(--text-color-secondary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacer-6);
}

.modal-actions {
  display: flex;
  gap: var(--spacer-4);
  width: 100%;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: var(--radius-l);
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  border: none;
  transition: all var(--transition-normal);
}

.modal-btn.cancel {
  background: var(--bg-color-hover);
  color: var(--text-color-secondary);
  border: 1rpx solid var(--border-color);
}

.modal-btn.confirm {
  background: var(--error-color);
  color: var(--text-color-inverse);
}

.modal-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
}

.loading-content {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-8);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacer-4);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-color);
  border-top: 4rpx solid var(--shop-theme-color);
  border-radius: var(--radius-round);
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: var(--font-size-m);
  color: var(--text-color-tertiary);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式优化 */
@media (max-width: 350px) {
  .cart-item {
    padding: var(--spacer-4);
  }
  
  .item-image {
    width: 120rpx;
    height: 120rpx;
  }
  
  .item-info {
    min-height: 120rpx;
  }
  
  .empty-recommend-grid {
    grid-template-columns: 1fr;
  }
  
  .checkout-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacer-2);
  }
  
  .checkout-btn {
    width: 160rpx;
  }
}