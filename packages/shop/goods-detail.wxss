/* packages/shop/goods-detail.wxss */
@import '/styles/unified-design-tokens.wxss';

.goods-detail-container {
  background-color: var(--bg-color-page);
  min-height: 100vh;
  padding-bottom: 120rpx; /* 为底部操作栏留空间 */
}

/* 商品图片轮播 */
.product-gallery {
  position: relative;
  width: 100%;
  height: 750rpx;
  background: var(--bg-color-container);
}

.product-swiper {
  width: 100%;
  height: 100%;
}

.product-image {
  width: 100%;
  height: 100%;
}

.gallery-actions {
  position: absolute;
  top: var(--spacer-6);
  right: var(--spacer-6);
  display: flex;
  flex-direction: column;
  gap: var(--spacer-3);
  z-index: 10;
}

.action-btn {
  width: 72rpx;
  height: 72rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-s);
  transition: all var(--transition-normal);
}

.action-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 1);
}

.favorite-btn.active {
  background: rgba(255, 59, 48, 0.1);
}

.image-indicator {
  position: absolute;
  bottom: var(--spacer-4);
  right: var(--spacer-4);
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: var(--spacer-1) var(--spacer-3);
  border-radius: var(--radius-s);
  font-size: var(--font-size-xs);
  backdrop-filter: blur(10px);
}

/* 商品基本信息 */
.product-info-section {
  background: var(--bg-color-container);
  padding: var(--spacer-6);
  margin-bottom: var(--spacer-2);
}

.price-section {
  display: flex;
  align-items: baseline;
  gap: var(--spacer-3);
  margin-bottom: var(--spacer-4);
  flex-wrap: wrap;
}

.current-price {
  font-size: 48rpx;
  font-weight: var(--font-weight-bold);
  color: var(--shop-theme-color);
  line-height: 1;
}

.original-price {
  font-size: var(--font-size-m);
  color: var(--text-color-tertiary);
  text-decoration: line-through;
}

.discount-tag {
  background: var(--error-color);
  color: var(--text-color-inverse);
  padding: var(--spacer-1) var(--spacer-2);
  border-radius: var(--radius-s);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.product-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacer-2);
}

.product-subtitle {
  font-size: var(--font-size-m);
  color: var(--text-color-secondary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacer-4);
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-1);
}

.rating {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  font-weight: var(--font-weight-medium);
}

.review-count {
  font-size: var(--font-size-s);
  color: var(--text-color-tertiary);
}

.sales {
  font-size: var(--font-size-s);
  color: var(--text-color-tertiary);
}

/* 规格选择 */
.specs-section {
  background: var(--bg-color-container);
  padding: var(--spacer-5) var(--spacer-6);
  margin-bottom: var(--spacer-2);
  transition: background-color var(--transition-normal);
}

.specs-section:active {
  background: var(--bg-color-hover);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-title {
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
}

.selected-specs {
  flex: 1;
  margin: 0 var(--spacer-4);
  text-align: right;
}

.specs-text {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.placeholder-text {
  font-size: var(--font-size-s);
  color: var(--text-color-placeholder);
}

/* 服务保障 */
.service-section {
  background: var(--bg-color-container);
  padding: var(--spacer-5) var(--spacer-6);
  margin-bottom: var(--spacer-2);
}

.service-section .section-title {
  margin-bottom: var(--spacer-3);
}

.service-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-2);
}

.service-item {
  display: flex;
  align-items: center;
  gap: var(--spacer-2);
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

/* 商品详情 */
.detail-section {
  background: var(--bg-color-container);
  margin-bottom: var(--spacer-2);
}

.detail-tabs {
  display: flex;
  border-bottom: 1rpx solid var(--border-color);
}

.tab-item {
  flex: 1;
  padding: var(--spacer-4) var(--spacer-6);
  text-align: center;
  font-size: var(--font-size-m);
  color: var(--text-color-secondary);
  border-bottom: 4rpx solid transparent;
  transition: all var(--transition-normal);
}

.tab-item.active {
  color: var(--shop-theme-color);
  border-bottom-color: var(--shop-theme-color);
  font-weight: var(--font-weight-medium);
}

.detail-content {
  padding: var(--spacer-6);
}

/* 商品详情内容 */
.detail-params {
  margin-bottom: var(--spacer-6);
}

.param-item {
  display: flex;
  justify-content: space-between;
  padding: var(--spacer-3) 0;
  border-bottom: 1rpx solid var(--border-color);
}

.param-item:last-child {
  border-bottom: none;
}

.param-name {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.param-value {
  font-size: var(--font-size-s);
  color: var(--text-color-primary);
  font-weight: var(--font-weight-medium);
}

.detail-description {
  line-height: var(--line-height-relaxed);
}

/* 评价内容 */
.reviews-summary {
  padding: var(--spacer-6) 0;
  border-bottom: 1rpx solid var(--border-color);
  margin-bottom: var(--spacer-6);
}

.summary-rating {
  display: flex;
  align-items: center;
  gap: var(--spacer-4);
}

.big-rating {
  font-size: 72rpx;
  font-weight: var(--font-weight-bold);
  color: var(--warning-color);
  line-height: 1;
}

.rating-stars {
  display: flex;
  gap: var(--spacer-1);
}

.total-reviews {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-6);
}

.review-item {
  padding-bottom: var(--spacer-6);
  border-bottom: 1rpx solid var(--border-color);
}

.review-item:last-child {
  border-bottom: none;
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacer-3);
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: var(--radius-round);
  margin-right: var(--spacer-3);
}

.user-info {
  flex: 1;
}

.username {
  display: block;
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-1);
}

.review-rating {
  display: flex;
  gap: var(--spacer-1);
}

.review-time {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

.review-content {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacer-3);
}

.review-images {
  display: flex;
  gap: var(--spacer-2);
  flex-wrap: wrap;
}

.review-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: var(--radius-m);
}

/* 相关推荐 */
.recommend-section {
  background: var(--bg-color-container);
  padding: var(--spacer-6);
  margin-bottom: var(--spacer-2);
}

.recommend-section .section-title {
  margin-bottom: var(--spacer-4);
}

.recommend-scroll {
  width: 100%;
}

.recommend-list {
  display: flex;
  gap: var(--spacer-4);
  padding-bottom: var(--spacer-2);
}

.recommend-item {
  width: 200rpx;
  flex-shrink: 0;
}

.recommend-image {
  width: 100%;
  height: 200rpx;
  border-radius: var(--radius-m);
  margin-bottom: var(--spacer-2);
}

.recommend-info {
  padding: 0 var(--spacer-1);
}

.recommend-title {
  display: block;
  font-size: var(--font-size-s);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-1);
  line-height: var(--line-height-normal);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.recommend-price {
  font-size: var(--font-size-s);
  color: var(--shop-theme-color);
  font-weight: var(--font-weight-bold);
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-color-container);
  padding: var(--spacer-4) var(--spacer-6);
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: var(--z-index-fixed);
  border-top: 1rpx solid var(--border-color);
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: var(--spacer-4);
}

.secondary-actions {
  display: flex;
  gap: var(--spacer-2);
}

.action-btn {
  background: transparent;
  border: none;
  padding: var(--spacer-2);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacer-1);
  font-size: var(--font-size-xs);
  color: var(--text-color-secondary);
  min-width: 80rpx;
}

.cart-icon-wrapper {
  position: relative;
}

.cart-count {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: var(--error-color);
  color: var(--text-color-inverse);
  font-size: var(--font-size-xs);
  padding: var(--spacer-1) var(--spacer-2);
  border-radius: var(--radius-round);
  min-width: 32rpx;
  text-align: center;
  font-weight: var(--font-weight-bold);
}

.primary-actions {
  flex: 1;
  display: flex;
  gap: var(--spacer-3);
}

.add-cart-btn, .buy-now-btn {
  flex: 1;
  height: 80rpx;
  border-radius: var(--radius-l);
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  border: none;
  transition: all var(--transition-normal);
}

.add-cart-btn {
  background: rgba(255, 59, 48, 0.1);
  color: var(--shop-theme-color);
  border: 1rpx solid var(--shop-theme-color);
}

.add-cart-btn:active {
  background: rgba(255, 59, 48, 0.2);
}

.buy-now-btn {
  background: var(--shop-theme-color);
  color: var(--text-color-inverse);
}

.buy-now-btn:active {
  background: var(--shop-theme-color);
  opacity: 0.8;
}

.add-cart-btn[disabled], .buy-now-btn[disabled] {
  opacity: 0.5;
  pointer-events: none;
}

/* 规格选择弹窗 */
.spec-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: flex-end;
  z-index: var(--z-index-modal);
}

.spec-modal {
  width: 100%;
  background: var(--bg-color-container);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  padding: var(--spacer-6);
  max-height: 80vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacer-6);
  padding-bottom: var(--spacer-4);
  border-bottom: 1rpx solid var(--border-color);
}

.selected-product {
  display: flex;
  align-items: center;
  gap: var(--spacer-4);
}

.product-thumb {
  width: 120rpx;
  height: 120rpx;
  border-radius: var(--radius-m);
}

.product-summary {
  display: flex;
  flex-direction: column;
  gap: var(--spacer-2);
}

.modal-price {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--shop-theme-color);
}

.modal-stock {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-color-hover);
  border-radius: var(--radius-round);
}

/* 规格选项 */
.spec-options {
  margin-bottom: var(--spacer-6);
}

.spec-group {
  margin-bottom: var(--spacer-5);
}

.spec-name {
  display: block;
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  margin-bottom: var(--spacer-3);
}

.spec-values {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacer-3);
}

.spec-value {
  padding: var(--spacer-2) var(--spacer-4);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-m);
  font-size: var(--font-size-s);
  color: var(--text-color-primary);
  background: var(--bg-color-container);
  transition: all var(--transition-normal);
  min-width: 80rpx;
  text-align: center;
}

.spec-value.selected {
  border-color: var(--shop-theme-color);
  background: var(--shop-theme-color);
  color: var(--text-color-inverse);
}

.spec-value.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 数量选择 */
.quantity-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacer-6);
}

.quantity-label {
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
}

.quantity-controls {
  display: flex;
  align-items: center;
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-m);
  overflow: hidden;
}

.quantity-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-color-hover);
  transition: all var(--transition-normal);
}

.quantity-btn:active {
  background: var(--border-color);
}

.quantity-btn.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.quantity-input {
  width: 100rpx;
  height: 64rpx;
  text-align: center;
  font-size: var(--font-size-m);
  border: none;
  background: var(--bg-color-container);
  border-left: 1rpx solid var(--border-color);
  border-right: 1rpx solid var(--border-color);
}

/* 弹窗操作 */
.modal-actions {
  padding-top: var(--spacer-4);
  border-top: 1rpx solid var(--border-color);
}

.modal-action-btn {
  width: 100%;
  height: 80rpx;
  background: var(--shop-theme-color);
  color: var(--text-color-inverse);
  border: none;
  border-radius: var(--radius-l);
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
}

.modal-action-btn:active {
  opacity: 0.8;
}

.modal-action-btn[disabled] {
  opacity: 0.5;
  pointer-events: none;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
}

.loading-content {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-8);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacer-4);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-color);
  border-top: 4rpx solid var(--shop-theme-color);
  border-radius: var(--radius-round);
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: var(--font-size-m);
  color: var(--text-color-tertiary);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式优化 */
@media (max-width: 350px) {
  .product-gallery {
    height: 600rpx;
  }
  
  .price-section {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacer-2);
  }
  
  .product-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacer-2);
  }
  
  .spec-values {
    gap: var(--spacer-2);
  }
  
  .spec-value {
    min-width: 60rpx;
    padding: var(--spacer-1) var(--spacer-3);
  }
}

/* 安全区域适配 */
.bottom-actions {
  padding-bottom: calc(var(--spacer-4) + env(safe-area-inset-bottom));
}