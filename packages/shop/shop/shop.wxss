/* packages/shop/shop/shop.wxss */
@import '/styles/unified-design-tokens.wxss';

.shop-container {
  padding: var(--page-padding);
  background-color: var(--bg-color-page);
  min-height: 100vh;
}

/* 页面头部 */
.shop-main-header {
  background: linear-gradient(135deg, var(--shop-theme-color) 0%, var(--error-color-light) 100%);
  border-radius: var(--radius-xl);
  padding: var(--spacer-8) var(--spacer-6);
  margin-bottom: var(--section-margin);
  box-shadow: var(--shadow-m);
}

/* 搜索区域 */
.search-section {
  display: flex;
  align-items: center;
  gap: var(--spacer-3);
  margin-bottom: var(--section-margin);
}

.search-bar {
  flex: 1;
  display: flex;
  align-items: center;
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  padding: var(--spacer-3) var(--spacer-4);
  border: 1rpx solid var(--border-color);
  gap: var(--spacer-2);
}

.search-input {
  flex: 1;
  font-size: var(--font-size-m);
  color: var(--text-color-primary);
}

.search-placeholder {
  color: var(--text-color-placeholder);
}

.filter-btn {
  width: 80rpx;
  height: 80rpx;
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal);
}

.filter-btn:active {
  background: var(--bg-color-hover);
  transform: scale(0.95);
}

/* 分类导航 */
.category-section {
  margin-bottom: var(--section-margin);
}

.category-scroll {
  width: 100%;
}

.category-list {
  display: flex;
  gap: var(--spacer-4);
  padding: 0 var(--spacer-2);
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120rpx;
  padding: var(--spacer-3);
  border-radius: var(--radius-m);
  transition: all var(--transition-normal);
}

.category-item.active {
  background: var(--shop-theme-bg);
}

.category-item:active {
  transform: scale(0.95);
}

.category-icon {
  width: 60rpx;
  height: 60rpx;
  background: var(--bg-color-container);
  border-radius: var(--radius-m);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacer-2);
  border: 1rpx solid var(--border-color);
}

.category-item.active .category-icon {
  background: var(--shop-theme-color);
  border-color: var(--shop-theme-color);
}

.category-text {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  text-align: center;
}

.category-item.active .category-text {
  color: var(--shop-theme-color);
  font-weight: var(--font-weight-medium);
}

/* 商品区域 */
.products-section {
  margin-bottom: var(--section-margin);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-4);
}

.section-title {
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color-primary);
}

.view-mode-switch {
  display: flex;
  background: var(--bg-color-container);
  border-radius: var(--radius-m);
  padding: var(--spacer-1);
  border: 1rpx solid var(--border-color);
}

.mode-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-s);
  transition: all var(--transition-normal);
}

.mode-btn.active {
  background: var(--shop-theme-color);
  color: white;
}

/* 商品网格 */
.products-grid {
  display: grid;
  gap: var(--spacer-4);
}

.products-grid.grid {
  grid-template-columns: repeat(2, 1fr);
}

.products-grid.list {
  grid-template-columns: 1fr;
}

.product-card {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  overflow: hidden;
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
  transition: all var(--transition-normal) var(--ease-out);
  position: relative;
}

.product-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-m);
}

.product-image-container {
  position: relative;
  width: 100%;
  height: 300rpx;
}

.product-image {
  width: 100%;
  height: 100%;
}

.discount-badge {
  position: absolute;
  top: var(--spacer-2);
  left: var(--spacer-2);
  background: var(--error-color);
  color: var(--text-color-inverse);
  padding: var(--spacer-1) var(--spacer-2);
  border-radius: var(--radius-s);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
}

.favorite-btn {
  position: absolute;
  top: var(--spacer-2);
  right: var(--spacer-2);
  width: 56rpx;
  height: 56rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
}

.favorite-btn:active {
  transform: scale(0.9);
}

.product-info {
  padding: var(--spacer-4);
}

.product-title {
  font-size: var(--font-size-m);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacer-1);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-description {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacer-3);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.product-price {
  display: flex;
  align-items: baseline;
  gap: var(--spacer-2);
  margin-bottom: var(--spacer-2);
}

.current-price {
  font-size: var(--font-size-l);
  font-weight: var(--font-weight-bold);
  color: var(--shop-theme-color);
}

.original-price {
  font-size: var(--font-size-s);
  color: var(--text-color-tertiary);
  text-decoration: line-through;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacer-3);
}

.rating {
  display: flex;
  align-items: center;
  gap: var(--spacer-1);
}

.rating-score {
  font-size: var(--font-size-s);
  color: var(--text-color-secondary);
  font-weight: var(--font-weight-medium);
}

.rating-count {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

.sales-count {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

.product-actions {
  padding: 0 var(--spacer-4) var(--spacer-4);
}

.add-cart-btn {
  width: 100%;
  background: var(--shop-theme-color);
  color: var(--text-color-inverse);
  border: none;
  border-radius: var(--radius-m);
  padding: var(--spacer-3) var(--spacer-4);
  font-size: var(--font-size-s);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacer-2);
  transition: all var(--transition-normal);
}

.add-cart-btn:active {
  background: var(--shop-theme-color);
  opacity: 0.8;
  transform: scale(0.98);
}

/* 购物车悬浮按钮 */
.cart-float-btn {
  position: fixed;
  bottom: 120rpx;
  right: var(--spacer-6);
  width: 120rpx;
  height: 120rpx;
  background: var(--shop-theme-color);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-l);
  z-index: var(--z-index-fixed);
  transition: all var(--transition-normal);
}

.cart-float-btn:active {
  transform: scale(0.9);
}

.cart-icon-container {
  position: relative;
}

.cart-badge {
  position: absolute;
  top: -16rpx;
  right: -16rpx;
  background: var(--error-color);
  color: var(--text-color-inverse);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  padding: var(--spacer-1) var(--spacer-2);
  border-radius: var(--radius-round);
  min-width: 32rpx;
  text-align: center;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacer-16);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacer-4);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-color);
  border-top: 4rpx solid var(--shop-theme-color);
  border-radius: var(--radius-round);
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: var(--font-size-m);
  color: var(--text-color-tertiary);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 列表模式特殊样式 */
.products-grid.list .product-card {
  display: flex;
  flex-direction: row;
}

.products-grid.list .product-image-container {
  width: 200rpx;
  height: 200rpx;
  flex-shrink: 0;
}

.products-grid.list .product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.products-grid.list .product-actions {
  padding: var(--spacer-2) var(--spacer-4) var(--spacer-4);
}

/* 响应式优化 */
@media (max-width: 350px) {
  .products-grid.grid {
    grid-template-columns: 1fr;
  }
  
  .category-list {
    gap: var(--spacer-2);
  }
  
  .category-item {
    min-width: 100rpx;
  }
}