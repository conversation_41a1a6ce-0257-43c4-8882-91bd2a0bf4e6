<!--packages/shop/shop/shop.wxml-->
<view class="shop-container theme-shop">
  
  <!-- 页面头部 -->
  <global-page-header 
    title="智慧商城" 
    subtitle="优质产品，信赖之选"
    theme="shop"
    size="large"
    custom-class="shop-main-header">
  </global-page-header>

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <global-icon name="搜索" size="16" color="var(--text-color-tertiary)"></global-icon>
      <input class="search-input" 
             placeholder="搜索商品..."
             placeholder-class="search-placeholder"
             bindconfirm="onSearch" />
    </view>
    <view class="filter-btn" bindtap="showFilter">
      <global-icon name="筛选" size="16" color="var(--shop-theme-color)"></global-icon>
    </view>
  </view>

  <!-- 分类导航 -->
  <view class="category-section">
    <scroll-view class="category-scroll" scroll-x="true" show-scrollbar="{{false}}">
      <view class="category-list">
        <view wx:for="{{categories}}" wx:key="id" 
              class="category-item {{currentCategory === item.id ? 'active' : ''}}"
              bindtap="selectCategory"
              data-category="{{item.id}}">
          <view class="category-icon">
            <global-icon name="{{item.icon}}" size="20" color="{{currentCategory === item.id ? 'var(--shop-theme-color)' : 'var(--text-color-secondary)'}}"></global-icon>
          </view>
          <text class="category-text">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 商品网格 -->
  <view class="products-section">
    <view class="section-header">
      <text class="section-title">精选商品</text>
      <view class="view-mode-switch">
        <view class="mode-btn {{viewMode === 'grid' ? 'active' : ''}}" 
              bindtap="switchViewMode" data-mode="grid">
          <global-icon name="网格" size="16"></global-icon>
        </view>
        <view class="mode-btn {{viewMode === 'list' ? 'active' : ''}}" 
              bindtap="switchViewMode" data-mode="list">
          <global-icon name="列表" size="16"></global-icon>
        </view>
      </view>
    </view>
    
    <view class="products-grid {{viewMode}}">
      <view wx:for="{{products}}" wx:key="id" 
            class="product-card"
            bindtap="goToProductDetail"
            data-product="{{item}}">
        
        <!-- 商品图片 -->
        <view class="product-image-container">
          <lazy-image 
            src="{{item.image}}" 
            class="product-image"
            mode="aspectFill" />
          
          <!-- 折扣标识 -->
          <view wx:if="{{item.discount}}" class="discount-badge">
            <text>-{{item.discount}}%</text>
          </view>
          
          <!-- 收藏按钮 -->
          <view class="favorite-btn {{item.isFavorite ? 'active' : ''}}"
                bindtap="toggleFavorite"
                data-product-id="{{item.id}}"
                catchtap="true">
            <global-icon name="{{(item.isFavorite ? '收藏实心' : '收藏空心') || '收藏'}}"
                         size="16"
                         color="{{item.isFavorite ? 'var(--error-color)' : 'var(--text-color-tertiary)'}}"></global-icon>
          </view>
        </view>
        
        <!-- 商品信息 -->
        <view class="product-info">
          <text class="product-title">{{item.title}}</text>
          <text class="product-description">{{item.description}}</text>
          
          <view class="product-price">
            <text class="current-price">¥{{item.price}}</text>
            <text wx:if="{{item.originalPrice}}" class="original-price">¥{{item.originalPrice}}</text>
          </view>
          
          <view class="product-meta">
            <view class="rating">
              <global-icon name="星星" size="12" color="var(--warning-color)"></global-icon>
              <text class="rating-score">{{item.rating}}</text>
              <text class="rating-count">({{item.reviewCount}})</text>
            </view>
            <text class="sales-count">已售{{item.salesCount}}</text>
          </view>
        </view>
        
        <!-- 购买按钮 -->
        <view class="product-actions">
          <button class="add-cart-btn" 
                  bindtap="addToCart" 
                  data-product="{{item}}"
                  catchtap="true">
            <global-icon name="购物车" size="14" color="white"></global-icon>
            <text>加购物车</text>
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 购物车悬浮按钮 -->
  <view wx:if="{{cartCount > 0}}" class="cart-float-btn" bindtap="goToCart">
    <view class="cart-icon-container">
      <global-icon name="购物车" size="24" color="white"></global-icon>
      <view class="cart-badge">
        <text>{{cartCount > 99 ? '99+' : cartCount}}</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{products.length === 0 && !loading}}" class="empty-state">
    <global-icon name="空盒子" size="48" color="var(--text-color-tertiary)"></global-icon>
    <text class="empty-text">暂无商品</text>
  </view>

</view>