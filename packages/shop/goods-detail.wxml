<!--packages/shop/goods-detail.wxml-->
<view class="goods-detail-container theme-shop">
  
  <!-- 商品图片轮播 -->
  <view class="product-gallery">
    <swiper class="product-swiper" 
            indicator-dots="{{true}}" 
            indicator-color="rgba(255,255,255,0.5)"
            indicator-active-color="#ffffff"
            autoplay="{{false}}"
            interval="3000"
            duration="500"
            circular="{{true}}">
      <swiper-item wx:for="{{product.images}}" wx:key="*this">
        <image 
          src="{{item}}" 
          class="product-image"
          mode="aspectFill"
          bindtap="previewImage"
          data-src="{{item}}"
          lazy-load="{{true}}" />
      </swiper-item>
    </swiper>
    
    <!-- 分享和收藏按钮 -->
    <view class="gallery-actions">
      <view class="action-btn share-btn" bindtap="shareProduct">
        <global-icon name="分享" size="20" color="var(--text-color-primary)"></global-icon>
      </view>
      <view class="action-btn favorite-btn {{product.isFavorite ? 'active' : ''}}" 
            bindtap="toggleFavorite">
        <global-icon name="{{(product.isFavorite ? '收藏实心' : '收藏空心') || '收藏'}}"
                     size="20"
                     color="{{product.isFavorite ? 'var(--error-color)' : 'var(--text-color-primary)'}}"></global-icon>
      </view>
    </view>
    
    <!-- 图片指示器 -->
    <view class="image-indicator">
      <text>{{currentImageIndex + 1}}/{{product.images.length}}</text>
    </view>
  </view>

  <!-- 商品基本信息 -->
  <view class="product-info-section">
    <view class="price-section">
      <view class="current-price">¥{{product.price}}</view>
      <view wx:if="{{product.originalPrice}}" class="original-price">¥{{product.originalPrice}}</view>
      <view wx:if="{{product.discount}}" class="discount-tag">
        <text>限时{{product.discount}}折</text>
      </view>
    </view>
    
    <view class="product-title">{{product.title}}</view>
    <view class="product-subtitle">{{product.subtitle}}</view>
    
    <view class="product-meta">
      <view class="meta-item">
        <global-icon name="星星" size="14" color="var(--warning-color)"></global-icon>
        <text class="rating">{{product.rating}}</text>
        <text class="review-count">({{product.reviewCount}}条评价)</text>
      </view>
      <view class="meta-item sales">
        <text>已售{{product.salesCount}}件</text>
      </view>
    </view>
  </view>

  <!-- 规格选择 -->
  <view class="specs-section" bindtap="showSpecSelector">
    <view class="section-header">
      <text class="section-title">选择规格</text>
      <view class="selected-specs">
        <text wx:if="{{selectedSpecs.length > 0}}" class="specs-text">
          {{selectedSpecs.join(' ')}}
        </text>
        <text wx:else class="placeholder-text">请选择商品规格</text>
      </view>
      <global-icon name="箭头右" size="16" color="var(--text-color-tertiary)"></global-icon>
    </view>
  </view>

  <!-- 服务保障 -->
  <view class="service-section">
    <view class="section-title">服务保障</view>
    <view class="service-list">
      <view wx:for="{{product.services}}" wx:key="*this" class="service-item">
        <global-icon name="对勾" size="12" color="var(--success-color)"></global-icon>
        <text>{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 商品详情 -->
  <view class="detail-section">
    <view class="detail-tabs">
      <view wx:for="{{detailTabs}}" wx:key="key"
            class="tab-item {{currentTab === item.key ? 'active' : ''}}"
            bindtap="switchTab"
            data-tab="{{item.key}}">
        <text>{{item.title}}</text>
      </view>
    </view>
    
    <view class="detail-content">
      <!-- 商品详情 -->
      <view wx:if="{{currentTab === 'detail'}}" class="detail-info">
        <view class="detail-params">
          <view wx:for="{{product.params}}" wx:key="name" class="param-item">
            <text class="param-name">{{item.name}}</text>
            <text class="param-value">{{item.value}}</text>
          </view>
        </view>
        <view class="detail-description">
          <rich-text nodes="{{product.description}}"></rich-text>
        </view>
      </view>
      
      <!-- 用户评价 -->
      <view wx:if="{{currentTab === 'reviews'}}" class="reviews-content">
        <view class="reviews-summary">
          <view class="summary-rating">
            <text class="big-rating">{{product.rating}}</text>
            <view class="rating-stars">
              <global-icon wx:for="{{5}}" wx:key="*this" 
                           name="星星" 
                           size="16" 
                           color="{{index < Math.floor(product.rating) ? 'var(--warning-color)' : 'var(--border-color)'}}"></global-icon>
            </view>
            <text class="total-reviews">{{product.reviewCount}}条评价</text>
          </view>
        </view>
        
        <view class="reviews-list">
          <view wx:for="{{product.reviews}}" wx:key="id" class="review-item">
            <view class="review-header">
              <image class="user-avatar" src="{{item.avatar}}" mode="aspectFill"></image>
              <view class="user-info">
                <text class="username">{{item.username}}</text>
                <view class="review-rating">
                  <global-icon wx:for="{{5}}" wx:key="*this" 
                               name="星星" 
                               size="12" 
                               color="{{index < item.rating ? 'var(--warning-color)' : 'var(--border-color)'}}"></global-icon>
                </view>
              </view>
              <text class="review-time">{{item.timeLabel}}</text>
            </view>
            <view class="review-content">{{item.content}}</view>
            <view wx:if="{{item.images.length > 0}}" class="review-images">
              <image wx:for="{{item.images}}" wx:key="*this" 
                     src="{{item}}" 
                     class="review-image"
                     mode="aspectFill"
                     bindtap="previewReviewImage"
                     data-src="{{item}}"></image>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 相关推荐 -->
  <view class="recommend-section">
    <view class="section-title">相关推荐</view>
    <scroll-view class="recommend-scroll" scroll-x="{{true}}" show-scrollbar="{{false}}">
      <view class="recommend-list">
        <view wx:for="{{recommendProducts}}" wx:key="id" 
              class="recommend-item"
              bindtap="goToProduct"
              data-product="{{item}}">
          <image src="{{item.image}}" class="recommend-image" mode="aspectFill"></image>
          <view class="recommend-info">
            <text class="recommend-title">{{item.title}}</text>
            <text class="recommend-price">¥{{item.price}}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <view class="action-buttons">
      <view class="secondary-actions">
        <button class="action-btn customer-service" bindtap="contactService">
          <global-icon name="客服" size="20" color="var(--text-color-secondary)"></global-icon>
          <text>客服</text>
        </button>
        <button class="action-btn cart" bindtap="goToCart">
          <view class="cart-icon-wrapper">
            <global-icon name="购物车" size="20" color="var(--text-color-secondary)"></global-icon>
            <view wx:if="{{cartCount > 0}}" class="cart-count">{{cartCount > 99 ? '99+' : cartCount}}</view>
          </view>
          <text>购物车</text>
        </button>
      </view>
      
      <view class="primary-actions">
        <button class="add-cart-btn" 
                bindtap="addToCart"
                disabled="{{!selectedSpecs.length}}">
          <text>加入购物车</text>
        </button>
        <button class="buy-now-btn" 
                bindtap="buyNow"
                disabled="{{!selectedSpecs.length}}">
          <text>立即购买</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 规格选择弹窗 -->
  <view wx:if="{{showSpecModal}}" class="spec-modal-mask" bindtap="hideSpecModal">
    <view class="spec-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <view class="selected-product">
          <image src="{{product.images[0]}}" class="product-thumb" mode="aspectFill"></image>
          <view class="product-summary">
            <text class="modal-price">¥{{selectedPrice || product.price}}</text>
            <text class="modal-stock">库存{{selectedStock || product.stock}}件</text>
          </view>
        </view>
        <view class="close-btn" bindtap="hideSpecModal">
          <global-icon name="关闭" size="20" color="var(--text-color-tertiary)"></global-icon>
        </view>
      </view>
      
      <view class="spec-options">
        <view wx:for="{{product.specGroups}}" wx:key="name" class="spec-group">
          <text class="spec-name">{{item.name}}</text>
          <view class="spec-values">
            <view wx:for="{{item.values}}" wx:key="value" 
                  class="spec-value {{selectedSpecs[index] === item.value ? 'selected' : ''}} {{item.disabled ? 'disabled' : ''}}"
                  bindtap="selectSpec"
                  data-group="{{index}}"
                  data-value="{{item.value}}">
              <text>{{item.value}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="quantity-section">
        <text class="quantity-label">数量</text>
        <view class="quantity-controls">
          <view class="quantity-btn minus {{quantity <= 1 ? 'disabled' : ''}}" 
                bindtap="changeQuantity" 
                data-action="minus">
            <global-icon name="减号" size="16" color="var(--text-color-secondary)"></global-icon>
          </view>
          <input class="quantity-input" 
                 type="number" 
                 value="{{quantity}}" 
                 bindchange="inputQuantity"
                 maxlength="3"></input>
          <view class="quantity-btn plus {{quantity >= (selectedStock || product.stock) ? 'disabled' : ''}}" 
                bindtap="changeQuantity" 
                data-action="plus">
            <global-icon name="加号" size="16" color="var(--text-color-secondary)"></global-icon>
          </view>
        </view>
      </view>
      
      <view class="modal-actions">
        <button class="modal-action-btn confirm" 
                bindtap="confirmSpec"
                disabled="{{selectedSpecs.length !== product.specGroups.length}}">
          <text>确定</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

</view>