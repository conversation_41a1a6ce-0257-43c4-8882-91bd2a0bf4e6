<!--packages/shop/cart.wxml-->
<view class="cart-container theme-shop">
  
  <!-- 页面头部 -->
  <global-page-header 
    title="购物车" 
    subtitle="{{cartItems.length}}件商品"
    theme="shop"
    show-back="{{true}}"
    custom-class="cart-header">
  </global-page-header>

  <!-- 购物车商品列表 -->
  <view wx:if="{{cartItems.length > 0}}" class="cart-content">
    
    <!-- 全选控制 -->
    <view class="select-all-section">
      <view class="select-all-control" bindtap="toggleSelectAll">
        <view class="checkbox {{allSelected ? 'checked' : ''}}">
          <global-icon wx:if="{{allSelected}}" 
                       name="对勾" 
                       size="16" 
                       color="white"></global-icon>
        </view>
        <text class="select-all-text">全选</text>
      </view>
      
      <view class="cart-actions">
        <text class="manage-btn {{isEditing ? 'active' : ''}}" 
              bindtap="toggleEditMode">
          {{isEditing ? '完成' : '编辑'}}
        </text>
      </view>
    </view>

    <!-- 商品列表 -->
    <view class="cart-items-list">
      <view wx:for="{{cartItems}}" wx:key="id" class="cart-item">
        
        <!-- 选择框 -->
        <view class="item-checkbox" 
              bindtap="toggleItemSelect" 
              data-item-id="{{item.id}}">
          <view class="checkbox {{item.selected ? 'checked' : ''}}">
            <global-icon wx:if="{{item.selected}}" 
                         name="对勾" 
                         size="14" 
                         color="white"></global-icon>
          </view>
        </view>

        <!-- 商品信息 -->
        <view class="item-content" bindtap="goToProductDetail" data-product="{{item}}">
          <image src="{{item.image}}" 
                 class="item-image" 
                 mode="aspectFill"
                 lazy-load="{{true}}"></image>
          
          <view class="item-info">
            <text class="item-title">{{item.title}}</text>
            <text class="item-specs">{{item.specs}}</text>
            
            <view class="item-price-section">
              <view class="price-info">
                <text class="current-price">¥{{item.price}}</text>
                <text wx:if="{{item.originalPrice}}" class="original-price">¥{{item.originalPrice}}</text>
              </view>
              
              <!-- 数量控制 -->
              <view class="quantity-controls">
                <view class="quantity-btn minus {{item.quantity <= 1 ? 'disabled' : ''}}" 
                      bindtap="decreaseQuantity" 
                      data-item-id="{{item.id}}">
                  <global-icon name="减号" size="12" color="var(--text-color-secondary)"></global-icon>
                </view>
                <input class="quantity-input" 
                       type="number" 
                       value="{{item.quantity}}" 
                       bindchange="inputQuantity"
                       data-item-id="{{item.id}}"
                       maxlength="3"></input>
                <view class="quantity-btn plus {{item.quantity >= item.stock ? 'disabled' : ''}}" 
                      bindtap="increaseQuantity" 
                      data-item-id="{{item.id}}">
                  <global-icon name="加号" size="12" color="var(--text-color-secondary)"></global-icon>
                </view>
              </view>
            </view>
            
            <!-- 库存状态 -->
            <view wx:if="{{item.stock <= 10}}" class="stock-warning">
              <global-icon name="警告" size="12" color="var(--warning-color)"></global-icon>
              <text>仅剩{{item.stock}}件</text>
            </view>
          </view>
          
          <!-- 删除按钮 -->
          <view wx:if="{{isEditing}}" 
                class="delete-btn" 
                bindtap="removeItem" 
                data-item-id="{{item.id}}"
                catchtap="true">
            <global-icon name="删除" size="20" color="var(--error-color)"></global-icon>
          </view>
        </view>
      </view>
    </view>

    <!-- 推荐商品 -->
    <view class="recommend-section">
      <view class="recommend-header">
        <global-icon name="推荐" size="16" color="var(--shop-theme-color)"></global-icon>
        <text class="recommend-title">为你推荐</text>
      </view>
      
      <scroll-view class="recommend-scroll" scroll-x="{{true}}" show-scrollbar="{{false}}">
        <view class="recommend-list">
          <view wx:for="{{recommendItems}}" wx:key="id" 
                class="recommend-item"
                bindtap="addRecommendToCart"
                data-product="{{item}}">
            <image src="{{item.image}}" class="recommend-image" mode="aspectFill"></image>
            <view class="recommend-info">
              <text class="recommend-title">{{item.title}}</text>
              <text class="recommend-price">¥{{item.price}}</text>
            </view>
            <view class="add-recommend-btn">
              <global-icon name="加号" size="16" color="var(--shop-theme-color)"></global-icon>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部结算栏 -->
    <view class="checkout-bar">
      <view class="checkout-info">
        <view class="select-info">
          <view class="select-all-mini" bindtap="toggleSelectAll">
            <view class="checkbox {{allSelected ? 'checked' : ''}}">
              <global-icon wx:if="{{allSelected}}" 
                           name="对勾" 
                           size="12" 
                           color="white"></global-icon>
            </view>
            <text>全选</text>
          </view>
        </view>
        
        <view class="price-info">
          <view class="total-section">
            <text class="total-label">合计：</text>
            <text class="total-price">¥{{totalPrice}}</text>
          </view>
          <text wx:if="{{discountAmount > 0}}" class="discount-info">
            已优惠 ¥{{discountAmount}}
          </text>
        </view>
      </view>
      
      <button class="checkout-btn {{selectedItems.length === 0 ? 'disabled' : ''}}" 
              bindtap="goToCheckout"
              disabled="{{selectedItems.length === 0}}">
        <text>结算({{selectedItems.length}})</text>
      </button>
    </view>
  </view>

  <!-- 空购物车状态 -->
  <view wx:else class="empty-cart">
    <view class="empty-icon">
      <global-icon name="购物车空" size="80" color="var(--text-color-tertiary)"></global-icon>
    </view>
    <text class="empty-text">购物车还是空的</text>
    <text class="empty-subtitle">快去添加一些心仪的商品吧~</text>
    
    <button class="go-shopping-btn" bindtap="goToShop">
      <text>去逛逛</text>
    </button>
    
    <!-- 推荐商品 -->
    <view class="empty-recommend">
      <text class="empty-recommend-title">猜你喜欢</text>
      <view class="empty-recommend-grid">
        <view wx:for="{{recommendItems}}" wx:key="id" 
              class="empty-recommend-item"
              bindtap="goToProductDetail"
              data-product="{{item}}">
          <image src="{{item.image}}" class="empty-recommend-image" mode="aspectFill"></image>
          <text class="empty-recommend-name">{{item.title}}</text>
          <text class="empty-recommend-price">¥{{item.price}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 删除确认弹窗 -->
  <view wx:if="{{showDeleteModal}}" class="delete-modal-mask" bindtap="hideDeleteModal">
    <view class="delete-modal" catchtap="stopPropagation">
      <view class="modal-icon">
        <global-icon name="警告" size="48" color="var(--warning-color)"></global-icon>
      </view>
      <text class="modal-title">确认删除</text>
      <text class="modal-message">确定要从购物车中删除该商品吗？</text>
      
      <view class="modal-actions">
        <button class="modal-btn cancel" bindtap="hideDeleteModal">取消</button>
        <button class="modal-btn confirm" bindtap="confirmDelete">删除</button>
      </view>
    </view>
  </view>

</view>