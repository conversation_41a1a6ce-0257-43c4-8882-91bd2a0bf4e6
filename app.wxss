/**
 * 全局样式 - 智慧养鹅小程序
 * 基于微信小程序最佳实践
 */

/* 引入统一设计令牌 */
@import "styles/unified-design-tokens.wxss";

/* 全局样式重置 */
page {
  height: 100%;
  font-size: var(--font-size-m);
  line-height: var(--line-height-normal);
  color: var(--text-color-primary);
  background-color: var(--bg-color-page);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 性能优化 - 减少重排重绘 */
view, text, image {
  box-sizing: border-box;
}

/* 通用布局类 */
.container {
  padding: var(--page-padding);
  min-height: 100vh;
}

.safe-area {
  padding-bottom: env(safe-area-inset-bottom);
}

/* 通用交互反馈 */
.tap-highlight {
  background-color: var(--bg-color-hover);
  transition: background-color var(--transition-fast);
}

/* 模块主题样式 */
.theme-oa {
  --module-primary: var(--oa-theme-color);
  --module-bg: var(--oa-theme-bg);
}

.theme-health {
  --module-primary: var(--health-theme-color);
  --module-bg: var(--health-theme-bg);
}

.theme-production {
  --module-primary: var(--production-theme-color);
  --module-bg: var(--production-theme-bg);
}

.theme-shop {
  --module-primary: var(--shop-theme-color);
  --module-bg: var(--shop-theme-bg);
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacer-8);
  color: var(--text-color-tertiary);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacer-16) var(--spacer-8);
  color: var(--text-color-tertiary);
}

.empty-state .empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: var(--spacer-4);
  opacity: 0.5;
}

.empty-state .empty-text {
  font-size: var(--font-size-m);
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacer-16) var(--spacer-8);
  color: var(--error-color);
}

/* 通用卡片样式 */
.card {
  background: var(--bg-color-container);
  border-radius: var(--radius-l);
  box-shadow: var(--shadow-s);
  border: 1rpx solid var(--border-color);
  overflow: hidden;
}

.card-header {
  padding: var(--spacer-6) var(--spacer-6) var(--spacer-4);
  border-bottom: 1rpx solid var(--border-color);
}

.card-body {
  padding: var(--spacer-6);
}

.card-footer {
  padding: var(--spacer-4) var(--spacer-6) var(--spacer-6);
  border-top: 1rpx solid var(--border-color);
}

/* 分割线 */
.divider {
  height: 1rpx;
  background: var(--border-color);
  margin: var(--spacer-4) 0;
}

.divider-vertical {
  width: 1rpx;
  background: var(--border-color);
  margin: 0 var(--spacer-4);
}

/* 响应式文字大小 */
@media (max-width: 350px) {
  page {
    font-size: var(--font-size-s);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .card {
    border-width: 2rpx;
  }
  
  .divider {
    height: 2rpx;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .page, .container, .animation-element {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 屏幕阅读器优化 */
.sr-only {
  position: absolute;
  width: 1rpx;
  height: 1rpx;
  padding: 0;
  margin: -1rpx;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}