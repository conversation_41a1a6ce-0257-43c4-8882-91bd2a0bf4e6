# 智慧养鹅SAAS平台

## 项目简介

智慧养鹅SAAS平台是一个集养殖管理、健康监控、生产记录、OA办公、商城交易于一体的综合性养鹅管理系统。采用微信小程序 + Node.js + MySQL 的技术架构，支持多租户模式，为养鹅企业提供全方位的数字化管理解决方案。

## 📋 功能特性

### 🏠 首页仪表板
- **实时数据概览**: 显示鹅群数量、健康状态、环境监控等关键指标
- **任务管理**: 待办事项提醒和任务进度跟踪
- **消息通知**: 系统公告和重要消息推送
- **快捷操作**: 常用功能快速入口

### 🏥 健康管理
- **健康档案**: 鹅群健康记录管理
- **AI智能诊断**: 基于症状的智能健康诊断
- **疫苗管理**: 疫苗接种计划和记录
- **健康报告**: 生成详细的健康分析报告
- **知识库**: 养殖知识和疾病防治指南

### 🏭 生产管理
- **生产记录**: 产蛋量、饲料消耗、增重等数据记录
- **环境监控**: 温度、湿度、光照等环境参数监控
- **物料管理**: 饲料、药品等物料库存管理
- **设备管理**: 设备维护和故障记录
- **财务统计**: 生产成本和收益分析

### 🏢 OA办公
- **请假管理**: 员工请假申请和审批流程
- **采购管理**: 采购申请和供应商管理
- **报销管理**: 费用报销申请和财务审核
- **审批流程**: 自定义审批流程和工作流
- **权限管理**: 用户角色和权限控制
- **通知系统**: 消息推送和通知管理

### 🛒 商城系统
- **商品管理**: 产品展示和库存管理
- **订单处理**: 下单、支付、发货全流程
- **购物车**: 商品收藏和批量购买
- **支付集成**: 微信支付和其他支付方式
- **物流跟踪**: 订单状态和物流信息

## 🛠 技术架构

### 前端技术栈
- **微信小程序**: 原生小程序开发
- **组件化**: 模块化组件设计
- **数据缓存**: 本地存储和性能优化
- **网络请求**: 统一的API调用封装

### 后端技术栈
- **Node.js**: 服务端运行环境
- **Express.js**: Web应用框架
- **Sequelize**: ORM数据库操作
- **MySQL**: 关系型数据库
- **JWT**: 用户认证和授权
- **Winston**: 日志管理系统

### 架构特点
- **多租户架构**: 支持多个养殖场独立管理
- **微服务设计**: 模块化的服务架构
- **RESTful API**: 标准化的接口设计
- **统一错误处理**: 完善的错误处理机制
- **性能监控**: 请求日志和性能统计

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- MySQL >= 5.7
- npm >= 8.0.0

### 安装部署

1. **克隆项目**
```bash
git clone https://github.com/your-org/smart-goose-saas.git
cd smart-goose-saas
```

2. **安装依赖**
```bash
npm install
```

3. **数据库初始化**
```bash
# 创建数据库
mysql -u root -p < database/init.sql

# 如果使用SAAS平台模式
mysql -u root -p < database/saas-platform-init.sql
```

4. **环境配置**
```bash
# 复制环境配置文件
cp .env.example .env.development

# 编辑配置文件，设置数据库连接等信息
nano .env.development
```

5. **启动服务**
```bash
# 开发模式
npm run dev

# 生产模式
npm start

# 启动管理后台
npm run start:admin
```

### 小程序配置

1. **导入项目**: 使用微信开发者工具导入项目根目录
2. **配置AppID**: 在 `project.config.json` 中设置你的小程序AppID
3. **配置服务器域名**: 在小程序后台配置服务器域名
4. **调试**: 启用开发者工具的本地设置

## 📁 项目结构

```
智慧养鹅/
├── 📱 小程序前端
│   ├── pages/          # 页面文件
│   ├── components/     # 组件文件
│   ├── utils/          # 工具函数
│   ├── constants/      # 常量定义
│   ├── styles/         # 样式文件
│   └── assets/         # 静态资源
├── 🖥️ 后端服务
│   ├── backend/
│   │   ├── controllers/    # 控制器
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由定义
│   │   ├── middleware/     # 中间件
│   │   ├── utils/          # 工具函数
│   │   ├── config/         # 配置文件
│   │   ├── admin/          # 管理后台
│   │   ├── saas-admin/     # SAAS管理
│   │   └── tests/          # 测试文件
├── 🗄️ 数据库
│   ├── database/
│   │   ├── init.sql           # 基础数据库结构
│   │   └── saas-platform-init.sql  # SAAS平台结构
│   └── migrations/         # 数据库迁移
├── 📚 文档
│   ├── docs/              # 技术文档
│   └── README.md          # 项目说明
└── 🔧 配置文件
    ├── package.json       # 项目配置
    ├── app.json          # 小程序配置
    └── project.config.json  # 开发工具配置
```

## 🔧 开发指南

### 代码规范

项目采用统一的代码规范，包括：

- **ESLint**: JavaScript代码质量检查
- **Prettier**: 代码格式化
- **统一注释**: JSDoc风格的函数注释
- **命名规范**: 驼峰命名法和语义化命名

### 网络请求

使用统一的网络请求封装：

```javascript
// 推荐的请求方式
const { get, post } = require('../utils/request-unified');

// GET请求
const userList = await get('/api/users', { page: 1, limit: 10 });

// POST请求
const newUser = await post('/api/users', userData);
```

### 页面开发

使用页面混入提高开发效率：

```javascript
const { createPage } = require('../utils/page-mixin');

createPage({
  // 自动获得错误处理、loading管理等功能
  initPage(options) {
    // 页面特定的初始化逻辑
  },
  
  // 其他页面方法
});
```

### 日志记录

使用统一的日志系统：

```javascript
const { Logger } = require('../utils/logger');

// 记录不同级别的日志
Logger.info('用户登录成功', { userId, loginTime });
Logger.error('数据库连接失败', { error: error.message });
Logger.business('订单创建', { orderId, amount });
```

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
npm test

# 运行集成测试
npm run test:integration

# 查看测试覆盖率
npm run test:coverage
```

### 测试用例编写
- 单元测试：测试独立的函数和模块
- 集成测试：测试API接口和数据库操作
- E2E测试：测试完整的用户流程

## 📊 监控与维护

### 性能监控
- **响应时间**: API接口响应时间统计
- **错误率**: 系统错误和异常监控
- **用户行为**: 用户操作路径分析
- **资源使用**: 服务器资源使用情况

### 日志管理
- **结构化日志**: JSON格式的日志记录
- **日志分级**: Info、Warn、Error不同级别
- **日志轮转**: 自动清理旧日志文件
- **日志查询**: 支持关键字和时间范围查询

### 数据备份
- **定期备份**: 自动化的数据库备份
- **增量备份**: 减少备份时间和存储空间
- **恢复测试**: 定期验证备份数据完整性

## 🚀 部署指南

### 生产环境部署

1. **服务器配置**
```bash
# 安装Node.js和MySQL
sudo apt update
sudo apt install nodejs npm mysql-server

# 安装PM2进程管理器
npm install -g pm2
```

2. **项目部署**
```bash
# 克隆代码
git clone https://github.com/your-org/smart-goose-saas.git
cd smart-goose-saas

# 安装依赖
npm install --production

# 配置环境变量
cp .env.example .env.production
nano .env.production

# 初始化数据库
npm run db:init
```

3. **启动服务**
```bash
# 使用PM2启动
pm2 start ecosystem.config.js --env production

# 设置开机自启
pm2 startup
pm2 save
```

### Docker部署

```dockerfile
# 使用官方Node.js镜像
FROM node:16-alpine

# 设置工作目录
WORKDIR /app

# 复制package.json文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["npm", "start"]
```

## 🤝 贡献指南

### 开发流程

1. **Fork项目**: 从主仓库fork到个人仓库
2. **创建分支**: `git checkout -b feature/new-feature`
3. **编写代码**: 遵循项目代码规范
4. **编写测试**: 为新功能添加测试用例
5. **提交代码**: `git commit -m "feat: add new feature"`
6. **推送分支**: `git push origin feature/new-feature`
7. **创建PR**: 在GitHub上创建Pull Request

### 提交规范

使用[Conventional Commits](https://conventionalcommits.org/)规范：

- `feat`: 新功能
- `fix`: 错误修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建或辅助工具变动

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。

## 📞 联系我们

- **项目维护者**: Smart Goose Team
- **邮箱**: <EMAIL>
- **技术支持**: <EMAIL>
- **文档问题**: <EMAIL>

## 🙏 致谢

感谢所有为项目做出贡献的开发者和用户。特别感谢：

- 微信小程序开发团队提供的技术支持
- Node.js和Express社区的开源贡献
- MySQL和Sequelize的优秀文档
- 所有测试用户提供的宝贵反馈

---

📝 **最后更新时间**: 2024年12月  
🔄 **版本**: v2.0.0  
📊 **状态**: 生产就绪