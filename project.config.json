{"description": "智慧养鹅微信小程序项目配置文件", "packOptions": {"ignore": [{"type": "file", "value": ".eslintrc.js"}]}, "setting": {"urlCheck": true, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": false, "autoAudits": true, "showShadowRootInWxmlPanel": true, "scopeDataCheck": true, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": false, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "ignoreUploadUnusedFiles": true}, "compileType": "miniprogram", "libVersion": "3.8.12", "appid": "wx68a4d75dd80665ec", "projectname": "smart-goose-saas", "debugOptions": {"hidedInDevtools": []}, "scripts": {}, "staticServerOptions": {"baseURL": "", "servePath": ""}, "isGameTourist": false, "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "plugin": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": []}}}